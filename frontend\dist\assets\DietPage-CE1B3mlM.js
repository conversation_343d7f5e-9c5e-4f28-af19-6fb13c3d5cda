import{c as Fe,h as Z,u as _e,as as W,y as F,b as T,r as C,j as e,aV as as,aW as ts,D as rs,n as Ge,V as ns,aX as is,aY as ls,aZ as os,a0 as Ve,a_ as cs,Y as ds,_ as ms,$ as xs,a$ as Oe,B as Te,af as Me,aj as We,b0 as gs,b1 as us,b2 as hs,b3 as ps,an as ie,a4 as fs,ao as he,G as oe,b4 as bs,a5 as js,am as qe,l as Le,b5 as Ee,f as pe,b6 as le,a7 as ue,ah as ae,b7 as vs,R as _,a6 as ys,b8 as Ns,t as He,ab as ws,H as ee,b9 as Je,ba as $e,bb as Ze,at as Ae,bc as ks,bd as Cs,P as se,aP as Ke,a8 as Ss,az as Ms,be as Ps,aa as Ds,bf as As,bg as U,bh as Fs,bi as Rs}from"./index-ClF00oko.js";import{a as Is,u as Ts}from"./useDiary-BwDTOFpy.js";import{C as qs}from"./CircularProgress-BmDVDTBm.js";import{D as Xe}from"./download-68ewrBI8.js";import{P as Es}from"./ProtocolDetailsModal-DaSPSmT-.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $s=[["path",{d:"M17 21a1 1 0 0 0 1-1v-5.35c0-.457.316-.844.727-1.041a4 4 0 0 0-2.134-7.589 5 5 0 0 0-9.186 0 4 4 0 0 0-2.134 7.588c.411.198.727.585.727 1.041V20a1 1 0 0 0 1 1Z",key:"1qvrer"}],["path",{d:"M6 17h12",key:"1jwigz"}]],Bs=Fe("chef-hat",$s);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _s=[["path",{d:"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z",key:"wa1lgi"}],["path",{d:"m8.5 8.5 7 7",key:"rvfmvr"}]],Pe=Fe("pill",_s);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ls=[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]],zs=Fe("shopping-cart",Ls);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vs=[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]],Be=Fe("utensils",Vs),De={protocols:()=>["diet","protocols"],activeProtocol:()=>["diet","protocols","active"],protocol:s=>["diet","protocols",s]};function Os(){return Z({queryKey:De.activeProtocol(),queryFn:async()=>{var s;console.log("🔍 useActiveDietProtocol: Buscando protocolo ativo...");try{const r=await T.get("users/protocols/diet/active");return console.log("✅ useActiveDietProtocol: Resposta completa:",r),(r==null?void 0:r.status)==="success"&&((s=r==null?void 0:r.data)!=null&&s.has_protocol)?(console.log("✅ useActiveDietProtocol: Protocolo ativo encontrado:",r.data),localStorage.removeItem("mock-protocol-deleted"),r.data):(console.log("⚠️ useActiveDietProtocol: Nenhum protocolo ativo encontrado no backend"),console.log("💾 useActiveDietProtocol: Nenhum protocolo ativo, retornando null"),null)}catch(r){return console.log("❌ useActiveDietProtocol: Erro ao buscar protocolo:",r),console.log("💾 useActiveDietProtocol: Erro ao buscar protocolo, retornando null"),null}},staleTime:5*60*1e3,retry:!1})}function Ks(){const s=_e();return W({mutationFn:async r=>{if(console.log("🗑️ useRemoveDietProtocol: Removendo protocolo...",r,"Tipo:",typeof r),r==="mock-protocol-id-123"||String(r)==="mock-protocol-id-123")return localStorage.removeItem("mock-protocol-data"),localStorage.setItem("mock-protocol-deleted","true"),console.log("✅ useRemoveDietProtocol: Dados mock removidos e flag de deleção definida"),{success:!0,message:"Protocolo mock removido"};console.log("🌐 useRemoveDietProtocol: Fazendo chamada para API...");const c=await T.delete(`users/protocols/diet/${r}`);return console.log("✅ useRemoveDietProtocol: Resposta da API:",c),c.data||c},onSuccess:(r,c)=>{s.invalidateQueries({queryKey:De.protocols()}),s.invalidateQueries({queryKey:De.activeProtocol()}),s.removeQueries({queryKey:De.protocol(c)}),F.success("Protocolo removido com sucesso!",{position:"bottom-right"}),console.log("✅ useRemoveDietProtocol: Protocolo removido com sucesso")},onError:r=>{console.error("❌ useRemoveDietProtocol: Erro ao remover protocolo:",r),F.error("Erro ao remover protocolo. Tente novamente.",{position:"bottom-right"})}})}function Qs({nutritionGoals:s,className:r=""}){var j,u,n,o,t,m,x,v,M,R,Q,q,k,L,X,Y,te,H;const[c,a]=C.useState("pie"),l=[{name:"Proteínas",current:((j=s.protein)==null?void 0:j.current)||0,target:((u=s.protein)==null?void 0:u.target)||0,calories:(((n=s.protein)==null?void 0:n.current)||0)*4,percentage:Math.round((((o=s.protein)==null?void 0:o.current)||0)*4/Math.max(((t=s.calories)==null?void 0:t.current)||1,1)*100),color:"#B9FF43",icon:e.jsx(as,{className:"w-4 h-4"})},{name:"Carboidratos",current:((m=s.carbs)==null?void 0:m.current)||0,target:((x=s.carbs)==null?void 0:x.target)||0,calories:(((v=s.carbs)==null?void 0:v.current)||0)*4,percentage:Math.round((((M=s.carbs)==null?void 0:M.current)||0)*4/Math.max(((R=s.calories)==null?void 0:R.current)||1,1)*100),color:"#4CAF50",icon:e.jsx(ts,{className:"w-4 h-4"})},{name:"Gorduras",current:((Q=s.fat)==null?void 0:Q.current)||0,target:((q=s.fat)==null?void 0:q.target)||0,calories:(((k=s.fat)==null?void 0:k.current)||0)*9,percentage:Math.round((((L=s.fat)==null?void 0:L.current)||0)*9/Math.max(((X=s.calories)==null?void 0:X.current)||1,1)*100),color:"#FFC107",icon:e.jsx(rs,{className:"w-4 h-4"})}],g=l.map(w=>({name:w.name,value:w.calories,percentage:w.percentage,color:w.color})),h=l.map(w=>({name:w.name.substring(0,4),current:w.current,target:w.target,color:w.color})),b=l.reduce((w,$)=>w+$.calories,0),y=Math.round(b/Math.max(((Y=s.calories)==null?void 0:Y.target)||1,1)*100),f=(((te=s.calories)==null?void 0:te.target)||0)-b,d=({active:w,payload:$})=>{if(w&&$&&$.length){const G=$[0];return e.jsxs("div",{className:"bg-snapfit-dark-gray border border-snapfit-green/30 rounded-lg p-3 shadow-lg",children:[e.jsx("p",{className:"text-white font-medium",children:G.name}),e.jsxs("p",{className:"text-snapfit-green",children:[G.value," kcal (",G.payload.percentage,"%)"]})]})}return null};return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20 ${r}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(Ge,{className:"w-5 h-5 text-snapfit-green"}),e.jsx("h3",{className:"text-base sm:text-lg font-bold text-white",children:"Distribuição Calórica"})]}),e.jsxs("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20",children:[e.jsx("button",{onClick:()=>a("pie"),className:`px-3 py-1 text-xs rounded-md transition-colors ${c==="pie"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white"}`,children:"Pizza"}),e.jsx("button",{onClick:()=>a("bar"),className:`px-3 py-1 text-xs rounded-md transition-colors ${c==="bar"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white"}`,children:"Barras"})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-3 mb-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Consumidas"}),e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:b}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Meta"}),e.jsx("div",{className:"text-lg font-bold text-white",children:((H=s.calories)==null?void 0:H.target)||0}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 text-center border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1",children:"Restantes"}),e.jsx("div",{className:`text-lg font-bold ${f>=0?"text-blue-400":"text-red-400"}`,children:Math.abs(f)}),e.jsx("div",{className:"text-xs text-gray-500",children:"kcal"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between text-sm mb-2",children:[e.jsx("span",{className:"text-gray-400",children:"Progresso Calórico"}),e.jsxs("span",{className:"text-white font-medium",children:[y,"%"]})]}),e.jsx("div",{className:"w-full bg-snapfit-dark-gray rounded-full h-2 border border-snapfit-green/20",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-500 ${y<=100?"bg-snapfit-green":"bg-red-400"}`,style:{width:`${Math.min(y,100)}%`}})})]}),e.jsx("div",{className:"h-[250px] mb-6",children:e.jsx(ns,{width:"100%",height:"100%",children:c==="pie"?e.jsxs(is,{children:[e.jsx(ls,{data:g,dataKey:"value",nameKey:"name",cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:2,stroke:"transparent",children:g.map((w,$)=>e.jsx(os,{fill:w.color,filter:"drop-shadow(0 0 3px rgba(185, 255, 67, 0.3))"},$))}),e.jsx(Ve,{content:e.jsx(d,{})})]}):e.jsxs(cs,{data:h,margin:{top:5,right:5,bottom:5,left:5},children:[e.jsx(ds,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),e.jsx(ms,{dataKey:"name",fontSize:12,stroke:"rgba(255,255,255,0.5)"}),e.jsx(xs,{fontSize:12,stroke:"rgba(255,255,255,0.5)"}),e.jsx(Ve,{contentStyle:{backgroundColor:"#1E1E1E",border:"1px solid rgba(185, 255, 67, 0.3)",borderRadius:"8px",color:"white"}}),e.jsx(Oe,{dataKey:"current",name:"Atual",fill:"#B9FF43",radius:[2,2,0,0]}),e.jsx(Oe,{dataKey:"target",name:"Meta",fill:"rgba(255,255,255,0.2)",radius:[2,2,0,0]})]})})}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-3",children:l.map((w,$)=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center",style:{backgroundColor:`${w.color}20`},children:e.jsx("div",{style:{color:w.color},children:w.icon})}),e.jsx("span",{className:"text-sm font-medium text-white",children:w.name})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Atual"}),e.jsxs("span",{className:"text-white font-medium",children:[w.current,"g"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Meta"}),e.jsxs("span",{className:"text-gray-300",children:[w.target,"g"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"Calorias"}),e.jsxs("span",{style:{color:w.color},className:"font-medium",children:[w.calories," kcal"]})]}),e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-gray-400",children:"% Total"}),e.jsxs("span",{style:{color:w.color},className:"font-medium",children:[w.percentage,"%"]})]})]}),e.jsx("div",{className:"mt-3",children:e.jsx("div",{className:"w-full bg-gray-600 rounded-full h-1.5",children:e.jsx("div",{className:"h-1.5 rounded-full transition-all duration-500",style:{width:`${Math.min(w.current/w.target*100,100)}%`,backgroundColor:w.color}})})})]},$))})]})}function Us({onGenerateAI:s,onCreateManual:r,onImportNutritionist:c,onReadProtocol:a,onClose:l}){return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Novo Protocolo de Dieta"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{onClick:s,className:`\r
            w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10`,children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Te,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Gerar com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gerar protocolo personalizado baseado no seu perfil"})]})]}),e.jsxs("button",{onClick:r,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Me,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Criar Manualmente"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Monte seu próprio protocolo selecionando alimentos"})]})]}),a&&e.jsxs("button",{onClick:a,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(We,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Ler seu protocolo atual com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Faça upload de um PDF ou imagem do seu protocolo atual"})]})]}),e.jsxs("button",{onClick:c,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Xe,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Importar do Nutricionista"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Importar protocolo criado pelo seu nutricionista"})]})]})]}),e.jsx("button",{onClick:l,className:"w-full mt-6 px-4 py-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"})]})})}function Gs({protocolGoals:s,className:r=""}){const{getAssessmentForWeightEstimation:c,hasAssessment:a,getAssessmentSummary:l}=gs(),g=c()||us(),h=l();return!s.calories||s.calories<=0?null:e.jsxs("div",{className:`space-y-2 ${r}`,children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Estimativa:"}),e.jsx(hs,{targetCalories:s.calories,userData:g}),e.jsx(ps,{})]}),a&&h?e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx("span",{className:"text-snapfit-green",children:"📊"}),e.jsxs("span",{children:["Baseado na avaliação física de ",h.formattedDate,h.isRecent?e.jsx("span",{className:"text-snapfit-green ml-1",children:"(recente)"}):e.jsxs("span",{className:"text-yellow-400 ml-1",children:["(",h.daysSince," dias atrás)"]})]})]}):e.jsxs("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[e.jsx("span",{className:"text-yellow-400",children:"⚠️"}),e.jsxs("span",{children:["Baseado em dados padrão -",e.jsx("button",{className:"text-snapfit-green hover:underline ml-1",children:"faça uma avaliação física para maior precisão"})]})]})]})}const ce=[{id:"vitamin_d",name:"Vitamina D",unit:"μg",dailyValue:15,category:"vitamin",description:"Essencial para saúde óssea e função imunológica",sources:["Exposição solar","Peixes gordurosos","Ovos","Cogumelos"],deficiencySymptoms:["Fadiga","Dores ósseas","Fraqueza muscular","Depressão"]},{id:"vitamin_b12",name:"Vitamina B12",unit:"μg",dailyValue:2.4,category:"vitamin",description:"Crucial para função neurológica e formação de glóbulos vermelhos",sources:["Carnes","Peixes","Laticínios","Ovos"],deficiencySymptoms:["Fadiga","Anemia","Problemas neurológicos","Depressão"]},{id:"iron",name:"Ferro",unit:"mg",dailyValue:14,category:"mineral",description:"Essencial para transporte de oxigênio no sangue",sources:["Carnes vermelhas","Feijão","Espinafre","Quinoa"],deficiencySymptoms:["Fadiga","Anemia","Palidez","Falta de ar"]},{id:"magnesium",name:"Magnésio",unit:"mg",dailyValue:400,category:"mineral",description:"Importante para função muscular e óssea",sources:["Nozes","Sementes","Vegetais verdes","Grãos integrais"],deficiencySymptoms:["Cãibras","Fadiga","Irritabilidade","Insônia"]},{id:"zinc",name:"Zinco",unit:"mg",dailyValue:11,category:"mineral",description:"Essencial para sistema imunológico e cicatrização",sources:["Carnes","Frutos do mar","Sementes","Nozes"],deficiencySymptoms:["Imunidade baixa","Cicatrização lenta","Perda de apetite"]},{id:"vitamin_c",name:"Vitamina C",unit:"mg",dailyValue:90,category:"vitamin",description:"Antioxidante poderoso, essencial para colágeno",sources:["Frutas cítricas","Morango","Kiwi","Pimentão"],deficiencySymptoms:["Fadiga","Imunidade baixa","Sangramento gengival"]}],Ws=s=>{switch(s){case"deficient":return"text-red-500";case"low":return"text-orange-500";case"adequate":return"text-green-500";case"high":return"text-yellow-500";case"toxic":return"text-red-600";default:return"text-gray-500"}},Hs=s=>{switch(s){case"deficient":return"❌";case"low":return"⚠️";case"adequate":return"✅";case"high":return"🟡";case"toxic":return"🔴";default:return"❓"}},Re=()=>{const s=_e(),{data:r,isLoading:c,error:a}=Z({queryKey:["micronutrients","analysis"],queryFn:async()=>{var u,n;console.log("🔄 Fetching nutritional analysis...");try{const o=await T.get("users/progress/nutritional_analysis");if((n=(u=o==null?void 0:o.data)==null?void 0:u.weekly_averages)!=null&&n.macronutrient_distribution){console.log("✅ Success with nutritional analysis endpoint",o.data);const t=o.data.weekly_averages.macronutrient_distribution,m=Js(t);return{date:new Date().toISOString(),totalIntake:m,deficiencies:m.filter(x=>x.status==="deficient"),excesses:m.filter(x=>x.status==="excess"),recommendations:Ye(m),overallScore:es(m),improvementAreas:["Vitamina D","Ferro","Vitamina B12"],macronutrients:t}}return console.log("⚠️ No nutritional data found, using mock data"),Qe()}catch(o){return console.error("❌ Failed to fetch nutritional analysis:",o),console.log("🔄 Using fallback mock data due to API error"),Qe()}},staleTime:1e3*60*10,refetchOnWindowFocus:!1,retry:1,throwOnError:!1}),{data:l=[],isLoading:g,error:h}=Z({queryKey:["micronutrients","bloodtests"],queryFn:async()=>{console.log("🔄 Fetching blood tests...");try{const u=await T.get("users/blood-tests");return u!=null&&u.data?(console.log("✅ Success with blood tests endpoint",u.data),Array.isArray(u.data)?u.data:[]):[]}catch(u){return console.error("❌ Failed to fetch blood tests:",u),[]}},staleTime:1e3*60*15,refetchOnWindowFocus:!1,retry:2,throwOnError:!1}),b=c||g;console.log("🔍 useMicronutrients debug:",{analysis:!!r,bloodTests:!!l,isLoadingAnalysis:c,isLoadingBloodTests:g,analysisError:!!a,bloodTestsError:!!h});const y=!r&&a||!l&&h,f=C.useMemo(()=>{if(!r||!r.totalIntake||!Array.isArray(r.totalIntake))return{total:0,adequate:0,deficient:0,adequatePercentage:0,criticalDeficiencies:[],overallScore:0,hasRecentBloodTest:!1};const u=r.totalIntake.length,n=r.totalIntake.filter(m=>m.status==="adequate").length,o=r.totalIntake.filter(m=>m.status==="deficient"||m.status==="low").length,t=r.totalIntake.filter(m=>m.status==="deficient").map(m=>{var x;return((x=ce.find(v=>v.id===m.micronutrientId))==null?void 0:x.name)||m.name}).filter(Boolean);return{total:u,adequate:n,deficient:o,adequatePercentage:u>0?Math.round(n/u*100):0,criticalDeficiencies:t,overallScore:r.overallScore||0,hasRecentBloodTest:l.length>0&&new Date(l[0].date)>new Date(Date.now()-90*24*60*60*1e3)}},[r,l]),d=W({mutationFn:async u=>{console.log("📤 Uploading blood test file:",u.name);const n=new FormData;return n.append("file",u),(await T.post("users/blood-tests/upload",n)).data},onSuccess:()=>{s.invalidateQueries({queryKey:["micronutrients","bloodtests"]}),s.invalidateQueries({queryKey:["micronutrients","analysis"]})}}),j=W({mutationFn:async u=>(console.log("💊 Generating supplement plan for:",u),(await T.post("users/supplements/plan",{recommendations:u})).data)});return{analysis:r,bloodTests:l,summary:f,isLoading:b,error:y,uploadBloodTest:d.mutateAsync,generateSupplementPlan:j.mutateAsync,isUploadingBloodTest:d.isPending,isGeneratingPlan:j.isPending,micronutrientsData:ce}};function Js(s){const{calories:r,protein:c,carbs:a,fat:l}=s;return[{micronutrientId:"vitamin_d",name:"Vitamina D",currentIntake:Math.max(5,Math.min(15,r/200)),recommendedIntake:10,unit:"mcg",status:r>1800?"adequate":"deficient",percentage:Math.min(100,r/2e3*80)},{micronutrientId:"iron",name:"Ferro",currentIntake:Math.max(8,Math.min(18,c/10)),recommendedIntake:14,unit:"mg",status:c>100?"adequate":"low",percentage:Math.min(100,c/120*90)},{micronutrientId:"vitamin_b12",name:"Vitamina B12",currentIntake:Math.max(1.5,Math.min(3,c/50)),recommendedIntake:2.4,unit:"mcg",status:c>80?"adequate":"deficient",percentage:Math.min(100,c/100*85)},{micronutrientId:"calcium",name:"Cálcio",currentIntake:Math.max(600,Math.min(1200,r*.5)),recommendedIntake:1e3,unit:"mg",status:r>2e3?"adequate":"low",percentage:Math.min(100,r/2200*95)},{micronutrientId:"vitamin_c",name:"Vitamina C",currentIntake:Math.max(40,Math.min(120,a/2)),recommendedIntake:90,unit:"mg",status:a>150?"adequate":"low",percentage:Math.min(100,a/180*88)},{micronutrientId:"omega_3",name:"Ômega-3",currentIntake:Math.max(.8,Math.min(2.5,l/30)),recommendedIntake:1.6,unit:"g",status:l>50?"adequate":"deficient",percentage:Math.min(100,l/60*92)}]}function Ye(s){return s.filter(a=>a.status==="deficient"||a.status==="low").map(a=>({micronutrientId:a.micronutrientId,recommendation:`Aumentar consumo de ${a.name}`,foods:Zs(a.micronutrientId),priority:a.status==="deficient"?"high":"medium"}))}function Zs(s){return{vitamin_d:["Salmão","Sardinha","Ovos","Cogumelos"],iron:["Carne vermelha","Feijão","Espinafre","Lentilha"],vitamin_b12:["Carne","Peixe","Ovos","Laticínios"],calcium:["Leite","Queijo","Iogurte","Brócolis"],vitamin_c:["Laranja","Morango","Kiwi","Pimentão"],omega_3:["Salmão","Sardinha","Nozes","Linhaça"]}[s]||["Alimentos variados"]}function es(s){const r=s.reduce((c,a)=>c+a.percentage,0);return Math.round(r/s.length)}function Qe(){const s=[{micronutrientId:"vitamin_d",name:"Vitamina D",currentIntake:8,recommendedIntake:10,unit:"mcg",status:"low",percentage:80},{micronutrientId:"iron",name:"Ferro",currentIntake:12,recommendedIntake:14,unit:"mg",status:"adequate",percentage:86},{micronutrientId:"vitamin_b12",name:"Vitamina B12",currentIntake:1.8,recommendedIntake:2.4,unit:"mcg",status:"deficient",percentage:75}];return{date:new Date().toISOString(),totalIntake:s,deficiencies:s.filter(r=>r.status==="deficient"),excesses:[],recommendations:Ye(s),overallScore:es(s),improvementAreas:["Vitamina D","Vitamina B12"],macronutrients:{calories:0,protein:0,carbs:0,fat:0}}}function Xs({data:s}){const g=s.map(t=>{const m=ce.find(x=>x.id===t.micronutrientId);return{name:(m==null?void 0:m.name)||t.micronutrientId,value:Math.min(t.percentage,200),status:t.status}}),h=2*Math.PI/g.length,b=(t,m)=>{const x=150+m*Math.cos(t-Math.PI/2),v=150+m*Math.sin(t-Math.PI/2);return{x,y:v}},y=g.map((t,m)=>{const x=m*h,v=t.value/100*120;return b(x,v)}),f=`M ${y.map(t=>`${t.x},${t.y}`).join(" L ")} Z`,d=Array.from({length:5},(t,m)=>{const x=(m+1)/5*120;return e.jsx("circle",{cx:150,cy:150,r:x,fill:"none",stroke:"rgba(34, 197, 94, 0.2)",strokeWidth:"1"},m)}),j=g.map((t,m)=>{const x=m*h,v=b(x,120);return e.jsx("line",{x1:150,y1:150,x2:v.x,y2:v.y,stroke:"rgba(34, 197, 94, 0.2)",strokeWidth:"1"},m)}),u=g.map((t,m)=>{const x=m*h,M=b(x,140);return e.jsx("text",{x:M.x,y:M.y,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs fill-gray-300",fontSize:"11",children:t.name},m)}),n=Array.from({length:5},(t,m)=>{const x=(m+1)/5*100,v=(m+1)/5*120;return e.jsxs("text",{x:155,y:150-v,className:"text-xs fill-gray-400",fontSize:"10",children:[x,"%"]},m)}),o=y.map((t,m)=>{const x=g[m],v=x.status==="adequate"?"#22c55e":x.status==="low"?"#f59e0b":x.status==="deficient"?"#ef4444":x.status==="high"?"#eab308":"#dc2626";return e.jsxs("g",{children:[e.jsx("circle",{cx:t.x,cy:t.y,r:"4",fill:v,stroke:"white",strokeWidth:"2"}),e.jsx("circle",{cx:t.x,cy:t.y,r:"8",fill:"transparent",className:"cursor-pointer",children:e.jsx("title",{children:`${x.name}: ${x.value.toFixed(1)}%`})})]},m)});return e.jsxs("div",{className:"flex flex-col items-center space-y-4",children:[e.jsxs("svg",{width:300,height:300,className:"overflow-visible",children:[d,j,e.jsx("path",{d:f,fill:"rgba(34, 197, 94, 0.1)",stroke:"rgba(34, 197, 94, 0.6)",strokeWidth:"2"}),o,u,n]}),e.jsxs("div",{className:"flex flex-wrap justify-center gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Adequado (≥80%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-yellow-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Alto (>120%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-orange-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Baixo (50-80%)"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 bg-red-500 rounded-full"}),e.jsx("span",{className:"text-gray-300",children:"Deficiente (<50%)"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-md text-center",children:[e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-green-400",children:g.filter(t=>t.status==="adequate").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Adequados"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-orange-400",children:g.filter(t=>t.status==="low").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Baixos"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-red-400",children:g.filter(t=>t.status==="deficient").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Deficientes"})]}),e.jsxs("div",{className:"p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-lg font-bold text-yellow-400",children:g.filter(t=>t.status==="high").length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Altos"})]})]})]})}function Ue(){const{analysis:s,summary:r,isLoading:c,error:a}=Re();return c?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded"}),e.jsx("div",{className:"space-y-2",children:[1,2,3].map(l=>e.jsx("div",{className:"h-16 bg-gray-600 rounded"},l))})]})}):a||!s||!r?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(ie,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar análise"}),e.jsx("p",{className:"text-gray-400",children:a||"Dados não disponíveis"})]}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Análise de Micronutrientes"}),e.jsx(fs,{title:"Base Científica da Análise",description:"A análise de micronutrientes segue as Ingestões Diárias Recomendadas (IDR) da ANVISA e diretrizes internacionais da OMS para avaliação nutricional."})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center",children:e.jsx(he,{className:"w-5 h-5 text-green-400"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:r.adequate}),e.jsx("div",{className:"text-sm text-gray-400",children:"Adequados"})]})]})}),e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center",children:e.jsx(ie,{className:"w-5 h-5 text-orange-400"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-orange-400",children:r.deficient}),e.jsx("div",{className:"text-sm text-gray-400",children:"Precisam atenção"})]})]})}),e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center",children:e.jsx(oe,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:r.overallScore}),e.jsx("div",{className:"text-sm text-gray-400",children:"Score geral"})]})]})})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx("span",{children:"📊"}),"Visão Geral dos Micronutrientes"]}),e.jsx(Xs,{data:s.totalIntake})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx("span",{children:"📋"}),"Análise Detalhada"]}),e.jsx("div",{className:"space-y-4",children:s.totalIntake.map(l=>{const g=ce.find(h=>h.id===l.micronutrientId);return g?e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("span",{className:"text-lg",children:Hs(l.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:g.name}),e.jsx("p",{className:"text-sm text-gray-400",children:g.description})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`text-sm font-medium ${Ws(l.status)}`,children:[l.percentage,"% da IDR"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[l.amount.toFixed(1)," ",g.unit]})]})]}),e.jsx("div",{className:"mb-3",children:e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${l.status==="adequate"?"bg-green-500":l.status==="low"?"bg-orange-500":l.status==="deficient"?"bg-red-500":l.status==="high"?"bg-yellow-500":"bg-red-600"}`,style:{width:`${Math.min(l.percentage,100)}%`}})})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Principais fontes:"}),e.jsx("div",{className:"text-gray-300",children:g.sources.slice(0,3).join(", ")})]}),(l.status==="deficient"||l.status==="low")&&e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Sintomas de deficiência:"}),e.jsx("div",{className:"text-orange-300",children:g.deficiencySymptoms.slice(0,2).join(", ")})]})]})]},l.micronutrientId):null})})]}),s.improvementAreas.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(bs,{className:"w-5 h-5 text-snapfit-green"}),"Áreas de Melhoria"]}),e.jsx("div",{className:"space-y-2",children:s.improvementAreas.map((l,g)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-snapfit-green",children:"•"}),e.jsx("span",{className:"text-gray-300",children:l})]},g))})]}),e.jsx(js,{context:"análise de micronutrientes"})]})}function Ys(){const{bloodTests:s,isLoading:r,uploadBloodTest:c}=Re(),[a,l]=C.useState(!1),[g,h]=C.useState(!1),b=async u=>{if(!u.type.includes("pdf")&&!u.type.includes("image")){alert("Por favor, envie apenas arquivos PDF ou imagens");return}try{h(!0),await c(u)}catch(n){console.error("Erro no upload:",n)}finally{h(!1)}},y=u=>{u.preventDefault(),l(!1);const n=Array.from(u.dataTransfer.files);n.length>0&&b(n[0])},f=u=>{const n=u.target.files;n&&n.length>0&&b(n[0])},d=u=>{switch(u){case"low":return e.jsx(Ee,{className:"w-4 h-4 text-red-400"});case"high":return e.jsx(oe,{className:"w-4 h-4 text-orange-400"});default:return e.jsx(he,{className:"w-4 h-4 text-green-400"})}},j=u=>{switch(u){case"low":return"text-red-400";case"high":return"text-orange-400";default:return"text-green-400"}};return r?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded"})]})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(qe,{className:"w-5 h-5 text-snapfit-green"}),"Upload de Exames"]}),e.jsx("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${a?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-snapfit-green/50"}`,onDrop:y,onDragOver:u=>u.preventDefault(),onDragEnter:()=>l(!0),onDragLeave:()=>l(!1),children:g?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto animate-pulse",children:e.jsx(Te,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Analisando exame com IA..."}),e.jsx("div",{className:"text-sm text-gray-400",children:"Isso pode levar alguns segundos"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(qe,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Arraste seus exames aqui ou clique para selecionar"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suportamos PDF e imagens (JPG, PNG)"})]}),e.jsx("input",{type:"file",accept:".pdf,image/*",onChange:f,className:"hidden",id:"file-upload"}),e.jsxs("label",{htmlFor:"file-upload",className:"inline-flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors cursor-pointer",children:[e.jsx(Me,{className:"w-4 h-4"}),"Selecionar Arquivo"]})]})}),e.jsx("div",{className:"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Te,{className:"w-5 h-5 text-blue-400 mt-0.5"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("div",{className:"text-blue-400 font-medium mb-1",children:"IA Avançada"}),e.jsx("div",{className:"text-gray-300",children:"Nossa IA analisa automaticamente seus exames e identifica valores relacionados aos micronutrientes, correlacionando com sua alimentação atual para gerar recomendações personalizadas."})]})]})})]}),s.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Me,{className:"w-5 h-5 text-snapfit-green"}),"Histórico de Exames"]}),e.jsx("div",{className:"space-y-4",children:s.map(u=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(Le,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-white font-medium",children:new Date(u.date).toLocaleDateString("pt-BR")}),u.analyzedByAI&&e.jsx("span",{className:"px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded-full",children:"Analisado por IA"})]}),u.uploadedFile&&e.jsx("span",{className:"text-xs text-gray-400",children:u.uploadedFile})]}),e.jsx("div",{className:"grid gap-3",children:u.results.map(n=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-dark-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[d(n.status),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:n.testName}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Referência: ",n.referenceRange.min," - ",n.referenceRange.max," ",n.unit]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-medium ${j(n.status)}`,children:[n.value," ",n.unit]}),e.jsx("div",{className:"text-xs text-gray-400 capitalize",children:n.status==="normal"?"Normal":n.status==="low"?"Baixo":"Alto"})]})]},n.id))}),u.notes&&e.jsxs("div",{className:"mt-3 p-3 bg-gray-700/50 rounded text-sm text-gray-300",children:[e.jsx("strong",{children:"Observações:"})," ",u.notes]})]},u.id))})]}),s.length===0&&!r&&e.jsxs("div",{className:"text-center py-12",children:[e.jsx(Me,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum exame encontrado"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Faça upload dos seus exames de sangue para uma análise mais precisa dos micronutrientes"}),e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("div",{children:"✓ Análise automática por IA"}),e.jsx("div",{children:"✓ Correlação com sua alimentação"}),e.jsx("div",{children:"✓ Recomendações personalizadas"})]})]})]})}function ea(){const{analysis:s,generateSupplementPlan:r,isLoading:c}=Re(),[a,l]=C.useState([]),[g,h]=C.useState(null),[b,y]=C.useState(!1),f=t=>{l(m=>m.includes(t)?m.filter(x=>x!==t):[...m,t])},d=async()=>{if(a.length!==0)try{y(!0);const t=await r(a);h(t)}catch(t){console.error("Erro ao gerar plano:",t)}finally{y(!1)}},j=t=>{switch(t){case"high":return"text-red-400 bg-red-500/20";case"medium":return"text-yellow-400 bg-yellow-500/20";case"low":return"text-green-400 bg-green-500/20"}},u=t=>{switch(t){case"high":return"Alta";case"medium":return"Média";case"low":return"Baixa"}},n=t=>{switch(t){case"morning":return"🌅";case"afternoon":return"☀️";case"evening":return"🌙";case"with_meal":return"🍽️";case"empty_stomach":return"⏰"}},o=t=>{switch(t){case"morning":return"Manhã";case"afternoon":return"Tarde";case"evening":return"Noite";case"with_meal":return"Com refeição";case"empty_stomach":return"Estômago vazio"}};return c?e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),[1,2,3].map(t=>e.jsx("div",{className:"h-32 bg-gray-600 rounded"},t))]})}):!s||s.recommendations.length===0?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(Pe,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhuma recomendação"}),e.jsx("p",{className:"text-gray-400",children:"Seus níveis de micronutrientes estão adequados! Continue com sua alimentação balanceada."})]}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-2 flex items-center gap-2",children:[e.jsx(Pe,{className:"w-5 h-5 text-snapfit-green"}),"Recomendações de Suplementos"]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Baseado na sua análise nutricional e exames de sangue, recomendamos os seguintes suplementos:"})]}),e.jsx("div",{className:"space-y-4",children:s.recommendations.map(t=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("input",{type:"checkbox",checked:a.includes(t.id),onChange:()=>f(t.id),className:"mt-1 w-4 h-4 text-snapfit-green bg-gray-700 border-gray-600 rounded focus:ring-snapfit-green"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx("h4",{className:"text-white font-medium",children:t.name}),e.jsx("span",{className:`px-2 py-1 text-xs rounded-full ${j(t.priority)}`,children:u(t.priority)})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:t.reason})]})]}),t.cost&&e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:["R$ ",t.cost.toFixed(2)]}),e.jsx("div",{className:"text-xs text-gray-400",children:"por mês"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(Pe,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-gray-400",children:"Dosagem:"}),e.jsxs("span",{className:"text-white",children:[t.dosage," ",t.unit]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(pe,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-gray-400",children:"Horário:"}),e.jsxs("span",{className:"text-white",children:[n(t.timing)," ",o(t.timing)]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Duração:"}),e.jsx("span",{className:"text-white",children:t.duration})]})]}),(t.interactions.length>0||t.sideEffects.length>0)&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[t.interactions.length>0&&e.jsxs("div",{children:[e.jsxs("div",{className:"text-yellow-400 mb-1 flex items-center gap-1",children:[e.jsx(ie,{className:"w-3 h-3"}),"Interações:"]}),e.jsx("ul",{className:"text-gray-300 space-y-1",children:t.interactions.map((m,x)=>e.jsxs("li",{className:"text-xs",children:["• ",m]},x))})]}),t.sideEffects.length>0&&e.jsxs("div",{children:[e.jsx("div",{className:"text-gray-400 mb-1",children:"Efeitos colaterais:"}),e.jsx("ul",{className:"text-gray-300 space-y-1",children:t.sideEffects.map((m,x)=>e.jsxs("li",{className:"text-xs",children:["• ",m]},x))})]})]})]},t.id))}),a.length>0&&!g&&e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h4",{className:"text-white font-medium mb-1",children:[a.length," suplemento(s) selecionado(s)"]}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Gere um plano personalizado com cronograma e estimativa de custos"})]}),e.jsx("button",{onClick:d,disabled:b,className:"flex items-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:b?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Gerando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(le,{className:"w-4 h-4"}),"Gerar Plano"]})})]})}),g&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(he,{className:"w-5 h-5 text-green-400"}),"Seu Plano de Suplementação"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:["R$ ",g.totalCost.toFixed(2)]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Custo total mensal"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:g.duration}),e.jsx("div",{className:"text-sm text-gray-400",children:"Duração recomendada"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:g.recommendations.length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suplementos"})]})]}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsx("h4",{className:"text-white font-medium",children:"Melhorias esperadas:"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:g.expectedImprovements.map((t,m)=>e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-300",children:[e.jsx(he,{className:"w-4 h-4 text-green-400"}),t]},m))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{className:"flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(zs,{className:"w-4 h-4"}),"Comprar Suplementos"]}),e.jsx("button",{onClick:()=>h(null),className:"px-4 py-3 text-gray-400 border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors",children:"Modificar"})]})]})]})}function sa(){const[s,r]=C.useState("3months"),[c,a]=C.useState("all"),{data:l,isLoading:g,error:h}=Z({queryKey:["micronutrients","evolution",s],queryFn:async()=>(await T.get(`users/micronutrients/evolution?period=${s}`)).data,staleTime:1e3*60*10}),y=l||{vitamin_d:{timeline:[{date:"2024-01-01",value:8.5,percentage:57},{date:"2024-01-15",value:12.2,percentage:81},{date:"2024-02-01",value:15.8,percentage:105},{date:"2024-02-15",value:18.3,percentage:122}],trend:"improving",targetReached:!0},vitamin_b12:{timeline:[{date:"2024-01-01",value:1.2,percentage:50},{date:"2024-01-15",value:1.8,percentage:75},{date:"2024-02-01",value:2.1,percentage:88},{date:"2024-02-15",value:2.4,percentage:100}],trend:"improving",targetReached:!0},iron:{timeline:[{date:"2024-01-01",value:12.8,percentage:91},{date:"2024-01-15",value:13.2,percentage:94},{date:"2024-02-01",value:12.5,percentage:89},{date:"2024-02-15",value:13.8,percentage:99}],trend:"stable",targetReached:!1}},f=[{value:"1month",label:"1 mês"},{value:"3months",label:"3 meses"},{value:"6months",label:"6 meses"},{value:"1year",label:"1 ano"}];if(g)return e.jsx("div",{className:"p-6 space-y-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"h-32 bg-gray-600 rounded"}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[1,2,3].map(o=>e.jsx("div",{className:"h-64 bg-gray-600 rounded"},o))})]})});if(h)return e.jsxs("div",{className:"p-6 text-center",children:[e.jsx(Ee,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar evolução"}),e.jsx("p",{className:"text-gray-400",children:"Não foi possível carregar os dados de evolução nutricional."})]});const d=o=>{switch(o){case"improving":return e.jsx(oe,{className:"w-4 h-4 text-green-400"});case"declining":return e.jsx(Ee,{className:"w-4 h-4 text-red-400"});default:return e.jsx("div",{className:"w-4 h-4 bg-yellow-400 rounded-full"})}},j=o=>{switch(o){case"improving":return"text-green-400";case"declining":return"text-red-400";default:return"text-yellow-400"}},u=o=>{switch(o){case"improving":return"Melhorando";case"declining":return"Piorando";default:return"Estável"}},n=o=>{const t=Math.max(...o.timeline.map(x=>x.percentage)),m=o.timeline.map((x,v)=>{const M=v/(o.timeline.length-1)*300,R=100-x.percentage/t*80;return`${M},${R}`}).join(" ");return e.jsxs("svg",{width:"300",height:"100",className:"w-full h-24",children:[e.jsx("defs",{children:e.jsxs("linearGradient",{id:"gradient",x1:"0%",y1:"0%",x2:"0%",y2:"100%",children:[e.jsx("stop",{offset:"0%",stopColor:"rgba(34, 197, 94, 0.3)"}),e.jsx("stop",{offset:"100%",stopColor:"rgba(34, 197, 94, 0.1)"})]})}),e.jsx("line",{x1:"0",y1:"20",x2:"300",y2:"20",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("line",{x1:"0",y1:"50",x2:"300",y2:"50",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("line",{x1:"0",y1:"80",x2:"300",y2:"80",stroke:"rgba(75, 85, 99, 0.3)",strokeWidth:"1"}),e.jsx("polygon",{points:`0,100 ${m} 300,100`,fill:"url(#gradient)"}),e.jsx("polyline",{points:m,fill:"none",stroke:"rgb(34, 197, 94)",strokeWidth:"2"}),o.timeline.map((x,v)=>{const M=v/(o.timeline.length-1)*300,R=100-x.percentage/t*80;return e.jsx("circle",{cx:M,cy:R,r:"3",fill:"rgb(34, 197, 94)",stroke:"white",strokeWidth:"2"},v)})]})};return e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(oe,{className:"w-5 h-5 text-snapfit-green"}),"Evolução Temporal"]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3",children:[e.jsx("select",{value:s,onChange:o=>r(o.target.value),className:"px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white text-sm focus:ring-1 focus:ring-snapfit-green",children:f.map(o=>e.jsx("option",{value:o.value,children:o.label},o.value))}),e.jsxs("select",{value:c,onChange:o=>a(o.target.value),className:"px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white text-sm focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"all",children:"Todos os micronutrientes"}),ce.map(o=>e.jsx("option",{value:o.id,children:o.name},o.id))]}),e.jsxs("button",{className:"flex items-center gap-2 px-3 py-2 text-sm text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:[e.jsx(Xe,{className:"w-4 h-4"}),"Exportar"]})]})]})}),e.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:Object.entries(y).map(([o,t])=>{const m=ce.find(R=>R.id===o);if(!m)return null;const x=t.timeline[t.timeline.length-1],v=t.timeline[0],M=x.percentage-v.percentage;return e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:m.name}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[d(t.trend),e.jsx("span",{className:j(t.trend),children:u(t.trend)}),t.targetReached&&e.jsx("span",{className:"px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded-full",children:"Meta atingida"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-medium",children:[x.percentage,"%"]}),e.jsxs("div",{className:`text-sm ${M>=0?"text-green-400":"text-red-400"}`,children:[M>=0?"+":"",M.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-4",children:n(t)}),e.jsx("div",{className:"space-y-2",children:t.timeline.slice(-3).map((R,Q)=>e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:new Date(R.date).toLocaleDateString("pt-BR")}),e.jsxs("span",{className:"text-white",children:[R.value.toFixed(1)," ",m.unit," (",R.percentage,"%)"]})]},Q))})]},o)})}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Ge,{className:"w-5 h-5 text-snapfit-green"}),"Resumo do Progresso"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:"2"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Metas atingidas"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:"1"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Em progresso"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:"+28%"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Melhoria média"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:"45"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Dias de acompanhamento"})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"💡 Insights da Evolução"}),e.jsxs("div",{className:"space-y-3 text-sm",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-green-400",children:"✓"}),e.jsx("span",{className:"text-gray-300",children:"Sua Vitamina D melhorou 65% desde o início da suplementação"})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-green-400",children:"✓"}),e.jsx("span",{className:"text-gray-300",children:"B12 atingiu níveis adequados após 6 semanas de suplementação"})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-yellow-400",children:"⚠"}),e.jsx("span",{className:"text-gray-300",children:"Ferro mantém-se estável, considere aumentar fontes alimentares"})]})]})]})]})}const aa=[{id:"analysis",label:"Micronutrientes",icon:vs,description:"Análise detalhada dos micronutrientes"},{id:"bloodtests",label:"Exames",icon:qe,description:"Upload e análise de exames de sangue"},{id:"supplements",label:"Suplementos",icon:Pe,description:"Recomendações personalizadas"},{id:"evolution",label:"Evolução",icon:oe,description:"Acompanhamento temporal"}];function ta({isOpen:s,onClose:r,initialTab:c="analysis"}){const[a,l]=C.useState(c);if(!s)return null;const g=()=>{switch(a){case"analysis":return e.jsx(Ue,{});case"bloodtests":return e.jsx(Ys,{});case"supplements":return e.jsx(ea,{});case"evolution":return e.jsx(sa,{});default:return e.jsx(Ue,{})}};return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ue,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Análise Nutricional Completa"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Micronutrientes, exames e recomendações personalizadas"})]})]}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsx("div",{className:"border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex overflow-x-auto",children:aa.map(h=>{const b=h.icon,y=a===h.id;return e.jsxs("button",{onClick:()=>l(h.id),className:`flex items-center gap-2 px-6 py-4 text-sm font-medium whitespace-nowrap border-b-2 transition-all duration-200 ${y?"text-snapfit-green border-snapfit-green bg-snapfit-green/5":"text-gray-400 border-transparent hover:text-white hover:bg-snapfit-green/5"}`,children:[e.jsx(b,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:h.label}),e.jsx("span",{className:"sm:hidden",children:h.label.split(" ")[0]})]},h.id)})})}),e.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-140px)]",children:g()})]})})}function ra({onOpenAnalysis:s,className:r=""}){const{summary:c,isLoading:a,error:l}=Re(),[g,h]=_.useState(!1);if(console.log("🔍 MicronutrientsCard debug:",{summary:!!c,isLoading:a,error:!!l,summaryType:typeof c}),_.useEffect(()=>{if(a){const f=setTimeout(()=>{h(!0)},1e4);return()=>clearTimeout(f)}else h(!1)},[a]),a&&!g)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center animate-pulse",children:e.jsx(ue,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded animate-pulse mb-2"}),e.jsx("div",{className:"h-3 bg-gray-700 rounded animate-pulse w-2/3"})]})]})});if(a&&g)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-yellow-500/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ie,{className:"w-5 h-5 text-yellow-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm text-yellow-400 font-medium",children:"Carregamento demorado"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Usando dados de fallback..."})]})]})});if(l&&!c)return e.jsxs("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-red-500/20 ${r}`,children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ie,{className:"w-5 h-5 text-red-400"}),e.jsx("div",{className:"text-sm text-red-400",children:"Erro ao carregar análise nutricional"})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:l instanceof Error?l.message:"Erro desconhecido"})]});if(!c)return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-gray-500/20 ${r}`,children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-500/20 rounded-full flex items-center justify-center",children:e.jsx(ue,{className:"w-5 h-5 text-gray-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-sm text-gray-400 font-medium",children:"Análise Nutricional"}),e.jsx("div",{className:"text-xs text-gray-500",children:"Dados não disponíveis"})]})]})});const b=f=>f>=80?"text-green-400":f>=60?"text-yellow-400":"text-red-400",y=f=>f>=80?"🎯":f>=60?"⚠️":"🔴";return e.jsx("div",{className:`p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 hover:border-snapfit-green/40 transition-all duration-200 ${r}`,children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ue,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Análise Nutricional"}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-400",children:[e.jsxs("span",{className:b(c.overallScore),children:[y(c.overallScore)," Score: ",c.overallScore,"/100"]}),c.hasRecentBloodTest&&e.jsx("span",{className:"text-snapfit-green",children:"• Exame recente"})]})]}),e.jsx(ys,{title:"Base Científica da Análise",description:"A análise de micronutrientes é baseada nas Ingestões Diárias Recomendadas (IDR) estabelecidas pela ANVISA e diretrizes internacionais da OMS."})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Micronutrientes adequados:"}),e.jsxs("span",{className:"text-green-400 font-medium",children:[c.adequate,"/",c.total," (",c.adequatePercentage,"%)"]})]}),c.criticalDeficiencies.length>0&&e.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[e.jsx(ie,{className:"w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-orange-400",children:"Precisam atenção: "}),e.jsx("span",{className:"text-gray-300",children:c.criticalDeficiencies.join(", ")})]})]}),c.deficient===0&&e.jsxs("div",{className:"flex items-center gap-2 text-sm text-green-400",children:[e.jsx(he,{className:"w-4 h-4"}),e.jsx("span",{children:"Todos os micronutrientes em níveis adequados!"})]})]}),e.jsx("div",{children:e.jsxs("button",{onClick:s,className:"w-full flex items-center justify-center gap-2 px-4 py-2 text-sm text-white bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-all duration-200",children:[e.jsx(oe,{className:"w-4 h-4"}),e.jsx("span",{children:"Ver Análise Completa"})]})}),e.jsxs("div",{className:"flex items-center gap-1 text-xs",children:[e.jsx("span",{className:"text-gray-500",children:"Status rápido:"}),e.jsxs("div",{className:"flex gap-1",children:[c.criticalDeficiencies.slice(0,3).map((f,d)=>e.jsx("span",{className:"text-red-400",children:"❌"},d)),Array.from({length:Math.min(3,c.adequate)}).map((f,d)=>e.jsx("span",{className:"text-green-400",children:"✅"},`adequate-${d}`)),c.total>6&&e.jsxs("span",{className:"text-gray-400",children:["+",c.total-6]})]})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2 opacity-75",children:"📊 Dados demonstrativos - Análise completa em breve"}),e.jsx(Ns,{})]})})}function na({date_start:s,date_end:r,onReuseProtocol:c,showProtocolHistory:a=!1}){const l=He(),[g,h]=C.useState(null),[b,y]=C.useState(!1),f=n=>{h(n.id),y(!0)},d=(n,o)=>{o?l(`/dashboard/diet/edit-protocol/${n.id}`):c&&c({...n,edit:o})},j=n=>{console.log("🔄 Duplicando protocolo:",n),c&&c({...n,edit:!1})},u=n=>{console.log("🏁 Protocolo finalizado:",n)};return a?e.jsxs(e.Fragment,{children:[e.jsx(ws,{protocolType:"diet",onProtocolSelect:f,onProtocolDuplicate:j,onProtocolFinish:u}),g&&e.jsx(Es,{protocolId:g,type:"diet",isOpen:b,onClose:()=>{y(!1),h(null)},onReuseProtocol:d})]}):e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Le,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Protocolos"})]}),e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 mx-auto mb-4 text-gray-400 opacity-50"}),e.jsx("p",{className:"text-gray-400 mb-4",children:'Para ver o histórico completo de protocolos, use a seção "Histórico" nas ferramentas de análise.'}),e.jsx("button",{onClick:()=>l("/dashboard/diet"),className:"px-4 py-2 bg-snapfit-green/20 text-snapfit-green rounded-lg border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors",children:"Ir para Histórico Completo"})]})]})}function ia({filters:s,onFiltersChange:r,onClose:c}){var f;const[a,l]=C.useState(s),g=[{value:"breakfast",label:"Café da Manhã"},{value:"lunch",label:"Almoço"},{value:"dinner",label:"Jantar"},{value:"snack",label:"Lanche"},{value:"dessert",label:"Sobremesa"},{value:"drink",label:"Bebida"}],h=[{value:"easy",label:"Fácil"},{value:"medium",label:"Médio"},{value:"hard",label:"Difícil"}],b=()=>{r(a),c&&c()},y=()=>{const d={limit:20};l(d),r(d)};return e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("h3",{className:"text-lg font-bold text-white flex items-center gap-2",children:[e.jsx(Je,{className:"w-5 h-5"}),"Filtros de Busca"]}),c&&e.jsx("button",{onClick:c,className:"p-1 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Buscar por nome ou descrição"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:a.search||"",onChange:d=>l({...a,search:d.target.value}),placeholder:"Digite o nome da receita...",className:"w-full pl-10 pr-4 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"}),e.jsx($e,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Categoria"}),e.jsxs("select",{value:a.category||"",onChange:d=>l({...a,category:d.target.value||void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Todas as categorias"}),g.map(d=>e.jsx("option",{value:d.value,children:d.label},d.value))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Dificuldade"}),e.jsxs("select",{value:a.difficulty||"",onChange:d=>l({...a,difficulty:d.target.value||void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Todas as dificuldades"}),h.map(d=>e.jsx("option",{value:d.value,children:d.label},d.value))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tempo máximo de preparo (minutos)"}),e.jsx("input",{type:"number",value:a.maxPrepTime||"",onChange:d=>l({...a,maxPrepTime:d.target.value?Number(d.target.value):void 0}),placeholder:"Ex: 30",min:"0",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Avaliação mínima"}),e.jsxs("select",{value:a.minRating||"",onChange:d=>l({...a,minRating:d.target.value?Number(d.target.value):void 0}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"",children:"Qualquer avaliação"}),e.jsx("option",{value:"4",children:"4+ estrelas"}),e.jsx("option",{value:"3",children:"3+ estrelas"}),e.jsx("option",{value:"2",children:"2+ estrelas"}),e.jsx("option",{value:"1",children:"1+ estrela"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tags (separadas por vírgula)"}),e.jsx("input",{type:"text",value:((f=a.tags)==null?void 0:f.join(", "))||"",onChange:d=>l({...a,tags:d.target.value?d.target.value.split(",").map(j=>j.trim()).filter(Boolean):void 0}),placeholder:"Ex: proteína, baixa gordura, vegetariano",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Tipo de receita"}),e.jsxs("select",{value:a.isPublic===void 0?"all":a.isPublic?"public":"private",onChange:d=>{const j=d.target.value;l({...a,isPublic:j==="all"?void 0:j==="public"})},className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50",children:[e.jsx("option",{value:"all",children:"Todas as receitas"}),e.jsx("option",{value:"public",children:"Receitas públicas"}),e.jsx("option",{value:"private",children:"Minhas receitas"})]})]})]}),e.jsxs("div",{className:"flex gap-3 mt-6",children:[e.jsx("button",{onClick:b,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors",children:"Aplicar Filtros"}),e.jsx("button",{onClick:y,className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Limpar"})]})]})}function la({recipe:s,onClose:r,onAddToFavorites:c,onRemoveFromFavorites:a,onRate:l,isFavorite:g=!1,isAddingToFavorites:h=!1,isRemovingFromFavorites:b=!1,isRating:y=!1}){const[f,d]=C.useState(!1),[j,u]=C.useState(!1),[n,o]=C.useState(0),[t,m]=C.useState(""),x=k=>{switch(k){case"easy":return"text-green-400";case"medium":return"text-yellow-400";case"hard":return"text-red-400";default:return"text-gray-400"}},v=k=>{switch(k){case"easy":return"Fácil";case"medium":return"Médio";case"hard":return"Difícil";default:return k}},M=k=>({breakfast:"Café da Manhã",lunch:"Almoço",dinner:"Jantar",snack:"Lanche",dessert:"Sobremesa",drink:"Bebida"})[k]||k,R=()=>{n>0&&l&&(l(s.id,n,t||void 0),u(!1),o(0),m(""))},Q=()=>{g&&a?a(s):!g&&c&&c(s.id)},q={calories:Math.round(s.totalCalories/s.servings),protein:Math.round(s.totalProtein/s.servings*10)/10,carbs:Math.round(s.totalCarbs/s.servings*10)/10,fat:Math.round(s.totalFat/s.servings*10)/10,fiber:s.totalFiber?Math.round(s.totalFiber/s.servings*10)/10:0,sugar:s.totalSugar?Math.round(s.totalSugar/s.servings*10)/10:0,sodium:s.totalSodium?Math.round(s.totalSodium/s.servings*10)/10:0};return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:[e.jsx("div",{className:"sticky top-0 bg-snapfit-dark-gray border-b border-snapfit-green/20 p-6",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:s.name}),e.jsx("p",{className:"text-gray-400 mb-4",children:s.description}),e.jsxs("div",{className:"flex items-center gap-4 text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-gray-300",children:[s.prepTime," min"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Ze,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-gray-300",children:[s.servings," porções"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(Bs,{className:`w-4 h-4 ${x(s.difficulty)}`}),e.jsx("span",{className:x(s.difficulty),children:v(s.difficulty)})]}),e.jsx("span",{className:"px-2 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-xs",children:M(s.category)}),s.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(le,{className:"w-4 h-4 text-yellow-400"}),e.jsx("span",{className:"text-gray-300",children:s.averageRating.toFixed(1)}),e.jsxs("span",{className:"text-gray-500",children:["(",s.ratingsCount,")"]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[e.jsx("button",{onClick:Q,disabled:h||b,className:`p-2 rounded-full transition-colors disabled:opacity-50 ${g?"bg-red-500/20 text-red-400 hover:bg-red-500/30":"bg-snapfit-green/20 text-snapfit-green hover:bg-snapfit-green/30"}`,title:g?"Remover dos favoritos":"Adicionar aos favoritos",children:e.jsx(Ae,{className:`w-5 h-5 ${g?"fill-current":""}`})}),e.jsx("button",{onClick:()=>u(!0),className:"p-2 bg-yellow-500/20 text-yellow-400 rounded-full hover:bg-yellow-500/30 transition-colors",title:"Avaliar receita",children:e.jsx(le,{className:"w-5 h-5"})}),e.jsx("button",{onClick:()=>{var k;return(k=navigator.share)==null?void 0:k.call(navigator,{title:s.name,text:s.description})},className:"p-2 bg-blue-500/20 text-blue-400 rounded-full hover:bg-blue-500/30 transition-colors",title:"Compartilhar",children:e.jsx(ks,{className:"w-5 h-5"})}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]})]})}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Informações Nutricionais"}),e.jsxs("button",{onClick:()=>d(!f),className:"flex items-center gap-2 text-snapfit-green hover:text-snapfit-green/80 transition-colors",children:[e.jsx(Cs,{className:"w-4 h-4"}),f?"Ocultar detalhes":"Ver detalhes"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:q.calories}),e.jsx("div",{className:"text-sm text-gray-400",children:"Calorias"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:[q.protein,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Proteínas"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-green-500",children:[q.carbs,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Carboidratos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-yellow-500",children:[q.fat,"g"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gorduras"})]})]}),f&&e.jsxs("div",{className:"grid grid-cols-3 gap-4 pt-4 border-t border-snapfit-green/20",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[q.fiber,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Fibras"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[q.sugar,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Açúcares"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-semibold text-white",children:[q.sodium,"mg"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Sódio"})]})]}),e.jsx("div",{className:"text-xs text-gray-500 mt-2 text-center",children:"* Valores por porção"})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center gap-2",children:[e.jsx(We,{className:"w-5 h-5"}),"Ingredientes"]}),e.jsx("div",{className:"space-y-2",children:s.ingredients.map((k,L)=>e.jsxs("div",{className:"flex justify-between items-center p-3 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:k.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[k.quantity," ",k.unit]})]}),e.jsxs("div",{className:"text-right text-sm text-gray-400",children:[k.calories," cal • ",k.protein,"g prot"]})]},k.id||L))})]}),s.instructions&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Modo de Preparo"}),e.jsx("div",{className:"bg-snapfit-gray rounded-lg p-4",children:e.jsx("p",{className:"text-gray-300 whitespace-pre-wrap",children:s.instructions})})]}),s.tags&&s.tags.length>0&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Tags"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map((k,L)=>e.jsx("span",{className:"px-3 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-sm",children:k},L))})]})]}),j&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-60 p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 max-w-md w-full",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:"Avaliar Receita"}),e.jsxs("div",{className:"mb-4",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Sua avaliação"}),e.jsx("div",{className:"flex gap-1",children:[1,2,3,4,5].map(k=>e.jsx("button",{onClick:()=>o(k),className:`p-1 transition-colors ${k<=n?"text-yellow-400":"text-gray-600"}`,children:e.jsx(le,{className:`w-6 h-6 ${k<=n?"fill-current":""}`})},k))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Comentário (opcional)"}),e.jsx("textarea",{value:t,onChange:k=>m(k.target.value),placeholder:"Compartilhe sua experiência com esta receita...",className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white resize-none",rows:3})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:R,disabled:n===0||y,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors disabled:opacity-50",children:y?"Enviando...":"Enviar Avaliação"}),e.jsx("button",{onClick:()=>u(!1),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"})]})]})})]})})}function oa({recipe:s,onClose:r,onAddToMealPlan:c}){const[a,l]=C.useState(new Date().toISOString().split("T")[0]),[g,h]=C.useState("breakfast"),[b,y]=C.useState(1),[f,d]=C.useState(""),j=[{value:"breakfast",label:"Café da Manhã",icon:"🌅"},{value:"lunch",label:"Almoço",icon:"☀️"},{value:"dinner",label:"Jantar",icon:"🌙"},{value:"snack",label:"Lanche",icon:"🍎"}],u=()=>{const t={recipeId:s.id,date:a,mealType:g,servings:b,notes:f||void 0};c(t),r()},n={calories:Math.round(s.totalCalories/s.servings),protein:Math.round(s.totalProtein/s.servings*10)/10,carbs:Math.round(s.totalCarbs/s.servings*10)/10,fat:Math.round(s.totalFat/s.servings*10)/10},o={calories:n.calories*b,protein:Math.round(n.protein*b*10)/10,carbs:Math.round(n.carbs*b*10)/10,fat:Math.round(n.fat*b*10)/10};return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl max-w-md w-full",children:[e.jsxs("div",{className:"flex justify-between items-center p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold text-white",children:"Adicionar ao Planejamento"}),e.jsx("p",{className:"text-sm text-gray-400",children:s.name})]}),e.jsx("button",{onClick:r,className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(Le,{className:"w-4 h-4 inline mr-2"}),"Data"]}),e.jsx("input",{type:"date",value:a,onChange:t=>l(t.target.value),min:new Date().toISOString().split("T")[0],className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:border-snapfit-green/50"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(Be,{className:"w-4 h-4 inline mr-2"}),"Tipo de Refeição"]}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:j.map(t=>e.jsxs("button",{onClick:()=>h(t.value),className:`p-3 rounded-lg border transition-colors ${g===t.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-lg mb-1",children:t.icon}),e.jsx("div",{className:"text-sm font-medium",children:t.label})]},t.value))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:[e.jsx(Ze,{className:"w-4 h-4 inline mr-2"}),"Número de Porções"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:()=>y(Math.max(1,b-1)),className:"w-10 h-10 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white hover:bg-snapfit-green/20 transition-colors",children:"-"}),e.jsxs("div",{className:"flex-1 text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:b}),e.jsx("div",{className:"text-xs text-gray-400",children:"porções"})]}),e.jsx("button",{onClick:()=>y(b+1),className:"w-10 h-10 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white hover:bg-snapfit-green/20 transition-colors",children:"+"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Informações Nutricionais"}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-lg font-bold text-white",children:o.calories}),e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-snapfit-green",children:[o.protein,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-green-500",children:[o.carbs,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Carboidratos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-lg font-bold text-yellow-500",children:[o.fat,"g"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx(pe,{className:"w-4 h-4 text-blue-400"}),e.jsxs("div",{className:"text-sm text-blue-300",children:["Tempo de preparo: ",e.jsxs("span",{className:"font-medium",children:[s.prepTime," minutos"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Observações (opcional)"}),e.jsx("textarea",{value:f,onChange:t=>d(t.target.value),placeholder:"Ex: Preparar na noite anterior, dobrar a receita...",className:"w-full p-3 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white resize-none focus:outline-none focus:border-snapfit-green/50",rows:3})]})]}),e.jsxs("div",{className:"flex gap-3 p-6 border-t border-snapfit-green/20",children:[e.jsx("button",{onClick:r,className:"flex-1 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"}),e.jsxs("button",{onClick:u,className:"flex-1 bg-snapfit-green text-white py-2 px-4 rounded-lg hover:bg-snapfit-green/80 transition-colors flex items-center justify-center gap-2",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar ao Plano"]})]})]})})}const ca=()=>{const s=_e(),r=(d={})=>Z({queryKey:["recipes","search",d],queryFn:async()=>{const j=new URLSearchParams;return Object.entries(d).forEach(([n,o])=>{o!=null&&o!==""&&(Array.isArray(o)?j.append(n,o.join(",")):j.append(n,o.toString()))}),(await T.get(`users/recipes/search?${j.toString()}`)).data},staleTime:1e3*60*5}),c=()=>Z({queryKey:["recipes","favorites"],queryFn:async()=>(await T.get("users/recipes/favorites")).data,staleTime:1e3*60*5}),a=d=>Z({queryKey:["recipes",d],queryFn:async()=>(await T.get(`users/recipes/${d}`)).data,enabled:!!d,staleTime:1e3*60*10}),l=W({mutationFn:async d=>(await T.post("users/recipes",d)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes"]})}}),g=W({mutationFn:async({id:d,data:j})=>(await T.put(`users/recipes/${d}`,j)).data,onSuccess:(d,j)=>{s.invalidateQueries({queryKey:["recipes"]}),s.invalidateQueries({queryKey:["recipes",j.id]})}}),h=W({mutationFn:async d=>(await T.delete(`users/recipes/${d}`)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes"]})}}),b=W({mutationFn:async({recipeId:d,notes:j})=>(await T.post("users/recipes/favorites",{recipeId:d,notes:j})).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes","favorites"]}),s.invalidateQueries({queryKey:["recipes","search"]})}}),y=W({mutationFn:async d=>(await T.delete(`users/recipes/favorites/${d}`)).data,onSuccess:()=>{s.invalidateQueries({queryKey:["recipes","favorites"]}),s.invalidateQueries({queryKey:["recipes","search"]})}}),f=W({mutationFn:async({recipeId:d,rating:j,comment:u})=>(await T.post("users/recipes/rate",{recipeId:d,rating:j,comment:u})).data,onSuccess:(d,j)=>{s.invalidateQueries({queryKey:["recipes",j.recipeId]}),s.invalidateQueries({queryKey:["recipes","search"]})}});return{useSearchRecipes:r,useFavoriteRecipes:c,useRecipeById:a,createRecipe:l.mutateAsync,updateRecipe:g.mutateAsync,deleteRecipe:h.mutateAsync,addToFavorites:b.mutateAsync,removeFromFavorites:y.mutateAsync,rateRecipe:f.mutateAsync,isCreating:l.isPending,isUpdating:g.isPending,isDeleting:h.isPending,isAddingToFavorites:b.isPending,isRemovingFromFavorites:y.isPending,isRating:f.isPending}};function da(){var xe,ge;const{useFavoriteRecipes:s,useSearchRecipes:r,createRecipe:c,deleteRecipe:a,addToFavorites:l,removeFromFavorites:g,rateRecipe:h,isDeleting:b,isAddingToFavorites:y,isRemovingFromFavorites:f,isRating:d}=ca(),{data:j,isLoading:u,error:n}=s(),o=j||[],[t,m]=C.useState({limit:20}),{data:x,isLoading:v}=r(t),[M,R]=C.useState(""),[Q,q]=C.useState(!1),[k,L]=C.useState(!1),[X,Y]=C.useState(!1),[te,H]=C.useState(!1),[w,$]=C.useState(null),[G,re]=C.useState(null),[N,V]=C.useState({name:"",description:"",ingredients:[],servings:1,prepTime:0,difficulty:"easy",category:"breakfast",isPublic:!0}),[Ie,fe]=C.useState(null),[ne,be]=C.useState(100),je=o.filter(i=>i.name.toLowerCase().includes(M.toLowerCase())),ve=i=>{if(!i||!ne)return;const I=ne/100,J={id:`new-${Date.now()}`,name:i.name,quantity:ne,unit:"g",protein:Math.round((i.protein||0)*I*10)/10,carbs:Math.round((i.carbs||0)*I*10)/10,fat:Math.round((i.fat||0)*I*10)/10,calories:Math.round((i.calories||0)*I)};V({...N,ingredients:[...N.ingredients||[],J]}),L(!1),fe(null),be(100)},ye=i=>{V({...N,ingredients:(N.ingredients||[]).filter(I=>I.id!==i)})},Ne=async()=>{if(!(!N.name||!(N.ingredients&&N.ingredients.length>0)))try{const i={name:N.name,description:N.description||"",ingredients:N.ingredients,servings:N.servings||1,prepTime:N.prepTime||0,cookTime:N.cookTime,difficulty:N.difficulty||"easy",category:N.category||"breakfast",isPublic:N.isPublic??!0,tags:N.tags};await c(i),F.success("Receita criada com sucesso!"),q(!1),V({name:"",description:"",ingredients:[],servings:1,prepTime:0,difficulty:"easy",category:"breakfast",isPublic:!0})}catch(i){console.error("Error creating recipe:",i),F.error("Erro ao criar receita. Tente novamente.")}},de=async i=>{if(i.id)try{await g(i.id),F.success("Receita removida dos favoritos!")}catch(I){console.error("Error removing from favorites:",I),F.error("Erro ao remover dos favoritos. Tente novamente.")}},we=async i=>{if(!(!i.id||!window.confirm("Tem certeza que deseja excluir esta receita?")))try{await a(i.id),F.success("Receita excluída com sucesso!")}catch(J){console.error("Error deleting recipe:",J),F.error("Erro ao excluir receita. Tente novamente.")}},me=async i=>{try{await l({recipeId:i}),F.success("Receita adicionada aos favoritos!")}catch(I){console.error("Error adding to favorites:",I),F.error("Erro ao adicionar aos favoritos. Tente novamente.")}},ke=async(i,I,J)=>{try{await h({recipeId:i,rating:I,comment:J}),F.success("Avaliação enviada com sucesso!")}catch(Se){console.error("Error rating recipe:",Se),F.error("Erro ao enviar avaliação. Tente novamente.")}},Ce=async i=>{try{console.log("Adding to meal plan:",i),F.success("Receita adicionada ao planejamento de refeições!"),re(null)}catch(I){console.error("Error adding to meal plan:",I),F.error("Erro ao adicionar ao planejamento. Tente novamente.")}};return u?e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"animate-pulse space-y-4",children:[e.jsx("div",{className:"h-8 bg-gray-600 rounded w-1/3"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[1,2,3,4].map(i=>e.jsx("div",{className:"h-64 bg-gray-600 rounded"},i))})]})}):n?e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 text-red-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Erro ao carregar receitas"}),e.jsx("p",{className:"text-gray-400",children:"Não foi possível carregar suas receitas favoritas."})]})}):e.jsxs("div",{className:"card p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Minhas Receitas Favoritas"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",placeholder:"Buscar receitas...",value:M,onChange:i=>R(i.target.value),className:"pl-9 pr-4 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-full text-sm text-white focus:outline-none focus:border-snapfit-green/50"}),e.jsx($e,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"})]}),e.jsxs("button",{onClick:()=>Y(!0),className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx($e,{className:"w-4 h-4"}),"Explorar"]}),e.jsxs("button",{onClick:()=>q(!0),className:"btn-primary flex items-center gap-2",children:[e.jsx(se,{className:"w-4 h-4"}),"Nova Receita"]})]})]}),Q?e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-4 border border-snapfit-green/20 mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h3",{className:"text-base font-bold text-white",children:"Nova Receita"}),e.jsx("button",{onClick:()=>q(!1),className:"p-1 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Nome da Receita"}),e.jsx("input",{type:"text",value:N.name,onChange:i=>V({...N,name:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",placeholder:"Ex: Omelete de Claras"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Descrição"}),e.jsx("textarea",{value:N.description,onChange:i=>V({...N,description:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",placeholder:"Descreva brevemente sua receita",rows:2})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Categoria"}),e.jsxs("select",{value:N.category,onChange:i=>V({...N,category:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",children:[e.jsx("option",{value:"breakfast",children:"Café da Manhã"}),e.jsx("option",{value:"lunch",children:"Almoço"}),e.jsx("option",{value:"dinner",children:"Jantar"}),e.jsx("option",{value:"snack",children:"Lanche"}),e.jsx("option",{value:"dessert",children:"Sobremesa"}),e.jsx("option",{value:"drink",children:"Bebida"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Dificuldade"}),e.jsxs("select",{value:N.difficulty,onChange:i=>V({...N,difficulty:i.target.value}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",children:[e.jsx("option",{value:"easy",children:"Fácil"}),e.jsx("option",{value:"medium",children:"Médio"}),e.jsx("option",{value:"hard",children:"Difícil"})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Porções"}),e.jsx("input",{type:"number",value:N.servings,onChange:i=>V({...N,servings:Number(i.target.value)}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",min:"1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Tempo de Preparo (min)"}),e.jsx("input",{type:"number",value:N.prepTime,onChange:i=>V({...N,prepTime:Number(i.target.value)}),className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white",min:"0"})]})]})]}),e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Ingredientes"}),e.jsxs("div",{className:"space-y-4 mb-6",children:[(N.ingredients||[]).map(i=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded-lg",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:i.name}),e.jsxs("div",{className:"text-xs text-gray-400",children:[i.quantity," ",i.unit," • P: ",i.protein,"g • C: ",i.carbs,"g • G: ",i.fat,"g •",i.calories," kcal"]})]}),e.jsx("button",{onClick:()=>ye(i.id),className:"p-1 hover:bg-snapfit-dark-gray rounded-full transition-colors",children:e.jsx(Ke,{className:"w-4 h-4 text-red-400"})})]},i.id)),e.jsx("div",{className:"p-3 bg-snapfit-gray rounded-lg",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("button",{onClick:()=>L(!0),className:"flex items-center gap-2 px-4 py-3 bg-snapfit-green/20 text-snapfit-green rounded-lg text-sm border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors mx-auto",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar Ingrediente"]}),e.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"Selecione alimentos da nossa base de dados com macros calculados automaticamente"})]})})]}),e.jsxs("div",{className:"flex justify-end gap-3",children:[e.jsx("button",{onClick:()=>q(!1),className:"px-4 py-2 text-sm text-gray-400 hover:text-white transition-colors",children:"Cancelar"}),e.jsxs("button",{onClick:Ne,className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-full text-sm font-medium hover:bg-snapfit-green/90 transition-colors",disabled:!N.name||!(N.ingredients&&N.ingredients.length>0),children:[e.jsx(Ss,{className:"w-4 h-4"}),"Salvar Receita"]})]})]}):null,e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:je.map(i=>e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-base font-bold text-white",children:i.name}),e.jsx("p",{className:"text-sm text-gray-400",children:i.description})]}),e.jsxs("div",{className:"flex gap-2",children:[i.createdBy&&e.jsx("button",{className:"p-1.5 bg-snapfit-green/10 rounded-full hover:bg-snapfit-green/20 transition-colors",title:"Editar receita",children:e.jsx(Ms,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("button",{onClick:()=>de(i),disabled:f,className:"p-1.5 bg-red-500/10 rounded-full hover:bg-red-500/20 transition-colors disabled:opacity-50",title:"Remover dos favoritos",children:e.jsx(Ae,{className:"w-4 h-4 text-red-400"})}),i.createdBy&&e.jsx("button",{onClick:()=>we(i),disabled:b,className:"p-1.5 bg-red-500/10 rounded-full hover:bg-red-500/20 transition-colors disabled:opacity-50",title:"Excluir receita",children:e.jsx(Ke,{className:"w-4 h-4 text-red-400"})})]})]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400 mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(ee,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.servings," porções"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.prepTime," min"]})]}),i.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(le,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:i.averageRating.toFixed(1)})]}),e.jsx("span",{className:"px-2 py-1 bg-snapfit-green/10 text-snapfit-green rounded-full text-xs",children:i.category==="breakfast"?"Café da Manhã":i.category==="lunch"?"Almoço":i.category==="dinner"?"Jantar":i.category==="snack"?"Lanche":i.category==="dessert"?"Sobremesa":"Bebida"})]}),e.jsxs("div",{className:"grid grid-cols-4 gap-2 mb-3",children:[e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:i.totalCalories})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[i.totalProtein,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carbos"}),e.jsxs("div",{className:"text-sm font-medium text-green-500",children:[i.totalCarbs,"g"]})]}),e.jsxs("div",{className:"bg-snapfit-gray p-2 rounded-lg text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-sm font-medium text-yellow-500",children:[i.totalFat,"g"]})]})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("button",{onClick:()=>$(i),className:"text-xs text-gray-400 hover:text-white transition-colors",children:"Ver detalhes"}),e.jsxs("button",{onClick:()=>re(i),className:"flex items-center gap-1 px-3 py-1.5 bg-snapfit-green/20 text-snapfit-green rounded-lg text-xs border border-snapfit-green/30 hover:bg-snapfit-green/30 transition-colors",children:[e.jsx(se,{className:"w-3 h-3"}),"Adicionar à Refeição"]})]})]},i.id))}),X&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-xl p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h3",{className:"text-xl font-bold text-white",children:"Explorar Receitas"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs("button",{onClick:()=>H(!0),className:"flex items-center gap-2 px-3 py-2 bg-snapfit-green/10 text-snapfit-green rounded-lg hover:bg-snapfit-green/20 transition-colors",children:[e.jsx(Je,{className:"w-4 h-4"}),"Filtros"]}),e.jsx("button",{onClick:()=>Y(!1),className:"p-2 hover:bg-snapfit-gray rounded-full transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]})]}),v?e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[1,2,3,4,5,6].map(i=>e.jsx("div",{className:"h-48 bg-gray-600 rounded-lg animate-pulse"},i))}):e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(xe=x==null?void 0:x.recipes)==null?void 0:xe.map(i=>e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-start mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:i.name}),e.jsx("p",{className:"text-sm text-gray-400 line-clamp-2",children:i.description})]}),e.jsx("button",{onClick:()=>me(i.id),disabled:y,className:"p-1.5 bg-snapfit-green/10 rounded-full hover:bg-snapfit-green/20 transition-colors disabled:opacity-50",title:"Adicionar aos favoritos",children:e.jsx(Ae,{className:"w-4 h-4 text-snapfit-green"})})]}),e.jsxs("div",{className:"flex items-center gap-3 text-xs text-gray-400 mb-3",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(pe,{className:"w-3 h-3"}),e.jsxs("span",{children:[i.prepTime," min"]})]}),i.averageRating>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(le,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{children:i.averageRating.toFixed(1)})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsx("div",{className:"text-sm font-medium text-white",children:i.totalCalories})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray p-2 rounded text-center",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[i.totalProtein,"g"]})]})]}),e.jsx("button",{onClick:()=>$(i),className:"w-full text-xs text-gray-400 hover:text-white transition-colors py-2 border-t border-gray-600",children:"Ver detalhes completos"})]},i.id))}),((ge=x==null?void 0:x.recipes)==null?void 0:ge.length)===0&&e.jsxs("div",{className:"text-center py-8",children:[e.jsx(ee,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"Nenhuma receita encontrada"})]})]})}),te&&e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsx("div",{className:"max-w-md w-full",children:e.jsx(ia,{filters:t,onFiltersChange:i=>{m(i),H(!1)},onClose:()=>H(!1)})})}),w&&e.jsx(la,{recipe:w,onClose:()=>$(null),onAddToFavorites:me,onRemoveFromFavorites:de,onRate:ke,isFavorite:!0,isAddingToFavorites:y,isRemovingFromFavorites:f,isRating:d}),G&&e.jsx(oa,{recipe:G,onClose:()=>re(null),onAddToMealPlan:Ce}),k&&e.jsx(Ps,{onSelect:ve,onClose:()=>L(!1)})]})}const ma=[{id:"micronutrients",label:"Análise Nutricional",icon:ue,description:"Micronutrientes e suplementos"},{id:"history",label:"Histórico",icon:Ds,description:"Protocolos anteriores"},{id:"recipes",label:"Receitas Favoritas",icon:Ae,description:"Suas receitas salvas"}];function xa({onOpenMicronutrientsModal:s,onReuseProtocol:r}){const[c,a]=C.useState("micronutrients"),l=()=>{switch(c){case"micronutrients":return e.jsx(ra,{onOpenAnalysis:()=>s("analysis")});case"history":return e.jsx(na,{showProtocolHistory:!0,onReuseProtocol:r});case"recipes":return e.jsx(da,{});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Ferramentas e Análises"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Acesse análises avançadas, histórico e suas receitas favoritas"})]}),e.jsx("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20",children:ma.map(g=>{const h=g.icon,b=g.id===c;return e.jsxs("button",{onClick:()=>a(g.id),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${b?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(h,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline text-sm",children:g.label}),e.jsx("span",{className:"sm:hidden text-xs",children:g.id==="micronutrients"?"Análise":g.id==="history"?"Histórico":"Receitas"})]},g.id)})}),e.jsx("div",{className:"min-h-[200px]",children:l()})]})}const ga=[{value:"breakfast",label:"Café da Manhã",time:"07:00"},{value:"morning_snack",label:"Lanche da Manhã",time:"10:00"},{value:"lunch",label:"Almoço",time:"12:00"},{value:"afternoon_snack",label:"Lanche da Tarde",time:"15:00"},{value:"dinner",label:"Jantar",time:"19:00"},{value:"evening_snack",label:"Ceia",time:"22:00"}];function ua({isOpen:s,onClose:r,selectedDate:c}){const a=Is(),{control:l,handleSubmit:g,watch:h,setValue:b,reset:y,formState:{errors:f,isValid:d}}=As({mode:"onChange",defaultValues:{name:"",meal_time:"lunch",foods:[{name:"",quantity:100,unit:"g",calories:0,protein:0,carbs:0,fat:0}],notes:""}}),j=h("foods"),u=()=>{const m=h("foods");b("foods",[...m,{name:"",quantity:100,unit:"g",calories:0,protein:0,carbs:0,fat:0}])},n=m=>{const x=h("foods");x.length>1&&b("foods",x.filter((v,M)=>M!==m))},o=async m=>{try{await a.mutateAsync({...m,date:c}),F.success("Refeição adicionada com sucesso!",{position:"bottom-right"}),y(),r()}catch(x){console.error("Error adding meal:",x),F.error("Erro ao adicionar refeição",{position:"bottom-right"})}},t=()=>{y(),r()};return s?e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Be,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Nova Refeição"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Adicione uma nova refeição ao seu diário"})]})]}),e.jsx("button",{onClick:t,className:"p-2 hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(ae,{className:"w-5 h-5 text-gray-400"})})]}),e.jsxs("form",{onSubmit:g(o),className:"p-6 space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Nome da Refeição"}),e.jsx(U,{name:"name",control:l,rules:{required:"Nome da refeição é obrigatório"},render:({field:m})=>e.jsx("input",{...m,type:"text",className:`w-full p-3 bg-snapfit-dark-gray border rounded-lg text-white placeholder-gray-400 ${f.name?"border-red-500":"border-snapfit-green/30"}`,placeholder:"Ex: Almoço Especial"})}),f.name&&e.jsx("p",{className:"text-red-400 text-sm",children:f.name.message})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Horário da Refeição"}),e.jsx(U,{name:"meal_time",control:l,rules:{required:"Horário é obrigatório"},render:({field:m})=>e.jsx("select",{...m,className:`w-full p-3 bg-snapfit-dark-gray border rounded-lg text-white ${f.meal_time?"border-red-500":"border-snapfit-green/30"}`,children:ga.map(x=>e.jsxs("option",{value:x.value,children:[x.label," (",x.time,")"]},x.value))})}),f.meal_time&&e.jsx("p",{className:"text-red-400 text-sm",children:f.meal_time.message})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Alimentos"}),e.jsxs("button",{type:"button",onClick:u,className:"flex items-center gap-2 px-3 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(se,{className:"w-4 h-4"}),"Adicionar Alimento"]})]}),j.map((m,x)=>e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[e.jsxs("div",{className:"md:col-span-2 lg:col-span-1",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Nome do Alimento"}),e.jsx(U,{name:`foods.${x}.name`,control:l,rules:{required:"Nome do alimento é obrigatório"},render:({field:v})=>e.jsx("input",{...v,type:"text",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white placeholder-gray-400",placeholder:"Ex: Arroz integral"})})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Quantidade"}),e.jsx(U,{name:`foods.${x}.quantity`,control:l,rules:{required:!0,min:.1},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Unidade"}),e.jsx(U,{name:`foods.${x}.unit`,control:l,render:({field:v})=>e.jsxs("select",{...v,className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white",children:[e.jsx("option",{value:"g",children:"g"}),e.jsx("option",{value:"ml",children:"ml"}),e.jsx("option",{value:"unidade",children:"unidade"}),e.jsx("option",{value:"colher",children:"colher"}),e.jsx("option",{value:"xícara",children:"xícara"})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-2",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Calorias"}),e.jsx(U,{name:`foods.${x}.calories`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Proteína (g)"}),e.jsx(U,{name:`foods.${x}.protein`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Carboidratos (g)"}),e.jsx(U,{name:`foods.${x}.carbs`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Gordura (g)"}),e.jsx(U,{name:`foods.${x}.fat`,control:l,rules:{required:!0,min:0},render:({field:v})=>e.jsx("input",{...v,type:"number",step:"0.1",className:"w-full p-2 bg-snapfit-gray border border-snapfit-green/30 rounded-lg text-white"})})]})]})]}),j.length>1&&e.jsx("div",{className:"mt-3 flex justify-end",children:e.jsx("button",{type:"button",onClick:()=>n(x),className:"text-red-400 hover:text-red-300 text-sm",children:"Remover alimento"})})]},x))]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-white",children:"Observações (opcional)"}),e.jsx(U,{name:"notes",control:l,render:({field:m})=>e.jsx("textarea",{...m,rows:3,className:"w-full p-3 bg-snapfit-dark-gray border border-snapfit-green/30 rounded-lg text-white placeholder-gray-400 resize-none",placeholder:"Adicione observações sobre a refeição..."})})]}),e.jsxs("div",{className:"flex justify-end gap-3 pt-4 border-t border-snapfit-green/20",children:[e.jsx("button",{type:"button",onClick:t,className:"px-6 py-2 text-gray-400 hover:text-white transition-colors",children:"Cancelar"}),e.jsxs("button",{type:"submit",disabled:!d||a.isPending,className:"flex items-center gap-2 px-6 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[a.isPending&&e.jsx("div",{className:"w-4 h-4 border-2 border-black/20 border-t-black rounded-full animate-spin"}),e.jsx(Be,{className:"w-4 h-4"}),"Adicionar Refeição"]})]})]})]})}):null}function va(){var G,re,N,V,Ie,fe,ne,be,je,ve,ye,Ne,de,we,me,ke,Ce,xe,ge,i,I,J,Se,ze;const s=He(),[r,c]=_.useState("monday"),{data:a,isLoading:l}=Os(),g=Ks(),h=new Date().toISOString().split("T")[0],{data:b}=Ts(h,h),f=(p=>{const S=new Date,D=S.getDay(),O={sunday:0,monday:1,tuesday:2,wednesday:3,thursday:4,friday:5,saturday:6}[p]-D,K=new Date(S);return K.setDate(S.getDate()+O),K.toISOString().split("T")[0]})(r),{data:d,isLoading:j,error:u}=Z({queryKey:["diet-page","meals",f],queryFn:async()=>{var p,S,D,E;console.log("🔄 DietPage: Fetching meals for date:",f),console.log("📅 DietPage: Selected day:",r),console.log("🔑 DietPage: Access token:",localStorage.getItem("accessToken")?"Present":"Missing");try{const P=await T.get("users/protocols/diet/meals/active",{searchParams:{date:`${f} 00:00:00`}});return console.log("🍽️ DietPage: Full response:",P),console.log("📋 DietPage: Has protocol:",(p=P==null?void 0:P.data)==null?void 0:p.has_protocol),console.log("🥘 DietPage: Meals count:",((D=(S=P==null?void 0:P.data)==null?void 0:S.meals)==null?void 0:D.length)||0),(E=P==null?void 0:P.data)!=null&&E.has_protocol||console.warn("⚠️ DietPage: User does not have an active diet protocol"),(P==null?void 0:P.data)||{has_protocol:!1,meals:[]}}catch(P){return console.error("❌ DietPage: API error:",P),{has_protocol:!1,meals:[]}}},staleTime:1e3*60*5,refetchOnWindowFocus:!1,retry:1}),n=_.useMemo(()=>{const p=a==null?void 0:a.nutritional_goals,S=b,D=(P,O)=>{if(p){const K=p[P]||p[P+"_target"]||p[P+"s"]||p["target_"+P]||O;return typeof K=="number"?K:O}return O},E=P=>{var K;const O=((K=S==null?void 0:S[P])==null?void 0:K.current)||(S==null?void 0:S[P])||0;return typeof O=="number"?O:0};return p?{protein:{current:E("protein"),target:D("protein",150)},carbs:{current:E("carbs"),target:D("carbs",200)},fat:{current:E("fat"),target:D("fat",70)},calories:{current:E("calories"),target:D("calories",2e3)}}:{protein:{current:E("protein"),target:150},carbs:{current:E("carbs"),target:200},fat:{current:E("fat"),target:70},calories:{current:E("calories"),target:2e3}}},[a,b]);(((G=n.protein)==null?void 0:G.current)||0)*4,(((re=n.carbs)==null?void 0:re.current)||0)*4,(((N=n.fat)==null?void 0:N.current)||0)*9,(((V=n.protein)==null?void 0:V.current)||0)*4,`${Math.round((((Ie=n.protein)==null?void 0:Ie.current)||0)*4)}`,(((fe=n.carbs)==null?void 0:fe.current)||0)*4,`${Math.round((((ne=n.carbs)==null?void 0:ne.current)||0)*4)}`,(((be=n.fat)==null?void 0:be.current)||0)*9,`${Math.round((((je=n.fat)==null?void 0:je.current)||0)*9)}`;const o={startDate:"2023-10-15",duration:"8 semanas",mealsCompleted:42,totalMeals:56,adherenceRate:75,waterIntake:{average:2.2,goal:3},calorieDeficit:{daily:350},weightLoss:{total:3.2}},t={monday:[{id:"meal-1",name:"Café da Manhã",time:"07:30",foods:[{id:"food-1",name:"Ovos",quantity:3,unit:"unidades",calories:210,protein:18,carbs:0,fat:15},{id:"food-2",name:"Aveia",quantity:40,unit:"g",calories:150,protein:5,carbs:27,fat:3},{id:"food-3",name:"Banana",quantity:1,unit:"unidade",calories:105,protein:1,carbs:27,fat:0}]},{id:"meal-2",name:"Almoço",time:"12:00",foods:[{id:"food-4",name:"Frango",quantity:150,unit:"g",calories:165,protein:31,carbs:0,fat:3.5},{id:"food-5",name:"Arroz",quantity:100,unit:"g",calories:130,protein:2.7,carbs:28,fat:.3},{id:"food-6",name:"Brócolis",quantity:100,unit:"g",calories:55,protein:3.7,carbs:11,fat:.6}]},{id:"meal-3",name:"Lanche",time:"16:00",foods:[{id:"food-7",name:"Whey Protein",quantity:30,unit:"g",calories:120,protein:24,carbs:3,fat:2},{id:"food-8",name:"Maçã",quantity:1,unit:"unidade",calories:95,protein:.5,carbs:25,fat:.3}]}],tuesday:[{id:"meal-5",name:"Café da Manhã",time:"07:30",foods:[{id:"food-12",name:"Iogurte Grego",quantity:200,unit:"g",calories:130,protein:22,carbs:8,fat:0},{id:"food-13",name:"Granola",quantity:30,unit:"g",calories:120,protein:3,carbs:20,fat:3},{id:"food-14",name:"Morangos",quantity:100,unit:"g",calories:32,protein:.7,carbs:7.7,fat:.3}]}],wednesday:[{id:"meal-9",name:"Café da Manhã",time:"07:30",foods:[{id:"food-20",name:"Panquecas Proteicas",quantity:2,unit:"unidades",calories:250,protein:20,carbs:25,fat:8}]}],thursday:[{id:"meal-13",name:"Café da Manhã",time:"07:30",foods:[{id:"food-25",name:"Omelete",quantity:1,unit:"porção",calories:220,protein:18,carbs:5,fat:16}]}],friday:[{id:"meal-17",name:"Café da Manhã",time:"07:30",foods:[{id:"food-30",name:"Smoothie Proteico",quantity:1,unit:"copo",calories:280,protein:25,carbs:30,fat:8}]}],saturday:[{id:"meal-21",name:"Café da Manhã",time:"08:30",foods:[{id:"food-35",name:"Pão Integral",quantity:2,unit:"fatias",calories:160,protein:8,carbs:30,fat:2}]}],sunday:[{id:"meal-25",name:"Café da Manhã",time:"09:00",foods:[{id:"food-40",name:"Tapioca",quantity:1,unit:"unidade",calories:130,protein:3,carbs:28,fat:.5}]}]},[m,x]=_.useState(!1),[v,M]=_.useState(!1),[R,Q]=_.useState(!1),[q,k]=_.useState(null),[L,X]=_.useState(!1),[Y,te]=_.useState("analysis"),[H,w]=_.useState(!1);_.useEffect(()=>{var p;console.log("🔍 DietPage - currentProtocol atualizado:",a),console.log("🔍 DietPage - loadingProtocol:",l),console.log("🍽️ DietPage - currentProtocol.meals:",a==null?void 0:a.meals),console.log("🎯 DietPage - nutritional_goals:",a==null?void 0:a.nutritional_goals),console.log("📊 DietPage - nutritionalSummary:",b),console.log("🥗 DietPage - nutritionGoals calculado - type:",typeof n),console.log("📅 DietPage - selectedDay:",r),console.log("🥘 DietPage - meals for selected day:",(p=a==null?void 0:a.meals)==null?void 0:p[r])},[a,l,b,n]),_.useEffect(()=>{var p;console.log("📅 DietPage - selectedDay changed to:",r),console.log("🥘 DietPage - meals for new selected day:",(p=a==null?void 0:a.meals)==null?void 0:p[r])},[r,a]);const $=async()=>{if(window.confirm("Tem certeza que deseja remover este protocolo?")){if(console.log("🔍 Debug - currentProtocol:",a),console.log("🔍 Debug - currentProtocol?.id:",a==null?void 0:a.id),console.log("🔍 Debug - typeof currentProtocol?.id:",typeof(a==null?void 0:a.id)),!(a!=null&&a.id)){console.error("❌ ID do protocolo não encontrado:",a),F.error("Erro: ID do protocolo não encontrado");return}try{console.log("🗑️ Removendo protocolo com ID:",a.id),await g.mutateAsync(a.id),console.log("✅ Protocolo removido com sucesso, UI será atualizada automaticamente")}catch(S){console.error("Erro ao remover protocolo:",S),F.error("Erro ao remover protocolo. Tente novamente.")}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Dieta"}),v&&e.jsx(Us,{onGenerateAI:()=>{s("/dashboard/diet/create-protocol/ai"),M(!1)},onCreateManual:()=>{s("/dashboard/diet/create-protocol/manual"),M(!1)},onImportNutritionist:()=>{s("/dashboard/diet/create-protocol/import"),M(!1)},onReadProtocol:()=>{s("/dashboard/diet/create-protocol/import"),M(!1)},onClose:()=>M(!1)}),!a&&!l&&e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center",children:e.jsx(ee,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-semibold text-gray-800 dark:text-white mb-2",children:"Nenhum protocolo ativo"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"Você não possui um protocolo de dieta ativo. Crie um novo protocolo para começar."}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 mx-auto",onClick:()=>M(!0),children:[e.jsx(se,{className:"w-4 h-4"}),"Criar Protocolo"]})]})}),a&&e.jsxs(_.Fragment,{children:[e.jsxs("div",{className:"card p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:(a==null?void 0:a.name)||"Protocolo de Cutting"}),e.jsx("div",{className:"flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:e.jsx("span",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:"Cutting"})}),(a==null?void 0:a.lastUpdated)&&e.jsx("div",{className:"flex items-center gap-1 px-2 py-1 bg-blue-500/20 rounded-lg border border-blue-500/30",children:e.jsx("span",{className:"text-xs font-medium text-blue-400",children:"Editado"})})]}),e.jsx("div",{className:"flex items-center gap-2",children:e.jsxs("button",{onClick:()=>{a!=null&&a.id?(console.log("🔧 Navegando para edição do protocolo:",a.id),s(`/dashboard/diet/edit-protocol/${a.id}`)):(console.error("❌ Nenhum protocolo ativo para editar"),F.error("Nenhum protocolo ativo para editar"))},disabled:!a,className:"flex items-center gap-1.5 px-3 py-2 text-sm font-medium text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/20 rounded-lg hover:bg-snapfit-green/20 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ee,{className:"w-4 h-4"}),"Editar Protocolo"]})}),e.jsx("div",{className:"flex gap-2",children:e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 text-sm",onClick:()=>M(!0),children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 5v14"}),e.jsx("path",{d:"M5 12h14"})]}),"Criar Protocolo"]})})]}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4",children:"Perda de gordura mantendo massa muscular"}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10 mb-6",children:[e.jsx("h3",{className:"font-bold text-white mb-3",children:"Metas Nutricionais do Protocolo"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Calorias"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((ve=n.calories)==null?void 0:ve.target)||0," kcal"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Proteínas"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((ye=n.protein)==null?void 0:ye.target)||0,"g (",Math.round((((Ne=n.protein)==null?void 0:Ne.target)||0)*4/(((de=n.calories)==null?void 0:de.target)||1)*100),"%)"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Carboidratos"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((we=n.carbs)==null?void 0:we.target)||0,"g (",Math.round((((me=n.carbs)==null?void 0:me.target)||0)*4/(((ke=n.calories)==null?void 0:ke.target)||1)*100),"%)"]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Gorduras"}),e.jsxs("div",{className:"text-base font-bold text-snapfit-green",children:[((Ce=n.fat)==null?void 0:Ce.target)||0,"g (",Math.round((((xe=n.fat)==null?void 0:xe.target)||0)*9/(((ge=n.calories)==null?void 0:ge.target)||1)*100),"%)"]})]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx(Gs,{protocolGoals:{calories:((i=n.calories)==null?void 0:i.target)||0,protein:((I=n.protein)==null?void 0:I.target)||0,carbs:((J=n.carbs)==null?void 0:J.target)||0,fat:((Se=n.fat)==null?void 0:Se.target)||0,water:2500}})}),e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Refeições"}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2",onClick:()=>w(!0),children:[e.jsx(se,{className:"w-4 h-4"}),"Nova Refeição"]})]}),e.jsxs("div",{className:"flex overflow-x-auto gap-2 pb-2 mb-4 hide-scrollbar",children:[e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="monday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("monday"),children:"Seg"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="tuesday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("tuesday"),children:"Ter"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="wednesday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("wednesday"),children:"Qua"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="thursday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("thursday"),children:"Qui"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="friday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("friday"),children:"Sex"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="saturday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("saturday"),children:"Sáb"}),e.jsx("button",{className:`px-4 py-2 rounded-lg text-sm font-medium whitespace-nowrap ${r==="sunday"?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-snapfit-dark-gray text-gray-300 border border-snapfit-gray/30"}`,onClick:()=>c("sunday"),children:"Dom"})]}),e.jsx("div",{className:"space-y-4",children:(ze=(()=>{const p=(d==null?void 0:d.meals)||[],S=t[r],D=p.length>0?p:S||[];return console.log("🍽️ DietPage - Meals to show:",{selectedDay:r,selectedDate:f,apiMealsCount:(p==null?void 0:p.length)||0,fallbackMealsCount:(S==null?void 0:S.length)||0,usingAPI:p.length>0,mealsToShow:D.length,hasProtocol:d==null?void 0:d.has_protocol}),D})())==null?void 0:ze.map(p=>{const S=p.foods||[],D=p.nutrients&&(p.nutrients.calories>0||p.nutrients.protein>0||p.nutrients.carbs>0||p.nutrients.fat>0);let E,P,O,K;return D?(E=Math.round(p.nutrients.calories||0),P=Math.round((p.nutrients.protein||0)*10)/10,O=Math.round((p.nutrients.carbs||0)*10)/10,K=Math.round((p.nutrients.fat||0)*10)/10):(E=S.reduce((A,B)=>{const z=B.calories||B.kcal||0;return A+(typeof z=="number"?z:0)},0),P=S.reduce((A,B)=>{const z=B.protein||B.proteins||0;return A+(typeof z=="number"?z:0)},0),O=S.reduce((A,B)=>{const z=B.carbs||B.carbohydrates||B.carb||0;return A+(typeof z=="number"?z:0)},0),K=S.reduce((A,B)=>{const z=B.fat||B.fats||B.lipids||0;return A+(typeof z=="number"?z:0)},0)),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex justify-between items-center mb-3",children:[e.jsx("h3",{className:"font-bold text-white",children:p.name}),e.jsx("span",{className:"text-sm text-gray-400",children:p.meal_time||p.time})]}),e.jsx("div",{className:"space-y-2",children:S.map((A,B)=>{const z=A.quantity||A.amount||0,ss=A.unit||A.measure||"g";return e.jsxs("div",{className:"flex justify-between items-center py-2 border-b border-snapfit-gray/20 last:border-0",children:[e.jsxs("div",{children:[e.jsx("div",{className:"text-white",children:A.name||"Alimento"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[z," ",ss]})]}),!D&&e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green",children:[Math.round(A.calories||A.kcal||0)," kcal"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["P: ",Math.round((A.protein||A.proteins||0)*10)/10,"g • C: ",Math.round((A.carbs||A.carbohydrates||A.carb||0)*10)/10,"g • G: ",Math.round((A.fat||A.fats||A.lipids||0)*10)/10,"g"]})]})]},A.id||B)})}),e.jsx("div",{className:"mt-4 pt-3 border-t border-snapfit-gray/20",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("div",{className:"text-sm font-medium text-white",children:"Total da Refeição"}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-snapfit-green font-bold",children:[E," kcal"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:["P: ",P.toFixed(1),"g • C: ",O.toFixed(1),"g • G: ",K.toFixed(1),"g"]})]})]})})]},p.id)})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Estatísticas do Protocolo"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Início"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:new Date(o.startDate).toLocaleDateString("pt-BR")})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Duração"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:o.duration})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Refeições Realizadas"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[o.mealsCompleted,"/",o.totalMeals]})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Adesão"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[o.adherenceRate,"%"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h3",{className:"text-base font-bold mb-3",children:"Adesão ao Protocolo"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Adesão Geral"}),e.jsxs("span",{children:[o.adherenceRate,"%"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"h-2.5 rounded-full bg-yellow-500",style:{width:`${o.adherenceRate}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Refeições Completadas"}),e.jsxs("span",{children:[o.mealsCompleted,"/",o.totalMeals]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-snapfit-green h-2.5 rounded-full",style:{width:`${o.mealsCompleted/o.totalMeals*100}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Consumo de Água"}),e.jsxs("span",{children:[o.waterIntake.average,"L / ",o.waterIntake.goal,"L"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-blue-500 h-2.5 rounded-full",style:{width:`${o.waterIntake.average/o.waterIntake.goal*100}%`}})})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between text-sm mb-1",children:[e.jsx("span",{className:"font-medium",children:"Déficit Calórico"}),e.jsxs("span",{children:[o.calorieDeficit.daily," kcal/dia"]})]}),e.jsx("div",{className:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5",children:e.jsx("div",{className:"bg-orange-500 h-2.5 rounded-full",style:{width:`${Math.min(o.calorieDeficit.daily/500*100,100)}%`}})})]})]})]}),e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h3",{className:"text-base font-bold mb-3",children:"Progresso"}),e.jsxs("div",{className:"flex flex-col items-center justify-center h-full",children:[e.jsx("div",{className:"mb-4",children:e.jsx(qs,{value:o.weightLoss.total,max:5,label:"Perda de Peso",sublabel:`${o.weightLoss.total}kg`,color:"#B9FF43",size:120})}),e.jsxs("div",{className:"text-center mt-2",children:[e.jsxs("div",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:["Meta: 5kg em ",o.duration]}),e.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:["Progresso: ",Math.round(o.weightLoss.total/5*100),"% concluído"]})]})]})]})]}),e.jsx(Qs,{nutritionGoals:n,className:"mt-6"})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Suplementos"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Whey Protein"}),e.jsx("div",{className:"text-sm text-gray-400",children:"30g • Após treino"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar imediatamente após o treino"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Creatina"}),e.jsx("div",{className:"text-sm text-gray-400",children:"5g • Diariamente"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar em qualquer momento do dia"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h3",{className:"font-bold text-white mb-1",children:"Multivitamínico"}),e.jsx("div",{className:"text-sm text-gray-400",children:"1 cápsula • Diariamente"}),e.jsx("div",{className:"text-xs text-gray-500 mt-2",children:"Tomar com o café da manhã"})]})]})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white mb-4",children:"Observações Gerais"}),e.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"Manter boa hidratação durante o dia. Evitar alimentos processados e com açúcar adicionado. Priorizar proteínas magras e carboidratos complexos."})]}),e.jsx("div",{className:"flex justify-end",children:e.jsxs("button",{className:"flex justify-between items-center gap-1 text-sm mb-4 text-slate-500 hover:text-red-500 transition-colors disabled:opacity-50",onClick:$,disabled:g.isPending||!a,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"w-4 h-4",children:[e.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),e.jsx("span",{children:"Remover Protocolo"})]})})]}),m&&e.jsx(Fs,{initialMode:q,onProtocolGenerated:p=>{console.log("Protocolo de dieta gerado pela IA:",p),alert(`Protocolo de dieta "${p.name}" gerado com sucesso!`),x(!1),k(null)},onClose:()=>{x(!1),k(null)}}),R&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20",children:e.jsx(Rs,{onSave:p=>{console.log("Protocolo manual criado:",p),alert(`Protocolo "${p.name}" criado com sucesso!`),Q(!1)},onCancel:()=>Q(!1)})})}),e.jsx(xa,{onOpenMicronutrientsModal:p=>{te(p),X(!0)},onReuseProtocol:async p=>{var S;if(p.edit){console.log("Editando protocolo:",p);try{const D=await T.get("users/protocols/diet/active");(S=D==null?void 0:D.data)!=null&&S.id?s(`/dashboard/diet/edit-protocol/${D.data.id}`):s("/dashboard/diet/edit-protocol/mock-protocol-id-123")}catch(D){console.error("Error getting active protocol:",D),s("/dashboard/diet/edit-protocol/mock-protocol-id-123")}}else{console.log("Reutilizando protocolo:",p);try{alert(`Protocolo ${p.name} reutilizado com sucesso!`)}catch(D){console.error("Error reusing protocol:",D),alert("Erro ao reutilizar protocolo")}}}}),e.jsx(ta,{isOpen:L,onClose:()=>X(!1),initialTab:Y}),e.jsx(ua,{isOpen:H,onClose:()=>w(!1),selectedDate:new Date().toISOString().split("T")[0]})]})}export{va as DietPage};
