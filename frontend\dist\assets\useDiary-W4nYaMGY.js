import{h as u,u as c,as as d,y as n,b as t}from"./index-BSexrr-f.js";const i={all:["diary"],entries:()=>[...i.all,"entries"],entry:e=>[...i.entries(),e],nutritionalSummary:(e,s)=>[...i.all,"nutritional-summary",e,s],meals:()=>[...i.all,"meals"],mealsByDate:e=>[...i.meals(),e],activities:()=>[...i.all,"activities"],activitiesByDate:e=>[...i.activities(),e],water:()=>[...i.all,"water"],waterByDate:e=>[...i.water(),e]};function l(e,s){return u({queryKey:i.nutritionalSummary(e,s),queryFn:async()=>{console.log("🔄 useNutritionalSummary: Buscando resumo nutricional...",{dateStart:e,dateEnd:s});try{if(e===s)try{const o=await t.get("dashboard/nutritional-summary",{searchParams:{date:e}});return console.log("🍎 useNutritionalSummary: Dashboard resumo recebido:",o.data),o.data}catch{console.log("Dashboard endpoint failed, trying users endpoint...")}const a=await t.get("users/diary/nutritional_summary",{searchParams:{date_start:e,date_end:s}});return console.log("🍎 useNutritionalSummary: Users resumo recebido:",a.data),a.data}catch(a){throw console.warn("⚠️ useNutritionalSummary: All endpoints failed:",a),a}},staleTime:1e3*60*2,enabled:!!e&&!!s})}function m(e){return u({queryKey:i.entry(e),queryFn:async()=>{console.log("🔄 useDiaryEntry: Buscando entrada do diário...",e);const s=await t.get("users/diary",{searchParams:{date:e}});return console.log("📖 useDiaryEntry: Entrada recebida:",s.data),s.data},staleTime:1e3*60*5,enabled:!!e})}function v(e){return u({queryKey:i.mealsByDate(e),queryFn:async()=>{console.log("🔄 useMealsByDate: Buscando refeições...",e);const s=await t.get("users/diary/meals",{searchParams:{date:e}});return console.log("🍽️ useMealsByDate: Refeições recebidas:",s.data),s.data},staleTime:1e3*60*3,enabled:!!e})}function g(e){return u({queryKey:i.activitiesByDate(e),queryFn:async()=>{console.log("🔄 useActivitiesByDate: Buscando atividades...",e);const s=await t.get("users/diary/activities",{searchParams:{date:e}});return console.log("🏃 useActivitiesByDate: Atividades recebidas:",s.data),s.data},staleTime:1e3*60*3,enabled:!!e})}function p(e){return u({queryKey:i.waterByDate(e),queryFn:async()=>{console.log("🔄 useWaterByDate: Buscando consumo de água...",e);const s=await t.get("users/diary/water",{searchParams:{date:e}});return console.log("💧 useWaterByDate: Consumo recebido:",s.data),s.data},staleTime:1e3*60*5,enabled:!!e})}function A(){const e=c();return d({mutationFn:async({date:s,mealData:a})=>(console.log("🍽️ useAddMeal: Adicionando refeição...",{date:s,mealData:a}),await t.post("users/diary/meals",{date:s,...a})),onSuccess:(s,a)=>{e.invalidateQueries({queryKey:i.mealsByDate(a.date)}),e.invalidateQueries({queryKey:i.entry(a.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(a.date,a.date)}),n.success("Refeição adicionada com sucesso!",{position:"bottom-right"}),console.log("✅ useAddMeal: Refeição adicionada com sucesso")},onError:s=>{var o,r;const a=((r=(o=s==null?void 0:s.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao adicionar refeição";n.error(a,{position:"bottom-right"}),console.error("❌ useAddMeal: Erro ao adicionar refeição:",s)}})}function B(){const e=c();return d({mutationFn:async({date:s,activityData:a})=>(console.log("🏃 useAddActivity: Adicionando atividade...",{date:s,activityData:a}),await t.post("users/diary/activities",{date:s,...a})),onSuccess:(s,a)=>{e.invalidateQueries({queryKey:i.activitiesByDate(a.date)}),e.invalidateQueries({queryKey:i.entry(a.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(a.date,a.date)}),n.success("Atividade adicionada com sucesso!",{position:"bottom-right"}),console.log("✅ useAddActivity: Atividade adicionada com sucesso")},onError:s=>{var o,r;const a=((r=(o=s==null?void 0:s.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao adicionar atividade";n.error(a,{position:"bottom-right"}),console.error("❌ useAddActivity: Erro ao adicionar atividade:",s)}})}function q(){const e=c();return d({mutationFn:async({activityId:s,date:a})=>(console.log("🗑️ useRemoveActivity: Removendo atividade...",{activityId:s,date:a}),await t.delete(`users/diary/activities/${s}`)),onSuccess:(s,a)=>{e.invalidateQueries({queryKey:i.activitiesByDate(a.date)}),e.invalidateQueries({queryKey:i.entry(a.date)}),e.invalidateQueries({queryKey:i.nutritionalSummary(a.date,a.date)}),n.success("Atividade removida com sucesso!",{position:"bottom-right"}),console.log("✅ useRemoveActivity: Atividade removida com sucesso")},onError:s=>{var o,r;const a=((r=(o=s==null?void 0:s.response)==null?void 0:o.payload)==null?void 0:r.message)||"Erro ao remover atividade";n.error(a,{position:"bottom-right"}),console.error("❌ useRemoveActivity: Erro ao remover atividade:",s)}})}export{A as a,m as b,v as c,g as d,p as e,B as f,q as g,l as u};
