import{t as N,j as e,aO as w,H as b,bi as _,b as A,y as j}from"./index-BSexrr-f.js";function M(){const c=N(),y=async a=>{var m,p,u,g,x,h;try{const s=r=>({"weight-loss":1,maintenance:2,"muscle-gain":3})[r]||1,i=r=>{const o=[];return Object.entries(r).forEach(([v,f])=>{Array.isArray(f)&&f.forEach(l=>{o.push({name:l.name,day_of_week:v,meal_time:l.time,foods:l.foods.map(n=>({name:n.name,quantity:Number(n.quantity)||0,unit:n.unit||"g",calories:Number(n.calories)||0,protein:Number(n.protein)||0,carbs:Number(n.carbs)||0,fat:Number(n.fat)||0,fiber:Number(n.fiber)||0}))})})}),o},t={name:a.name,type_id:s(a.type),objective:a.objective||"",nutritional_goals:a.nutritional_goals||a.goals,meals:i(a.weeklyMeals),supplements:((m=a.supplements)==null?void 0:m.map(r=>({name:r.name,dosage:r.dosage,supplement_time:r.supplement_time,notes:r.notes||""})))||[],general_notes:a.notes||""};if(console.log("🚀 Sending protocol data to API:",t),!t.name)throw new Error("Protocol name is required");if(!t.type_id||typeof t.type_id!="number")throw new Error("Valid type_id is required");if(!t.nutritional_goals||typeof t.nutritional_goals!="object")throw new Error("Nutritional goals are required");if(!Array.isArray(t.meals))throw new Error("Meals must be an array");if(console.log("✅ Validation passed, sending to API..."),await A.post("users/protocols/diet",t))j.success("Protocolo de dieta salvo com sucesso!",{position:"bottom-right"}),setTimeout(()=>{c("/dashboard/diet")},2e3);else throw new Error("Failed to create diet protocol")}catch(s){console.error("Error saving diet protocol:",s);let i="Erro ao salvar protocolo de dieta";(u=(p=s==null?void 0:s.response)==null?void 0:p.data)!=null&&u.message?Array.isArray(s.response.data.message)?i=s.response.data.message.join(", "):i=s.response.data.message:s!=null&&s.message&&(i=s.message),console.error("🚨 Detailed error:",{status:(g=s==null?void 0:s.response)==null?void 0:g.status,statusText:(x=s==null?void 0:s.response)==null?void 0:x.statusText,data:(h=s==null?void 0:s.response)==null?void 0:h.data,message:s==null?void 0:s.message}),j.error(i,{position:"bottom-right"})}},d=()=>{c("/dashboard/diet")};return e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:d,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(w,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(b,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h1",{className:"text-lg font-medium text-white",children:"Criar Protocolo de Dieta Manual"})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-4xl mx-auto",children:[e.jsx("div",{className:"bg-snapfit-green/10 rounded-xl p-4 border border-snapfit-green/20 mb-6",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5",children:e.jsx(b,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Criação Manual de Protocolo"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Configure objetivos nutricionais personalizados"}),e.jsx("p",{children:"• Selecione alimentos do banco de dados integrado"}),e.jsx("p",{children:"• Organize refeições por dia da semana"}),e.jsx("p",{children:"• Adicione suplementos com horários específicos"}),e.jsx("p",{children:"• Cálculo automático de calorias e macronutrientes"})]})]})]})}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20",children:e.jsx(_,{onSave:y,onCancel:d})})]})})]})}export{M as CreateDietProtocolManualPage};
