import React from 'react';
import { WorkoutTracker } from '../components/WorkoutTracker';
import { WorkoutSelector } from '../components/WorkoutSelector';
// import { mockWorkouts } from '../data/mockWorkouts'; // Replaced with workoutService
import { workoutService, type WorkoutProtocol } from '../services/workoutService';
import { useNavigate } from 'react-router-dom';
import { WorkoutHistoryTabs } from '../components/WorkoutHistoryTabs';
import { UnifiedWorkoutCard } from '../components/UnifiedWorkoutCard';
import { WorkoutProtocolOptions } from '../components/WorkoutProtocolOptions';
import { WorkoutProtocolAIForm } from '../components/WorkoutProtocolAIForm';
import { WorkoutProtocolImporter } from '../components/WorkoutProtocolImporter';
import { WorkoutProtocolManualCreator } from '../components/WorkoutProtocolManualCreator';
import { LoadingScreen } from '../components/LoadingScreen';
import { apiService } from '../services/api';
import { Activity, Award, Dumbbell, Flame, Plus, Timer, TrendingUp, Weight, XIcon } from 'lucide-react';
import LoadingOverlay from '../components/LoadingOverlay';
import { toast } from 'react-toastify';
import { ModernChart } from '../components/ModernChart';
import { StatCard } from '../components/StatCard';

import { CircularProgress } from '../components/CircularProgress';
import { WorkoutAIGenerator } from '../components/WorkoutAIGenerator';
import WorkoutSectionSelector from '../components/WorkoutSectionSelector';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  useWorkoutData
} from '../hooks/useWorkoutProtocol';
import {
  useActiveWorkoutProtocol,
  useWorkoutsByDate,
  useWorkoutHistory,
  useStartWorkout,
  useCompleteWorkout,
  useCreateWorkoutProtocol
} from '../hooks/useWorkout';
import { useDashboardFilters } from '../hooks/useDashboardFilters';
import { PeriodSelector } from '../components/PeriodSelector';
import { ConfirmationDialog } from '../components/ConfirmationDialog';
import { useCheckActiveProtocol, useCreateProtocolWithCheck, useFinalizeProtocol } from '../hooks/useProtocolManagement';

type CreationMode = 'options' | 'ai' | 'manual' | 'import' | null;

export function WorkoutPage() {
  const queryClient = useQueryClient();

  // Dashboard filters for period management
  const filters = useDashboardFilters('week', ['workout-stats', 'workout-analytics']);

  // Hook principal para gerenciar todas as queries de workout
  const {
    protocol: protocolWorkouts,
    stats: workoutStatsData,
    isLoadingProtocol: loadingPage,
    isLoadingStats,
    protocolError,
    statsError,
    removeProtocol,
    createProtocol,
    createProtocolAI,
    isRemovingProtocol,
    isCreatingProtocol,
    isCreatingProtocolAI,
    refetchStats,
    refetchAll,
  } = useWorkoutData(filters.period);

  // Usar dados do hook ou fallback para valores padrão
  const workoutStats = workoutStatsData || {
    weeklyWorkouts: { completed: 0, planned: 5 },
    totalTime: 0,
    totalCalories: 0,
    totalVolume: 0,
    currentStreak: 0,
    bestStreak: 0,
    weeklyProgressPercentage: 0,
    protocolCompletionPercentage: 0,
    totalWorkouts: 0,
    // Backward compatibility
    progressPercentage: 0
  };
  // Processar dados do gráfico a partir da API
  const workoutProgressData = React.useMemo(() => {
    if (workoutStats.chartData && workoutStats.chartData.length > 0) {
      // Usar dados reais da API
      return workoutStats.chartData.map((item: any) => ({
        value: item.training_volume || 0,
        label: item.label || 'N/A',
        color: '#B9FF43'
      }));
    }

    // Fallback para dados padrão se não houver dados da API
    return [
      { value: 0, label: 'Seg', color: '#B9FF43' },
      { value: 0, label: 'Ter', color: '#B9FF43' },
      { value: 0, label: 'Qua', color: '#B9FF43' },
      { value: 0, label: 'Qui', color: '#B9FF43' },
      { value: 0, label: 'Sex', color: '#B9FF43' },
      { value: 0, label: 'Sáb', color: '#B9FF43' },
      { value: 0, label: 'Dom', color: '#B9FF43' }
    ];
  }, [workoutStats.chartData]);
  const [strengthProgressData, setStrengthProgressData] = React.useState([
    { value: 0, label: 'Supino', color: '#B9FF43' },
    { value: 0, label: 'Agachamento', color: '#4CAF50' },
    { value: 0, label: 'Levantamento', color: '#FFC107' },
    { value: 0, label: 'Remada', color: '#FF5722' }
  ]);

  // UI states
  const [selectedWorkout, setSelectedWorkout] = React.useState<any>(null);
  const [isGenerating, setIsGenerating] = React.useState(false);
  const [showSuccess, setShowSuccess] = React.useState(false);
  const [creationMode, setCreationMode] = React.useState<CreationMode>(null);
  const [protocolNotes, setProtocolNotes] = React.useState(
    'Foco em tempo sob tensão nos exercícios de peito.\nAumentar carga progressivamente nos exercícios compostos.\nManter strict form em todos os exercícios.'
  );
  const [showProtocolOptions, setShowProtocolOptions] = React.useState(false);
  const [showProtocolReader, setShowProtocolReader] = React.useState(false);
  const [protocolMode, setProtocolMode] = React.useState<'form' | 'upload' | null>(null);
  const [showActiveProtocolConfirmation, setShowActiveProtocolConfirmation] = React.useState(false);
  const [pendingProtocolAction, setPendingProtocolAction] = React.useState<(() => void) | null>(null);
  const navigate = useNavigate();

  // Protocol management hooks
  const checkActiveProtocol = useCheckActiveProtocol();
  const createProtocolWithCheck = useCreateProtocolWithCheck();
  const finalizeProtocol = useFinalizeProtocol();

  const showToastErrors = (errors: string | string[]) => {
    if (Array.isArray(errors)) {
        // Se for um array, exibe todos os erros
        errors.forEach((error) => toast.error(error, { position: 'bottom-right' }));
    } else if (typeof errors === 'string') {
        // Se for uma string, exibe o erro diretamente
        toast.error(errors, { position: 'bottom-right' });
    }
  };

  // Efeito para selecionar primeiro treino quando protocolo carrega
  React.useEffect(() => {
    if (protocolWorkouts?.workouts && protocolWorkouts.workouts.length > 0) {
      setSelectedWorkout(protocolWorkouts.workouts[0]);
      console.log('✅ WorkoutPage: Primeiro treino selecionado:', protocolWorkouts.workouts[0]);
    } else {
      console.log('⚠️ WorkoutPage: Nenhum treino disponível para seleção');
      setSelectedWorkout(null);
    }
  }, [protocolWorkouts]);

  // Load workout statistics
  // Função removida - agora usamos useWorkoutStats hook

  // Tratamento de erro do React Query
  React.useEffect(() => {
    if (protocolError) {
      console.error('❌ React Query: Erro ao carregar protocolo:', protocolError);
      toast.error('Erro ao carregar protocolo de treino', { position: 'bottom-right' });
    }
  }, [protocolError]);



  // Estatísticas agora são carregadas automaticamente pelo useWorkoutStats hook

  const handleRemoveProtocol = async () => {
    const confirm = window.confirm('Tem certeza que deseja remover este protocolo?');
    if (!confirm || !protocolWorkouts?.id) return;

    removeProtocol(protocolWorkouts.id);
  };

  // Function to check for active protocol before showing options
  const handleShowProtocolOptions = async () => {
    try {
      const activeProtocol = await checkActiveProtocol.mutateAsync('workout');

      if (activeProtocol && activeProtocol.id) {
        // There's an active protocol, show confirmation dialog
        setShowActiveProtocolConfirmation(true);
        setPendingProtocolAction(() => () => setShowProtocolOptions(true));
      } else {
        // No active protocol, proceed directly
        setShowProtocolOptions(true);
      }
    } catch (error) {
      console.error('Error checking active protocol:', error);
      // If there's an error checking, proceed anyway
      setShowProtocolOptions(true);
    }
  };

  // Handle confirmation to finalize active protocol and create new one
  const handleConfirmFinalizeAndCreate = async () => {
    try {
      // First, get the active protocol to finalize it
      const activeProtocol = await checkActiveProtocol.mutateAsync('workout');

      console.log('🔍 Active protocol found:', activeProtocol);

      if (activeProtocol && activeProtocol.id) {
        console.log(`🏁 Finalizing active protocol with ID: ${activeProtocol.id}`);

        // Finalize the active protocol using the finalize hook
        await finalizeProtocol.mutateAsync({
          protocolId: activeProtocol.id.toString(),
          protocolType: 'workout'
        });

        console.log('✅ Active protocol finalized successfully');
      } else {
        console.log('ℹ️ No active protocol found to finalize');
      }

      // Now proceed with the pending action (show protocol options)
      if (pendingProtocolAction) {
        pendingProtocolAction();
        setPendingProtocolAction(null);
      }
      setShowActiveProtocolConfirmation(false);
    } catch (error) {
      console.error('❌ Error in finalization flow:', error);

      // Check if it's a "protocol not found" error
      const errorMessage = error?.response?.data?.message || error?.message || 'Erro desconhecido';

      if (errorMessage.includes('não encontrado') || errorMessage.includes('já finalizado')) {
        console.log('ℹ️ Protocol already finalized or not found, proceeding with creation');

        // Protocol is already finalized or doesn't exist, proceed with creation
        if (pendingProtocolAction) {
          pendingProtocolAction();
          setPendingProtocolAction(null);
        }
      } else {
        console.error('❌ Unexpected error during finalization:', errorMessage);
      }

      setShowActiveProtocolConfirmation(false);
    }
  };

  const handleGenerateAI = () => {
    navigate('/dashboard/workout/create-protocol/ai');
    setShowProtocolOptions(false);
  };

  const handleCreateManual = () => {
    navigate('/dashboard/workout/create-protocol/manual');
    setShowProtocolOptions(false);
  };

  const handleImportFromCoach = () => {
    navigate('/dashboard/workout/create-protocol/import');
    setShowProtocolOptions(false);
  };

  const handleSaveProtocol = async (protocol: any) => {
    const protocolData: any = {
      name: protocol.name,
      type_id: protocol.type,
      objective: protocol.objective,
      started_at: protocol.startDate,
      frequency: protocol.frequency,
      split: protocol.split,
      goals: protocol.goals,
      workouts: protocol.workouts.map((workout: any) => {
        return {
          exercises: workout.exercises.map((exercise: any) => {
            return {
              exercise_id: exercise.exercise.id,
              sets: exercise.sets,
              reps: exercise.reps,
              rpe: exercise.rpe,
              rest_seconds: exercise.restTime,
              notes: exercise.notes
            };
          })
        };
      }),
      supplements: protocol.supplements,
      general_notes: protocol.notes
    };

    console.log('Saving protocol:', protocol);
    setShowSuccess(true);
    setIsGenerating(true);

    // Use the new hook - active protocol should already be finalized by confirmation flow
    createProtocolWithCheck.mutate({
      protocolData,
      protocolType: 'workout',
      shouldFinalizeActive: false
    }, {
      onSuccess: () => {
        setTimeout(() => {
          setIsGenerating(false);
          setShowSuccess(false);
          setCreationMode(null);
        }, 1000);
      },
      onError: () => {
        setIsGenerating(false);
        setShowSuccess(false);
      }
    });
  };

  const handleCancel = () => {
    setCreationMode(null);
    setIsGenerating(false);
    setShowSuccess(false);
    setShowProtocolOptions(false);
  };

  if (isGenerating) {
    return (
      <LoadingScreen
        type="workout"
        isSuccess={showSuccess}
        message={showSuccess ? "Protocolo gerado com sucesso! 🎉" : undefined}
      />
    );
  }

  const onGenerateNewProtocolAi = async (data: any) => {
    // Show loading screen first
    setIsGenerating(true);

    // Use the new hook - active protocol should already be finalized by confirmation flow
    createProtocolWithCheck.mutate({
      protocolData: data,
      protocolType: 'workout',
      shouldFinalizeActive: false
    }, {
      onSuccess: () => {
        setShowSuccess(true);
        // Reset states and close
        setTimeout(() => {
          setIsGenerating(false);
          setShowSuccess(false);
          setCreationMode(null);
        }, 1000);
      },
      onError: () => {
        // Reset states and close
        setCreationMode(null);
        setIsGenerating(false);
      }
    });
  };



  return (
    <>
      {loadingPage && (
        <LoadingOverlay />
      )}

      <div className="space-y-6">
        <h1 className="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
          Treino
        </h1>

        {showProtocolOptions && (
          <WorkoutProtocolOptions
            onGenerateAI={handleGenerateAI}
            onCreateManual={handleCreateManual}
            onImportFromCoach={handleImportFromCoach}
            onReadProtocol={() => {
              navigate('/dashboard/workout/create-protocol/import');
              setShowProtocolOptions(false);
            }}
            onClose={handleCancel}
          />
        )}

        {creationMode === 'ai' && (
          <WorkoutAIGenerator
            onProtocolGenerated={(protocol) => {
              console.log('Protocolo de treino gerado pela IA:', protocol);
              handleSaveProtocol(protocol);
            }}
            onClose={handleCancel}
          />
        )}

        {creationMode === 'manual' && (
          <WorkoutProtocolManualCreator
            onSave={handleSaveProtocol}
            onCancel={handleCancel}
          />
        )}

        {creationMode === 'import' && (
          <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50">
            <div className="bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20">
              <WorkoutProtocolImporter
                onImport={handleSaveProtocol}
                onCancel={handleCancel}
              />
            </div>
          </div>
        )}

        {protocolWorkouts && !protocolWorkouts?.has_protocol && !creationMode && (
          <div className="card p-6">
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <h2 className="text-lg sm:text-xl font-bold text-gray-800 dark:text-white">
                  Você não tem um protocolo de treino ativo
                </h2>
                <button
                  onClick={handleShowProtocolOptions}
                  className="btn-primary flex items-center justify-center gap-2 w-full sm:w-auto"
                >
                  <Plus className="w-5 h-5" />
                  Novo Protocolo
                </button>
              </div>

              {/* Modern workout stats */}
              <div className="mt-8">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4">
                  <h3 className="text-lg font-bold">Estatísticas de Treino</h3>
                  <PeriodSelector
                    period={filters.period}
                    onPeriodChange={filters.setPeriod}
                    onCustomDateChange={filters.setCustomDates}
                    className="w-full sm:w-auto"
                  />
                </div>
                {isLoadingStats ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"></div>
                    <span className="ml-2 text-gray-600">Carregando estatísticas...</span>
                  </div>
                ) : statsError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <p className="text-red-600 text-sm">Erro ao carregar estatísticas</p>
                    <button
                      onClick={() => refetchStats()}
                      className="mt-2 text-red-700 underline text-xs"
                    >
                      Tentar novamente
                    </button>
                  </div>
                ) : (
                <div className="mobile-scroll-container">
                  <div className="mobile-scroll-content">
                    <StatCard
                      title="Treinos Semana"
                      value={`${workoutStats.weeklyWorkouts.completed}/${workoutStats.weeklyWorkouts.planned}`}
                      icon={<Dumbbell className="animate-pulse-slow" />}
                      change={workoutStats.weeklyWorkouts.completed > 0 ? 10 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                    />
                    <StatCard
                      title="Tempo Total"
                      value={`${workoutStats.totalTime} min`}
                      icon={<Timer className="animate-pulse-slow" />}
                      change={workoutStats.totalTime > 0 ? 5 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                    />
                    <StatCard
                      title="Calorias"
                      value={`${workoutStats.totalCalories} kcal`}
                      icon={<Flame className="animate-pulse-slow" />}
                      change={workoutStats.totalCalories > 0 ? 8 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                      showScientificBadge={true}
                    />
                    <StatCard
                      title="Volume"
                      value={`${workoutStats.totalVolume} kg`}
                      icon={<Weight className="animate-pulse-slow" />}
                      change={workoutStats.totalVolume > 0 ? 15 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                      showScientificBadge={true}
                    />
                    <StatCard
                      title="Sequência Atual"
                      value={`${workoutStats.currentStreak} dias`}
                      icon={<TrendingUp className="animate-pulse-slow" />}
                      change={workoutStats.currentStreak > 0 ? 5 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                    />
                    <StatCard
                      title="Melhor Sequência"
                      value={`${workoutStats.bestStreak} dias`}
                      icon={<Award className="animate-pulse-slow" />}
                      change={workoutStats.bestStreak > 0 ? 3 : 0}
                      className="mobile-card stagger-item animate-slide-in-right"
                    />
                  </div>
                </div>
                )}
              </div>

              {/* Modern charts */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <ModernChart
                  data={workoutProgressData}
                  title="Duração dos Treinos (min)"
                  type="bar"
                  className="animate-slide-in-left"
                />
                <ModernChart
                  data={strengthProgressData}
                  title="Progresso de Força (kg)"
                  type="bar"
                  className="animate-slide-in-right"
                />
              </div>
            </div>
          </div>
        )}



      {(() => {
        console.log('🔍 WorkoutPage: Verificando condições de renderização:', {
          protocolWorkouts: !!protocolWorkouts,
          creationMode,
          hasProtocol: protocolWorkouts?.has_protocol,
          shouldRender: protocolWorkouts && !creationMode && protocolWorkouts?.has_protocol
        });
        return protocolWorkouts && !creationMode && protocolWorkouts?.has_protocol;
      })() && (
        <>
          {/* Unified Workout Card - Moved to top */}
          <div className="card p-6 animate-slide-up">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
              <div>
                <div className="flex items-center gap-2">
                  <h3 className="text-lg font-bold text-gray-800 dark:text-white">Protocolo de Treino</h3>
                  <div className="flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30">
                    <span className="text-xs sm:text-sm font-medium text-snapfit-green">
                      {protocolWorkouts?.type || 'Hipertrofia'}
                    </span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{protocolWorkouts?.objective}</p>
              </div>
              <div className="flex gap-2 mt-2 sm:mt-0">
                <button
                  className="btn-secondary flex items-center justify-center gap-2 text-sm"
                  onClick={() => {
                    console.log('🔄 Botão Editar Protocolo clicado. Protocolo ID:', protocolWorkouts?.id);
                    if (protocolWorkouts?.id) {
                      // Para coaches, usar a rota profissional
                      if (window.location.pathname.includes('/professional/')) {
                        console.log('🏃‍♂️ Navegando para rota profissional:', `/dashboard/professional/protocols/edit/${protocolWorkouts.id}?type=workout`);
                        navigate(`/dashboard/professional/protocols/edit/${protocolWorkouts.id}?type=workout`);
                      } else {
                        // Para usuários normais, usar a rota de usuário
                        console.log('👤 Navegando para rota de usuário:', `/dashboard/workout/edit-protocol/${protocolWorkouts.id}`);
                        navigate(`/dashboard/workout/edit-protocol/${protocolWorkouts.id}`);
                      }
                    } else {
                      console.log('❌ Nenhum protocolo ID encontrado, navegando para criação');
                      navigate('/dashboard/workout/create-protocol/manual');
                    }
                  }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                  </svg>
                  Editar Protocolo
                </button>

                <button
                  className="btn-primary flex items-center justify-center gap-2 text-sm"
                  onClick={handleShowProtocolOptions}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M12 5v14"></path>
                    <path d="M5 12h14"></path>
                  </svg>
                  Criar Protocolo
                </button>
              </div>
            </div>
          </div>

          <UnifiedWorkoutCard
            protocolId={protocolWorkouts?.id}
            protocolName={protocolWorkouts?.name}
            startDate={protocolWorkouts?.started_at}
            splitInfo={protocolWorkouts?.split}
            frequency={`${protocolWorkouts?.frequency}x/semana`}
            objective={protocolWorkouts?.objective}
            completedWorkouts={protocolWorkouts?.workouts_completed || 0}
            notes={protocolWorkouts?.notes}
            workouts={protocolWorkouts?.workouts || []}
            workoutsDb={protocolWorkouts?.workouts}
            selectedWorkout={selectedWorkout}
            onSelectWorkout={setSelectedWorkout}
            onGenerateNewProtocol={handleShowProtocolOptions}
            onEditNotes={() => {
              console.log('Editing protocol notes...');
            }}
            onDeleteProtocol={handleRemoveProtocol}
          />


          {/* Modern workout overview - Moved below */}
          <div className="card p-6 animate-slide-up">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
              <h3 className="text-lg font-bold text-gray-800 dark:text-white">Estatísticas do Protocolo</h3>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                Progresso: {Math.round(workoutStats.protocolCompletionPercentage)}% completo
              </div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
                <div className="text-xs text-gray-500 dark:text-gray-400">Frequência</div>
                <div className="text-base font-bold text-gray-800 dark:text-white">{`${protocolWorkouts?.frequency || 0}x/semana`}</div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
                <div className="text-xs text-gray-500 dark:text-gray-400">Divisão</div>
                <div className="text-base font-bold text-gray-800 dark:text-white">{protocolWorkouts?.split || 'N/A'}</div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
                <div className="text-xs text-gray-500 dark:text-gray-400">Total de Treinos</div>
                <div className="text-base font-bold text-gray-800 dark:text-white">{workoutStats.totalWorkouts}</div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-xl">
                <div className="text-xs text-gray-500 dark:text-gray-400">Sequência Atual</div>
                <div className="text-base font-bold text-gray-800 dark:text-white">{workoutStats.currentStreak} dias</div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <ModernChart
                data={workoutProgressData}
                title="Duração dos Treinos (min)"
                type="line"
                height={180}
                className="animate-slide-in-left"
              />
              <div className="card-glass p-4">
                <h4 className="text-base font-bold mb-3">Progresso</h4>
                <div className="flex justify-around gap-4">
                  <CircularProgress
                    value={workoutStats.weeklyWorkouts.completed}
                    max={workoutStats.weeklyWorkouts.planned}
                    label="Esta Semana"
                    sublabel={`${workoutStats.weeklyWorkouts.completed}/${workoutStats.weeklyWorkouts.planned} treinos`}
                    color="#B9FF43"
                    size={90}
                  />
                  <CircularProgress
                    value={workoutStats.weeklyProgressPercentage}
                    max={100}
                    label="Meta Semanal"
                    sublabel={`${Math.round(workoutStats.weeklyProgressPercentage)}% atingido`}
                    color="#4CAF50"
                    size={90}
                  />
                  <CircularProgress
                    value={workoutStats.protocolCompletionPercentage}
                    max={100}
                    label="Protocolo"
                    sublabel={`${Math.round(workoutStats.protocolCompletionPercentage)}% completo`}
                    color="#FF9800"
                    size={90}
                  />
                </div>
              </div>
            </div>
          </div>

          {showProtocolReader && (
            <WorkoutAIGenerator
              initialMode={protocolMode}
              onProtocolGenerated={(protocol) => {
                console.log('Protocolo gerado pela IA:', protocol);
                handleSaveProtocol(protocol);
                setShowProtocolReader(false);
                setProtocolMode(null);
              }}
              onClose={() => {
                setShowProtocolReader(false);
                setProtocolMode(null);
              }}
            />
          )}

          {/* Seções de Análise e Ferramentas IA */}
          <WorkoutSectionSelector
            onReuseProtocol={(protocol) => {
              if (protocol.edit) {
                // Abrir o protocolo para edição
                console.log('Editando e usando protocolo:', protocol);
                alert(`Editando e usando protocolo: ${protocol.name || protocol.workout_name}`);
                // Aqui você pode abrir o modal de edição ou navegar para a página de edição
                // setShowProtocolCreator(true);
                // setEditingProtocol(protocol);
              } else {
                // Usar o protocolo diretamente
                console.log('Reutilizando protocolo:', protocol);
                alert(`Protocolo ${protocol.name || protocol.workout_name} reutilizado com sucesso!`);
                // Aqui você pode definir o protocolo como ativo
                // setCurrentProtocol(protocol);
              }
            }}
          />
        </>
      )}

      {/* Workout History with Tabs - Always visible regardless of active protocol status */}
      {!creationMode && (
        <div className="space-y-6">
          <WorkoutHistoryTabs
            hasActiveProtocol={protocolWorkouts?.has_protocol || false}
            defaultTab={protocolWorkouts?.has_protocol ? 'sessions' : 'protocols'}
            onReuseProtocol={(protocol) => {
              if (protocol.edit) {
                console.log('Editando e usando protocolo:', protocol);
                alert(`Editando e usando protocolo: ${protocol.name || protocol.workout_name}`);
              } else {
                console.log('Reutilizando protocolo:', protocol);
                alert(`Protocolo ${protocol.name || protocol.workout_name} reutilizado com sucesso!`);
              }
            }}
          />
        </div>
      )}
    </div>

    {/* Confirmation Dialog for Active Protocol */}
    <ConfirmationDialog
      isOpen={showActiveProtocolConfirmation}
      onClose={() => {
        setShowActiveProtocolConfirmation(false);
        setPendingProtocolAction(null);
      }}
      onConfirm={handleConfirmFinalizeAndCreate}
      title="Protocolo Ativo Encontrado"
      message="Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?"
      confirmText="Sim, Continuar"
      cancelText="Cancelar"
      type="warning"
      isLoading={checkActiveProtocol.isPending || createProtocolWithCheck.isPending || finalizeProtocol.isPending}
    />

    </>
  );
}