var E=Object.defineProperty;var T=(a,s,t)=>s in a?E(a,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[s]=t;var N=(a,s,t)=>T(a,typeof s!="symbol"?s+"":s,t);import{c as D,r as c,j as e,ca as w,af as L,I as A,ao as F,o as I,y as h,cb as R,an as y,ah as W,a as q,aP as S,cc as _,cd as B,bm as p,b as x,t as V,ce as O,ar as $,aj as U,bv as z,b4 as J}from"./index-ClF00oko.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],H=D("rotate-ccw",X);function C(){return{id:"mock-diet-protocol-123",name:"Protocolo de Cutting Teste",type:"diet",objective:"Perda de gordura mantendo massa muscular",startDate:new Date().toISOString().split("T")[0],notes:"Protocolo de teste para validação da funcionalidade de edição",goals:{calories:1800,protein:140,carbs:150,fat:60,water:3e3},weeklyMeals:{monday:[{id:"meal-1",name:"Café da Manhã",time:"07:00",foods:[{id:"food-1",name:"Aveia",quantity:50,unit:"g",calories:190,protein:6.9,carbs:32.8,fat:3.4}],completed:!1}],tuesday:[],wednesday:[],thursday:[],friday:[],saturday:[],sunday:[]},supplements:[{name:"Whey Protein",dosage:"30g",supplement_time:"Pós-treino",time:"16:00",notes:"Misturar com água"}]}}function M(){return{id:"mock-workout-protocol-123",name:"Protocolo de Hipertrofia Teste",type:"workout",objective:"Ganho de massa muscular",startDate:new Date().toISOString().split("T")[0],notes:"Protocolo de teste para validação da funcionalidade de edição",frequency:4,split:"A-B-C-D",workouts:[{name:"Treino A - Peito e Tríceps",exercises:[{exercise:{id:1,name:"Supino Reto",muscle_group:"Peito",equipment:"Barra"},sets:4,reps:12,rpe:8,restTime:90,notes:"Controlar a descida"}]},{name:"Treino B - Costas e Bíceps",exercises:[]}]}}function k(a){return new Promise(s=>{console.log("🧪 Simulando salvamento de protocolo:",a),setTimeout(()=>{const t=`snapfit_test_protocol_${a.type}_${a.id}`;localStorage.setItem(t,JSON.stringify({...a,lastUpdated:new Date().toISOString(),testMode:!0})),console.log("✅ Protocolo salvo com sucesso no teste"),s(!0)},1e3)})}function j(a,s){try{const t=`snapfit_test_protocol_${a}_${s}`,r=localStorage.getItem(t);if(r){const i=JSON.parse(r);return console.log("📖 Protocolo de teste carregado:",i),i}return null}catch(t){return console.error("❌ Erro ao carregar protocolo de teste:",t),null}}function G(){Object.keys(localStorage).filter(s=>s.startsWith("snapfit_test_protocol_")).forEach(s=>localStorage.removeItem(s)),console.log("🧹 Protocolos de teste limpos")}function Z(a){var t,r,i,n;const s=[];return(t=a.name)!=null&&t.trim()||s.push("Nome é obrigatório"),(r=a.objective)!=null&&r.trim()||s.push("Objetivo é obrigatório"),(!((i=a.goals)!=null&&i.calories)||a.goals.calories<=0)&&s.push("Meta de calorias deve ser maior que zero"),(!((n=a.goals)!=null&&n.protein)||a.goals.protein<=0)&&s.push("Meta de proteína deve ser maior que zero"),{isValid:s.length===0,errors:s}}function K(a){var t,r;const s=[];return(t=a.name)!=null&&t.trim()||s.push("Nome é obrigatório"),(r=a.objective)!=null&&r.trim()||s.push("Objetivo é obrigatório"),(!a.frequency||a.frequency<=0)&&s.push("Frequência deve ser maior que zero"),(!a.workouts||a.workouts.length===0)&&s.push("Pelo menos um treino deve ser adicionado"),{isValid:s.length===0,errors:s}}async function Q(){const a=[];let s=!0;try{a.push("🧪 Iniciando testes de edição de protocolos...");const t=C(),r=Z(t);r.isValid?a.push("✅ Protocolo de dieta mock criado e validado"):(a.push(`❌ Protocolo de dieta inválido: ${r.errors.join(", ")}`),s=!1);const i=M(),n=K(i);n.isValid?a.push("✅ Protocolo de treino mock criado e validado"):(a.push(`❌ Protocolo de treino inválido: ${n.errors.join(", ")}`),s=!1);const f=await k(t),m=await k(i);f&&m?a.push("✅ Simulação de salvamento bem-sucedida"):(a.push("❌ Falha na simulação de salvamento"),s=!1);const u=j("diet",t.id),l=j("workout",i.id);u&&l?a.push("✅ Protocolos carregados com sucesso"):(a.push("❌ Falha ao carregar protocolos"),s=!1),a.push(s?"🎉 Todos os testes passaram!":"⚠️ Alguns testes falharam")}catch(t){a.push(`❌ Erro durante os testes: ${t}`),s=!1}return{success:s,results:a}}function Y(){const[a,s]=c.useState(!1),[t,r]=c.useState([]),[i,n]=c.useState(null),f=async()=>{s(!0),r([]),n(null);try{const{success:o,results:d}=await Q();r(d),n(o),o?h.success("Todos os testes passaram!",{position:"bottom-right"}):h.error("Alguns testes falharam",{position:"bottom-right"})}catch(o){r([`❌ Erro ao executar testes: ${o}`]),n(!1),h.error("Erro ao executar testes",{position:"bottom-right"})}finally{s(!1)}},m=()=>{G(),r([]),n(null),h.info("Dados de teste limpos",{position:"bottom-right"})},u=()=>{try{const o=C(),d=M();localStorage.setItem("snapfit_test_diet_protocol",JSON.stringify(o)),localStorage.setItem("snapfit_test_workout_protocol",JSON.stringify(d)),h.success("Dados de teste criados!",{position:"bottom-right"}),r(["📝 Dados de teste criados:",`🥗 Protocolo de Dieta: ${o.name}`,`💪 Protocolo de Treino: ${d.name}`,"","💡 Você pode agora testar a edição navegando para:","• /dashboard/diet/edit-protocol/mock-diet-protocol-123","• /dashboard/workout/edit-protocol/mock-workout-protocol-123"])}catch{h.error("Erro ao criar dados de teste",{position:"bottom-right"})}},l=()=>{const o=j("diet","mock-diet-protocol-123"),d=j("workout","mock-workout-protocol-123"),b=["🔍 Verificando dados existentes:"];o?b.push(`✅ Protocolo de Dieta encontrado: ${o.name}`):b.push("❌ Protocolo de Dieta não encontrado"),d?b.push(`✅ Protocolo de Treino encontrado: ${d.name}`):b.push("❌ Protocolo de Treino não encontrado"),r(b)};return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-6 border border-snapfit-green/20 hidden",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(w,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Testador de Edição de Protocolos"})]}),e.jsxs("div",{className:"space-y-4 mb-6",children:[e.jsx("p",{className:"text-gray-400 text-sm",children:"Use este painel para testar as funcionalidades de edição de protocolos de dieta e treino."}),e.jsxs("div",{className:"flex flex-wrap gap-3",children:[e.jsx("button",{onClick:f,disabled:a,className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50",children:a?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Executando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(w,{className:"w-4 h-4"}),"Executar Testes"]})}),e.jsxs("button",{onClick:u,className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[e.jsx(L,{className:"w-4 h-4"}),"Criar Dados de Teste"]}),e.jsxs("button",{onClick:l,className:"flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:[e.jsx(A,{className:"w-4 h-4"}),"Verificar Dados"]}),e.jsxs("button",{onClick:m,className:"flex items-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors",children:[e.jsx(H,{className:"w-4 h-4"}),"Limpar Testes"]})]})]}),t.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-3",children:[i===!0&&e.jsx(F,{className:"w-5 h-5 text-green-400"}),i===!1&&e.jsx(I,{className:"w-5 h-5 text-red-400"}),e.jsx("h3",{className:"text-lg font-medium text-white",children:i===null?"Informações":i?"Testes Aprovados":"Testes Falharam"})]}),e.jsx("div",{className:"space-y-1",children:t.map((o,d)=>e.jsxs("div",{className:"text-sm font-mono",children:[o.startsWith("✅")&&e.jsx("span",{className:"text-green-400",children:o}),o.startsWith("❌")&&e.jsx("span",{className:"text-red-400",children:o}),o.startsWith("⚠️")&&e.jsx("span",{className:"text-yellow-400",children:o}),o.startsWith("🧪")&&e.jsx("span",{className:"text-blue-400",children:o}),o.startsWith("🎉")&&e.jsx("span",{className:"text-green-400",children:o}),o.startsWith("📝")&&e.jsx("span",{className:"text-purple-400",children:o}),o.startsWith("🥗")&&e.jsx("span",{className:"text-green-300",children:o}),o.startsWith("💪")&&e.jsx("span",{className:"text-orange-400",children:o}),o.startsWith("💡")&&e.jsx("span",{className:"text-yellow-300",children:o}),o.startsWith("🔍")&&e.jsx("span",{className:"text-cyan-400",children:o}),o.startsWith("•")&&e.jsx("span",{className:"text-gray-300 ml-4",children:o}),!o.match(/^[✅❌⚠️🧪🎉📝🥗💪💡🔍•]/)&&e.jsx("span",{className:"text-gray-300",children:o})]},d))})]}),e.jsxs("div",{className:"mt-6 p-4 bg-amber-500/10 rounded-lg border border-amber-500/20",children:[e.jsx("h4",{className:"text-sm font-medium text-amber-400 mb-2",children:"Como testar:"}),e.jsxs("ol",{className:"text-xs text-gray-400 space-y-1 list-decimal list-inside",children:[e.jsx("li",{children:'Clique em "Criar Dados de Teste" para gerar protocolos mock'}),e.jsx("li",{children:"Navegue para as URLs de edição mostradas nos resultados"}),e.jsx("li",{children:"Teste a edição dos protocolos (modificar campos, adicionar exercícios/alimentos)"}),e.jsx("li",{children:"Salve as alterações e verifique se funcionam corretamente"}),e.jsx("li",{children:'Execute "Executar Testes" para validar as funcionalidades automaticamente'})]})]})]})}function ee({isOpen:a,onClose:s,onConfirm:t}){const[r,i]=c.useState(!1),[n,f]=c.useState("");if(c.useEffect(()=>(a?(document.body.style.overflow="hidden",document.body.style.paddingRight="0px",document.body.classList.add("modal-open")):(document.body.style.overflow="",document.body.style.paddingRight="",document.body.classList.remove("modal-open")),()=>{document.body.style.overflow="",document.body.style.paddingRight="",document.body.classList.remove("modal-open")}),[a]),!a)return null;const m=async()=>{if(n.toLowerCase()==="excluir")try{i(!0),await t(),s()}catch(o){console.error("Error deleting account:",o)}finally{i(!1)}},u=n.toLowerCase()==="excluir",l=o=>{o.target===o.currentTarget&&!r&&s()};return e.jsx(R,{children:e.jsx("div",{className:"critical-modal",onClick:l,children:e.jsxs("div",{className:"critical-modal-content bg-snapfit-gray rounded-xl shadow-2xl max-w-md w-full border-2 border-red-500/40 shadow-red-500/20",onClick:o=>o.stopPropagation(),children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30 bg-gradient-to-r from-red-600/10 to-red-700/10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-3 bg-red-500/20 rounded-lg border border-red-500/30",children:e.jsx(y,{className:"w-6 h-6 text-red-300"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold text-red-300",children:"⚠️ Excluir Conta"}),e.jsx("p",{className:"text-sm text-red-400/80",children:"Ação crítica do sistema"})]})]}),e.jsx("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white hover:bg-red-500/20 rounded-lg transition-colors border border-transparent hover:border-red-500/30",disabled:r,children:e.jsx(W,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"p-6 space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-gray-300",children:"Tem certeza que deseja excluir sua conta? Esta ação irá:"}),e.jsxs("ul",{className:"space-y-2 text-sm text-gray-400 ml-4",children:[e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-red-400 mt-1",children:"•"}),e.jsx("span",{children:"Marcar sua conta para exclusão temporária"})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-yellow-400 mt-1",children:"•"}),e.jsxs("span",{children:["Você terá ",e.jsx("strong",{className:"text-yellow-400",children:"30 dias"})," para recuperar sua conta fazendo login novamente"]})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-red-400 mt-1",children:"•"}),e.jsxs("span",{children:["Após 30 dias, a exclusão se tornará ",e.jsx("strong",{className:"text-red-400",children:"permanente"})]})]}),e.jsxs("li",{className:"flex items-start gap-2",children:[e.jsx("span",{className:"text-gray-400 mt-1",children:"•"}),e.jsx("span",{children:"Todos os seus dados serão preservados durante o período de recuperação"})]})]})]}),e.jsx("div",{className:"bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border-2 border-yellow-500/30 rounded-lg p-4 shadow-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"p-1.5 bg-yellow-500/20 rounded-lg",children:e.jsx(y,{className:"w-5 h-5 text-yellow-300 flex-shrink-0"})}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"text-yellow-300 font-semibold mb-2 flex items-center gap-2",children:"🚨 Período de Recuperação de 30 Dias"}),e.jsxs("p",{className:"text-yellow-200 leading-relaxed",children:["Durante os 30 dias de recuperação, você pode restaurar sua conta simplesmente fazendo login. Após esse período, todos os seus dados serão ",e.jsx("span",{className:"font-semibold text-red-300",children:"permanentemente excluídos"}),"."]})]})]})}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300",children:["Para confirmar esta ação ",e.jsx("span",{className:"text-red-400 font-bold",children:"IRREVERSÍVEL"}),", digite ",e.jsx("span",{className:"text-red-300 font-bold bg-red-500/20 px-2 py-1 rounded",children:'"EXCLUIR"'})," abaixo:"]}),e.jsx("input",{type:"text",value:n,onChange:o=>f(o.target.value),placeholder:"Digite EXCLUIR para confirmar",className:"w-full px-4 py-3 bg-snapfit-dark-gray border-2 border-red-500/30 rounded-lg text-white placeholder-red-400/60 focus:outline-none focus:border-red-400 focus:ring-2 focus:ring-red-500/20 transition-all",disabled:r}),n&&n.toLowerCase()!=="excluir"&&e.jsx("p",{className:"text-red-400 text-xs",children:'⚠️ Digite exatamente "EXCLUIR" para continuar'})]})]}),e.jsxs("div",{className:"flex items-center justify-end gap-3 p-6 border-t border-red-500/30 bg-gradient-to-r from-red-600/5 to-red-700/5",children:[e.jsx("button",{onClick:s,className:"px-6 py-2.5 text-gray-300 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors border border-gray-600 hover:border-gray-500",disabled:r,children:"Cancelar"}),e.jsx("button",{onClick:m,disabled:!u||r,className:"flex items-center gap-2 px-6 py-2.5 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg hover:from-red-700 hover:to-red-800 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-red-500/25 border border-red-500/50",children:r?e.jsxs(e.Fragment,{children:[e.jsx(q,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{children:"Excluindo..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(S,{className:"w-4 h-4"}),e.jsx("span",{children:"⚠️ Excluir Conta"})]})})]})]})})})}function se(){const[a,s]=c.useState(!1),{showNotification:t}=_(),r=async()=>{try{const i=await B();i.success?t({type:"success",message:i.message}):t({type:"error",message:i.message})}catch(i){console.error("Error deleting account:",i),t({type:"error",message:"Erro inesperado ao excluir conta"})}};return e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>s(!0),className:`flex items-center justify-center gap-3 w-full px-6 py-4
                  text-red-300 bg-gradient-to-r from-red-600/20 to-red-700/20
                  rounded-xl hover:from-red-600/30 hover:to-red-700/30
                  transition-all duration-300 border-2 border-red-500/40
                  hover:border-red-500/60 hover:shadow-lg hover:shadow-red-500/20
                  group relative overflow-hidden`,children:[e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"h-full w-full bg-gradient-to-r from-transparent via-red-500/20 to-transparent transform -skew-x-12"})}),e.jsxs("div",{className:"relative flex items-center gap-3",children:[e.jsx("div",{className:"p-1.5 bg-red-500/20 rounded-lg group-hover:bg-red-500/30 transition-colors",children:e.jsx(S,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-300"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("div",{className:"font-semibold text-red-300 group-hover:text-red-200 transition-colors",children:"Excluir Conta"}),e.jsx("div",{className:"text-xs text-red-400/80 group-hover:text-red-300/80 transition-colors",children:"Ação permanente após 30 dias"})]})]})]}),e.jsx(ee,{isOpen:a,onClose:()=>s(!1),onConfirm:r})]})}const g=class g{static getInstance(){return g.instance||(g.instance=new g),g.instance}async getUserProfile(){if(p.isMockModeEnabled())return this.getMockProfile();try{const s=await x.get("users/me");return(s==null?void 0:s.data)||this.getMockProfile()}catch(s){return console.warn("Failed to load user profile, falling back to mock data:",s),this.getMockProfile()}}async updateUserProfile(s){if(p.isMockModeEnabled())return console.log("Mock mode: Updating user profile",s),{success:!0};try{const t=await x.put("users/me",s);return(t==null?void 0:t.data)||{success:!0}}catch(t){throw console.error("Failed to update user profile:",t),t}}async getSubscriptions(){if(p.isMockModeEnabled())return this.getMockSubscriptions();try{const s=await x.get("users/subscriptions");return(s==null?void 0:s.data)||this.getMockSubscriptions()}catch(s){return console.warn("Failed to load subscriptions, falling back to mock data:",s),this.getMockSubscriptions()}}async getPaymentHistory(){if(p.isMockModeEnabled())return this.getMockPayments();try{const s=await x.get("users/payments/history");return(s==null?void 0:s.data)||this.getMockPayments()}catch(s){return console.warn("Failed to load payment history, falling back to mock data:",s),this.getMockPayments()}}async cancelSubscription(s){if(p.isMockModeEnabled())return console.log("Mock mode: Cancelling subscription",s),{success:!0};try{const t=await x.post(`users/subscriptions/${s}/cancel`,{});return(t==null?void 0:t.data)||{success:!0}}catch(t){throw console.error("Failed to cancel subscription:",t),t}}async downloadInvoice(s){if(p.isMockModeEnabled())return console.log("Mock mode: Downloading invoice",s),{success:!0,url:"https://example.com/invoice.pdf"};try{const t=await x.get(`users/payments/${s}/invoice`);return(t==null?void 0:t.data)||{success:!0}}catch(t){throw console.error("Failed to download invoice:",t),t}}async updateNotificationPreferences(s){if(p.isMockModeEnabled())return console.log("Mock mode: Updating notification preferences",s),{success:!0};try{const t=await x.put("users/preferences/notifications",s);return(t==null?void 0:t.data)||{success:!0}}catch(t){throw console.error("Failed to update notification preferences:",t),t}}getMockProfile(){return{id:"user-123",name:"João Silva",email:"<EMAIL>",phone:"(11) 99999-9999",photo:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",birthDate:"1990-05-15",gender:"male",height:175,weight:80,activityLevel:"moderate",goals:["muscle_gain","strength"],preferences:{units:"metric",language:"pt",notifications:{meals:!0,workouts:!0,water:!0,achievements:!0}},subscription:{type:"premium",status:"active",expiresAt:"2024-12-31"}}}getMockSubscriptions(){return[{id:"sub1",type:"app",name:"SnapFit Premium",status:"active",price:29.9,currency:"BRL",billingCycle:"monthly",nextBilling:"2024-04-15",features:["Protocolos ilimitados","Análises avançadas","Suporte prioritário","Integração com wearables"]},{id:"sub2",type:"coach",name:"Acompanhamento com Coach",status:"active",price:150,currency:"BRL",billingCycle:"monthly",nextBilling:"2024-04-20",features:["Protocolo personalizado","Acompanhamento semanal","Ajustes em tempo real","Chat direto com coach"]}]}getMockPayments(){return[{id:"pay1",date:"2024-03-15",amount:29.9,currency:"BRL",description:"SnapFit Premium - Março 2024",status:"completed",method:"credit_card",invoiceUrl:"https://example.com/invoice1.pdf"},{id:"pay2",date:"2024-02-15",amount:29.9,currency:"BRL",description:"SnapFit Premium - Fevereiro 2024",status:"completed",method:"pix",invoiceUrl:"https://example.com/invoice2.pdf"}]}};N(g,"instance");let v=g;const P=v.getInstance();function oe(){const a=V(),{shouldHideIOSOnlyFeatures:s}=O(),[t,r]=c.useState([]),[i,n]=c.useState([]),[f,m]=c.useState(!0),u=async()=>{try{m(!0);const[l,o]=await Promise.all([P.getSubscriptions(),P.getPaymentHistory()]);r(l),n(o)}catch(l){console.error("Error loading settings data:",l),h.error("Erro ao carregar configurações",{position:"bottom-right"})}finally{m(!1)}};return c.useEffect(()=>{u()},[]),e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsxs("div",{className:"space-y-4 sm:space-y-6 max-w-4xl mx-auto p-4",children:[e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-white flex items-center gap-2 animate-fade-in",children:[e.jsx($,{className:"w-8 h-8 text-snapfit-green"}),"Configurações"]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-blue-500/10 rounded-lg",children:e.jsx(U,{className:"w-5 h-5 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-white",children:"Fontes Científicas"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Bases científicas das recomendações do app"})]})]}),e.jsx("p",{className:"text-gray-300 text-sm mb-4 leading-relaxed",children:"Todas as recomendações nutricionais e de saúde do SnapFit são baseadas em diretrizes oficiais de instituições reconhecidas e pesquisas científicas revisadas por pares."}),e.jsxs("button",{onClick:()=>a("/dashboard/scientific-sources"),className:"flex items-center gap-2 bg-blue-500/10 hover:bg-blue-500/20 border border-blue-500/20 hover:border-blue-500/40 text-blue-400 px-4 py-2.5 rounded-lg transition-all duration-200 font-medium",children:[e.jsx(z,{className:"w-4 h-4"}),"Ver fontes e referências científicas"]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-snapfit-green/10 rounded-lg",children:e.jsx(J,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-white",children:"Sobre o SnapFit"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Informações importantes sobre o aplicativo"})]})]}),e.jsx("div",{className:"space-y-4",children:e.jsx("div",{className:"bg-yellow-500/5 border border-yellow-500/20 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(y,{className:"w-5 h-5 text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-yellow-400 font-medium mb-2 text-sm",children:"Aviso Importante"}),e.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:"As recomendações nutricionais e de exercício físico aqui apresentadas seguem diretrizes reconhecidas internacionalmente (OMS, ACSM, NSCA, FDA, USDA, Ministério da Saúde). Este app não substitui acompanhamento médico, nutricional ou de educação física profissional. Consulte sempre um profissional de saúde qualificado para orientações personalizadas sobre sua saúde, nutrição e atividade física."})]})]})})})]}),!1,!1,!1,window.location.hostname==="localhost"&&e.jsx(Y,{}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-red-500/10 rounded-lg",children:e.jsx("svg",{className:"w-5 h-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-base sm:text-lg font-semibold text-red-400",children:"Zona de Perigo"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Ações irreversíveis que afetam sua conta"})]})]}),e.jsx("div",{className:"bg-red-500/5 border border-red-500/20 rounded-lg p-4 mb-4",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("svg",{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"text-red-400 font-medium mb-1",children:"Atenção:"}),e.jsx("p",{className:"text-red-300",children:"A exclusão da conta é uma ação séria. Você terá 30 dias para recuperar sua conta antes que ela seja permanentemente excluída."})]})]})}),e.jsx(se,{})]})]})})}export{oe as SettingsPage};
