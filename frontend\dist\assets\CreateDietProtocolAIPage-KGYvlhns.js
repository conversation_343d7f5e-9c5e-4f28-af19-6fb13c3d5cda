import{t as p,bj as h,j as e,aD as f,aO as g,B as d,aT as j,bk as u,b,y as v}from"./index-ClF00oko.js";function y(){const r=p(),t=h(),x=async i=>{try{console.log("🚀 Enviando dados para IA:",i);const s=await t.mutateAsync(i);if(s!=null&&s.id){let o=0;const m=10,c=setInterval(async()=>{var l;o++;try{const a=await b.get(`users/protocols/diet/${s.id}`);(l=a==null?void 0:a.data)!=null&&l.has_protocol?(clearInterval(c),r("/dashboard/diet")):o>=m&&(clearInterval(c),v.error("Protocolo não encontrado após geração. Tente novamente."))}catch(a){console.error("Error checking protocol availability:",a)}},5e3)}else throw new Error("Protocolo de dieta não foi criado corretamente")}catch(s){console.error("Error generating AI diet protocol:",s)}},n=()=>{r("/dashboard/diet")};return t.isPending?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(f,{type:"diet",message:"Gerando seu protocolo de dieta personalizado..."})}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-between p-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:n,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(g,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(d,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-lg font-medium text-white",children:"Gerar Protocolo de Dieta com IA"}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsx(j,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:"Protocolo nutricional personalizado baseado em seus dados"})]})]})]})]})})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-2xl mx-auto space-y-6",children:[e.jsx(u,{onGenerate:x,onCancel:n}),e.jsx("div",{className:"bg-gradient-to-r from-snapfit-green/10 to-blue-500/10 rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-snapfit-green/20 to-blue-500/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0 mt-0.5",children:e.jsx(d,{className:"w-4 h-4 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white mb-2",children:"Como funciona a IA Nutricional do SnapFit"}),e.jsxs("div",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("p",{children:"• Analisa seus dados corporais, objetivos e preferências alimentares"}),e.jsx("p",{children:"• Calcula necessidades calóricas e distribuição de macronutrientes"}),e.jsx("p",{children:"• Considera restrições alimentares e alergias"}),e.jsx("p",{children:"• Gera cardápio personalizado com alimentos que você gosta"}),e.jsx("p",{children:"• Ajusta horários das refeições ao seu estilo de vida"}),e.jsx("p",{children:"• Inclui suplementação quando necessário"})]})]})]})})]})})]})}export{y as CreateDietProtocolAIPage};
