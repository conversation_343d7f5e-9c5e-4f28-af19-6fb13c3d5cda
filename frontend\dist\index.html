<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0" /> -->
    <meta name="viewport" content="width=device-width, user-scalable=no" />
    <meta name="theme-color" content="#00ff88" />
    <meta name="description" content="SnapFit - Sua plataforma completa de fitness e nutrição" />

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="/manifest.json" />
    <link rel="apple-touch-icon" href="/icon-192x192.svg" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="SnapFit" />

    <title>SnapFit - Fitness & Nutrição</title>
    <script type="module" crossorigin src="/assets/index-BSexrr-f.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-Brwen-e9.css">
  </head>
  <body>
    <div id="root"></div>


    <!-- Service Worker Registration -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
              console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
