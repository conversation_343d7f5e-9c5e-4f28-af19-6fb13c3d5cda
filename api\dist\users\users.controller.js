"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UsersController", {
    enumerable: true,
    get: function() {
        return UsersController;
    }
});
const _common = require("@nestjs/common");
const _platformexpress = require("@nestjs/platform-express");
const _jwtauthguard = require("../auth/jwt-auth.guard");
const _createclientdto = require("../dto/create-client.dto");
const _usersservice = require("./users.service");
const _dailywaterdto = require("./dto/daily-water.dto");
const _dailyworkoutsactivitiesdto = require("./dto/daily-workouts-activities.dto");
const _createprotocolworkoutdto = require("./dto/create-protocol-workout.dto");
const _createprotocoldietdto = require("./dto/create-protocol-diet.dto");
const _updateprotocolworkoutdto = require("./dto/update-protocol-workout.dto");
const _dateutil = require("../common/utils/date.util");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
const dayjs = require('dayjs');
let UsersController = class UsersController {
    getTime() {
        const tz = 'America/Sao_Paulo';
        const startOfDay = (0, _dateutil.convertToUTC)(dayjs().startOf('day').toDate(), tz);
        const endOfDay = (0, _dateutil.convertToUTC)(dayjs().endOf('day').toDate(), tz);
        return {
            startOfDay,
            endOfDay
        };
    }
    getAllUsers() {
        return [
            {
                "id": 1,
                "name": "Alice Johnson",
                "email": "<EMAIL>",
                "role": "Admin",
                "photo": "https://randomuser.me/api/portraits/women/1.jpg",
                "code": "f7k3s",
                "createdAt": "2022-05-14T10:22:31.000Z"
            },
            {
                "id": 2,
                "name": "Bob Smith",
                "email": "<EMAIL>",
                "role": "User",
                "photo": "https://randomuser.me/api/portraits/men/1.jpg",
                "code": "g2t7a",
                "createdAt": "2023-08-01T06:12:45.000Z"
            },
            {
                "id": 3,
                "name": "Charlie Brown",
                "email": "<EMAIL>",
                "role": "Editor",
                "photo": "https://randomuser.me/api/portraits/men/2.jpg",
                "code": "j8w4n",
                "createdAt": "2021-12-30T12:00:00.000Z"
            },
            {
                "id": 4,
                "name": "Daisy Miller",
                "email": "<EMAIL>",
                "role": "Admin",
                "photo": "https://randomuser.me/api/portraits/women/2.jpg",
                "code": "h5q2j",
                "createdAt": "2023-01-15T15:30:10.000Z"
            },
            {
                "id": 5,
                "name": "Edward Wilson",
                "email": "<EMAIL>",
                "role": "User",
                "photo": "https://randomuser.me/api/portraits/men/3.jpg",
                "code": "x9r3z",
                "createdAt": "2022-09-22T19:45:25.000Z"
            },
            {
                "id": 6,
                "name": "Fiona Green",
                "email": "<EMAIL>",
                "photo": "https://randomuser.me/api/portraits/women/3.jpg",
                "code": "n6k8d",
                "createdAt": "2020-11-14T08:11:31.000Z"
            },
            {
                "id": 7,
                "name": "George Black",
                "email": "<EMAIL>",
                "role": "Admin",
                "photo": "https://randomuser.me/api/portraits/men/4.jpg",
                "code": "v4w7p",
                "createdAt": "2021-03-10T13:22:45.000Z"
            },
            {
                "id": 8,
                "name": "Hannah White",
                "email": "<EMAIL>",
                "role": "User",
                "photo": "https://randomuser.me/api/portraits/women/4.jpg",
                "code": "b1j2k",
                "createdAt": "2022-02-28T11:15:50.000Z"
            },
            {
                "id": 9,
                "name": "Ian Black",
                "email": "<EMAIL>",
                "role": "Editor",
                "photo": "https://randomuser.me/api/portraits/men/5.jpg",
                "code": "x3l4y",
                "createdAt": "2021-10-09T19:30:12.000Z"
            },
            {
                "id": 10,
                "name": "Jane Doe",
                "email": "<EMAIL>",
                "role": "User",
                "photo": "https://randomuser.me/api/portraits/women/5.jpg",
                "code": "m0n6v",
                "createdAt": "2023-07-01T16:45:30.000Z"
            }
        ];
    }
    async create(role, createClientDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        if (role == 'admin') {
            throw new Error('Admin role is not allowed');
        }
        const user = await this.usersService.create(createClientDto, userId, role);
    }
    async getMe(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const user = await this.usersService.getMe(userId);
        return user;
    }
    async getGoals() {
        return this.usersService.getGoals();
    }
    async updateMe(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const user = await this.usersService.updateMe(userId, body);
        return user;
    }
    async deleteMe(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.softDeleteAccount(userId);
    }
    // User photo
    async updateMePhoto(req, file) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        if (!file) {
            throw new Error('Photo file is required');
        }
        const result = await this.usersService.updateMePhoto(userId, file);
        return result;
    }
    // Resumo Nutricional (Aba: Diário)
    async getDailyNutritionalSummary(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const nutritional_summary = await this.usersService.getDailyNutritionalSummary(query, userId);
        return nutritional_summary;
    }
    async getDaily(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const daily = await this.usersService.getDaily(query, userId);
        return daily;
    }
    async getAssessments(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const assessments = await this.usersService.getAssessments(userId);
        return assessments;
    }
    // Daily activity post
    async postDailyWater(req, dailyWaterDto) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const daily = await this.usersService.postDailyWater(userId, dailyWaterDto);
        return daily;
    }
    getDailyWorkoutsActivities() {
        return this.usersService.getDailyWorkoutsActivities();
    }
    // Daily Workouts Activities
    getDailyWorkoutsActivitiesByDate(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getDailyWorkoutsActivitiesByDate(userId, query);
    }
    postDailyWorkoutsActivities(dailyWorkoutsActivitiesDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.postDailyWorkoutsActivities(userId, dailyWorkoutsActivitiesDto);
    }
    async postProtocolsWorkout(createProtocolWorkoutDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocol = await this.usersService.postProtocolsWorkout(userId, createProtocolWorkoutDto);
        return protocol;
    }
    // AI
    async postProtocolsWorkoutAi(userInfo, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocol = await this.usersService.postProtocolsWorkoutAi(userId, userInfo);
        return protocol;
    }
    async getActiveProtocolsWorkouts(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocols = await this.usersService.getActiveProtocolsWorkouts(userId);
        return protocols;
    }
    async getProtocolWorkoutById(id, req) {
        console.log('🔍 Controller: req.user:', req.user);
        console.log('🔍 Controller: req.user.userId:', req.user?.userId);
        console.log('🔍 Controller: Protocol ID param:', id);
        const userId = req.user.userId;
        if (!userId) {
            console.log('❌ Controller: User ID não encontrado no token');
            throw new Error('User ID is required');
        }
        const protocolId = parseInt(id, 10);
        if (isNaN(protocolId)) {
            console.log('❌ Controller: Protocol ID inválido:', id);
            throw new Error('Invalid protocol ID');
        }
        console.log(`✅ Controller: Buscando protocolo ${protocolId} para usuário ${userId}`);
        const protocol = await this.usersService.getProtocolWorkoutById(protocolId, userId);
        return protocol;
    }
    async updateProtocolWorkout(id, updateData, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocolId = parseInt(id, 10);
        if (isNaN(protocolId)) {
            throw new Error('Invalid protocol ID');
        }
        const protocol = await this.usersService.updateProtocolWorkout(protocolId, updateData, userId);
        return protocol;
    }
    async postProtocolsDiet(createProtocolDietDto, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocol = await this.usersService.postProtocolsDiet(userId, createProtocolDietDto);
        return protocol;
    }
    // AI
    async postProtocolsDietAI(userInfo, req) {
        const userId = req.user.userId;
        const protocol = await this.usersService.postProtocolsDietAi(userId, userInfo);
        return protocol;
    }
    // get active protocol of diet
    async getActiveProtocolsDiet(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocols = await this.usersService.getActiveProtocolsDiet(userId);
        return protocols;
    }
    // HISTÓRICO DE PROTOCOLOS DE DIETA (DEVE VIR ANTES DA ROTA GENÉRICA :id)
    async getProtocolsDietHistory(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        try {
            console.log(`📚 Endpoint getProtocolsDietHistory chamado para usuário ${userId}`);
            console.log(`📋 Query params recebidos:`, query);
            const options = {
                page: query.page ? parseInt(query.page) : 1,
                limit: query.limit ? parseInt(query.limit) : 10,
                status: query.status || 'all',
                startDate: query.startDate,
                endDate: query.endDate,
                type: query.type
            };
            // Validar parâmetros
            if (options.page < 1) options.page = 1;
            if (options.limit < 1 || options.limit > 100) options.limit = 10;
            if (![
                'active',
                'finished',
                'all'
            ].includes(options.status)) {
                options.status = 'all';
            }
            console.log(`📊 Opções processadas:`, options);
            const result = await this.usersService.getProtocolsDietHistory(userId, options);
            console.log(`✅ Histórico recuperado com sucesso: ${result.protocols?.length || 0} protocolos`);
            return {
                status: 'success',
                message: 'Histórico de protocolos de dieta recuperado com sucesso',
                data: result
            };
        } catch (error) {
            console.error(`❌ Erro no endpoint getProtocolsDietHistory para usuário ${userId}:`, error);
            console.error(`❌ Query params que causaram erro:`, query);
            throw error;
        }
    }
    // get specific diet protocol by id (DEVE VIR DEPOIS DAS ROTAS ESPECÍFICAS)
    async getProtocolDietById(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`🔍 Recebido ID do protocolo: "${id}" (tipo: ${typeof id})`);
        // Validar se o ID foi fornecido
        if (!id || id.trim() === '') {
            console.error('❌ ID do protocolo está vazio ou nulo');
            throw new Error('Protocol ID is required');
        }
        // Tentar converter para número
        const protocolId = parseInt(id.trim(), 10);
        if (isNaN(protocolId) || protocolId <= 0) {
            console.error(`❌ ID do protocolo inválido: "${id}" -> ${protocolId}`);
            throw new Error(`Invalid protocol ID: "${id}". Must be a positive integer.`);
        }
        console.log(`🔍 Buscando protocolo de dieta ${protocolId} para usuário ${userId}`);
        const protocol = await this.usersService.getProtocolDietById(protocolId, userId);
        return {
            status: 'success',
            message: 'Protocolo de dieta recuperado com sucesso',
            data: protocol
        };
    }
    // get active meals of day week
    getActiveMealsOfDayWeek(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getActiveMealsOfDayWeek(userId, query);
    }
    getProtocolsDietMealOfDayWeek(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getActiveProtocolsDietMealsDayWeek(userId);
    }
    async deleteProtocolDiet(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocolId = parseInt(id, 10);
        if (isNaN(protocolId)) {
            throw new Error('Invalid protocol ID');
        }
        const protocol = await this.usersService.deleteProtocolDiet(protocolId, userId);
        return protocol;
    }
    async deleteProtocolWorkout(id, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const protocol = await this.usersService.deleteProtocolWorkout(id, userId);
        return protocol;
    }
    // Check protocol workout
    // Check protocol diet
    async checkProtocolDiet(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.checkProtocolDiet(userId, body);
    }
    // Uncheck protocol diet
    async uncheckProtocolDiet(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.uncheckProtocolDiet(userId, body);
    }
    // ENDPOINT TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS
    async fixOrphanProtocols(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`🔧 Endpoint fixOrphanProtocols chamado para usuário ${userId}`);
        const result = await this.usersService.fixOrphanProtocols(userId);
        return {
            status: 'success',
            message: 'Correção de protocolos órfãos executada',
            data: result
        };
    }
    // FINALIZAR PROTOCOLO DE DIETA
    async finishProtocolDiet(protocolId, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`🏁 Endpoint finishProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);
        const result = await this.usersService.finishProtocolDiet(userId, parseInt(protocolId));
        return {
            status: 'success',
            message: 'Protocolo finalizado com sucesso',
            data: result
        };
    }
    // DUPLICAR PROTOCOLO DE DIETA
    async duplicateProtocolDiet(protocolId, body, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`📋 Endpoint duplicateProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);
        const result = await this.usersService.duplicateProtocolDiet(userId, parseInt(protocolId), body.startDate);
        return {
            status: 'success',
            message: 'Protocolo duplicado com sucesso',
            data: result
        };
    }
    // FINALIZAR PROTOCOLO DE TREINO
    async finishProtocolWorkout(protocolId, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`🏁 Endpoint finishProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);
        const result = await this.usersService.finishProtocolWorkout(userId, parseInt(protocolId));
        return {
            status: 'success',
            message: 'Protocolo de treino finalizado com sucesso',
            data: result
        };
    }
    // DUPLICAR PROTOCOLO DE TREINO
    async duplicateProtocolWorkout(protocolId, body, req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`📋 Endpoint duplicateProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);
        const result = await this.usersService.duplicateProtocolWorkout(userId, parseInt(protocolId), body.startDate);
        return {
            status: 'success',
            message: 'Protocolo de treino duplicado com sucesso',
            data: result
        };
    }
    // DEBUG: VERIFICAR DADOS DE PROTOCOLOS NO BANCO
    async debugProtocolsRaw(req) {
        const userId = req.user.userId;
        console.log(`🔍 DEBUG: Verificando protocolos raw para usuário ${userId}`);
        const result = await this.usersService.debugProtocolsRaw(userId);
        return {
            status: 'debug',
            message: 'Dados raw de protocolos',
            data: result
        };
    }
    // DEBUG: VERIFICAR DADOS DE TREINOS COMPLETADOS NO BANCO
    async debugWorkoutsRaw(req) {
        const userId = req.user.userId;
        console.log(`🔍 DEBUG: Verificando treinos completados raw para usuário ${userId}`);
        const result = await this.usersService.debugWorkoutsRaw(userId);
        return {
            status: 'debug',
            message: 'Dados raw de treinos completados',
            data: result
        };
    }
    // HISTÓRICO DE PROTOCOLOS DE TREINO
    async getProtocolsWorkoutHistory(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const options = {
            page: query.page ? parseInt(query.page) : 1,
            limit: query.limit ? parseInt(query.limit) : 10,
            status: query.status || 'all',
            startDate: query.startDate,
            endDate: query.endDate,
            type: query.type
        };
        console.log(`🏋️ Endpoint getProtocolsWorkoutHistory chamado para usuário ${userId}`, options);
        const result = await this.usersService.getProtocolsWorkoutHistory(userId, options);
        return {
            status: 'success',
            message: 'Histórico de protocolos de treino recuperado com sucesso',
            data: result
        };
    }
    // DEBUG: Endpoint para verificar protocolos no banco
    async getProtocolsRaw(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        console.log(`🔍 DEBUG: Buscando protocolos raw para usuário ${userId}`);
        const result = await this.usersService.getProtocolsRawDebug(userId);
        return {
            status: 'success',
            message: 'Dados raw dos protocolos',
            data: result
        };
    }
    // get daily checked meal by date_start date_end
    getDailyCheckedMealk(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getDailyCheckedMeal(userId, query);
    }
    // get exercises from protocol coach active
    getProtocolsWorkoutExercises(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProtocolsWorkoutExercises(userId);
    }
    async postProtocolsWorkoutDaily(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.postProtocolsWorkoutDaily(userId, body);
    }
    async getProtocolsWorkoutDaily(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProtocolsWorkoutDaily(userId, query);
    }
    // Dias batendo a meta (Sequência) (Aba: Progresso)
    async getProgressNutritionalDaysGoalSequence(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressNutritionalDaysGoalSequence(userId);
    }
    // Registrar avaliação física
    async postProgressEvaluations(req, body, files) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        const front = files.find((file)=>file.fieldname === 'front');
        const back = files.find((file)=>file.fieldname === 'back');
        const side = files.find((file)=>file.fieldname === 'side');
        const photos = {
            front,
            back,
            side
        };
        return this.usersService.postProgressEvaluations(userId, body, photos);
    }
    // Registrar mediddas
    async getProgressEvaluationsMeasurements(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.postProgressEvaluationsMeasurements(userId, body);
    }
    // Histórico de avaliações
    async getProgressEvaluations(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressEvaluations(userId, query);
    }
    // Assiduidade
    async getProgressAttendance(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressAttendance(userId);
    }
    // Progresso Semanal
    async getProgressDietWeekly(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressDietWeekly(userId);
    }
    // Progresso Semanal Consolidado (Treinos, Nutrição, Sono, Água)
    async getWeeklyProgressConsolidated(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getWeeklyProgressConsolidated(userId);
    }
    // Aderência semanal
    async getProgressAdherence(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressAdherence(userId, query);
    }
    // Histórico
    async getProgressDiet(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressDiet(userId);
    }
    // Evolução de Peso e Gordura
    async getProgressWeightFat(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressWeightFat(userId, query);
    }
    // Evolução de Força
    async getProgressStrength(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressStrength(userId, query);
    }
    // Análise Nutricional
    async getProgressNutritionalAnalysis(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressNutritionalAnalysis(userId, query);
    }
    // Saldo Calórico
    async getProgressCaloricBalance(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressCaloricBalance(userId, query);
    }
    // Análise de Treinos progress/chart/workouts
    async getProgressWorkouts(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressWorkouts(userId, query);
    }
    async getProgressWorkoutsAnalysis(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressWorkoutsAnalysis(userId, query);
    }
    // Análise Completa
    async getProgressCompleteAnalysis(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressCompleteAnalysis(userId, query);
    }
    // Endpoint geral de progresso (para compatibilidade com frontend)
    async getProgress(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgress(userId, query);
    }
    // Análise nutricional semanal
    async getAnalyticsNutritionWeekly(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getAnalyticsNutritionWeekly(userId, query);
    }
    // Análise de treinos semanal
    async getAnalyticsWorkoutsWeekly(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getAnalyticsWorkoutsWeekly(userId, query);
    }
    // Evolução de força
    async getStrengthEvolution(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getStrengthEvolution(userId, query);
    }
    // WORKOUT SESSION ENDPOINTS
    // Start workout session
    async startWorkout(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.startWorkout(userId, body);
    }
    // Get workouts by date
    async getWorkoutsByDate(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getWorkoutsByDate(userId, query);
    }
    // Get workout history
    async getWorkoutHistory(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getWorkoutHistory(userId, query);
    }
    // Get workout session details
    async getWorkoutSession(req, sessionId) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getWorkoutSession(userId, sessionId);
    }
    // Complete workout session
    async completeWorkout(req, workoutId, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.completeWorkout(userId, workoutId, body);
    }
    // Distribuição de Volume
    async getProgressVolumeDistribution(req, query) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getProgressVolumeDistribution(userId, query);
    }
    // AI
    async getFoodsSuggestions(req, query, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getFoodsSuggestions(userId, query, body);
    }
    // User Options
    async getUserOptions(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getUserOptions(userId);
    }
    // Affiliates
    async createAffiliate(req, body) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.createAffiliate(body, userId);
    }
    // Plans
    async getAllPlans(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getAllPlans(userId);
    }
    // Check plan stripe
    async subscribePlan(req, planId) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.subscribePlan(userId, Number(planId));
    }
    // Subscriptions
    async getMySubscriptions(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getMySubscriptions(userId);
    }
    async cancelSubscription(req, id) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.cancelSubscription(Number(id), userId);
    }
    async cancelSubscriptionImmediately(req, id) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.cancelSubscriptionImmediately(Number(id), userId);
    }
    // Transactions
    async getMyTransactions(req) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getMyTransactions(userId);
    }
    async getMyTransactionDetails(req, id) {
        const userId = req.user.userId;
        if (!userId) {
            throw new Error('User ID is required');
        }
        return this.usersService.getMyTransactionDetails(Number(id), userId);
    }
    constructor(usersService){
        this.usersService = usersService;
    }
};
_ts_decorate([
    (0, _common.Get)('time'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getTime", null);
_ts_decorate([
    (0, _common.Get)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getAllUsers", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('/:role'),
    _ts_param(0, (0, _common.Param)('role')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        typeof _createclientdto.CreateClientDto === "undefined" ? Object : _createclientdto.CreateClientDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "create", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('me'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getMe", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('goals'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getGoals", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Put)('me'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "updateMe", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Delete)('me'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "deleteMe", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('me/photo'),
    (0, _common.UseInterceptors)((0, _platformexpress.FileInterceptor)('photo')),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.UploadedFile)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        typeof Express === "undefined" || typeof Express.Multer === "undefined" || typeof Express.Multer.File === "undefined" ? Object : Express.Multer.File
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "updateMePhoto", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('diary/nutritional_summary'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getDailyNutritionalSummary", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('daily'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getDaily", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('assessments'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getAssessments", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('daily/water'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        typeof _dailywaterdto.DailyWaterDto === "undefined" ? Object : _dailywaterdto.DailyWaterDto
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postDailyWater", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('daily/list/workouts_activities'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getDailyWorkoutsActivities", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('daily/workouts_activities'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getDailyWorkoutsActivitiesByDate", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('daily/workouts_activities'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _dailyworkoutsactivitiesdto.DailyWorkoutsActivitiesDto === "undefined" ? Object : _dailyworkoutsactivitiesdto.DailyWorkoutsActivitiesDto,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "postDailyWorkoutsActivities", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/workout'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createprotocolworkoutdto.CreateProtocolWorkoutDto === "undefined" ? Object : _createprotocolworkoutdto.CreateProtocolWorkoutDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProtocolsWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/workout/ai'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProtocolsWorkoutAi", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/workouts/active'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getActiveProtocolsWorkouts", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/workout/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolWorkoutById", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Put)('protocols/workout/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _updateprotocolworkoutdto.UpdateProtocolWorkoutDto === "undefined" ? Object : _updateprotocolworkoutdto.UpdateProtocolWorkoutDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "updateProtocolWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _createprotocoldietdto.CreateProtocolDietDto === "undefined" ? Object : _createprotocoldietdto.CreateProtocolDietDto,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProtocolsDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet/ai'),
    _ts_param(0, (0, _common.Body)()),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProtocolsDietAI", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/active'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getActiveProtocolsDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/history'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolsDietHistory", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolDietById", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/meals/active'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getActiveMealsOfDayWeek", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/mealofdayweek'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getProtocolsDietMealOfDayWeek", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Delete)('protocols/diet/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "deleteProtocolDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Delete)('protocols/workout/:id'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Number,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "deleteProtocolWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet/check'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "checkProtocolDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet/uncheck'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "uncheckProtocolDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/fix-orphans'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "fixOrphanProtocols", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet/:id/finish'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "finishProtocolDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/diet/:id/duplicate'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "duplicateProtocolDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/workout/:id/finish'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "finishProtocolWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/workout/:id/duplicate'),
    _ts_param(0, (0, _common.Param)('id')),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "duplicateProtocolWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('debug/protocols/raw'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "debugProtocolsRaw", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('debug/workouts/raw'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "debugWorkoutsRaw", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/workout/history'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolsWorkoutHistory", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('debug/protocols/raw'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolsRaw", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/diet/mealsofdayweekchecked'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getDailyCheckedMealk", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/workout/exercises'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", void 0)
], UsersController.prototype, "getProtocolsWorkoutExercises", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('protocols/workout/daily'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProtocolsWorkoutDaily", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('protocols/workout/daily'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProtocolsWorkoutDaily", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/nutritional/days_goal_sequence'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressNutritionalDaysGoalSequence", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('progress/evaluations'),
    (0, _common.UseInterceptors)((0, _platformexpress.AnyFilesInterceptor)()),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_param(2, (0, _common.UploadedFiles)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object,
        Array
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "postProgressEvaluations", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('progress/evaluations/measurements'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressEvaluationsMeasurements", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/evaluations'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressEvaluations", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/attendance'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressAttendance", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/diet/weekly'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressDietWeekly", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/weekly/consolidated'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getWeeklyProgressConsolidated", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/adherence'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressAdherence", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/diet'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressDiet", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/chart/weight_fat'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressWeightFat", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/chart/strength'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressStrength", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/nutritional_analysis'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressNutritionalAnalysis", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/chart/caloric_balance'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressCaloricBalance", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/chart/workouts'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressWorkouts", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/chart/workouts_analysis'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressWorkoutsAnalysis", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/complete_analysis'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressCompleteAnalysis", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgress", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('analytics/nutrition/weekly'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getAnalyticsNutritionWeekly", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('analytics/workouts/weekly'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getAnalyticsWorkoutsWeekly", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('strength/evolution'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getStrengthEvolution", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('workouts/start'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "startWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('workouts'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getWorkoutsByDate", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('workouts/history'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getWorkoutHistory", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('workouts/sessions/:id'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getWorkoutSession", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('workouts/:id/complete'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_param(2, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "completeWorkout", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('progress/volume_distribution'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getProgressVolumeDistribution", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('ai/foods-suggestions'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Query)()),
    _ts_param(2, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getFoodsSuggestions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('options'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getUserOptions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('affiliate/join'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "createAffiliate", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('plans'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getAllPlans", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('plans/checkout/:planId'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('planId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "subscribePlan", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('subscriptions'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getMySubscriptions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('subscriptions/:id/cancel'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "cancelSubscription", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Post)('subscriptions/:id/cancel-immediately'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "cancelSubscriptionImmediately", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('transactions'),
    _ts_param(0, (0, _common.Request)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getMyTransactions", null);
_ts_decorate([
    (0, _common.UseGuards)(_jwtauthguard.JwtAuthGuard),
    (0, _common.Get)('transactions/:id'),
    _ts_param(0, (0, _common.Request)()),
    _ts_param(1, (0, _common.Param)('id')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object,
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], UsersController.prototype, "getMyTransactionDetails", null);
UsersController = _ts_decorate([
    (0, _common.Controller)('users'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _usersservice.UsersService === "undefined" ? Object : _usersservice.UsersService
    ])
], UsersController);

//# sourceMappingURL=users.controller.js.map