import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';
import { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateClientDto } from '../dto/create-client.dto';
import { UsersService } from './users.service';
import { AuthGuard } from '@nestjs/passport';
import { DailyWaterDto } from './dto/daily-water.dto';
import { DailyWorkoutsActivitiesDto } from './dto/daily-workouts-activities.dto';
import { CreateProtocolWorkoutDto } from './dto/create-protocol-workout.dto';
import { CreateProtocolDietDto } from './dto/create-protocol-diet.dto';
import { UpdateProtocolWorkoutDto } from './dto/update-protocol-workout.dto';
import { convertFromUTC, convertToUTC } from 'src/common/utils/date.util';
const dayjs = require('dayjs');

@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService){}

    @Get('time')
    getTime() {
      const tz = 'America/Sao_Paulo';
      const startOfDay = convertToUTC(dayjs().startOf('day').toDate(), tz);
      const endOfDay = convertToUTC(dayjs().endOf('day').toDate(), tz);

      return {
        startOfDay,
        endOfDay
      };

    }

    @Get()
    getAllUsers() {
        return [
            {
              "id": 1,
              "name": "Alice Johnson",
              "email": "<EMAIL>",
              "role": "Admin",
              "photo": "https://randomuser.me/api/portraits/women/1.jpg",
              "code": "f7k3s",
              "createdAt": "2022-05-14T10:22:31.000Z"
            },
            {
              "id": 2,
              "name": "Bob Smith",
              "email": "<EMAIL>",
              "role": "User",
              "photo": "https://randomuser.me/api/portraits/men/1.jpg",
              "code": "g2t7a",
              "createdAt": "2023-08-01T06:12:45.000Z"
            },
            {
              "id": 3,
              "name": "Charlie Brown",
              "email": "<EMAIL>",
              "role": "Editor",
              "photo": "https://randomuser.me/api/portraits/men/2.jpg",
              "code": "j8w4n",
              "createdAt": "2021-12-30T12:00:00.000Z"
            },
            {
              "id": 4,
              "name": "Daisy Miller",
              "email": "<EMAIL>",
              "role": "Admin",
              "photo": "https://randomuser.me/api/portraits/women/2.jpg",
              "code": "h5q2j",
              "createdAt": "2023-01-15T15:30:10.000Z"
            },
            {
              "id": 5,
              "name": "Edward Wilson",
              "email": "<EMAIL>",
              "role": "User",
              "photo": "https://randomuser.me/api/portraits/men/3.jpg",
              "code": "x9r3z",
              "createdAt": "2022-09-22T19:45:25.000Z"
            },
            {
              "id": 6,
              "name": "Fiona Green",
              "email": "<EMAIL>",
              "photo": "https://randomuser.me/api/portraits/women/3.jpg",
              "code": "n6k8d",
              "createdAt": "2020-11-14T08:11:31.000Z"
            },
            {
              "id": 7,
              "name": "George Black",
              "email": "<EMAIL>",
              "role": "Admin",
              "photo": "https://randomuser.me/api/portraits/men/4.jpg",
              "code": "v4w7p",
              "createdAt": "2021-03-10T13:22:45.000Z"
            },
            {
              "id": 8,
              "name": "Hannah White",
              "email": "<EMAIL>",
              "role": "User",
              "photo": "https://randomuser.me/api/portraits/women/4.jpg",
              "code": "b1j2k",
              "createdAt": "2022-02-28T11:15:50.000Z"
            },
            {
              "id": 9,
              "name": "Ian Black",
              "email": "<EMAIL>",
              "role": "Editor",
              "photo": "https://randomuser.me/api/portraits/men/5.jpg",
              "code": "x3l4y",
              "createdAt": "2021-10-09T19:30:12.000Z"
            },
            {
              "id": 10,
              "name": "Jane Doe",
              "email": "<EMAIL>",
              "role": "User",
              "photo": "https://randomuser.me/api/portraits/women/5.jpg",
              "code": "m0n6v",
              "createdAt": "2023-07-01T16:45:30.000Z"
            }
          ];
    }

    @UseGuards(JwtAuthGuard)
    @Post('/:role')
    async create(@Param('role') role: any, @Body() createClientDto: CreateClientDto, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      if(role=='admin'){
        throw new Error('Admin role is not allowed');
      }

      const user = await this.usersService.create(createClientDto, userId, role)
    }

    @UseGuards(JwtAuthGuard)
    @Get('me')
    async getMe(@Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const user = await this.usersService.getMe(userId);

      return user;
    }

    @UseGuards(JwtAuthGuard)
    @Get('goals')
    async getGoals() {
      return this.usersService.getGoals();
    }

    @UseGuards(JwtAuthGuard)
    @Put('me')
    async updateMe(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const user = await this.usersService.updateMe(userId, body);

      return user;
    }

    @UseGuards(JwtAuthGuard)
    @Delete('me')
    async deleteMe(@Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      return this.usersService.softDeleteAccount(userId);
    }

    // User photo
    @UseGuards(JwtAuthGuard)
    @Post('me/photo')
    @UseInterceptors(FileInterceptor('photo'))
    async updateMePhoto(@Request() req: any, @UploadedFile() file: Express.Multer.File) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      if (!file) {
        throw new Error('Photo file is required');
      }

      const result = await this.usersService.updateMePhoto(userId, file);

      return result;
    }

    // Resumo Nutricional (Aba: Diário)
    @UseGuards(JwtAuthGuard)
    @Get('diary/nutritional_summary')
    async getDailyNutritionalSummary(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const nutritional_summary = await this.usersService.getDailyNutritionalSummary(query as any, userId);

      return nutritional_summary;
    }


    @UseGuards(JwtAuthGuard)
    @Get('daily')
    async getDaily(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const daily = await this.usersService.getDaily(query as any, userId);

      return daily;
    }

    @UseGuards(JwtAuthGuard)
    @Get('assessments')
    async getAssessments(@Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const assessments = await this.usersService.getAssessments(userId);

      return assessments;
    }

    // Daily activity post
    @UseGuards(JwtAuthGuard)
    @Post('daily/water')
    async postDailyWater(@Request() req: any, @Body() dailyWaterDto: DailyWaterDto) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const daily = await this.usersService.postDailyWater(userId, dailyWaterDto);

      return daily;
    }

    @UseGuards(JwtAuthGuard)
    @Get('daily/list/workouts_activities')
    getDailyWorkoutsActivities() {
      return this.usersService.getDailyWorkoutsActivities();
    }

    // Daily Workouts Activities
    @UseGuards(JwtAuthGuard)
    @Get('daily/workouts_activities')
    getDailyWorkoutsActivitiesByDate(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      return this.usersService.getDailyWorkoutsActivitiesByDate(userId, query as any);
    }

    @UseGuards(JwtAuthGuard)
    @Post('daily/workouts_activities')
    postDailyWorkoutsActivities(@Body() dailyWorkoutsActivitiesDto: DailyWorkoutsActivitiesDto, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      return this.usersService.postDailyWorkoutsActivities(userId, dailyWorkoutsActivitiesDto);
    }

    @UseGuards(JwtAuthGuard)
    @Post('protocols/workout')
    async postProtocolsWorkout(@Body() createProtocolWorkoutDto: CreateProtocolWorkoutDto, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const protocol = await this.usersService.postProtocolsWorkout(userId, createProtocolWorkoutDto);

      return protocol;
    }

    // AI
    @UseGuards(JwtAuthGuard)
    @Post('protocols/workout/ai')
    async postProtocolsWorkoutAi(@Body() userInfo: any, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const protocol = await this.usersService.postProtocolsWorkoutAi(userId, userInfo);

      return protocol;
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols/workouts/active')
    async getActiveProtocolsWorkouts(@Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const protocols = await this.usersService.getActiveProtocolsWorkouts(userId);

      return protocols;
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols/workout/:id')
    async getProtocolWorkoutById(@Param('id') id: string, @Request() req: any) {
      console.log('🔍 Controller: req.user:', req.user);
      console.log('🔍 Controller: req.user.userId:', req.user?.userId);
      console.log('🔍 Controller: Protocol ID param:', id);

      const userId = req.user.userId;

      if (!userId) {
        console.log('❌ Controller: User ID não encontrado no token');
        throw new Error('User ID is required');
      }

      const protocolId = parseInt(id, 10);
      if (isNaN(protocolId)) {
        console.log('❌ Controller: Protocol ID inválido:', id);
        throw new Error('Invalid protocol ID');
      }

      console.log(`✅ Controller: Buscando protocolo ${protocolId} para usuário ${userId}`);
      const protocol = await this.usersService.getProtocolWorkoutById(protocolId, userId);

      return protocol;
    }

    @UseGuards(JwtAuthGuard)
    @Put('protocols/workout/:id')
    async updateProtocolWorkout(@Param('id') id: string, @Body() updateData: UpdateProtocolWorkoutDto, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const protocolId = parseInt(id, 10);
      if (isNaN(protocolId)) {
        throw new Error('Invalid protocol ID');
      }

      const protocol = await this.usersService.updateProtocolWorkout(protocolId, updateData, userId);

      return protocol;
    }

    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet')
    async postProtocolsDiet(@Body() createProtocolDietDto: CreateProtocolDietDto, @Request() req: any) {
      const userId = req.user.userId;

      if (!userId) {
        throw new Error('User ID is required');
      }

      const protocol = await this.usersService.postProtocolsDiet(userId, createProtocolDietDto);

      return protocol;
    }

    // AI
    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet/ai')
    async postProtocolsDietAI(@Body() userInfo: any, @Request() req: any) {
      const userId = req.user.userId;

      const protocol = await this.usersService.postProtocolsDietAi(userId, userInfo);

      return protocol;
    }


    // get active protocol of diet
    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/active')
    async getActiveProtocolsDiet(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      const protocols = await this.usersService.getActiveProtocolsDiet(userId);
      return protocols;
    }

    // HISTÓRICO DE PROTOCOLOS DE DIETA (DEVE VIR ANTES DA ROTA GENÉRICA :id)
    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/history')
    async getProtocolsDietHistory(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      try {
        console.log(`📚 Endpoint getProtocolsDietHistory chamado para usuário ${userId}`);
        console.log(`📋 Query params recebidos:`, query);

        const options = {
          page: query.page ? parseInt(query.page) : 1,
          limit: query.limit ? parseInt(query.limit) : 10,
          status: query.status || 'all',
          startDate: query.startDate,
          endDate: query.endDate,
          type: query.type
        };

        // Validar parâmetros
        if (options.page < 1) options.page = 1;
        if (options.limit < 1 || options.limit > 100) options.limit = 10;
        if (!['active', 'finished', 'all'].includes(options.status)) {
          options.status = 'all';
        }

        console.log(`📊 Opções processadas:`, options);

        const result = await this.usersService.getProtocolsDietHistory(userId, options);

        console.log(`✅ Histórico recuperado com sucesso: ${result.protocols?.length || 0} protocolos`);

        return {
          status: 'success',
          message: 'Histórico de protocolos de dieta recuperado com sucesso',
          data: result
        };

      } catch (error) {
        console.error(`❌ Erro no endpoint getProtocolsDietHistory para usuário ${userId}:`, error);
        console.error(`❌ Query params que causaram erro:`, query);
        throw error;
      }
    }

    // get specific diet protocol by id (DEVE VIR DEPOIS DAS ROTAS ESPECÍFICAS)
    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/:id')
    async getProtocolDietById(@Param('id') id: string, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`🔍 Recebido ID do protocolo: "${id}" (tipo: ${typeof id})`);

      // Validar se o ID foi fornecido
      if (!id || id.trim() === '') {
        console.error('❌ ID do protocolo está vazio ou nulo');
        throw new Error('Protocol ID is required');
      }

      // Tentar converter para número
      const protocolId = parseInt(id.trim(), 10);
      if (isNaN(protocolId) || protocolId <= 0) {
        console.error(`❌ ID do protocolo inválido: "${id}" -> ${protocolId}`);
        throw new Error(`Invalid protocol ID: "${id}". Must be a positive integer.`);
      }

      console.log(`🔍 Buscando protocolo de dieta ${protocolId} para usuário ${userId}`);
      const protocol = await this.usersService.getProtocolDietById(protocolId, userId);

      return {
        status: 'success',
        message: 'Protocolo de dieta recuperado com sucesso',
        data: protocol
      };
    }

    // get active meals of day week
    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/meals/active')
    getActiveMealsOfDayWeek(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getActiveMealsOfDayWeek(userId, query as any);
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/mealofdayweek')
    getProtocolsDietMealOfDayWeek(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getActiveProtocolsDietMealsDayWeek(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Delete('protocols/diet/:id')
    async deleteProtocolDiet(@Param('id') id: string, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      const protocolId = parseInt(id, 10);
      if (isNaN(protocolId)) {
        throw new Error('Invalid protocol ID');
      }
      const protocol = await this.usersService.deleteProtocolDiet(protocolId, userId);
      return protocol;
    }

    @UseGuards(JwtAuthGuard)
    @Delete('protocols/workout/:id')
    async deleteProtocolWorkout(@Param('id') id: number, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      const protocol = await this.usersService.deleteProtocolWorkout(id, userId);
      return protocol;
    }

    // Check protocol workout

    // Check protocol diet
    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet/check')
    async checkProtocolDiet(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.checkProtocolDiet(userId, body);
    }

    // Uncheck protocol diet
    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet/uncheck')
    async uncheckProtocolDiet(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.uncheckProtocolDiet(userId, body);
    }

    // ENDPOINT TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS
    @UseGuards(JwtAuthGuard)
    @Post('protocols/fix-orphans')
    async fixOrphanProtocols(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`🔧 Endpoint fixOrphanProtocols chamado para usuário ${userId}`);
      const result = await this.usersService.fixOrphanProtocols(userId);

      return {
        status: 'success',
        message: 'Correção de protocolos órfãos executada',
        data: result
      };
    }



    // FINALIZAR PROTOCOLO DE DIETA
    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet/:id/finish')
    async finishProtocolDiet(@Param('id') protocolId: string, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`🏁 Endpoint finishProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);
      const result = await this.usersService.finishProtocolDiet(userId, parseInt(protocolId));

      return {
        status: 'success',
        message: 'Protocolo finalizado com sucesso',
        data: result
      };
    }

    // DUPLICAR PROTOCOLO DE DIETA
    @UseGuards(JwtAuthGuard)
    @Post('protocols/diet/:id/duplicate')
    async duplicateProtocolDiet(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`📋 Endpoint duplicateProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);
      const result = await this.usersService.duplicateProtocolDiet(userId, parseInt(protocolId), body.startDate);

      return {
        status: 'success',
        message: 'Protocolo duplicado com sucesso',
        data: result
      };
    }

    // FINALIZAR PROTOCOLO DE TREINO
    @UseGuards(JwtAuthGuard)
    @Post('protocols/workout/:id/finish')
    async finishProtocolWorkout(@Param('id') protocolId: string, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`🏁 Endpoint finishProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);
      const result = await this.usersService.finishProtocolWorkout(userId, parseInt(protocolId));

      return {
        status: 'success',
        message: 'Protocolo de treino finalizado com sucesso',
        data: result
      };
    }

    // DUPLICAR PROTOCOLO DE TREINO
    @UseGuards(JwtAuthGuard)
    @Post('protocols/workout/:id/duplicate')
    async duplicateProtocolWorkout(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      console.log(`📋 Endpoint duplicateProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);
      const result = await this.usersService.duplicateProtocolWorkout(userId, parseInt(protocolId), body.startDate);

      return {
        status: 'success',
        message: 'Protocolo de treino duplicado com sucesso',
        data: result
      };
    }

    // DEBUG: VERIFICAR DADOS DE PROTOCOLOS NO BANCO
    @UseGuards(JwtAuthGuard)
    @Get('debug/protocols/raw')
    async debugProtocolsRaw(@Request() req: any) {
      const userId = req.user.userId;
      console.log(`🔍 DEBUG: Verificando protocolos raw para usuário ${userId}`);

      const result = await this.usersService.debugProtocolsRaw(userId);
      return {
        status: 'debug',
        message: 'Dados raw de protocolos',
        data: result
      };
    }

    // DEBUG: VERIFICAR DADOS DE TREINOS COMPLETADOS NO BANCO
    @UseGuards(JwtAuthGuard)
    @Get('debug/workouts/raw')
    async debugWorkoutsRaw(@Request() req: any) {
      const userId = req.user.userId;
      console.log(`🔍 DEBUG: Verificando treinos completados raw para usuário ${userId}`);

      const result = await this.usersService.debugWorkoutsRaw(userId);
      return {
        status: 'debug',
        message: 'Dados raw de treinos completados',
        data: result
      };
    }

    // HISTÓRICO DE PROTOCOLOS DE TREINO
    @UseGuards(JwtAuthGuard)
    @Get('protocols/workout/history')
    async getProtocolsWorkoutHistory(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }

      const options = {
        page: query.page ? parseInt(query.page) : 1,
        limit: query.limit ? parseInt(query.limit) : 10,
        status: query.status || 'all',
        startDate: query.startDate,
        endDate: query.endDate,
        type: query.type
      };

      console.log(`🏋️ Endpoint getProtocolsWorkoutHistory chamado para usuário ${userId}`, options);
      const result = await this.usersService.getProtocolsWorkoutHistory(userId, options);

      return {
        status: 'success',
        message: 'Histórico de protocolos de treino recuperado com sucesso',
        data: result
      };
    }

    // get daily checked meal by date_start date_end
    @UseGuards(JwtAuthGuard)
    @Get('protocols/diet/mealsofdayweekchecked')
    getDailyCheckedMealk(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getDailyCheckedMeal(userId, query as any);
    }

    // get exercises from protocol coach active
    @UseGuards(JwtAuthGuard)
    @Get('protocols/workout/exercises')
    getProtocolsWorkoutExercises(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProtocolsWorkoutExercises(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('protocols/workout/daily')
    async postProtocolsWorkoutDaily(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.postProtocolsWorkoutDaily(userId, body);
    }

    @UseGuards(JwtAuthGuard)
    @Get('protocols/workout/daily')
    async getProtocolsWorkoutDaily(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProtocolsWorkoutDaily(userId, query as any);
    }

    // Dias batendo a meta (Sequência) (Aba: Progresso)
    @UseGuards(JwtAuthGuard)
    @Get('progress/nutritional/days_goal_sequence')
    async getProgressNutritionalDaysGoalSequence(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressNutritionalDaysGoalSequence(userId);
    }

    // Registrar avaliação física
    @UseGuards(JwtAuthGuard)
    @Post('progress/evaluations')
    @UseInterceptors(AnyFilesInterceptor())
    async postProgressEvaluations(
      @Request() req: any,
      @Body() body: any,
      @UploadedFiles() files: Express.Multer.File[],
    ) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      const front = files.find(file => file.fieldname === 'front');
      const back = files.find(file => file.fieldname === 'back');
      const side = files.find(file => file.fieldname === 'side');

      const photos = { front, back, side };

      return this.usersService.postProgressEvaluations(userId, body, photos);
    }

    // Registrar mediddas
    @UseGuards(JwtAuthGuard)
    @Post('progress/evaluations/measurements')
    async getProgressEvaluationsMeasurements(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.postProgressEvaluationsMeasurements(userId, body);
    }

    // Histórico de avaliações
    @UseGuards(JwtAuthGuard)
    @Get('progress/evaluations')
    async getProgressEvaluations(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressEvaluations(userId, query as any);
    }

    // Assiduidade
    @UseGuards(JwtAuthGuard)
    @Get('progress/attendance')
    async getProgressAttendance(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressAttendance(userId);
    }

    // Progresso Semanal
    @UseGuards(JwtAuthGuard)
    @Get('progress/diet/weekly')
    async getProgressDietWeekly(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressDietWeekly(userId);
    }

    // Progresso Semanal Consolidado (Treinos, Nutrição, Sono, Água)
    @UseGuards(JwtAuthGuard)
    @Get('progress/weekly/consolidated')
    async getWeeklyProgressConsolidated(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getWeeklyProgressConsolidated(userId);
    }

    // Aderência semanal
    @UseGuards(JwtAuthGuard)
    @Get('progress/adherence')
    async getProgressAdherence(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressAdherence(userId, query);
    }

    // Histórico
    @UseGuards(JwtAuthGuard)
    @Get('progress/diet')
    async getProgressDiet(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressDiet(userId);
    }

    // Evolução de Peso e Gordura
    @UseGuards(JwtAuthGuard)
    @Get('progress/chart/weight_fat')
    async getProgressWeightFat(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressWeightFat(userId, query as any);
    }

    // Evolução de Força
    @UseGuards(JwtAuthGuard)
    @Get('progress/chart/strength')
    async getProgressStrength(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressStrength(userId, query as any);
    }

    // Análise Nutricional
    @UseGuards(JwtAuthGuard)
    @Get('progress/nutritional_analysis')
    async getProgressNutritionalAnalysis(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressNutritionalAnalysis(userId, query as any);
    }

    // Saldo Calórico
    @UseGuards(JwtAuthGuard)
    @Get('progress/chart/caloric_balance')
    async getProgressCaloricBalance(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressCaloricBalance(userId, query as any);
    }

    // Análise de Treinos progress/chart/workouts
    @UseGuards(JwtAuthGuard)
    @Get('progress/chart/workouts')
    async getProgressWorkouts(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressWorkouts(userId, query as any);
    }

    @UseGuards(JwtAuthGuard)
    @Get('progress/chart/workouts_analysis')
    async getProgressWorkoutsAnalysis(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressWorkoutsAnalysis(userId, query as any);
    }


    // Análise Completa
    @UseGuards(JwtAuthGuard)
    @Get('progress/complete_analysis')
    async getProgressCompleteAnalysis(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressCompleteAnalysis(userId, query as any);
    }

    // Endpoint geral de progresso (para compatibilidade com frontend)
    @UseGuards(JwtAuthGuard)
    @Get('progress')
    async getProgress(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgress(userId, query as any);
    }

    // Análise nutricional semanal
    @UseGuards(JwtAuthGuard)
    @Get('analytics/nutrition/weekly')
    async getAnalyticsNutritionWeekly(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getAnalyticsNutritionWeekly(userId, query as any);
    }

    // Análise de treinos semanal
    @UseGuards(JwtAuthGuard)
    @Get('analytics/workouts/weekly')
    async getAnalyticsWorkoutsWeekly(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getAnalyticsWorkoutsWeekly(userId, query as any);
    }

    // Evolução de força
    @UseGuards(JwtAuthGuard)
    @Get('strength/evolution')
    async getStrengthEvolution(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getStrengthEvolution(userId, query as any);
    }

    // WORKOUT SESSION ENDPOINTS

    // Start workout session
    @UseGuards(JwtAuthGuard)
    @Post('workouts/start')
    async startWorkout(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.startWorkout(userId, body);
    }

    // Get workouts by date
    @UseGuards(JwtAuthGuard)
    @Get('workouts')
    async getWorkoutsByDate(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getWorkoutsByDate(userId, query);
    }

    // Get workout history
    @UseGuards(JwtAuthGuard)
    @Get('workouts/history')
    async getWorkoutHistory(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getWorkoutHistory(userId, query);
    }

    // Get workout session details
    @UseGuards(JwtAuthGuard)
    @Get('workouts/sessions/:id')
    async getWorkoutSession(@Request() req: any, @Param('id') sessionId: string) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getWorkoutSession(userId, sessionId);
    }

    // Complete workout session
    @UseGuards(JwtAuthGuard)
    @Post('workouts/:id/complete')
    async completeWorkout(@Request() req: any, @Param('id') workoutId: string, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.completeWorkout(userId, workoutId, body);
    }

    // Distribuição de Volume
    @UseGuards(JwtAuthGuard)
    @Get('progress/volume_distribution')
    async getProgressVolumeDistribution(@Request() req: any, @Query() query: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getProgressVolumeDistribution(userId, query as any);
    }


    // AI
    @UseGuards(JwtAuthGuard)
    @Post('ai/foods-suggestions')
    async getFoodsSuggestions(@Request() req: any, @Query() query: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getFoodsSuggestions(userId, query, body);
    }

    // User Options
    @UseGuards(JwtAuthGuard)
    @Get('options')
    async getUserOptions(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getUserOptions(userId);
    }

    // Affiliates
    @UseGuards(JwtAuthGuard)
    @Post('affiliate/join')
    async createAffiliate(@Request() req: any, @Body() body: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.createAffiliate(body, userId);
    }

    // Plans
    @UseGuards(JwtAuthGuard)
    @Get('plans')
    async getAllPlans(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getAllPlans(userId);
    }

    // Check plan stripe
    @UseGuards(JwtAuthGuard)
    @Post('plans/checkout/:planId')
    async subscribePlan(@Request() req: any, @Param('planId') planId: string) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.subscribePlan(userId, Number(planId));
    }

    // Subscriptions
    @UseGuards(JwtAuthGuard)
    @Get('subscriptions')
    async getMySubscriptions(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getMySubscriptions(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('subscriptions/:id/cancel')
    async cancelSubscription(@Request() req: any, @Param('id') id: string) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.cancelSubscription(Number(id), userId);
    }

    @UseGuards(JwtAuthGuard)
    @Post('subscriptions/:id/cancel-immediately')
    async cancelSubscriptionImmediately(@Request() req: any, @Param('id') id: string) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.cancelSubscriptionImmediately(Number(id), userId);
    }

    // Transactions
    @UseGuards(JwtAuthGuard)
    @Get('transactions')
    async getMyTransactions(@Request() req: any) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getMyTransactions(userId);
    }

    @UseGuards(JwtAuthGuard)
    @Get('transactions/:id')
    async getMyTransactionDetails(@Request() req: any, @Param('id') id: string) {
      const userId = req.user.userId;
      if (!userId) {
        throw new Error('User ID is required');
      }
      return this.usersService.getMyTransactionDetails(Number(id), userId);
    }
}