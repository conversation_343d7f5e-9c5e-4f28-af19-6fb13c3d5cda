{"version": 3, "sources": ["../../src/users/users.controller.ts"], "sourcesContent": ["import { Body, Controller, Delete, Get, Param, Post, Put, Query, Request, UploadedFile, UploadedFiles, UseGuards, UseInterceptors } from '@nestjs/common';\r\nimport { AnyFilesInterceptor, FileInterceptor } from '@nestjs/platform-express';\r\nimport { JwtAuthGuard } from '../auth/jwt-auth.guard';\r\nimport { CreateClientDto } from '../dto/create-client.dto';\r\nimport { UsersService } from './users.service';\r\nimport { AuthGuard } from '@nestjs/passport';\r\nimport { DailyWaterDto } from './dto/daily-water.dto';\r\nimport { DailyWorkoutsActivitiesDto } from './dto/daily-workouts-activities.dto';\r\nimport { CreateProtocolWorkoutDto } from './dto/create-protocol-workout.dto';\r\nimport { CreateProtocolDietDto } from './dto/create-protocol-diet.dto';\r\nimport { UpdateProtocolWorkoutDto } from './dto/update-protocol-workout.dto';\r\nimport { convertFromUTC, convertToUTC } from 'src/common/utils/date.util';\r\nconst dayjs = require('dayjs');\r\n\r\n@Controller('users')\r\nexport class UsersController {\r\n  constructor(private readonly usersService: UsersService){}\r\n\r\n    @Get('time')\r\n    getTime() {\r\n      const tz = 'America/Sao_Paulo';\r\n      const startOfDay = convertToUTC(dayjs().startOf('day').toDate(), tz);\r\n      const endOfDay = convertToUTC(dayjs().endOf('day').toDate(), tz);\r\n\r\n      return {\r\n        startOfDay,\r\n        endOfDay\r\n      };\r\n\r\n    }\r\n\r\n    @Get()\r\n    getAllUsers() {\r\n        return [\r\n            {\r\n              \"id\": 1,\r\n              \"name\": \"Alice Johnson\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/1.jpg\",\r\n              \"code\": \"f7k3s\",\r\n              \"createdAt\": \"2022-05-14T10:22:31.000Z\"\r\n            },\r\n            {\r\n              \"id\": 2,\r\n              \"name\": \"Bob Smith\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/1.jpg\",\r\n              \"code\": \"g2t7a\",\r\n              \"createdAt\": \"2023-08-01T06:12:45.000Z\"\r\n            },\r\n            {\r\n              \"id\": 3,\r\n              \"name\": \"Charlie Brown\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Editor\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/2.jpg\",\r\n              \"code\": \"j8w4n\",\r\n              \"createdAt\": \"2021-12-30T12:00:00.000Z\"\r\n            },\r\n            {\r\n              \"id\": 4,\r\n              \"name\": \"Daisy Miller\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/2.jpg\",\r\n              \"code\": \"h5q2j\",\r\n              \"createdAt\": \"2023-01-15T15:30:10.000Z\"\r\n            },\r\n            {\r\n              \"id\": 5,\r\n              \"name\": \"Edward Wilson\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/3.jpg\",\r\n              \"code\": \"x9r3z\",\r\n              \"createdAt\": \"2022-09-22T19:45:25.000Z\"\r\n            },\r\n            {\r\n              \"id\": 6,\r\n              \"name\": \"Fiona Green\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/3.jpg\",\r\n              \"code\": \"n6k8d\",\r\n              \"createdAt\": \"2020-11-14T08:11:31.000Z\"\r\n            },\r\n            {\r\n              \"id\": 7,\r\n              \"name\": \"George Black\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Admin\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/4.jpg\",\r\n              \"code\": \"v4w7p\",\r\n              \"createdAt\": \"2021-03-10T13:22:45.000Z\"\r\n            },\r\n            {\r\n              \"id\": 8,\r\n              \"name\": \"Hannah White\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/4.jpg\",\r\n              \"code\": \"b1j2k\",\r\n              \"createdAt\": \"2022-02-28T11:15:50.000Z\"\r\n            },\r\n            {\r\n              \"id\": 9,\r\n              \"name\": \"Ian Black\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"Editor\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/men/5.jpg\",\r\n              \"code\": \"x3l4y\",\r\n              \"createdAt\": \"2021-10-09T19:30:12.000Z\"\r\n            },\r\n            {\r\n              \"id\": 10,\r\n              \"name\": \"Jane Doe\",\r\n              \"email\": \"<EMAIL>\",\r\n              \"role\": \"User\",\r\n              \"photo\": \"https://randomuser.me/api/portraits/women/5.jpg\",\r\n              \"code\": \"m0n6v\",\r\n              \"createdAt\": \"2023-07-01T16:45:30.000Z\"\r\n            }\r\n          ];\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('/:role')\r\n    async create(@Param('role') role: any, @Body() createClientDto: CreateClientDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      if(role=='admin'){\r\n        throw new Error('Admin role is not allowed');\r\n      }\r\n\r\n      const user = await this.usersService.create(createClientDto, userId, role)\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('me')\r\n    async getMe(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const user = await this.usersService.getMe(userId);\r\n\r\n      return user;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('goals')\r\n    async getGoals() {\r\n      return this.usersService.getGoals();\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('me')\r\n    async updateMe(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const user = await this.usersService.updateMe(userId, body);\r\n\r\n      return user;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('me')\r\n    async deleteMe(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.softDeleteAccount(userId);\r\n    }\r\n\r\n    // User photo\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('me/photo')\r\n    @UseInterceptors(FileInterceptor('photo'))\r\n    async updateMePhoto(@Request() req: any, @UploadedFile() file: Express.Multer.File) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      if (!file) {\r\n        throw new Error('Photo file is required');\r\n      }\r\n\r\n      const result = await this.usersService.updateMePhoto(userId, file);\r\n\r\n      return result;\r\n    }\r\n\r\n    // Resumo Nutricional (Aba: Diário)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('diary/nutritional_summary')\r\n    async getDailyNutritionalSummary(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const nutritional_summary = await this.usersService.getDailyNutritionalSummary(query as any, userId);\r\n\r\n      return nutritional_summary;\r\n    }\r\n\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily')\r\n    async getDaily(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const daily = await this.usersService.getDaily(query as any, userId);\r\n\r\n      return daily;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('assessments')\r\n    async getAssessments(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const assessments = await this.usersService.getAssessments(userId);\r\n\r\n      return assessments;\r\n    }\r\n\r\n    // Daily activity post\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('daily/water')\r\n    async postDailyWater(@Request() req: any, @Body() dailyWaterDto: DailyWaterDto) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const daily = await this.usersService.postDailyWater(userId, dailyWaterDto);\r\n\r\n      return daily;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily/list/workouts_activities')\r\n    getDailyWorkoutsActivities() {\r\n      return this.usersService.getDailyWorkoutsActivities();\r\n    }\r\n\r\n    // Daily Workouts Activities\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('daily/workouts_activities')\r\n    getDailyWorkoutsActivitiesByDate(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.getDailyWorkoutsActivitiesByDate(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('daily/workouts_activities')\r\n    postDailyWorkoutsActivities(@Body() dailyWorkoutsActivitiesDto: DailyWorkoutsActivitiesDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      return this.usersService.postDailyWorkoutsActivities(userId, dailyWorkoutsActivitiesDto);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout')\r\n    async postProtocolsWorkout(@Body() createProtocolWorkoutDto: CreateProtocolWorkoutDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsWorkout(userId, createProtocolWorkoutDto);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/ai')\r\n    async postProtocolsWorkoutAi(@Body() userInfo: any, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsWorkoutAi(userId, userInfo);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workouts/active')\r\n    async getActiveProtocolsWorkouts(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocols = await this.usersService.getActiveProtocolsWorkouts(userId);\r\n\r\n      return protocols;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/:id')\r\n    async getProtocolWorkoutById(@Param('id') id: string, @Request() req: any) {\r\n      console.log('🔍 Controller: req.user:', req.user);\r\n      console.log('🔍 Controller: req.user.userId:', req.user?.userId);\r\n      console.log('🔍 Controller: Protocol ID param:', id);\r\n\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        console.log('❌ Controller: User ID não encontrado no token');\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        console.log('❌ Controller: Protocol ID inválido:', id);\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n\r\n      console.log(`✅ Controller: Buscando protocolo ${protocolId} para usuário ${userId}`);\r\n      const protocol = await this.usersService.getProtocolWorkoutById(protocolId, userId);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Put('protocols/workout/:id')\r\n    async updateProtocolWorkout(@Param('id') id: string, @Body() updateData: UpdateProtocolWorkoutDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n\r\n      const protocol = await this.usersService.updateProtocolWorkout(protocolId, updateData, userId);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet')\r\n    async postProtocolsDiet(@Body() createProtocolDietDto: CreateProtocolDietDto, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const protocol = await this.usersService.postProtocolsDiet(userId, createProtocolDietDto);\r\n\r\n      return protocol;\r\n    }\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/ai')\r\n    async postProtocolsDietAI(@Body() userInfo: any, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n\r\n      const protocol = await this.usersService.postProtocolsDietAi(userId, userInfo);\r\n\r\n      return protocol;\r\n    }\r\n\r\n\r\n    // get active protocol of diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/active')\r\n    async getActiveProtocolsDiet(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocols = await this.usersService.getActiveProtocolsDiet(userId);\r\n      return protocols;\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE DIETA (DEVE VIR ANTES DA ROTA GENÉRICA :id)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/history')\r\n    async getProtocolsDietHistory(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      try {\r\n        console.log(`📚 Endpoint getProtocolsDietHistory chamado para usuário ${userId}`);\r\n        console.log(`📋 Query params recebidos:`, query);\r\n\r\n        const options = {\r\n          page: query.page ? parseInt(query.page) : 1,\r\n          limit: query.limit ? parseInt(query.limit) : 10,\r\n          status: query.status || 'all',\r\n          startDate: query.startDate,\r\n          endDate: query.endDate,\r\n          type: query.type\r\n        };\r\n\r\n        // Validar parâmetros\r\n        if (options.page < 1) options.page = 1;\r\n        if (options.limit < 1 || options.limit > 100) options.limit = 10;\r\n        if (!['active', 'finished', 'all'].includes(options.status)) {\r\n          options.status = 'all';\r\n        }\r\n\r\n        console.log(`📊 Opções processadas:`, options);\r\n\r\n        const result = await this.usersService.getProtocolsDietHistory(userId, options);\r\n\r\n        console.log(`✅ Histórico recuperado com sucesso: ${result.protocols?.length || 0} protocolos`);\r\n\r\n        return {\r\n          status: 'success',\r\n          message: 'Histórico de protocolos de dieta recuperado com sucesso',\r\n          data: result\r\n        };\r\n\r\n      } catch (error) {\r\n        console.error(`❌ Erro no endpoint getProtocolsDietHistory para usuário ${userId}:`, error);\r\n        console.error(`❌ Query params que causaram erro:`, query);\r\n        throw error;\r\n      }\r\n    }\r\n\r\n    // get specific diet protocol by id (DEVE VIR DEPOIS DAS ROTAS ESPECÍFICAS)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/:id')\r\n    async getProtocolDietById(@Param('id') id: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔍 Recebido ID do protocolo: \"${id}\" (tipo: ${typeof id})`);\r\n\r\n      // Validar se o ID foi fornecido\r\n      if (!id || id.trim() === '') {\r\n        console.error('❌ ID do protocolo está vazio ou nulo');\r\n        throw new Error('Protocol ID is required');\r\n      }\r\n\r\n      // Tentar converter para número\r\n      const protocolId = parseInt(id.trim(), 10);\r\n      if (isNaN(protocolId) || protocolId <= 0) {\r\n        console.error(`❌ ID do protocolo inválido: \"${id}\" -> ${protocolId}`);\r\n        throw new Error(`Invalid protocol ID: \"${id}\". Must be a positive integer.`);\r\n      }\r\n\r\n      console.log(`🔍 Buscando protocolo de dieta ${protocolId} para usuário ${userId}`);\r\n      const protocol = await this.usersService.getProtocolDietById(protocolId, userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de dieta recuperado com sucesso',\r\n        data: protocol\r\n      };\r\n    }\r\n\r\n    // get active meals of day week\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/meals/active')\r\n    getActiveMealsOfDayWeek(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getActiveMealsOfDayWeek(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/mealofdayweek')\r\n    getProtocolsDietMealOfDayWeek(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getActiveProtocolsDietMealsDayWeek(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('protocols/diet/:id')\r\n    async deleteProtocolDiet(@Param('id') id: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocolId = parseInt(id, 10);\r\n      if (isNaN(protocolId)) {\r\n        throw new Error('Invalid protocol ID');\r\n      }\r\n      const protocol = await this.usersService.deleteProtocolDiet(protocolId, userId);\r\n      return protocol;\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Delete('protocols/workout/:id')\r\n    async deleteProtocolWorkout(@Param('id') id: number, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const protocol = await this.usersService.deleteProtocolWorkout(id, userId);\r\n      return protocol;\r\n    }\r\n\r\n    // Check protocol workout\r\n\r\n    // Check protocol diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/check')\r\n    async checkProtocolDiet(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.checkProtocolDiet(userId, body);\r\n    }\r\n\r\n    // Uncheck protocol diet\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/uncheck')\r\n    async uncheckProtocolDiet(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.uncheckProtocolDiet(userId, body);\r\n    }\r\n\r\n    // ENDPOINT TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/fix-orphans')\r\n    async fixOrphanProtocols(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔧 Endpoint fixOrphanProtocols chamado para usuário ${userId}`);\r\n      const result = await this.usersService.fixOrphanProtocols(userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Correção de protocolos órfãos executada',\r\n        data: result\r\n      };\r\n    }\r\n\r\n\r\n\r\n    // FINALIZAR PROTOCOLO DE DIETA\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/:id/finish')\r\n    async finishProtocolDiet(@Param('id') protocolId: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🏁 Endpoint finishProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.finishProtocolDiet(userId, parseInt(protocolId));\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo finalizado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DUPLICAR PROTOCOLO DE DIETA\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/diet/:id/duplicate')\r\n    async duplicateProtocolDiet(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`📋 Endpoint duplicateProtocolDiet chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.duplicateProtocolDiet(userId, parseInt(protocolId), body.startDate);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo duplicado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // FINALIZAR PROTOCOLO DE TREINO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/:id/finish')\r\n    async finishProtocolWorkout(@Param('id') protocolId: string, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🏁 Endpoint finishProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.finishProtocolWorkout(userId, parseInt(protocolId));\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de treino finalizado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DUPLICAR PROTOCOLO DE TREINO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/:id/duplicate')\r\n    async duplicateProtocolWorkout(@Param('id') protocolId: string, @Body() body: { startDate?: string }, @Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`📋 Endpoint duplicateProtocolWorkout chamado para protocolo ${protocolId}, usuário ${userId}`);\r\n      const result = await this.usersService.duplicateProtocolWorkout(userId, parseInt(protocolId), body.startDate);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Protocolo de treino duplicado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DEBUG: VERIFICAR DADOS DE PROTOCOLOS NO BANCO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/protocols/raw')\r\n    async debugProtocolsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      console.log(`🔍 DEBUG: Verificando protocolos raw para usuário ${userId}`);\r\n\r\n      const result = await this.usersService.debugProtocolsRaw(userId);\r\n      return {\r\n        status: 'debug',\r\n        message: 'Dados raw de protocolos',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DEBUG: VERIFICAR DADOS DE TREINOS COMPLETADOS NO BANCO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/workouts/raw')\r\n    async debugWorkoutsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      console.log(`🔍 DEBUG: Verificando treinos completados raw para usuário ${userId}`);\r\n\r\n      const result = await this.usersService.debugWorkoutsRaw(userId);\r\n      return {\r\n        status: 'debug',\r\n        message: 'Dados raw de treinos completados',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE TREINO\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/history')\r\n    async getProtocolsWorkoutHistory(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      const options = {\r\n        page: query.page ? parseInt(query.page) : 1,\r\n        limit: query.limit ? parseInt(query.limit) : 10,\r\n        status: query.status || 'all',\r\n        startDate: query.startDate,\r\n        endDate: query.endDate,\r\n        type: query.type\r\n      };\r\n\r\n      console.log(`🏋️ Endpoint getProtocolsWorkoutHistory chamado para usuário ${userId}`, options);\r\n      const result = await this.usersService.getProtocolsWorkoutHistory(userId, options);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Histórico de protocolos de treino recuperado com sucesso',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // DEBUG: Endpoint para verificar protocolos no banco\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('debug/protocols/raw')\r\n    async getProtocolsRaw(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n\r\n      console.log(`🔍 DEBUG: Buscando protocolos raw para usuário ${userId}`);\r\n      const result = await this.usersService.getProtocolsRawDebug(userId);\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Dados raw dos protocolos',\r\n        data: result\r\n      };\r\n    }\r\n\r\n    // get daily checked meal by date_start date_end\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/diet/mealsofdayweekchecked')\r\n    getDailyCheckedMealk(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getDailyCheckedMeal(userId, query as any);\r\n    }\r\n\r\n    // get exercises from protocol coach active\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/exercises')\r\n    getProtocolsWorkoutExercises(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProtocolsWorkoutExercises(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('protocols/workout/daily')\r\n    async postProtocolsWorkoutDaily(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.postProtocolsWorkoutDaily(userId, body);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('protocols/workout/daily')\r\n    async getProtocolsWorkoutDaily(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProtocolsWorkoutDaily(userId, query as any);\r\n    }\r\n\r\n    // Dias batendo a meta (Sequência) (Aba: Progresso)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/nutritional/days_goal_sequence')\r\n    async getProgressNutritionalDaysGoalSequence(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressNutritionalDaysGoalSequence(userId);\r\n    }\r\n\r\n    // Registrar avaliação física\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('progress/evaluations')\r\n    @UseInterceptors(AnyFilesInterceptor())\r\n    async postProgressEvaluations(\r\n      @Request() req: any,\r\n      @Body() body: any,\r\n      @UploadedFiles() files: Express.Multer.File[],\r\n    ) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      const front = files.find(file => file.fieldname === 'front');\r\n      const back = files.find(file => file.fieldname === 'back');\r\n      const side = files.find(file => file.fieldname === 'side');\r\n\r\n      const photos = { front, back, side };\r\n\r\n      return this.usersService.postProgressEvaluations(userId, body, photos);\r\n    }\r\n\r\n    // Registrar mediddas\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('progress/evaluations/measurements')\r\n    async getProgressEvaluationsMeasurements(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.postProgressEvaluationsMeasurements(userId, body);\r\n    }\r\n\r\n    // Histórico de avaliações\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/evaluations')\r\n    async getProgressEvaluations(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressEvaluations(userId, query as any);\r\n    }\r\n\r\n    // Assiduidade\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/attendance')\r\n    async getProgressAttendance(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressAttendance(userId);\r\n    }\r\n\r\n    // Progresso Semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/diet/weekly')\r\n    async getProgressDietWeekly(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressDietWeekly(userId);\r\n    }\r\n\r\n    // Progresso Semanal Consolidado (Treinos, Nutrição, Sono, Água)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/weekly/consolidated')\r\n    async getWeeklyProgressConsolidated(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWeeklyProgressConsolidated(userId);\r\n    }\r\n\r\n    // Aderência semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/adherence')\r\n    async getProgressAdherence(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressAdherence(userId, query);\r\n    }\r\n\r\n    // Histórico\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/diet')\r\n    async getProgressDiet(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressDiet(userId);\r\n    }\r\n\r\n    // Evolução de Peso e Gordura\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/weight_fat')\r\n    async getProgressWeightFat(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWeightFat(userId, query as any);\r\n    }\r\n\r\n    // Evolução de Força\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/strength')\r\n    async getProgressStrength(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressStrength(userId, query as any);\r\n    }\r\n\r\n    // Análise Nutricional\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/nutritional_analysis')\r\n    async getProgressNutritionalAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressNutritionalAnalysis(userId, query as any);\r\n    }\r\n\r\n    // Saldo Calórico\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/caloric_balance')\r\n    async getProgressCaloricBalance(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressCaloricBalance(userId, query as any);\r\n    }\r\n\r\n    // Análise de Treinos progress/chart/workouts\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/workouts')\r\n    async getProgressWorkouts(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWorkouts(userId, query as any);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/chart/workouts_analysis')\r\n    async getProgressWorkoutsAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressWorkoutsAnalysis(userId, query as any);\r\n    }\r\n\r\n\r\n    // Análise Completa\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/complete_analysis')\r\n    async getProgressCompleteAnalysis(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressCompleteAnalysis(userId, query as any);\r\n    }\r\n\r\n    // Endpoint geral de progresso (para compatibilidade com frontend)\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress')\r\n    async getProgress(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgress(userId, query as any);\r\n    }\r\n\r\n    // Análise nutricional semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('analytics/nutrition/weekly')\r\n    async getAnalyticsNutritionWeekly(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAnalyticsNutritionWeekly(userId, query as any);\r\n    }\r\n\r\n    // Análise de treinos semanal\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('analytics/workouts/weekly')\r\n    async getAnalyticsWorkoutsWeekly(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAnalyticsWorkoutsWeekly(userId, query as any);\r\n    }\r\n\r\n    // Evolução de força\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('strength/evolution')\r\n    async getStrengthEvolution(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getStrengthEvolution(userId, query as any);\r\n    }\r\n\r\n    // WORKOUT SESSION ENDPOINTS\r\n\r\n    // Start workout session\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('workouts/start')\r\n    async startWorkout(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.startWorkout(userId, body);\r\n    }\r\n\r\n    // Get workouts by date\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts')\r\n    async getWorkoutsByDate(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutsByDate(userId, query);\r\n    }\r\n\r\n    // Get workout history\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts/history')\r\n    async getWorkoutHistory(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutHistory(userId, query);\r\n    }\r\n\r\n    // Get workout session details\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('workouts/sessions/:id')\r\n    async getWorkoutSession(@Request() req: any, @Param('id') sessionId: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getWorkoutSession(userId, sessionId);\r\n    }\r\n\r\n    // Complete workout session\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('workouts/:id/complete')\r\n    async completeWorkout(@Request() req: any, @Param('id') workoutId: string, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.completeWorkout(userId, workoutId, body);\r\n    }\r\n\r\n    // Distribuição de Volume\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('progress/volume_distribution')\r\n    async getProgressVolumeDistribution(@Request() req: any, @Query() query: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getProgressVolumeDistribution(userId, query as any);\r\n    }\r\n\r\n\r\n    // AI\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('ai/foods-suggestions')\r\n    async getFoodsSuggestions(@Request() req: any, @Query() query: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getFoodsSuggestions(userId, query, body);\r\n    }\r\n\r\n    // User Options\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('options')\r\n    async getUserOptions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getUserOptions(userId);\r\n    }\r\n\r\n    // Affiliates\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('affiliate/join')\r\n    async createAffiliate(@Request() req: any, @Body() body: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.createAffiliate(body, userId);\r\n    }\r\n\r\n    // Plans\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('plans')\r\n    async getAllPlans(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getAllPlans(userId);\r\n    }\r\n\r\n    // Check plan stripe\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('plans/checkout/:planId')\r\n    async subscribePlan(@Request() req: any, @Param('planId') planId: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.subscribePlan(userId, Number(planId));\r\n    }\r\n\r\n    // Subscriptions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('subscriptions')\r\n    async getMySubscriptions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMySubscriptions(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('subscriptions/:id/cancel')\r\n    async cancelSubscription(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.cancelSubscription(Number(id), userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Post('subscriptions/:id/cancel-immediately')\r\n    async cancelSubscriptionImmediately(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.cancelSubscriptionImmediately(Number(id), userId);\r\n    }\r\n\r\n    // Transactions\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions')\r\n    async getMyTransactions(@Request() req: any) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMyTransactions(userId);\r\n    }\r\n\r\n    @UseGuards(JwtAuthGuard)\r\n    @Get('transactions/:id')\r\n    async getMyTransactionDetails(@Request() req: any, @Param('id') id: string) {\r\n      const userId = req.user.userId;\r\n      if (!userId) {\r\n        throw new Error('User ID is required');\r\n      }\r\n      return this.usersService.getMyTransactionDetails(Number(id), userId);\r\n    }\r\n}"], "names": ["UsersController", "dayjs", "require", "getTime", "tz", "startOfDay", "convertToUTC", "startOf", "toDate", "endOfDay", "endOf", "getAllUsers", "create", "role", "createClientDto", "req", "userId", "user", "Error", "usersService", "getMe", "getGoals", "updateMe", "body", "deleteMe", "softDeleteAccount", "updateMePhoto", "file", "result", "getDailyNutritionalSummary", "query", "nutritional_summary", "getDaily", "daily", "getAssessments", "assessments", "postDailyWater", "dailyWaterDto", "getDailyWorkoutsActivities", "getDailyWorkoutsActivitiesByDate", "postDailyWorkoutsActivities", "dailyWorkoutsActivitiesDto", "postProtocolsWorkout", "createProtocolWorkoutDto", "protocol", "postProtocolsWorkoutAi", "userInfo", "getActiveProtocolsWorkouts", "protocols", "getProtocolWorkoutById", "id", "console", "log", "protocolId", "parseInt", "isNaN", "updateProtocolWorkout", "updateData", "postProtocolsDiet", "createProtocolDietDto", "postProtocolsDietAI", "postProtocolsDietAi", "getActiveProtocolsDiet", "getProtocolsDietHistory", "options", "page", "limit", "status", "startDate", "endDate", "type", "includes", "length", "message", "data", "error", "getProtocolDietById", "trim", "getActiveMealsOfDayWeek", "getProtocolsDietMealOfDayWeek", "getActiveProtocolsDietMealsDayWeek", "deleteProtocolDiet", "deleteProtocolWorkout", "checkProtocolDiet", "uncheckProtocolDiet", "fixOrphanProtocols", "finishProtocolDiet", "duplicateProtocolDiet", "finishProtocolWorkout", "duplicateProtocolWorkout", "debugProtocolsRaw", "debugWorkoutsRaw", "getProtocolsWorkoutHistory", "getProtocolsRaw", "getProtocolsRawDebug", "getDailyCheckedMealk", "getDailyCheckedMeal", "getProtocolsWorkoutExercises", "postProtocolsWorkoutDaily", "getProtocolsWorkoutDaily", "getProgressNutritionalDaysGoalSequence", "postProgressEvaluations", "files", "front", "find", "fieldname", "back", "side", "photos", "getProgressEvaluationsMeasurements", "postProgressEvaluationsMeasurements", "getProgressEvaluations", "getProgressAttendance", "getProgressDietWeekly", "getWeeklyProgressConsolidated", "getProgressAdherence", "getProgressDiet", "getProgressWeightFat", "getProgressStrength", "getProgressNutritionalAnalysis", "getProgressCaloricBalance", "getProgressWorkouts", "getProgressWorkoutsAnalysis", "getProgressCompleteAnalysis", "getProgress", "getAnalyticsNutritionWeekly", "getAnalyticsWorkoutsWeekly", "getStrengthEvolution", "startWorkout", "getWorkoutsByDate", "getWorkoutHistory", "getWorkoutSession", "sessionId", "completeWorkout", "workoutId", "getProgressVolumeDistribution", "getFoodsSuggestions", "getUserOptions", "createAffiliate", "getAllPlans", "subscribePlan", "planId", "Number", "getMySubscriptions", "cancelSubscription", "cancelSubscriptionImmediately", "getMyTransactions", "getMyTransactionDetails", "constructor"], "mappings": ";;;;+BAeaA;;;eAAAA;;;wBAf4H;iCACpF;8BACxB;iCACG;8BACH;+BAEC;4CACa;0CACF;uCACH;0CACG;0BACI;;;;;;;;;;;;;;;AAC7C,MAAMC,QAAQC,QAAQ;AAGf,IAAA,AAAMF,kBAAN,MAAMA;IAITG,UAAU;QACR,MAAMC,KAAK;QACX,MAAMC,aAAaC,IAAAA,sBAAY,EAACL,QAAQM,OAAO,CAAC,OAAOC,MAAM,IAAIJ;QACjE,MAAMK,WAAWH,IAAAA,sBAAY,EAACL,QAAQS,KAAK,CAAC,OAAOF,MAAM,IAAIJ;QAE7D,OAAO;YACLC;YACAI;QACF;IAEF;IAGAE,cAAc;QACV,OAAO;YACH;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;YACA;gBACE,MAAM;gBACN,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,SAAS;gBACT,QAAQ;gBACR,aAAa;YACf;SACD;IACP;IAEA,MAEMC,OAAO,AAAeC,IAAS,EAAE,AAAQC,eAAgC,EAAE,AAAWC,GAAQ,EAAE;QACpG,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAGL,QAAM,SAAQ;YACf,MAAM,IAAIK,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACP,MAAM,CAACE,iBAAiBE,QAAQH;IACvE;IAEA,MAEMO,MAAM,AAAWL,GAAQ,EAAE;QAC/B,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACC,KAAK,CAACJ;QAE3C,OAAOC;IACT;IAEA,MAEMI,WAAW;QACf,OAAO,IAAI,CAACF,YAAY,CAACE,QAAQ;IACnC;IAEA,MAEMC,SAAS,AAAWP,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACrD,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMD,OAAO,MAAM,IAAI,CAACE,YAAY,CAACG,QAAQ,CAACN,QAAQO;QAEtD,OAAON;IACT;IAEA,MAEMO,SAAS,AAAWT,GAAQ,EAAE;QAClC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACM,iBAAiB,CAACT;IAC7C;IAEA,aAAa;IACb,MAGMU,cAAc,AAAWX,GAAQ,EAAE,AAAgBY,IAAyB,EAAE;QAClF,MAAMX,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAI,CAACS,MAAM;YACT,MAAM,IAAIT,MAAM;QAClB;QAEA,MAAMU,SAAS,MAAM,IAAI,CAACT,YAAY,CAACO,aAAa,CAACV,QAAQW;QAE7D,OAAOC;IACT;IAEA,mCAAmC;IACnC,MAEMC,2BAA2B,AAAWd,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMa,sBAAsB,MAAM,IAAI,CAACZ,YAAY,CAACU,0BAA0B,CAACC,OAAcd;QAE7F,OAAOe;IACT;IAGA,MAEMC,SAAS,AAAWjB,GAAQ,EAAE,AAASe,KAAU,EAAE;QACvD,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMe,QAAQ,MAAM,IAAI,CAACd,YAAY,CAACa,QAAQ,CAACF,OAAcd;QAE7D,OAAOiB;IACT;IAEA,MAEMC,eAAe,AAAWnB,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMiB,cAAc,MAAM,IAAI,CAAChB,YAAY,CAACe,cAAc,CAAClB;QAE3D,OAAOmB;IACT;IAEA,sBAAsB;IACtB,MAEMC,eAAe,AAAWrB,GAAQ,EAAE,AAAQsB,aAA4B,EAAE;QAC9E,MAAMrB,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMe,QAAQ,MAAM,IAAI,CAACd,YAAY,CAACiB,cAAc,CAACpB,QAAQqB;QAE7D,OAAOJ;IACT;IAIAK,6BAA6B;QAC3B,OAAO,IAAI,CAACnB,YAAY,CAACmB,0BAA0B;IACrD;IAEA,4BAA4B;IAG5BC,iCAAiC,AAAWxB,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACoB,gCAAgC,CAACvB,QAAQc;IACpE;IAIAU,4BAA4B,AAAQC,0BAAsD,EAAE,AAAW1B,GAAQ,EAAE;QAC/G,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,OAAO,IAAI,CAACC,YAAY,CAACqB,2BAA2B,CAACxB,QAAQyB;IAC/D;IAEA,MAEMC,qBAAqB,AAAQC,wBAAkD,EAAE,AAAW5B,GAAQ,EAAE;QAC1G,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACuB,oBAAoB,CAAC1B,QAAQ2B;QAEtE,OAAOC;IACT;IAEA,KAAK;IACL,MAEMC,uBAAuB,AAAQC,QAAa,EAAE,AAAW/B,GAAQ,EAAE;QACvE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC0B,sBAAsB,CAAC7B,QAAQ8B;QAExE,OAAOF;IACT;IAEA,MAEMG,2BAA2B,AAAWhC,GAAQ,EAAE;QACpD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM8B,YAAY,MAAM,IAAI,CAAC7B,YAAY,CAAC4B,0BAA0B,CAAC/B;QAErE,OAAOgC;IACT;IAEA,MAEMC,uBAAuB,AAAaC,EAAU,EAAE,AAAWnC,GAAQ,EAAE;QACzEoC,QAAQC,GAAG,CAAC,4BAA4BrC,IAAIE,IAAI;QAChDkC,QAAQC,GAAG,CAAC,mCAAmCrC,IAAIE,IAAI,EAAED;QACzDmC,QAAQC,GAAG,CAAC,qCAAqCF;QAEjD,MAAMlC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACXmC,QAAQC,GAAG,CAAC;YACZ,MAAM,IAAIlC,MAAM;QAClB;QAEA,MAAMmC,aAAaC,SAASJ,IAAI;QAChC,IAAIK,MAAMF,aAAa;YACrBF,QAAQC,GAAG,CAAC,uCAAuCF;YACnD,MAAM,IAAIhC,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,iCAAiC,EAAEC,WAAW,cAAc,EAAErC,QAAQ;QACnF,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC8B,sBAAsB,CAACI,YAAYrC;QAE5E,OAAO4B;IACT;IAEA,MAEMY,sBAAsB,AAAaN,EAAU,EAAE,AAAQO,UAAoC,EAAE,AAAW1C,GAAQ,EAAE;QACtH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAMmC,aAAaC,SAASJ,IAAI;QAChC,IAAIK,MAAMF,aAAa;YACrB,MAAM,IAAInC,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACqC,qBAAqB,CAACH,YAAYI,YAAYzC;QAEvF,OAAO4B;IACT;IAEA,MAEMc,kBAAkB,AAAQC,qBAA4C,EAAE,AAAW5C,GAAQ,EAAE;QACjG,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACuC,iBAAiB,CAAC1C,QAAQ2C;QAEnE,OAAOf;IACT;IAEA,KAAK;IACL,MAEMgB,oBAAoB,AAAQd,QAAa,EAAE,AAAW/B,GAAQ,EAAE;QACpE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAE9B,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC0C,mBAAmB,CAAC7C,QAAQ8B;QAErE,OAAOF;IACT;IAGA,8BAA8B;IAC9B,MAEMkB,uBAAuB,AAAW/C,GAAQ,EAAE;QAChD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAM8B,YAAY,MAAM,IAAI,CAAC7B,YAAY,CAAC2C,sBAAsB,CAAC9C;QACjE,OAAOgC;IACT;IAEA,yEAAyE;IACzE,MAEMe,wBAAwB,AAAWhD,GAAQ,EAAE,AAASe,KAAU,EAAE;QACtE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,IAAI;YACFiC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAEpC,QAAQ;YAChFmC,QAAQC,GAAG,CAAC,CAAC,0BAA0B,CAAC,EAAEtB;YAE1C,MAAMkC,UAAU;gBACdC,MAAMnC,MAAMmC,IAAI,GAAGX,SAASxB,MAAMmC,IAAI,IAAI;gBAC1CC,OAAOpC,MAAMoC,KAAK,GAAGZ,SAASxB,MAAMoC,KAAK,IAAI;gBAC7CC,QAAQrC,MAAMqC,MAAM,IAAI;gBACxBC,WAAWtC,MAAMsC,SAAS;gBAC1BC,SAASvC,MAAMuC,OAAO;gBACtBC,MAAMxC,MAAMwC,IAAI;YAClB;YAEA,qBAAqB;YACrB,IAAIN,QAAQC,IAAI,GAAG,GAAGD,QAAQC,IAAI,GAAG;YACrC,IAAID,QAAQE,KAAK,GAAG,KAAKF,QAAQE,KAAK,GAAG,KAAKF,QAAQE,KAAK,GAAG;YAC9D,IAAI,CAAC;gBAAC;gBAAU;gBAAY;aAAM,CAACK,QAAQ,CAACP,QAAQG,MAAM,GAAG;gBAC3DH,QAAQG,MAAM,GAAG;YACnB;YAEAhB,QAAQC,GAAG,CAAC,CAAC,sBAAsB,CAAC,EAAEY;YAEtC,MAAMpC,SAAS,MAAM,IAAI,CAACT,YAAY,CAAC4C,uBAAuB,CAAC/C,QAAQgD;YAEvEb,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAExB,OAAOoB,SAAS,EAAEwB,UAAU,EAAE,WAAW,CAAC;YAE7F,OAAO;gBACLL,QAAQ;gBACRM,SAAS;gBACTC,MAAM9C;YACR;QAEF,EAAE,OAAO+C,OAAO;YACdxB,QAAQwB,KAAK,CAAC,CAAC,wDAAwD,EAAE3D,OAAO,CAAC,CAAC,EAAE2D;YACpFxB,QAAQwB,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAE7C;YACnD,MAAM6C;QACR;IACF;IAEA,2EAA2E;IAC3E,MAEMC,oBAAoB,AAAa1B,EAAU,EAAE,AAAWnC,GAAQ,EAAE;QACtE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,8BAA8B,EAAEF,GAAG,SAAS,EAAE,OAAOA,GAAG,CAAC,CAAC;QAEvE,gCAAgC;QAChC,IAAI,CAACA,MAAMA,GAAG2B,IAAI,OAAO,IAAI;YAC3B1B,QAAQwB,KAAK,CAAC;YACd,MAAM,IAAIzD,MAAM;QAClB;QAEA,+BAA+B;QAC/B,MAAMmC,aAAaC,SAASJ,GAAG2B,IAAI,IAAI;QACvC,IAAItB,MAAMF,eAAeA,cAAc,GAAG;YACxCF,QAAQwB,KAAK,CAAC,CAAC,6BAA6B,EAAEzB,GAAG,KAAK,EAAEG,YAAY;YACpE,MAAM,IAAInC,MAAM,CAAC,sBAAsB,EAAEgC,GAAG,8BAA8B,CAAC;QAC7E;QAEAC,QAAQC,GAAG,CAAC,CAAC,+BAA+B,EAAEC,WAAW,cAAc,EAAErC,QAAQ;QACjF,MAAM4B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAACyD,mBAAmB,CAACvB,YAAYrC;QAEzE,OAAO;YACLmD,QAAQ;YACRM,SAAS;YACTC,MAAM9B;QACR;IACF;IAEA,+BAA+B;IAG/BkC,wBAAwB,AAAW/D,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC2D,uBAAuB,CAAC9D,QAAQc;IAC3D;IAIAiD,8BAA8B,AAAWhE,GAAQ,EAAE;QACjD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6D,kCAAkC,CAAChE;IAC9D;IAEA,MAEMiE,mBAAmB,AAAa/B,EAAU,EAAE,AAAWnC,GAAQ,EAAE;QACrE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAMmC,aAAaC,SAASJ,IAAI;QAChC,IAAIK,MAAMF,aAAa;YACrB,MAAM,IAAInC,MAAM;QAClB;QACA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC8D,kBAAkB,CAAC5B,YAAYrC;QACxE,OAAO4B;IACT;IAEA,MAEMsC,sBAAsB,AAAahC,EAAU,EAAE,AAAWnC,GAAQ,EAAE;QACxE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAM0B,WAAW,MAAM,IAAI,CAACzB,YAAY,CAAC+D,qBAAqB,CAAChC,IAAIlC;QACnE,OAAO4B;IACT;IAEA,yBAAyB;IAEzB,sBAAsB;IACtB,MAEMuC,kBAAkB,AAAWpE,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC9D,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgE,iBAAiB,CAACnE,QAAQO;IACrD;IAEA,wBAAwB;IACxB,MAEM6D,oBAAoB,AAAWrE,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAChE,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiE,mBAAmB,CAACpE,QAAQO;IACvD;IAEA,yDAAyD;IACzD,MAEM8D,mBAAmB,AAAWtE,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,oDAAoD,EAAEpC,QAAQ;QAC3E,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACkE,kBAAkB,CAACrE;QAE1D,OAAO;YACLmD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAIA,+BAA+B;IAC/B,MAEM0D,mBAAmB,AAAajC,UAAkB,EAAE,AAAWtC,GAAQ,EAAE;QAC7E,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,sDAAsD,EAAEC,WAAW,UAAU,EAAErC,QAAQ;QACpG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACmE,kBAAkB,CAACtE,QAAQsC,SAASD;QAE3E,OAAO;YACLc,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,8BAA8B;IAC9B,MAEM2D,sBAAsB,AAAalC,UAAkB,EAAE,AAAQ9B,IAA4B,EAAE,AAAWR,GAAQ,EAAE;QACtH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAEC,WAAW,UAAU,EAAErC,QAAQ;QACvG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACoE,qBAAqB,CAACvE,QAAQsC,SAASD,aAAa9B,KAAK6C,SAAS;QAEzG,OAAO;YACLD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,gCAAgC;IAChC,MAEM4D,sBAAsB,AAAanC,UAAkB,EAAE,AAAWtC,GAAQ,EAAE;QAChF,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAEC,WAAW,UAAU,EAAErC,QAAQ;QACvG,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACqE,qBAAqB,CAACxE,QAAQsC,SAASD;QAE9E,OAAO;YACLc,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,+BAA+B;IAC/B,MAEM6D,yBAAyB,AAAapC,UAAkB,EAAE,AAAQ9B,IAA4B,EAAE,AAAWR,GAAQ,EAAE;QACzH,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,4DAA4D,EAAEC,WAAW,UAAU,EAAErC,QAAQ;QAC1G,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACsE,wBAAwB,CAACzE,QAAQsC,SAASD,aAAa9B,KAAK6C,SAAS;QAE5G,OAAO;YACLD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,gDAAgD;IAChD,MAEM8D,kBAAkB,AAAW3E,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9BmC,QAAQC,GAAG,CAAC,CAAC,kDAAkD,EAAEpC,QAAQ;QAEzE,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACuE,iBAAiB,CAAC1E;QACzD,OAAO;YACLmD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,yDAAyD;IACzD,MAEM+D,iBAAiB,AAAW5E,GAAQ,EAAE;QAC1C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9BmC,QAAQC,GAAG,CAAC,CAAC,2DAA2D,EAAEpC,QAAQ;QAElF,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAACwE,gBAAgB,CAAC3E;QACxD,OAAO;YACLmD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,oCAAoC;IACpC,MAEMgE,2BAA2B,AAAW7E,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEA,MAAM8C,UAAU;YACdC,MAAMnC,MAAMmC,IAAI,GAAGX,SAASxB,MAAMmC,IAAI,IAAI;YAC1CC,OAAOpC,MAAMoC,KAAK,GAAGZ,SAASxB,MAAMoC,KAAK,IAAI;YAC7CC,QAAQrC,MAAMqC,MAAM,IAAI;YACxBC,WAAWtC,MAAMsC,SAAS;YAC1BC,SAASvC,MAAMuC,OAAO;YACtBC,MAAMxC,MAAMwC,IAAI;QAClB;QAEAnB,QAAQC,GAAG,CAAC,CAAC,6DAA6D,EAAEpC,QAAQ,EAAEgD;QACtF,MAAMpC,SAAS,MAAM,IAAI,CAACT,YAAY,CAACyE,0BAA0B,CAAC5E,QAAQgD;QAE1E,OAAO;YACLG,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,qDAAqD;IACrD,MAEMiE,gBAAgB,AAAW9E,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QAEAiC,QAAQC,GAAG,CAAC,CAAC,+CAA+C,EAAEpC,QAAQ;QACtE,MAAMY,SAAS,MAAM,IAAI,CAACT,YAAY,CAAC2E,oBAAoB,CAAC9E;QAE5D,OAAO;YACLmD,QAAQ;YACRM,SAAS;YACTC,MAAM9C;QACR;IACF;IAEA,gDAAgD;IAGhDmE,qBAAqB,AAAWhF,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC7D,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6E,mBAAmB,CAAChF,QAAQc;IACvD;IAEA,2CAA2C;IAG3CmE,6BAA6B,AAAWlF,GAAQ,EAAE;QAChD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8E,4BAA4B,CAACjF;IACxD;IAEA,MAEMkF,0BAA0B,AAAWnF,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACtE,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+E,yBAAyB,CAAClF,QAAQO;IAC7D;IAEA,MAEM4E,yBAAyB,AAAWpF,GAAQ,EAAE,AAASe,KAAU,EAAE;QACvE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgF,wBAAwB,CAACnF,QAAQc;IAC5D;IAEA,mDAAmD;IACnD,MAEMsE,uCAAuC,AAAWrF,GAAQ,EAAE;QAChE,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiF,sCAAsC,CAACpF;IAClE;IAEA,6BAA6B;IAC7B,MAGMqF,wBACJ,AAAWtF,GAAQ,EACnB,AAAQQ,IAAS,EACjB,AAAiB+E,KAA4B,EAC7C;QACA,MAAMtF,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,MAAMqF,QAAQD,MAAME,IAAI,CAAC7E,CAAAA,OAAQA,KAAK8E,SAAS,KAAK;QACpD,MAAMC,OAAOJ,MAAME,IAAI,CAAC7E,CAAAA,OAAQA,KAAK8E,SAAS,KAAK;QACnD,MAAME,OAAOL,MAAME,IAAI,CAAC7E,CAAAA,OAAQA,KAAK8E,SAAS,KAAK;QAEnD,MAAMG,SAAS;YAAEL;YAAOG;YAAMC;QAAK;QAEnC,OAAO,IAAI,CAACxF,YAAY,CAACkF,uBAAuB,CAACrF,QAAQO,MAAMqF;IACjE;IAEA,qBAAqB;IACrB,MAEMC,mCAAmC,AAAW9F,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC/E,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC2F,mCAAmC,CAAC9F,QAAQO;IACvE;IAEA,0BAA0B;IAC1B,MAEMwF,uBAAuB,AAAWhG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACrE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC4F,sBAAsB,CAAC/F,QAAQc;IAC1D;IAEA,cAAc;IACd,MAEMkF,sBAAsB,AAAWjG,GAAQ,EAAE;QAC/C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6F,qBAAqB,CAAChG;IACjD;IAEA,oBAAoB;IACpB,MAEMiG,sBAAsB,AAAWlG,GAAQ,EAAE;QAC/C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8F,qBAAqB,CAACjG;IACjD;IAEA,gEAAgE;IAChE,MAEMkG,8BAA8B,AAAWnG,GAAQ,EAAE;QACvD,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+F,6BAA6B,CAAClG;IACzD;IAEA,oBAAoB;IACpB,MAEMmG,qBAAqB,AAAWpG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgG,oBAAoB,CAACnG,QAAQc;IACxD;IAEA,YAAY;IACZ,MAEMsF,gBAAgB,AAAWrG,GAAQ,EAAE;QACzC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACiG,eAAe,CAACpG;IAC3C;IAEA,6BAA6B;IAC7B,MAEMqG,qBAAqB,AAAWtG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACkG,oBAAoB,CAACrG,QAAQc;IACxD;IAEA,oBAAoB;IACpB,MAEMwF,oBAAoB,AAAWvG,GAAQ,EAAE,AAASe,KAAU,EAAE;QAClE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACmG,mBAAmB,CAACtG,QAAQc;IACvD;IAEA,sBAAsB;IACtB,MAEMyF,+BAA+B,AAAWxG,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC7E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACoG,8BAA8B,CAACvG,QAAQc;IAClE;IAEA,iBAAiB;IACjB,MAEM0F,0BAA0B,AAAWzG,GAAQ,EAAE,AAASe,KAAU,EAAE;QACxE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACqG,yBAAyB,CAACxG,QAAQc;IAC7D;IAEA,6CAA6C;IAC7C,MAEM2F,oBAAoB,AAAW1G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAClE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACsG,mBAAmB,CAACzG,QAAQc;IACvD;IAEA,MAEM4F,4BAA4B,AAAW3G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACuG,2BAA2B,CAAC1G,QAAQc;IAC/D;IAGA,mBAAmB;IACnB,MAEM6F,4BAA4B,AAAW5G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACwG,2BAA2B,CAAC3G,QAAQc;IAC/D;IAEA,kEAAkE;IAClE,MAEM8F,YAAY,AAAW7G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1D,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACyG,WAAW,CAAC5G,QAAQc;IAC/C;IAEA,8BAA8B;IAC9B,MAEM+F,4BAA4B,AAAW9G,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC1E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC0G,2BAA2B,CAAC7G,QAAQc;IAC/D;IAEA,6BAA6B;IAC7B,MAEMgG,2BAA2B,AAAW/G,GAAQ,EAAE,AAASe,KAAU,EAAE;QACzE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC2G,0BAA0B,CAAC9G,QAAQc;IAC9D;IAEA,oBAAoB;IACpB,MAEMiG,qBAAqB,AAAWhH,GAAQ,EAAE,AAASe,KAAU,EAAE;QACnE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC4G,oBAAoB,CAAC/G,QAAQc;IACxD;IAEA,4BAA4B;IAE5B,wBAAwB;IACxB,MAEMkG,aAAa,AAAWjH,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QACzD,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6G,YAAY,CAAChH,QAAQO;IAChD;IAEA,uBAAuB;IACvB,MAEM0G,kBAAkB,AAAWlH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8G,iBAAiB,CAACjH,QAAQc;IACrD;IAEA,sBAAsB;IACtB,MAEMoG,kBAAkB,AAAWnH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAChE,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+G,iBAAiB,CAAClH,QAAQc;IACrD;IAEA,8BAA8B;IAC9B,MAEMqG,kBAAkB,AAAWpH,GAAQ,EAAE,AAAaqH,SAAiB,EAAE;QAC3E,MAAMpH,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgH,iBAAiB,CAACnH,QAAQoH;IACrD;IAEA,2BAA2B;IAC3B,MAEMC,gBAAgB,AAAWtH,GAAQ,EAAE,AAAauH,SAAiB,EAAE,AAAQ/G,IAAS,EAAE;QAC5F,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACkH,eAAe,CAACrH,QAAQsH,WAAW/G;IAC9D;IAEA,yBAAyB;IACzB,MAEMgH,8BAA8B,AAAWxH,GAAQ,EAAE,AAASe,KAAU,EAAE;QAC5E,MAAMd,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACoH,6BAA6B,CAACvH,QAAQc;IACjE;IAGA,KAAK;IACL,MAEM0G,oBAAoB,AAAWzH,GAAQ,EAAE,AAASe,KAAU,EAAE,AAAQP,IAAS,EAAE;QACrF,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACqH,mBAAmB,CAACxH,QAAQc,OAAOP;IAC9D;IAEA,eAAe;IACf,MAEMkH,eAAe,AAAW1H,GAAQ,EAAE;QACxC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACsH,cAAc,CAACzH;IAC1C;IAEA,aAAa;IACb,MAEM0H,gBAAgB,AAAW3H,GAAQ,EAAE,AAAQQ,IAAS,EAAE;QAC5D,MAAMP,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACuH,eAAe,CAACnH,MAAMP;IACjD;IAEA,QAAQ;IACR,MAEM2H,YAAY,AAAW5H,GAAQ,EAAE;QACrC,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACwH,WAAW,CAAC3H;IACvC;IAEA,oBAAoB;IACpB,MAEM4H,cAAc,AAAW7H,GAAQ,EAAE,AAAiB8H,MAAc,EAAE;QACxE,MAAM7H,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACyH,aAAa,CAAC5H,QAAQ8H,OAAOD;IACxD;IAEA,gBAAgB;IAChB,MAEME,mBAAmB,AAAWhI,GAAQ,EAAE;QAC5C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC4H,kBAAkB,CAAC/H;IAC9C;IAEA,MAEMgI,mBAAmB,AAAWjI,GAAQ,EAAE,AAAamC,EAAU,EAAE;QACrE,MAAMlC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC6H,kBAAkB,CAACF,OAAO5F,KAAKlC;IAC1D;IAEA,MAEMiI,8BAA8B,AAAWlI,GAAQ,EAAE,AAAamC,EAAU,EAAE;QAChF,MAAMlC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC8H,6BAA6B,CAACH,OAAO5F,KAAKlC;IACrE;IAEA,eAAe;IACf,MAEMkI,kBAAkB,AAAWnI,GAAQ,EAAE;QAC3C,MAAMC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAAC+H,iBAAiB,CAAClI;IAC7C;IAEA,MAEMmI,wBAAwB,AAAWpI,GAAQ,EAAE,AAAamC,EAAU,EAAE;QAC1E,MAAMlC,SAASD,IAAIE,IAAI,CAACD,MAAM;QAC9B,IAAI,CAACA,QAAQ;YACX,MAAM,IAAIE,MAAM;QAClB;QACA,OAAO,IAAI,CAACC,YAAY,CAACgI,uBAAuB,CAACL,OAAO5F,KAAKlC;IAC/D;IA9pCFoI,YAAY,AAAiBjI,YAA0B,CAAC;aAA3BA,eAAAA;IAA4B;AA+pC3D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yDA/+B2E,yCAAA,OAAO,wCAAP,OAAO"}