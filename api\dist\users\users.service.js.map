{"version": 3, "sources": ["../../src/users/users.service.ts"], "sourcesContent": ["import { HttpException, HttpStatus, Injectable } from '@nestjs/common';\r\nimport { db } from '../database';\r\nimport { CreateClientDto } from '../dto/create-client.dto';\r\nimport * as bcrypt from 'bcryptjs';\r\nimport { DailyWaterDto } from './dto/daily-water.dto';\r\nimport { sql } from 'kysely';\r\n// dayjs\r\n// import { dayjs } from '../dayjs';\r\n// import dayjs from 'dayjs';\r\n// import * as dayjs from 'dayjs';\r\n// import { default as dayjs } from 'dayjs';\r\nconst dayjs = require('dayjs');\r\nconst timezone = require('dayjs/plugin/timezone');\r\nconst utc = require('dayjs/plugin/utc');\r\nconst isoWeek = require('dayjs/plugin/isoWeek');\r\ndayjs.extend(utc);\r\ndayjs.extend(timezone);\r\ndayjs.extend(isoWeek);\r\n\r\n// Interfaces para tipagem\r\nexport interface SleepData {\r\n  day: string;\r\n  hours: number;\r\n  quality: number;\r\n}\r\n\r\nexport interface WeeklyConsolidatedData {\r\n  day: string;\r\n  date: string;\r\n  nutrition: {\r\n    completed: number;\r\n    total: number;\r\n    percentage: number;\r\n  };\r\n  workout: {\r\n    completed: number;\r\n    calories_burned: number;\r\n    target: number;\r\n    percentage: number;\r\n  };\r\n  water: {\r\n    consumed: number;\r\n    target: number;\r\n    percentage: number;\r\n  };\r\n  sleep: {\r\n    hours: number;\r\n    quality: number;\r\n    target: number;\r\n    percentage: number;\r\n  };\r\n}\r\nimport { DailyWorkoutsActivitiesDto } from './dto/daily-workouts-activities.dto';\r\nimport { CreateProtocolWorkoutDto } from './dto/create-protocol-workout.dto';\r\nimport { CreateProtocolDietDto } from './dto/create-protocol-diet.dto';\r\nimport * as fs from 'fs';\r\nimport * as path from 'path';\r\nimport sharp from 'sharp';\r\nimport { randomUUID } from 'crypto';\r\nimport { convertToUTC } from 'src/common/utils/date.util';\r\n\r\nimport OpenAI from 'openai';\r\n\r\nimport { v4 as uuidv4 } from 'uuid';\r\n\r\nimport * as os from 'os';\r\n\r\nimport * as fsPromises from 'fs/promises';\r\nimport * as fsExtra from 'fs-extra';\r\nimport axios from 'axios';\r\nimport { jsonrepair } from 'jsonrepair';\r\nimport { Stripe } from 'stripe';\r\n\r\n@Injectable()\r\nexport class UsersService {\r\n    private stripe: Stripe;\r\n    private openai: OpenAI;\r\n\r\n    constructor() {\r\n      // @ts-ignore\r\n      this.stripe = new Stripe(process.env.STRIPE_SK, {\r\n        // apiVersion: '2025-04-30',\r\n      });\r\n\r\n      this.openai = new OpenAI({\r\n        apiKey: process.env.OPENAI_API_KEY,\r\n      });\r\n    }\r\n\r\n\r\n    async create(createClientDto: CreateClientDto, userId: number, role: string) {\r\n        const checkUser = await db\r\n            .selectFrom('users')\r\n            .where('email', '=', createClientDto.email)\r\n            .select('id')\r\n            .executeTakeFirst();\r\n\r\n            if (checkUser) {\r\n                throw new HttpException({\r\n                    status: 400,\r\n                    message: ['O e-mail já está cadastrado.'],\r\n                }, 400);\r\n            }\r\n\r\n\r\n        const roles = {\r\n            'admin': 1,\r\n            'coach': 2,\r\n            'nutritionist': 3,\r\n            'user': 4\r\n        };\r\n\r\n        const role_id: number = roles[role];\r\n\r\n        const hashedPassword = await bcrypt.hash(createClientDto.password, 10);\r\n\r\n\r\n        const new_client = await db\r\n        .insertInto('users')\r\n        .values({...createClientDto, password: hashedPassword})\r\n        .executeTakeFirst();\r\n\r\n        // add role id\r\n        await db\r\n        .insertInto('users_roles')\r\n        .values({\r\n            user_id: Number(new_client.insertId),\r\n            role_id: Number(role_id),\r\n        })\r\n        .execute();\r\n\r\n        // client\r\n        await db\r\n        .insertInto('clients')\r\n        .values({\r\n            client_id: Number(new_client.insertId),\r\n            user_id: Number(userId),\r\n            role_id: Number(role_id),\r\n        })\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                id: Number(new_client.insertId),\r\n            },\r\n        };\r\n    }\r\n\r\n    async getMe(userId: number) {\r\n        const user: any = await db\r\n            .selectFrom('users as u')\r\n            .where('u.id', '=', userId)\r\n            .leftJoin('select_options as goal', 'goal.id', 'u.goal_id')\r\n            .select([\r\n                'u.id',\r\n                'u.name',\r\n                'u.email',\r\n                'u.username',\r\n                'u.phone',\r\n                'u.photo',\r\n                // 'u.created_at',\r\n                // 'u.updated_at',\r\n                'u.height',\r\n                'u.weight',\r\n                'u.bodyfat',\r\n                'goal.value_option as goal',\r\n                'u.date_of_birth',\r\n            ])\r\n            .groupBy('u.id')\r\n            .executeTakeFirst();\r\n\r\n            const dataFormat = {\r\n                id: user.id,\r\n                name: user.name,\r\n                email: user.email,\r\n                username: user.username,\r\n                phone: user.phone,\r\n                photo: user.photo,\r\n                height: parseFloat(Number(user.height).toFixed(2)) || 0,\r\n                weight: parseFloat(Number(user.weight).toFixed(2)) || 0,\r\n                bodyfat: parseFloat(Number(user.bodyfat).toFixed(2)) || 0,\r\n                goal: user.goal,\r\n                date_of_birth: user.date_of_birth,\r\n            };\r\n\r\n        return {\r\n            status: 'success',\r\n            data: dataFormat,\r\n        };\r\n    }\r\n\r\n    async getGoals() {\r\n      const goals = await db\r\n        .selectFrom('select_options')\r\n        .select(['id', 'value_option as name'])\r\n        .where('area_key', '=', 'goals')\r\n        .orderBy('sort_order')\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        data: goals,\r\n      };\r\n    }\r\n\r\n    async updateMe(userId: number, body: any) {\r\n      const {\r\n        name,\r\n        username,\r\n        phone,\r\n        height,\r\n        weight,\r\n        bodyfat,\r\n        goal_id,\r\n        date_of_birth,\r\n        photo\r\n      } = body;\r\n\r\n      // Validar apenas campos obrigatórios se fornecidos\r\n      if (height !== undefined && (height < 100 || height > 250)) {\r\n        throw new HttpException({\r\n          status: 400,\r\n          message: ['Altura deve estar entre 100 e 250 cm.'],\r\n        }, 400);\r\n      }\r\n\r\n      if (weight !== undefined && (weight < 30 || weight > 300)) {\r\n        throw new HttpException({\r\n          status: 400,\r\n          message: ['Peso deve estar entre 30 e 300 kg.'],\r\n        }, 400);\r\n      }\r\n\r\n      // Validar username se fornecido\r\n      if (username !== undefined) {\r\n        if (username.length < 3 || username.length > 30) {\r\n          throw new HttpException({\r\n            status: 400,\r\n            message: ['Username deve ter entre 3 e 30 caracteres.'],\r\n          }, 400);\r\n        }\r\n\r\n        // Verificar se username já existe\r\n        const existingUser = await db\r\n          .selectFrom('users')\r\n          .where('username', '=', username)\r\n          .where('id', '!=', userId)\r\n          .select('id')\r\n          .executeTakeFirst();\r\n\r\n        if (existingUser) {\r\n          throw new HttpException({\r\n            status: 400,\r\n            message: ['Este username já está em uso.'],\r\n          }, 400);\r\n        }\r\n      }\r\n\r\n      // Preparar dados para atualização (apenas campos fornecidos)\r\n      const updateData: any = {\r\n        updated_at: new Date(),\r\n      };\r\n\r\n      if (name !== undefined) updateData.name = name;\r\n      if (username !== undefined) updateData.username = username;\r\n      if (phone !== undefined) updateData.phone = phone;\r\n      if (height !== undefined) updateData.height = height;\r\n      if (weight !== undefined) updateData.weight = weight;\r\n      if (bodyfat !== undefined) updateData.bodyfat = bodyfat;\r\n      if (goal_id !== undefined) updateData.goal_id = goal_id;\r\n      if (date_of_birth !== undefined) updateData.date_of_birth = new Date(date_of_birth);\r\n      if (photo !== undefined) updateData.photo = photo;\r\n\r\n      await db\r\n        .updateTable('users')\r\n        .set(updateData)\r\n        .where('id', '=', userId)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        data: {\r\n          message: 'Perfil atualizado com sucesso'\r\n        },\r\n      };\r\n    }\r\n\r\n    async softDeleteAccount(userId: number) {\r\n      // Check if user exists and is not already deleted\r\n      const user = await db\r\n        .selectFrom('users')\r\n        .where('id', '=', userId)\r\n        .select(['id', 'deleted_at'])\r\n        .executeTakeFirst();\r\n\r\n      if (!user) {\r\n        throw new HttpException({\r\n          status: 404,\r\n          message: ['Usuário não encontrado.'],\r\n        }, 404);\r\n      }\r\n\r\n      if (user.deleted_at) {\r\n        throw new HttpException({\r\n          status: 400,\r\n          message: ['Conta já está marcada para exclusão.'],\r\n        }, 400);\r\n      }\r\n\r\n      // Set deleted_at timestamp for soft delete\r\n      await db\r\n        .updateTable('users')\r\n        .set({\r\n          deleted_at: new Date(),\r\n          updated_at: new Date(),\r\n        })\r\n        .where('id', '=', userId)\r\n        .execute();\r\n\r\n      // Remove all active user sessions\r\n      await db\r\n        .deleteFrom('user_auths')\r\n        .where('user_id', '=', userId)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        data: {\r\n          message: 'Conta marcada para exclusão. Você tem 30 dias para recuperá-la fazendo login novamente.',\r\n        },\r\n      };\r\n    }\r\n\r\n    async updateMePhoto(userId: number, file: Express.Multer.File) {\r\n      // Validar arquivo\r\n      const maxFileSize = 30 * 1024 * 1024; // 30 MB\r\n      if (file.size > maxFileSize) {\r\n        throw new HttpException({\r\n          status: 400,\r\n          message: ['O arquivo deve ter no máximo 30MB.'],\r\n        }, 400);\r\n      }\r\n\r\n      // Validar tipo de arquivo\r\n      const allowedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];\r\n      if (!allowedMimeTypes.includes(file.mimetype)) {\r\n        throw new HttpException({\r\n          status: 400,\r\n          message: ['Apenas arquivos de imagem são permitidos (JPEG, PNG, WebP).'],\r\n        }, 400);\r\n      }\r\n    \r\n      // Criar o diretório se não existir\r\n      const uploadDir = path.join('media', 'users', userId.toString(), 'profile');\r\n      await fs.promises.mkdir(uploadDir, { recursive: true });\r\n\r\n      // Gerar nome aleatório para o arquivo\r\n      const fileExtension = file.mimetype === 'image/png' ? 'png' : 'jpg';\r\n      const randomName = `profile-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`;\r\n      const filePath = path.join(uploadDir, randomName);\r\n    \r\n      // Verificar dimensões da imagem usando sharp\r\n      const image: any = sharp(file.buffer);\r\n      const metadata: any = await image.metadata();\r\n    \r\n      // Verificar se precisa de resize\r\n      const MAX_DIMENSION = 1080;\r\n      if (metadata.width > MAX_DIMENSION || metadata.height > MAX_DIMENSION) {\r\n        // Calcular proporção para manter aspect ratio\r\n        const resizeOptions: sharp.ResizeOptions = {\r\n          width: metadata.width > metadata.height ? MAX_DIMENSION : undefined,\r\n          height: metadata.height > metadata.width ? MAX_DIMENSION : undefined,\r\n          fit: 'inside', // Mantém proporção sem cortar\r\n          withoutEnlargement: true // Não aumenta se for menor que 1080\r\n        };\r\n    \r\n        // Aplicar resize e salvar\r\n        await image\r\n          .resize(resizeOptions)\r\n          .jpeg({ quality: 80 }) // Ajuste qualidade se necessário\r\n          .toFile(filePath);\r\n      } else {\r\n        // Se não precisa de resize, só salvar\r\n        await image\r\n          .jpeg({ quality: 80 })\r\n          .toFile(filePath);\r\n      }\r\n    \r\n      // Gerar URL da foto para salvar no banco\r\n      const photoUrl = `/media/users/${userId}/profile/${randomName}`;\r\n\r\n      // Atualizar o banco de dados com a nova URL da foto\r\n      await db\r\n        .updateTable('users')\r\n        .set({\r\n          photo: photoUrl,\r\n          updated_at: new Date(),\r\n        })\r\n        .where('id', '=', userId)\r\n        .execute();\r\n\r\n      return {\r\n        status: 'success',\r\n        message: 'Foto atualizada com sucesso',\r\n        data: {\r\n          photo: photoUrl,\r\n          filename: randomName,\r\n        }\r\n      };\r\n    }\r\n\r\n\r\n    async getDailyNutritionalSummary(query: any, userId: number) {\r\n      const { date_start, date_end } = query;\r\n\r\n      const tz = 'America/Sao_Paulo';\r\n      \r\n      const localStart = dayjs.tz(date_start || undefined, tz).startOf('day');\r\n      const localEnd = dayjs.tz(date_end || undefined, tz).endOf('day');\r\n      \r\n      const startOfDay = localStart.utc().toDate();\r\n      const endOfDay = localEnd.utc().toDate();\r\n\r\n        /*\r\n        Get goals from protocols active\r\n        - calories\r\n        - protein\r\n        - fat\r\n        - carbs\r\n        - fiber\r\n        - water        \r\n        */\r\n\r\n        const goals: any = await db\r\n        .selectFrom('nutritionist_protocols')\r\n        .select([\r\n            'goal_calories',\r\n            'goal_protein',\r\n            'goal_carbs',\r\n            'goal_fat',\r\n            'goal_water'\r\n        ])\r\n        .where('client_id', '=', userId)\r\n        .where((eb) => eb.or([\r\n            eb('started_at', '>=', startOfDay),\r\n            eb('started_at', '<=', endOfDay),\r\n        ]))\r\n        .where('ended_at', 'is', null)\r\n        .orderBy('started_at', 'desc')\r\n        .executeTakeFirst();\r\n\r\n        // water consumed\r\n        const water: any = await db\r\n        .selectFrom('daily_water')\r\n        .where('user_id', '=', userId)\r\n        .where('daily_at', '>=', startOfDay)\r\n        .where('daily_at', '<=', endOfDay)\r\n        // sum consumed\r\n        .select([\r\n            db.fn.sum('consumed').as('consumed')\r\n        ])\r\n        .groupBy('user_id')\r\n        .executeTakeFirst();\r\n\r\n        // daily_nutritionist_protocol\r\n        const daily_nutritionist_protocol = await db\r\n        .selectFrom('daily_meals as d')\r\n        .where('d.user_id', '=', userId)\r\n        .where('d.daily_at', '>=', startOfDay)\r\n        .where('d.daily_at', '<=', endOfDay)\r\n        .select([\r\n            'd.calories',\r\n            'd.protein',\r\n            'd.carbs',\r\n            'd.fat',\r\n        ])\r\n        .execute();\r\n\r\n        /*\r\n        // daily_meals daily_meals_foods\r\n        const daily_meals = await db\r\n        .selectFrom('daily_meals as d')\r\n        .leftJoin('daily_meals_foods as f', 'f.meal_id', 'd.id')\r\n        .leftJoin('foods as food', 'food.id', 'f.food_id')\r\n        .where('d.user_id', '=', userId)\r\n        .where('d.created_at', '>=', startOfDay)\r\n        .where('d.created_at', '<=', endOfDay)\r\n        .select([\r\n            'f.calories',\r\n            'f.protein',\r\n            'f.carbs',\r\n            'f.fat',\r\n        ])\r\n        .execute();\r\n        */\r\n\r\n        // daily_coach_protocol\r\n        const dailyCoachProtocol: any = await db\r\n        .selectFrom('daily_coach_protocol')\r\n        .select([\r\n            db.fn.sum('total_calories').as('total_calories'),\r\n        ])\r\n        .where('user_id', '=', userId)\r\n        .where('daily_at', '>=', startOfDay)\r\n        .where('daily_at', '<=', endOfDay)\r\n        .executeTakeFirst();\r\n\r\n        const dailyWorkoutsActivities: any = await db\r\n        .selectFrom('daily_workouts_activities')\r\n        .select([\r\n            db.fn.sum('calories').as('calories'),\r\n        ])\r\n        .where('user_id', '=', userId)\r\n        .where('daily_at', '>=', startOfDay)\r\n        .where('daily_at', '<=', endOfDay)\r\n        .executeTakeFirst();\r\n\r\n        const caloriesProtocolBurned = Number(dailyCoachProtocol?.total_calories);\r\n        const caloriesWorkoutsBurned = Number(dailyWorkoutsActivities?.calories);\r\n        const total_calories_burned = caloriesProtocolBurned + caloriesWorkoutsBurned;\r\n\r\n        // sum - garantir que os valores sejam sempre não-negativos\r\n        const sum_daily_nutritionist_protocol = daily_nutritionist_protocol.reduce((acc, curr) => {\r\n            // Validar e garantir que os valores sejam números válidos e não-negativos\r\n            const calories = Math.max(0, Number(curr.calories) || 0);\r\n            const protein = Math.max(0, Number(curr.protein) || 0);\r\n            const carbs = Math.max(0, Number(curr.carbs) || 0);\r\n            const fat = Math.max(0, Number(curr.fat) || 0);\r\n\r\n            acc.calories += calories;\r\n            acc.protein += protein;\r\n            acc.carbs += carbs;\r\n            acc.fat += fat;\r\n            return acc;\r\n        }, { calories: 0, protein: 0, carbs: 0, fat: 0 });\r\n\r\n        /*\r\n        const sum_daily_meals = daily_meals.reduce((acc: any, curr: any) => {\r\n            acc.calories += Number(curr.calories);\r\n            acc.protein += Number(curr.protein);\r\n            acc.carbs += Number(curr.carbs);\r\n            acc.fat += Number(curr.fat);\r\n            return acc;\r\n        }, { calories: 0, protein: 0, carbs: 0, fat: 0 });\r\n\r\n        const total = {\r\n            calories: sum_daily_nutritionist_protocol.calories + sum_daily_meals.calories,\r\n            protein: sum_daily_nutritionist_protocol.protein + sum_daily_meals.protein,\r\n            carbs: sum_daily_nutritionist_protocol.carbs + sum_daily_meals.carbs,\r\n            fat: sum_daily_nutritionist_protocol.fat + sum_daily_meals.fat,\r\n        }\r\n        */\r\n       const total = sum_daily_nutritionist_protocol;\r\n\r\n        // Se não há refeições registradas (total zerado), garantir que os valores sejam realmente zero\r\n        if (daily_nutritionist_protocol.length === 0 ||\r\n            (total.calories === 0 && total.protein === 0 && total.carbs === 0 && total.fat === 0)) {\r\n            total.calories = 0;\r\n            total.protein = 0;\r\n            total.carbs = 0;\r\n            total.fat = 0;\r\n        }\r\n\r\n        const calories_remaining: any = (Number(goals?.goal_calories) || 0) - (Number(total.calories) - Number(total_calories_burned));\r\n\r\n        const dataNutritionalSummary = {\r\n            calories: {\r\n                consumed: parseFloat(Number(total.calories).toFixed(2)) || 0,\r\n                burned: parseFloat(Number(total_calories_burned).toFixed(2)) || 0,\r\n                remaining: calories_remaining > 0 ? parseFloat(Number(calories_remaining).toFixed(2)) : 0,\r\n                goal: parseFloat(Number(goals?.goal_calories).toFixed(2)) || 0,\r\n            },\r\n            protein: {\r\n                protein: parseFloat(Number(total.protein).toFixed(2)) || 0,\r\n                goal: parseFloat(Number(goals?.goal_protein).toFixed(2)) || 0,\r\n            },\r\n            fat: {\r\n                fat: parseFloat(Number(total.fat).toFixed(2)) || 0,\r\n                goal: parseFloat(Number(goals?.goal_fat).toFixed(2)) || 0,\r\n            },\r\n            carbs: {\r\n                carbs: parseFloat(Number(total.carbs).toFixed(2)) || 0,\r\n                goal: parseFloat(Number(goals?.goal_carbs).toFixed(2)) || 0,\r\n            },\r\n            fiber: {\r\n                fiber: 0,\r\n                goal: 0,\r\n            },\r\n            water: {\r\n                water: parseFloat(Number(water?.consumed).toFixed(2)) || 0,\r\n                goal: parseFloat(Number(goals?.goal_water).toFixed(2)) || 0,\r\n            },\r\n        }        \r\n\r\n        return {\r\n            status: 'success',\r\n            data: dataNutritionalSummary,\r\n        }\r\n    }\r\n\r\n\r\n    async getDaily(query: any, userId: number) {\r\n        const { date } = query;\r\n\r\n        const startOfDay = dayjs(date).startOf('day').toDate();\r\n        const endOfDay = dayjs(date).endOf('day').toDate();\r\n\r\n        // sum water of this date\r\n        const water = await db\r\n                .selectFrom('daily_water')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                // sum consumed\r\n                .select([\r\n                    db.fn.sum('consumed').as('consumed')\r\n                ])\r\n                .groupBy('user_id')\r\n                .executeTakeFirst();\r\n\r\n        const waterConsumed = water?.consumed || 0;\r\n\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                activity: {\r\n                    device_source: 'Apple Health',\r\n                    steps: {\r\n                        steps: 0,\r\n                        goal: 0,\r\n                    },\r\n                    active: {\r\n                        minutes: 0,\r\n                        goal: 0,\r\n                    },\r\n                    calories: {  \r\n                        calories: 0,\r\n                        goal: 0,\r\n                    },\r\n                    },\r\n                    heart: {\r\n                        heart: 0,\r\n                        default_min: 60,\r\n                        default_max: 120,\r\n                    },\r\n                    nutritional_summary: {\r\n                        calories: {\r\n                            consumed: 0,\r\n                            burned: 0,\r\n                            remaining: 0,\r\n                            goal: 0\r\n                        },\r\n                        protein: {\r\n                            protein: 0,\r\n                            goal: 0,\r\n                        },\r\n                        fat: {\r\n                            fat: 0,\r\n                            goal: 0,\r\n                        },\r\n                        carbs: {\r\n                            carbs: 0,\r\n                            goal: 0,\r\n                        },\r\n                        fiber: {\r\n                            fiber: 0,\r\n                            goal: 0,\r\n                        },\r\n                    },\r\n                    water: {\r\n                        water: waterConsumed,\r\n                        goal: 0,\r\n                    },\r\n                }\r\n                \r\n            }\r\n            \r\n        }\r\n\r\n        getFirstName(name: string) {\r\n            const names = name.split(' ');\r\n            return names[0];\r\n        }\r\n\r\n        async getAssessments(userId: number) {\r\n        const user = await db\r\n        .selectFrom('users')\r\n        .where('id', '=', userId)\r\n        .select([\r\n          'name'\r\n        ])\r\n        .groupBy('id')\r\n        .executeTakeFirst();\r\n        \r\n        const assessmentsData = {\r\n            name: user?.name ? this.getFirstName(user.name) : '',\r\n            assessments: {\r\n                days_met_goals: 0,\r\n                attendance: {\r\n                    weekly: 0,\r\n                    monthly: 0,\r\n                    streak: 0,\r\n                    record_streak: 0,\r\n                },\r\n                weekly_diet_progress: {\r\n                    weekly: [\r\n                        {\r\n                            day: 'Seg',\r\n                            completed: 0,\r\n                            total: 0\r\n                        },\r\n                        {\r\n                            day: 'Ter',\r\n                            completed: 0,\r\n                            total: 0\r\n                        },\r\n                        {\r\n                            day: 'Qua',\r\n                            completed: 0,\r\n                            total: 0\r\n                        },\r\n                        {\r\n                            day: 'Qui',\r\n                            completed: 1,\r\n                            total: 1\r\n                        },\r\n                        {\r\n                            day: 'Sex',\r\n                            completed: 0,\r\n                            total: 0\r\n                        },\r\n                        {\r\n                            day: 'Sab',\r\n                            completed: 0,\r\n                            total: 0\r\n                        },\r\n                        {\r\n                            day: 'Dom',\r\n                            completed: 0,\r\n                            total: 1\r\n                        }\r\n                    ],\r\n                    success_rate: 0,\r\n                    complete_meals: 0,\r\n                    perfect_days: 0,\r\n                }\r\n        }\r\n        }\r\n\r\n        return {\r\n            status: 'success',\r\n            data: assessmentsData,\r\n        };\r\n    \r\n    }\r\n\r\n    // Daily\r\n    async postDailyWater(user_id: number, dailyWaterDto: DailyWaterDto) {\r\n        const { consumed, daily_at } = dailyWaterDto;\r\n\r\n        const dailyAt = dayjs(daily_at).startOf('day').toDate();\r\n\r\n        const daily = await db\r\n        .insertInto('daily_water')\r\n        .values({\r\n            user_id,\r\n            consumed,\r\n            daily_at: dailyAt,\r\n            created_at: new Date(),\r\n            updated_at: new Date(),\r\n        })\r\n        .execute();\r\n\r\n        return {\r\n            \"status\": \"success\",\r\n            \"data\": []\r\n        }\r\n    }\r\n\r\n    async getDailyWorkoutsActivities() {\r\n        const dailyWorkoutsActivities = await db\r\n        .selectFrom('workouts_activities')\r\n        .select(['id', 'name'])\r\n        .orderBy('sort_order', 'asc')\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: dailyWorkoutsActivities,\r\n        };\r\n    }\r\n\r\n    // Daily Workouts Activities\r\n    async getDailyWorkoutsActivitiesByDate(userId: number, query: any) {\r\n        const { date_start, date_end } = query;\r\n        // if date_start and date_end are not provided, use current date\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n        const dailyWorkoutsActivities = await db\r\n        .selectFrom('daily_workouts_activities as wa')\r\n        .leftJoin('workouts_activities as w', 'w.id', 'wa.activity_id')\r\n        .where('wa.user_id', '=', userId)\r\n        .where('wa.daily_at', '>=', startOfDay)\r\n        .where('wa.daily_at', '<=', endOfDay)\r\n        .select([\r\n            'wa.id',\r\n            'w.name',\r\n            'wa.activity_time',\r\n            'wa.calories',\r\n        ])\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: dailyWorkoutsActivities,\r\n        };\r\n    }\r\n\r\n    // minutes to HH:MM:SS\r\n    minutesToHHMMSS(minutes: number): string {\r\n        const hours = Math.floor(minutes / 60);\r\n        const remainingMinutes = minutes % 60;\r\n        const seconds = 0;\r\n        return `${hours}:${remainingMinutes}:${seconds}`;\r\n    }\r\n\r\n    async postDailyWorkoutsActivities(userId: number, dailyWorkoutsActivitiesDto: DailyWorkoutsActivitiesDto) {\r\n        const { id, duration } = dailyWorkoutsActivitiesDto;\r\n\r\n        const workoutsActivities = await db\r\n        .selectFrom('workouts_activities')\r\n        .where('id', '=', id)\r\n        .select(['calories'])\r\n        .executeTakeFirst();\r\n\r\n        if (!workoutsActivities) {\r\n            return {\r\n                status: 'error',\r\n                message: 'Workouts activity not found',\r\n            };\r\n        }\r\n\r\n        // duration in minutes\r\n        const calculated_calories = duration * (workoutsActivities.calories/60);\r\n\r\n        const dailyWorkoutsActivities = await db\r\n        .insertInto('daily_workouts_activities')\r\n        .values({\r\n            user_id: userId,\r\n            activity_id: id,\r\n            activity_time: this.minutesToHHMMSS(duration),\r\n            calories: calculated_calories\r\n        })\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: [],\r\n        };\r\n    }\r\n\r\n    async hasActiveProtocolWorkout(userId: number) {\r\n        const today = new Date();\r\n\r\n        console.log(`🔍 hasActiveProtocolWorkout: Verificando protocolo ativo para usuário ${userId}`);\r\n        console.log(`📅 Data atual: ${today.toISOString()}`);\r\n\r\n        const protocol = await db\r\n            .selectFrom('coach_protocols')\r\n            .selectAll()\r\n            .where('client_id', '=', userId)\r\n            .where('started_at', '<=', today)\r\n            .where('ended_at', 'is', null)\r\n            .orderBy('started_at', 'desc') // Caso existam múltiplos, pega o mais recente\r\n            .executeTakeFirst();\r\n\r\n        if (protocol) {\r\n            console.log(`✅ hasActiveProtocolWorkout: Protocolo ativo encontrado - ID: ${protocol.id}, Nome: ${protocol.name}`);\r\n            console.log(`📊 Detalhes: started_at=${protocol.started_at}, ended_at=${protocol.ended_at}`);\r\n            return true;\r\n        } else {\r\n            console.log(`❌ hasActiveProtocolWorkout: Nenhum protocolo ativo encontrado`);\r\n            return false;\r\n        }\r\n    }\r\n\r\n    async hasActiveProtocolDiet(userId: number) {\r\n        const today = new Date();\r\n\r\n        console.log(`🔍 hasActiveProtocolDiet: Verificando protocolo ativo para usuário ${userId}`);\r\n        console.log(`📅 Data atual: ${today.toISOString()}`);\r\n\r\n        // CORREÇÃO CRÍTICA: Usar a mesma query da exibição para garantir consistência absoluta\r\n        const protocol = await db\r\n            .selectFrom('nutritionist_protocols as p')\r\n            .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n            .select(['p.id', 'p.name', 'p.started_at', 'p.ended_at', 's.value_option as type', 'p.type_id'])\r\n            .where('p.client_id', '=', userId)\r\n            .where('p.started_at', '<=', today)\r\n            .where('p.ended_at', 'is', null)\r\n            .orderBy('p.started_at', 'desc')\r\n            .executeTakeFirst();\r\n\r\n        if (protocol) {\r\n            console.log(`✅ hasActiveProtocolDiet: Protocolo ativo encontrado - ID: ${protocol.id}, Nome: ${protocol.name}`);\r\n            console.log(`📊 Detalhes: started_at=${protocol.started_at}, ended_at=${protocol.ended_at}, type=${protocol.type || 'sem tipo'}, type_id=${protocol.type_id}`);\r\n\r\n            // Aviso se o protocolo não tem type válido, mas ainda considera como ativo\r\n            if (!protocol.type) {\r\n                console.warn(`⚠️ hasActiveProtocolDiet: Protocolo ${protocol.id} não tem type_id válido, mas será considerado ativo`);\r\n            }\r\n            return true;\r\n        } else {\r\n            console.log(`❌ hasActiveProtocolDiet: Nenhum protocolo ativo encontrado`);\r\n\r\n            // DEBUG: Verificar se há protocolos órfãos\r\n            const orphanProtocols = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(['p.id', 'p.name', 'p.type_id', 's.value_option as type'])\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.started_at', '<=', today)\r\n                .where('p.ended_at', 'is', null)\r\n                .where('s.id', 'is', null) // Protocolos com type_id inválido\r\n                .execute();\r\n\r\n            if (orphanProtocols.length > 0) {\r\n                console.warn(`⚠️ hasActiveProtocolDiet: Encontrados ${orphanProtocols.length} protocolos órfãos (type_id inválido):`, orphanProtocols);\r\n\r\n                // AUTO-CORREÇÃO: Marcar protocolos órfãos como finalizados\r\n                for (const orphan of orphanProtocols) {\r\n                    await db\r\n                        .updateTable('nutritionist_protocols')\r\n                        .set({ ended_at: today, updated_at: today })\r\n                        .where('id', '=', orphan.id)\r\n                        .execute();\r\n\r\n                    console.log(`🔧 hasActiveProtocolDiet: Protocolo órfão ${orphan.id} automaticamente finalizado`);\r\n                }\r\n            }\r\n\r\n            return false;\r\n        }\r\n    }\r\n\r\n    // MÉTODO TEMPORÁRIO PARA CORREÇÃO DE PROTOCOLOS ÓRFÃOS\r\n    async fixOrphanProtocols(userId: number) {\r\n        console.log(`🔧 fixOrphanProtocols: Iniciando correção para usuário ${userId}`);\r\n\r\n        const today = new Date();\r\n        const results = {\r\n            orphansFound: 0,\r\n            orphansFixed: 0,\r\n            consistencyRestored: false,\r\n            details: []\r\n        };\r\n\r\n        try {\r\n            // 1. Identificar protocolos órfãos\r\n            const orphanProtocols = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(['p.id', 'p.name', 'p.type_id', 'p.started_at', 'p.ended_at'])\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.ended_at', 'is', null)\r\n                .where('s.id', 'is', null) // type_id inválido\r\n                .execute();\r\n\r\n            results.orphansFound = orphanProtocols.length;\r\n            console.log(`🔍 Encontrados ${orphanProtocols.length} protocolos órfãos`);\r\n\r\n            if (orphanProtocols.length > 0) {\r\n                // 2. Finalizar protocolos órfãos\r\n                for (const orphan of orphanProtocols) {\r\n                    await db\r\n                        .updateTable('nutritionist_protocols')\r\n                        .set({\r\n                            ended_at: today,\r\n                            updated_at: today\r\n                        })\r\n                        .where('id', '=', orphan.id)\r\n                        .execute();\r\n\r\n                    results.orphansFixed++;\r\n                    (results.details as any[]).push({\r\n                        id: orphan.id,\r\n                        name: orphan.name,\r\n                        type_id: orphan.type_id,\r\n                        action: 'finalizado'\r\n                    });\r\n\r\n                    console.log(`✅ Protocolo órfão finalizado: ID ${orphan.id}, Nome: ${orphan.name}`);\r\n                }\r\n            }\r\n\r\n            // 3. Verificar consistência após correção\r\n            const validationCount = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(db.fn.count('id').as('count'))\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.started_at', '<=', today)\r\n                .where('p.ended_at', 'is', null)\r\n                .where('s.id', 'is not', null)\r\n                .executeTakeFirst();\r\n\r\n            const displayCount = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(db.fn.count('id').as('count'))\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.started_at', '<=', today)\r\n                .where('p.ended_at', 'is', null)\r\n                .executeTakeFirst();\r\n\r\n            results.consistencyRestored = Number(validationCount?.count || 0) === Number(displayCount?.count || 0);\r\n\r\n            console.log(`📊 Pós-correção: Validação=${validationCount?.count || 0}, Exibição=${displayCount?.count || 0}`);\r\n            console.log(`✅ Consistência restaurada: ${results.consistencyRestored}`);\r\n\r\n            return results;\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro na correção de protocolos órfãos:`, error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // REGRA OTIMIZADA: MANTER APENAS UM PROTOCOLO ATIVO POR VEZ\r\n    async ensureSingleActiveProtocol(userId: number): Promise<{\r\n        activeProtocol: any | null;\r\n        deactivatedCount: number;\r\n        message: string\r\n    }> {\r\n        console.log(`🔧 ensureSingleActiveProtocol: Verificando protocolos ativos para usuário ${userId}`);\r\n\r\n        try {\r\n            // Buscar todos os protocolos ativos (ended_at = null)\r\n            const activeProtocols = await db\r\n                .selectFrom('nutritionist_protocols')\r\n                .select(['id', 'name', 'started_at', 'created_at'])\r\n                .where('client_id', '=', userId)\r\n                .where('ended_at', 'is', null)\r\n                .orderBy('started_at', 'desc') // Mais recente primeiro\r\n                .execute();\r\n\r\n            console.log(`📊 Encontrados ${activeProtocols.length} protocolos ativos`);\r\n\r\n            if (activeProtocols.length === 0) {\r\n                console.log(`✅ Nenhum protocolo ativo encontrado`);\r\n                return {\r\n                    activeProtocol: null,\r\n                    deactivatedCount: 0,\r\n                    message: 'Nenhum protocolo ativo encontrado'\r\n                };\r\n            }\r\n\r\n            if (activeProtocols.length === 1) {\r\n                console.log(`✅ Apenas um protocolo ativo encontrado: ${activeProtocols[0].name} (ID: ${activeProtocols[0].id})`);\r\n                return {\r\n                    activeProtocol: activeProtocols[0],\r\n                    deactivatedCount: 0,\r\n                    message: 'Protocolo único ativo mantido'\r\n                };\r\n            }\r\n\r\n            // Múltiplos protocolos ativos - manter apenas o mais recente\r\n            const mostRecentProtocol = activeProtocols[0]; // Primeiro da lista ordenada por started_at desc\r\n            const protocolsToDeactivate = activeProtocols.slice(1); // Todos os outros\r\n\r\n            console.log(`⚠️ Múltiplos protocolos ativos encontrados (${activeProtocols.length})`);\r\n            console.log(`✅ Mantendo ativo: ${mostRecentProtocol.name} (ID: ${mostRecentProtocol.id}, Started: ${mostRecentProtocol.started_at})`);\r\n            console.log(`❌ Desativando ${protocolsToDeactivate.length} protocolos mais antigos`);\r\n\r\n            // Finalizar protocolos mais antigos\r\n            const now = new Date();\r\n            let deactivatedCount = 0;\r\n\r\n            for (const protocol of protocolsToDeactivate) {\r\n                try {\r\n                    await db\r\n                        .updateTable('nutritionist_protocols')\r\n                        .set({\r\n                            ended_at: now,\r\n                            updated_at: now\r\n                        })\r\n                        .where('id', '=', protocol.id)\r\n                        .where('client_id', '=', userId)\r\n                        .execute();\r\n\r\n                    console.log(`🔄 Protocolo desativado: ${protocol.name} (ID: ${protocol.id})`);\r\n                    deactivatedCount++;\r\n                } catch (updateError) {\r\n                    console.error(`❌ Erro ao desativar protocolo ${protocol.id}:`, updateError);\r\n                }\r\n            }\r\n\r\n            console.log(`✅ Regra aplicada com sucesso: ${deactivatedCount} protocolos desativados, 1 mantido ativo`);\r\n\r\n            return {\r\n                activeProtocol: mostRecentProtocol,\r\n                deactivatedCount,\r\n                message: `Mantido protocolo mais recente ativo. ${deactivatedCount} protocolos antigos foram finalizados.`\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao aplicar regra de protocolo único:`, error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // HISTÓRICO DE PROTOCOLOS DE DIETA - VERSÃO ULTRA SIMPLIFICADA\r\n    async getProtocolsDietHistory(userId: number, options: {\r\n        page?: number;\r\n        limit?: number;\r\n        status?: 'active' | 'finished' | 'all';\r\n        startDate?: string;\r\n        endDate?: string;\r\n        type?: string;\r\n    } = {}) {\r\n        console.log(`📚 getProtocolsDietHistory: Iniciando busca para usuário ${userId}`, options);\r\n\r\n        try {\r\n            const {\r\n                page = 1,\r\n                limit = 10,\r\n                status = 'all'\r\n            } = options;\r\n\r\n            const offset = (page - 1) * limit;\r\n\r\n            console.log(`🔍 Parâmetros processados: page=${page}, limit=${limit}, offset=${offset}, status=${status}`);\r\n\r\n            // Query mais simples possível\r\n            console.log(`🔍 Executando query básica para nutritionist_protocols...`);\r\n\r\n            const protocols = await db\r\n                .selectFrom('nutritionist_protocols')\r\n                .select([\r\n                    'id',\r\n                    'name',\r\n                    'objective',\r\n                    'started_at',\r\n                    'ended_at',\r\n                    'created_at',\r\n                    'goal_calories',\r\n                    'goal_protein',\r\n                    'goal_carbs',\r\n                    'goal_fat'\r\n                ])\r\n                .where('client_id', '=', userId)\r\n                .orderBy('started_at', 'desc')\r\n                .limit(limit)\r\n                .offset(offset)\r\n                .execute();\r\n\r\n            console.log(`✅ Query básica executada. Encontrados ${protocols.length} protocolos`);\r\n\r\n            // Processar protocolos de forma simples\r\n            const processedProtocols = protocols.map(protocol => ({\r\n                id: protocol.id,\r\n                name: protocol.name || 'Protocolo sem nome',\r\n                objective: protocol.objective || '',\r\n                started_at: protocol.started_at,\r\n                ended_at: protocol.ended_at,\r\n                created_at: protocol.created_at,\r\n                goal_calories: protocol.goal_calories || 0,\r\n                goal_protein: protocol.goal_protein || 0,\r\n                goal_carbs: protocol.goal_carbs || 0,\r\n                goal_fat: protocol.goal_fat || 0,\r\n                type: 'Dieta',\r\n                status: protocol.ended_at ? 'finished' : 'active',\r\n                duration_days: protocol.ended_at && protocol.started_at\r\n                    ? Math.floor((new Date(protocol.ended_at).getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))\r\n                    : 0\r\n            }));\r\n\r\n            // Filtrar por status se necessário\r\n            const filteredProtocols = status === 'all'\r\n                ? processedProtocols\r\n                : processedProtocols.filter(p => p.status === status);\r\n\r\n            console.log(`📊 Protocolos após filtro de status: ${filteredProtocols.length}`);\r\n\r\n            // Contar total simples\r\n            const totalResult = await db\r\n                .selectFrom('nutritionist_protocols')\r\n                .select(db.fn.count('id').as('total'))\r\n                .where('client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            const total = Number(totalResult?.total || 0);\r\n\r\n            console.log(`📊 Total de protocolos no banco: ${total}`);\r\n\r\n            // Estatísticas básicas\r\n            const activeCount = processedProtocols.filter(p => p.status === 'active').length;\r\n            const finishedCount = processedProtocols.filter(p => p.status === 'finished').length;\r\n\r\n            const result = {\r\n                protocols: filteredProtocols,\r\n                pagination: {\r\n                    page,\r\n                    limit,\r\n                    total,\r\n                    totalPages: Math.ceil(total / limit),\r\n                    hasNext: page < Math.ceil(total / limit),\r\n                    hasPrev: page > 1\r\n                },\r\n                stats: {\r\n                    total: total,\r\n                    active: activeCount,\r\n                    finished: finishedCount,\r\n                    avgDuration: 0,\r\n                    topTypes: [{ type: 'Dieta', count: total }]\r\n                },\r\n                filters: {\r\n                    status,\r\n                    startDate: options.startDate,\r\n                    endDate: options.endDate,\r\n                    type: options.type\r\n                }\r\n            };\r\n\r\n            console.log(`✅ Histórico processado com sucesso:`, {\r\n                protocolsCount: result.protocols.length,\r\n                total: result.pagination.total,\r\n                page: result.pagination.page,\r\n                activeCount,\r\n                finishedCount\r\n            });\r\n\r\n            return result;\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao buscar histórico de protocolos para usuário ${userId}:`, error);\r\n            console.error(`❌ Stack trace:`, error.stack);\r\n            console.error(`❌ Parâmetros recebidos:`, { userId, options });\r\n\r\n            // Retornar uma resposta de erro mais amigável\r\n            throw new HttpException({\r\n                status: 500,\r\n                message: ['Erro interno ao buscar histórico de protocolos.'],\r\n                error: error.message\r\n            }, 500);\r\n        }\r\n    }\r\n\r\n    // ESTATÍSTICAS DE PROTOCOLOS DE DIETA\r\n    async getProtocolsDietStats(userId: number) {\r\n        try {\r\n            const today = new Date();\r\n\r\n            // Estatísticas simplificadas\r\n            const totalProtocols = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(db.fn.count('id').as('count'))\r\n                .where('p.client_id', '=', userId)\r\n                .where('s.id', 'is not', null)\r\n                .executeTakeFirst();\r\n\r\n            const activeProtocols = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(db.fn.count('id').as('count'))\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.ended_at', 'is', null)\r\n                .where('p.started_at', '<=', today)\r\n                .where('s.id', 'is not', null)\r\n                .executeTakeFirst();\r\n\r\n            const finishedProtocols = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select(db.fn.count('id').as('count'))\r\n                .where('p.client_id', '=', userId)\r\n                .where('p.ended_at', 'is not', null)\r\n                .where('s.id', 'is not', null)\r\n                .executeTakeFirst();\r\n\r\n            // Tipos mais usados\r\n            const topTypes = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select([\r\n                    's.value_option as type',\r\n                    db.fn.count('id').as('count')\r\n                ])\r\n                .where('p.client_id', '=', userId)\r\n                .where('s.id', 'is not', null)\r\n                .groupBy('s.value_option')\r\n                .orderBy('count', 'desc')\r\n                .limit(5)\r\n                .execute();\r\n\r\n            return {\r\n                total: Number(totalProtocols?.count || 0),\r\n                active: Number(activeProtocols?.count || 0),\r\n                finished: Number(finishedProtocols?.count || 0),\r\n                avgDuration: 0, // Será calculado no frontend se necessário\r\n                topTypes: topTypes.map(t => ({\r\n                    type: t.type,\r\n                    count: Number(t.count)\r\n                }))\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao calcular estatísticas:`, error);\r\n            return {\r\n                total: 0,\r\n                active: 0,\r\n                finished: 0,\r\n                avgDuration: 0,\r\n                topTypes: []\r\n            };\r\n        }\r\n    }\r\n\r\n    // FINALIZAR PROTOCOLO DE DIETA\r\n    async finishProtocolDiet(userId: number, protocolId: number) {\r\n        console.log(`🏁 finishProtocolDiet: Finalizando protocolo ${protocolId} para usuário ${userId}`);\r\n\r\n        try {\r\n            const today = new Date();\r\n\r\n            // Verificar se o protocolo existe e pertence ao usuário\r\n            const protocol = await db\r\n                .selectFrom('nutritionist_protocols')\r\n                .selectAll()\r\n                .where('id', '=', protocolId)\r\n                .where('client_id', '=', userId)\r\n                .where('ended_at', 'is', null)\r\n                .executeTakeFirst();\r\n\r\n            if (!protocol) {\r\n                throw new Error('Protocolo não encontrado ou já finalizado');\r\n            }\r\n\r\n            // Finalizar protocolo\r\n            const updatedProtocol = await db\r\n                .updateTable('nutritionist_protocols')\r\n                .set({\r\n                    ended_at: today,\r\n                    updated_at: today\r\n                })\r\n                .where('id', '=', protocolId)\r\n                .where('client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            console.log(`✅ Protocolo ${protocolId} finalizado com sucesso`);\r\n\r\n            return {\r\n                id: protocolId,\r\n                name: protocol.name,\r\n                ended_at: today,\r\n                duration_days: Math.floor((today.getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao finalizar protocolo:`, error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    // DUPLICAR PROTOCOLO DE DIETA - VERSÃO SIMPLIFICADA\r\n    async duplicateProtocolDiet(userId: number, protocolId: number, newStartDate?: string) {\r\n        console.log(`📋 duplicateProtocolDiet: Duplicando protocolo ${protocolId} para usuário ${userId}`);\r\n\r\n        try {\r\n            // Verificar se usuário já tem protocolo ativo\r\n            if (await this.hasActiveProtocolDiet(userId)) {\r\n                throw new Error('Você já possui um protocolo ativo. Finalize o protocolo atual antes de duplicar outro.');\r\n            }\r\n\r\n            // Buscar protocolo original\r\n            const originalProtocol = await db\r\n                .selectFrom('nutritionist_protocols')\r\n                .selectAll()\r\n                .where('id', '=', protocolId)\r\n                .where('client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            if (!originalProtocol) {\r\n                throw new Error('Protocolo não encontrado');\r\n            }\r\n\r\n            const startDate = newStartDate ? new Date(newStartDate) : new Date();\r\n            const today = new Date();\r\n\r\n            // Criar novo protocolo baseado no original (apenas dados básicos)\r\n            const newProtocol = await db\r\n                .insertInto('nutritionist_protocols')\r\n                .values({\r\n                    client_id: userId,\r\n                    name: `${originalProtocol.name} (Cópia)`,\r\n                    type_id: originalProtocol.type_id,\r\n                    objective: originalProtocol.objective,\r\n                    initial_weight: originalProtocol.initial_weight,\r\n                    general_notes: originalProtocol.general_notes,\r\n                    goal_calories: originalProtocol.goal_calories,\r\n                    goal_protein: originalProtocol.goal_protein,\r\n                    goal_carbs: originalProtocol.goal_carbs,\r\n                    goal_fat: originalProtocol.goal_fat,\r\n                    goal_water: originalProtocol.goal_water,\r\n                    started_at: startDate,\r\n                    created_at: today,\r\n                    updated_at: today\r\n                })\r\n                .executeTakeFirst();\r\n\r\n            const newProtocolId = Number(newProtocol.insertId);\r\n            console.log(`✅ Protocolo duplicado com sucesso. Novo ID: ${newProtocolId}`);\r\n\r\n            // Duplicar refeições do protocolo original\r\n            const originalMeals = await db\r\n                .selectFrom('nutritionist_protocols_meals')\r\n                .selectAll()\r\n                .where('protocol_id', '=', protocolId)\r\n                .execute();\r\n\r\n            let mealsCount = 0;\r\n            for (const meal of originalMeals) {\r\n                const newMeal = await db\r\n                    .insertInto('nutritionist_protocols_meals')\r\n                    .values({\r\n                        protocol_id: newProtocolId,\r\n                        name: meal.name,\r\n                        day_of_week: meal.day_of_week,\r\n                        meal_time: meal.meal_time,\r\n                        created_at: today,\r\n                        updated_at: today\r\n                    })\r\n                    .executeTakeFirst();\r\n\r\n                const newMealId = Number(newMeal.insertId);\r\n\r\n                // Duplicar alimentos da refeição\r\n                const originalFoods = await db\r\n                    .selectFrom('nutritionist_protocols_meals_foods')\r\n                    .selectAll()\r\n                    .where('meal_id', '=', meal.id)\r\n                    .execute();\r\n\r\n                for (const food of originalFoods) {\r\n                    await db\r\n                        .insertInto('nutritionist_protocols_meals_foods')\r\n                        .values({\r\n                            meal_id: newMealId,\r\n                            food_id: food.food_id,\r\n                            name: food.name,\r\n                            quantity: food.quantity,\r\n                            unit: food.unit,\r\n                            calories: food.calories,\r\n                            protein: food.protein,\r\n                            carbs: food.carbs,\r\n                            fat: food.fat,\r\n                            fiber: food.fiber,\r\n                            created_at: today,\r\n                            updated_at: today\r\n                        })\r\n                        .execute();\r\n                }\r\n                mealsCount++;\r\n            }\r\n\r\n            // Duplicar suplementos do protocolo original\r\n            const originalSupplements = await db\r\n                .selectFrom('nutritionist_protocols_supplements')\r\n                .selectAll()\r\n                .where('protocol_id', '=', protocolId)\r\n                .execute();\r\n\r\n            let supplementsCount = 0;\r\n            for (const supplement of originalSupplements) {\r\n                await db\r\n                    .insertInto('nutritionist_protocols_supplements')\r\n                    .values({\r\n                        protocol_id: newProtocolId,\r\n                        name: supplement.name,\r\n                        dosage: supplement.dosage,\r\n                        supplement_time: supplement.supplement_time,\r\n                        notes: supplement.notes,\r\n                        created_at: today,\r\n                        updated_at: today\r\n                    })\r\n                    .execute();\r\n                supplementsCount++;\r\n            }\r\n\r\n            console.log(`✅ Duplicação completa: ${mealsCount} refeições e ${supplementsCount} suplementos copiados`);\r\n\r\n            return {\r\n                id: newProtocolId,\r\n                name: `${originalProtocol.name} (Cópia)`,\r\n                originalId: protocolId,\r\n                started_at: startDate,\r\n                mealsCount,\r\n                supplementsCount\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao duplicar protocolo:`, error);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    async postProtocolsWorkout(userId: number, createProtocolWorkoutDto: CreateProtocolWorkoutDto) {\r\n      const { name, type_id, split, frequency, objective, general_notes, workouts } = createProtocolWorkoutDto;\r\n  \r\n      if (await this.hasActiveProtocolWorkout(userId)) {\r\n          throw new HttpException({\r\n              status: 400,\r\n              message: ['Você já possui um protocolo ativo.'],\r\n          }, 400);\r\n      }\r\n  \r\n      const new_protocol = await db\r\n          .insertInto('coach_protocols')\r\n          .values({\r\n              client_id: userId,\r\n              name,\r\n              type_id,\r\n              split,\r\n              frequency,\r\n              objective,\r\n              general_notes,\r\n              started_at: new Date(),\r\n              ended_at: null,\r\n              created_at: new Date(),\r\n              updated_at: new Date(),\r\n          })\r\n          .executeTakeFirst();\r\n  \r\n      const new_protocol_id = Number(new_protocol.insertId);\r\n  \r\n      if (Array.isArray(workouts)) {\r\n          const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n  \r\n          // Usar for...of para garantir a ordem\r\n          for (let index = 0; index < workouts.length; index++) {\r\n              const workout: any = workouts[index];\r\n              if (workout?.exercises && Array.isArray(workout.exercises)) {\r\n                  const workout_name = letters[index];\r\n                  const new_workout = await db\r\n                      .insertInto('coach_protocols_workouts')\r\n                      .values({\r\n                          protocol_id: new_protocol_id,\r\n                          name: `Treino ${workout_name}`,\r\n                      })\r\n                      .executeTakeFirst();\r\n  \r\n                  const new_workout_id = Number(new_workout.insertId);\r\n  \r\n                  // Garantir a ordem dos exercícios também\r\n                  for (const exercise of workout.exercises) {\r\n                      if (exercise.exercise_id) {\r\n                          await db\r\n                              .insertInto('coach_protocols_workouts_exercises')\r\n                              .values({\r\n                                  workout_id: new_workout_id,\r\n                                  exercise_id: exercise.exercise_id,\r\n                                  sets: exercise.sets,\r\n                                  reps: exercise.reps,\r\n                                  rpe: exercise.rpe,\r\n                                  rest_seconds: exercise.rest_seconds,\r\n                                  notes: exercise.notes || null,\r\n                              })\r\n                              .execute();\r\n                      }\r\n                  }\r\n              }\r\n          }\r\n      }\r\n  \r\n      return {\r\n          status: 'success',\r\n          data: [],\r\n      };\r\n  }\r\n\r\n  // ============================================================================\r\n  // WORKOUT PROTOCOL MANAGEMENT\r\n  // ============================================================================\r\n\r\n  /**\r\n   * Finalizar protocolo de treino\r\n   */\r\n  async finishProtocolWorkout(userId: number, protocolId: number) {\r\n      console.log(`🏁 finishProtocolWorkout: Finalizando protocolo ${protocolId} para usuário ${userId}`);\r\n\r\n      try {\r\n          const today = new Date();\r\n\r\n          // Verificar se o protocolo existe e pertence ao usuário\r\n          const protocol = await db\r\n              .selectFrom('coach_protocols')\r\n              .selectAll()\r\n              .where('id', '=', protocolId)\r\n              .where('client_id', '=', userId)\r\n              .where('ended_at', 'is', null)\r\n              .executeTakeFirst();\r\n\r\n          if (!protocol) {\r\n              throw new Error('Protocolo não encontrado ou já finalizado');\r\n          }\r\n\r\n          // Finalizar o protocolo\r\n          await db\r\n              .updateTable('coach_protocols')\r\n              .set({\r\n                  ended_at: today,\r\n                  updated_at: today\r\n              })\r\n              .where('id', '=', protocolId)\r\n              .where('client_id', '=', userId)\r\n              .execute();\r\n\r\n          console.log(`✅ Protocolo de treino ${protocolId} finalizado com sucesso`);\r\n\r\n          return {\r\n              id: protocolId,\r\n              name: protocol.name,\r\n              ended_at: today,\r\n              duration_days: Math.floor((today.getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24))\r\n          };\r\n\r\n      } catch (error) {\r\n          console.error(`❌ Erro ao finalizar protocolo de treino:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n  /**\r\n   * Duplicar protocolo de treino\r\n   */\r\n  async duplicateProtocolWorkout(userId: number, protocolId: number, newStartDate?: string) {\r\n      console.log(`📋 duplicateProtocolWorkout: Duplicando protocolo ${protocolId} para usuário ${userId}`);\r\n\r\n      try {\r\n          // Verificar se usuário já tem protocolo ativo\r\n          if (await this.hasActiveProtocolWorkout(userId)) {\r\n              throw new Error('Você já possui um protocolo de treino ativo. Finalize o protocolo atual antes de duplicar outro.');\r\n          }\r\n\r\n          // Buscar protocolo original\r\n          const originalProtocol = await db\r\n              .selectFrom('coach_protocols')\r\n              .selectAll()\r\n              .where('id', '=', protocolId)\r\n              .where('client_id', '=', userId)\r\n              .executeTakeFirst();\r\n\r\n          if (!originalProtocol) {\r\n              throw new Error('Protocolo não encontrado');\r\n          }\r\n\r\n          const startDate = newStartDate ? new Date(newStartDate) : new Date();\r\n          const today = new Date();\r\n\r\n          // Criar novo protocolo\r\n          const newProtocol = await db\r\n              .insertInto('coach_protocols')\r\n              .values({\r\n                  client_id: userId,\r\n                  name: `${originalProtocol.name} (Reutilizado)`,\r\n                  objective: originalProtocol.objective,\r\n                  type_id: originalProtocol.type_id,\r\n                  split: originalProtocol.split,\r\n                  frequency: originalProtocol.frequency,\r\n                  started_at: startDate,\r\n                  ended_at: null,\r\n                  created_at: today,\r\n                  updated_at: today\r\n              })\r\n              .executeTakeFirst();\r\n\r\n          const newProtocolId = Number(newProtocol.insertId);\r\n\r\n          // Duplicar workouts do protocolo\r\n          const originalWorkouts = await db\r\n              .selectFrom('coach_protocols_workouts')\r\n              .selectAll()\r\n              .where('protocol_id', '=', protocolId)\r\n              .execute();\r\n\r\n          for (const workout of originalWorkouts) {\r\n              const newWorkout = await db\r\n                  .insertInto('coach_protocols_workouts')\r\n                  .values({\r\n                      protocol_id: newProtocolId,\r\n                      name: workout.name,\r\n                      created_at: today,\r\n                      updated_at: today\r\n                  })\r\n                  .executeTakeFirst();\r\n\r\n              const newWorkoutId = Number(newWorkout.insertId);\r\n\r\n              // Duplicar exercícios do workout\r\n              const originalExercises = await db\r\n                  .selectFrom('coach_protocols_workouts_exercises')\r\n                  .selectAll()\r\n                  .where('workout_id', '=', workout.id)\r\n                  .execute();\r\n\r\n              for (const exercise of originalExercises) {\r\n                  await db\r\n                      .insertInto('coach_protocols_workouts_exercises')\r\n                      .values({\r\n                          workout_id: newWorkoutId,\r\n                          exercise_id: exercise.exercise_id,\r\n                          sets: exercise.sets,\r\n                          reps: exercise.reps,\r\n                          notes: exercise.notes,\r\n                          created_at: today,\r\n                          updated_at: today\r\n                      })\r\n                      .execute();\r\n              }\r\n          }\r\n\r\n          console.log(`✅ Protocolo de treino ${protocolId} duplicado com sucesso. Novo ID: ${newProtocolId}`);\r\n\r\n          return {\r\n              id: newProtocolId,\r\n              name: `${originalProtocol.name} (Reutilizado)`,\r\n              started_at: startDate,\r\n              original_protocol_id: protocolId\r\n          };\r\n\r\n      } catch (error) {\r\n          console.error(`❌ Erro ao duplicar protocolo de treino:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n\r\n\r\n  // ============================================================================\r\n  // DEBUG METHODS - TEMPORARY FOR DIAGNOSTICS\r\n  // ============================================================================\r\n\r\n  /**\r\n   * DEBUG: Verificar dados raw de protocolos no banco\r\n   */\r\n  async debugProtocolsRaw(userId: number) {\r\n      console.log(`🔍 DEBUG: debugProtocolsRaw para usuário ${userId}`);\r\n\r\n      try {\r\n          // Buscar todos os protocolos do usuário\r\n          const allProtocols = await db\r\n              .selectFrom('coach_protocols')\r\n              .selectAll()\r\n              .where('client_id', '=', userId)\r\n              .execute();\r\n\r\n          console.log(`🔍 DEBUG: Encontrados ${allProtocols.length} protocolos para usuário ${userId}`);\r\n\r\n          return {\r\n              userId,\r\n              totalProtocols: allProtocols.length,\r\n              protocols: allProtocols,\r\n              activeProtocols: allProtocols.filter(p => !p.ended_at),\r\n              finishedProtocols: allProtocols.filter(p => p.ended_at),\r\n              tables: {\r\n                  coach_protocols: allProtocols.length\r\n              }\r\n          };\r\n      } catch (error) {\r\n          console.error(`❌ DEBUG: Erro ao buscar protocolos raw:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n  /**\r\n   * DEBUG: Verificar dados raw de treinos completados no banco\r\n   */\r\n  async debugWorkoutsRaw(userId: number) {\r\n      console.log(`🔍 DEBUG: debugWorkoutsRaw para usuário ${userId}`);\r\n\r\n      try {\r\n          // Buscar todos os registros de treinos do usuário\r\n          const allWorkouts = await db\r\n              .selectFrom('daily_coach_protocol')\r\n              .selectAll()\r\n              .where('user_id', '=', userId)\r\n              .execute();\r\n\r\n          console.log(`🔍 DEBUG: Encontrados ${allWorkouts.length} registros de treinos para usuário ${userId}`);\r\n\r\n          const completedWorkouts = allWorkouts.filter(w => w.total_calories > 0);\r\n          const recentWorkouts = allWorkouts.filter(w => {\r\n              if (!w.daily_at) return false;\r\n              const workoutDate = new Date(w.daily_at);\r\n              const thirtyDaysAgo = new Date();\r\n              thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\r\n              return workoutDate >= thirtyDaysAgo;\r\n          });\r\n\r\n          return {\r\n              userId,\r\n              totalWorkouts: allWorkouts.length,\r\n              completedWorkouts: completedWorkouts.length,\r\n              recentWorkouts: recentWorkouts.length,\r\n              workouts: allWorkouts,\r\n              completedWorkoutsData: completedWorkouts,\r\n              recentWorkoutsData: recentWorkouts,\r\n              tables: {\r\n                  daily_coach_protocol: allWorkouts.length\r\n              }\r\n          };\r\n      } catch (error) {\r\n          console.error(`❌ DEBUG: Erro ao buscar treinos raw:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n  /**\r\n   * Buscar histórico de protocolos de treino\r\n   */\r\n  async getProtocolsWorkoutHistory(userId: number, options: any) {\r\n      console.log(`📚 getProtocolsWorkoutHistory: Buscando histórico para usuário ${userId}`, options);\r\n\r\n      try {\r\n          const { page = 1, limit = 10, status = 'all' } = options;\r\n          const offset = (page - 1) * limit;\r\n\r\n          // Base query\r\n          let query = db\r\n              .selectFrom('coach_protocols')\r\n              .selectAll()\r\n              .where('client_id', '=', userId)\r\n              .orderBy('created_at', 'desc');\r\n\r\n          // Apply status filter\r\n          if (status !== 'all') {\r\n              if (status === 'active') {\r\n                  query = query.where('ended_at', 'is', null);\r\n              } else if (status === 'finished') {\r\n                  query = query.where('ended_at', 'is not', null);\r\n              }\r\n          }\r\n\r\n          // Get total count\r\n          const totalResult = await query\r\n              .select(db.fn.count<number>('id').as('total'))\r\n              .executeTakeFirst();\r\n          const total = Number(totalResult?.total || 0);\r\n\r\n          // Get paginated results\r\n          const protocols = await query\r\n              .limit(limit)\r\n              .offset(offset)\r\n              .execute();\r\n\r\n          // Calculate stats\r\n          const stats = {\r\n              total,\r\n              active: protocols.filter(p => !p.ended_at).length,\r\n              finished: protocols.filter(p => p.ended_at).length,\r\n              avgDuration: 0, // Could calculate based on start/end dates\r\n              topTypes: []\r\n          };\r\n\r\n          // Format protocols for frontend\r\n          const formattedProtocols = protocols.map(protocol => ({\r\n              id: protocol.id,\r\n              name: protocol.name,\r\n              objective: protocol.objective,\r\n              started_at: protocol.started_at,\r\n              ended_at: protocol.ended_at,\r\n              created_at: protocol.created_at,\r\n              updated_at: protocol.updated_at,\r\n              type: protocol.type_id?.toString() || 'workout',\r\n              status: protocol.ended_at ? 'finished' : 'active',\r\n              duration_days: protocol.ended_at ?\r\n                  Math.floor((new Date(protocol.ended_at).getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24)) :\r\n                  Math.floor((new Date().getTime() - new Date(protocol.started_at).getTime()) / (1000 * 60 * 60 * 24)),\r\n              split: protocol.split,\r\n              frequency: protocol.frequency\r\n          }));\r\n\r\n          const pagination = {\r\n              page,\r\n              limit,\r\n              total,\r\n              totalPages: Math.ceil(total / limit),\r\n              hasNext: page < Math.ceil(total / limit),\r\n              hasPrev: page > 1\r\n          };\r\n\r\n          console.log(`✅ getProtocolsWorkoutHistory: Encontrados ${protocols.length} protocolos`);\r\n\r\n          return {\r\n              protocols: formattedProtocols,\r\n              pagination,\r\n              stats,\r\n              filters: {\r\n                  status,\r\n                  startDate: options.startDate,\r\n                  endDate: options.endDate,\r\n                  type: options.type\r\n              }\r\n          };\r\n\r\n      } catch (error) {\r\n          console.error(`❌ Erro ao buscar histórico de protocolos de treino:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n  /**\r\n   * Debug: Buscar protocolos raw do banco\r\n   */\r\n  async getProtocolsRawDebug(userId: number) {\r\n      console.log(`🔍 getProtocolsRawDebug: Buscando dados raw para usuário ${userId}`);\r\n\r\n      try {\r\n          // Buscar todos os protocolos do usuário\r\n          const protocols = await db\r\n              .selectFrom('coach_protocols')\r\n              .selectAll()\r\n              .where('client_id', '=', userId)\r\n              .orderBy('created_at', 'desc')\r\n              .execute();\r\n\r\n          console.log(`🔍 getProtocolsRawDebug: Encontrados ${protocols.length} protocolos`);\r\n\r\n          return {\r\n              total: protocols.length,\r\n              protocols: protocols.map(p => ({\r\n                  id: p.id,\r\n                  name: p.name,\r\n                  started_at: p.started_at,\r\n                  ended_at: p.ended_at,\r\n                  status: p.ended_at ? 'finished' : 'active',\r\n                  objective: p.objective,\r\n                  created_at: p.created_at\r\n              }))\r\n          };\r\n\r\n      } catch (error) {\r\n          console.error(`❌ Erro ao buscar protocolos raw:`, error);\r\n          throw error;\r\n      }\r\n  }\r\n\r\n  // Workout AI\r\n  async generateWorkoutProtocolPrompt(userInfo: any) {\r\n    const goalMapping = {\r\n      \"Hipertrofia\": 37,\r\n      \"Força\": 38,\r\n      \"Resistência\": 39,\r\n      \"Emagrecimento\": 103\r\n  };\r\n\r\n    const goalId = goalMapping[userInfo.goal] || 2; // Default para Hipertrofia\r\n    const frequency = userInfo.timeAvailable ? Math.floor(userInfo.timeAvailable / 60 * 3) : 4; // Calcula frequência baseada no tempo disponível\r\n\r\n    return `\r\n    # CONTEXTO OBRIGATÓRIO:\r\n    ## DADOS DO USUÁRIO:\r\n    - Altura: ${userInfo?.height || 'Não informada'} cm\r\n    - Peso: ${userInfo?.weight || 'Não informado'} kg\r\n    - % Gordura: ${userInfo?.bodyFat ? userInfo.bodyFat + '%' : 'Não medida'}\r\n    - Nível de Atividade: ${userInfo?.activityLevel}\r\n    - Objetivo: ${userInfo?.goal} (ID: ${goalId})\r\n    - Nível de Experiência com Musculação: ${userInfo?.experience || 'Não especificado'}\r\n    - Split Preferido: ${userInfo?.splitPreference || 'Não especificado'}\r\n    - Tempo Disponível por Sessão: ${userInfo?.timeAvailable || 60} minutos\r\n    - Equipamentos Disponíveis: ${userInfo?.equipment?.join(', ') || 'Nenhum equipamento específico'}\r\n    - Grupos Musculares Alvo: ${userInfo?.targetMuscles?.join(', ') || 'Todos os grupos musculares'}\r\n    - Restrições: ${userInfo?.restrictions?.join(', ') || 'Nenhuma restrição informada'}\r\n    - Preferências: ${userInfo?.preferences?.join(', ') || 'Nenhuma preferência específica'}\r\n\r\n    ${userInfo?.notes ? '## COMENTÁRIOS ADICIONAIS:\\n' + userInfo.notes : ''}\r\n\r\n    # TAREFA PRINCIPAL:\r\n    Como treinador IA especializado, crie um protocolo de treino personalizado considerando:\r\n    1. Objetivo do usuário (${userInfo?.goal})\r\n    2. Nível de experiência (${userInfo?.experience})\r\n    3. Disponibilidade de tempo (${userInfo?.timeAvailable} minutos por sessão)\r\n    4. Equipamentos disponíveis (${userInfo?.equipment?.join(', ')})\r\n    5. Grupos musculares alvo (${userInfo?.targetMuscles?.join(', ')})\r\n\r\n    # DIRETRIZES PARA O PROTOCOLO:\r\n    - Priorize exercícios compostos para usuários intermediários/avançados\r\n    - Inclua exercícios de isolamento conforme necessário\r\n    - Ajuste volume (séries/repetições) baseado no objetivo:\r\n      * Força: 3-6 séries de 1-6 repetições\r\n      * Hipertrofia: 3-5 séries de 6-12 repetições\r\n      * Resistência: 2-4 séries de 12-20+ repetições\r\n    - Defina descanso adequado:\r\n      * Força: 2-5 minutos\r\n      * Hipertrofia: 1-2 minutos\r\n      * Resistência: 30-60 segundos\r\n    - Use nomenclatura padrão de exercícios em português\r\n\r\n    # MODELO JSON OBRIGATÓRIO:\r\n    {\r\n        \"name\": \"[Nome criativo baseado no objetivo e split]\",\r\n        \"type_id\": ${goalId},\r\n        \"split\": \"[Split baseado na preferência do usuário]\",\r\n        \"frequency\": ${frequency}, // Frequência semanal de treinos (Apenas número de dias na semana)\r\n        \"objective\": \"Hipertrofia/Força/Resistência/Emagrecimento\",\r\n        \"general_notes\": \"[Recomendações personalizadas]\",\r\n        \"workouts\": [\r\n            {\r\n                \"name\": \"Treino [A/B/C/etc]\", // Usar letras sequenciais caso seja treino ABC. Aberto para outras convenções dependendo do split\r\n                \"exercises\": [\r\n                    {\r\n                        \"name\": \"[Nome do Exercício]\",\r\n                        \"sets\": [Número de séries],\r\n                        \"reps\": [Número de repetições ou intervalo],\r\n                        \"rpe\": [Escala de 1-10, opcional],\r\n                        \"rest_seconds\": [Tempo em segundos],\r\n                        \"notes\": \"[Instruções específicas, opcional]\"\r\n                    }\r\n                    // Continuar com outros exercícios\r\n                ]\r\n            }\r\n            // Continuar com outros treinos conforme a frequência\r\n        ]\r\n    }\r\n\r\n    # EXEMPLO MÍNIMO ESPERADO:\r\n    {\r\n        \"name\": \"Protocolo ABC\",\r\n        \"type_id\": 1,\r\n        \"split\": \"A-B-C\",\r\n        \"frequency\": 4,\r\n        \"objective\": \"Hipertrofia/Força/Resistência/Emagrecimento\",\r\n        \"general_notes\": \"Aumentar carga progressivamente a cada semana\",\r\n        \"workouts\": [\r\n            {\r\n                \"name\": \"Treino A\",\r\n                \"exercises\": [\r\n                    {\r\n                        \"name\": \"Supino reto\",\r\n                        \"sets\": 4,\r\n                        \"reps\": 4,\r\n                        \"rpe\": 8,\r\n                        \"rest_seconds\": 180,\r\n                        \"notes\": \"Manter forma estrita, 2s na fase excêntrica\"\r\n                    },\r\n                    {\r\n                        \"name\": \"Desenvolvimento com halteres\",\r\n                        \"sets\": 3,\r\n                        \"reps\": 6,\r\n                        \"rest_seconds\": 120\r\n                    }\r\n                ]\r\n            }\r\n            // Continuar com outros treinos\r\n        ]\r\n    }\r\n\r\n    # REGRAS ESSENCIAIS:\r\n    1. Gerar EXATAMENTE o número de treinos correspondente à frequência semanal\r\n    2. Cobrir TODOS os grupos musculares alvo do usuário\r\n    3. Variar exercícios para evitar monotonia\r\n    4. Usar nomenclatura de exercícios em português (ex: \"Agachamento livre\" em vez de \"Barbell squat\")\r\n    5. Incluir pelo menos 1 exercício composto por treino\r\n    6. Ajustar volume total conforme nível de experiência\r\n    7. Manter sessões dentro do tempo disponível (${userInfo?.timeAvailable || 60} minutos)\r\n\r\n    Retorne APENAS o JSON válido SEM comentários ou markdown.\r\n    `;\r\n}\r\n\r\nasync postProtocolsWorkoutAi(userId: number, userInfo: any) {\r\n    if (await this.hasActiveProtocolWorkout(userId)) {\r\n        throw new HttpException({\r\n            status: 400,\r\n            message: ['Você já possui um protocolo ativo.'],\r\n        }, 400);\r\n    }\r\n\r\n    try {\r\n        const prompt = await this.generateWorkoutProtocolPrompt(userInfo);\r\n        const response = await this.openai.chat.completions.create({\r\n            model: \"gpt-4o-mini\", // Optimized model for better performance\r\n            messages: [\r\n                {\r\n                    role: \"system\",\r\n                    content: \"You are a professional fitness coach. Create workout protocols in valid JSON format only, ensuring complete muscle group coverage and exercise safety.\"\r\n                },\r\n                { role: \"user\", content: prompt }\r\n            ],\r\n            temperature: 0.1, // Reduced for consistency\r\n            max_tokens: 2048, // Optimized for efficiency\r\n            response_format: { type: \"json_object\" }\r\n        });\r\n\r\n        const aiResponseText: any = response.choices[0].message.content;\r\n        const protocolDataText = this.extractJsonFromString(aiResponseText);\r\n        const protocolData = JSON.parse(protocolDataText);\r\n\r\n        if (!protocolData.name || !protocolData.type_id || !protocolData.workouts) {\r\n            throw new Error('Dados essenciais ausentes no protocolo gerado');\r\n        }\r\n\r\n        // Iniciar transação\r\n        const result = await db.transaction().execute(async (trx) => {\r\n            // Inserir protocolo\r\n            const new_protocol = await trx\r\n                .insertInto('coach_protocols')\r\n                .values({\r\n                    client_id: userId,\r\n                    name: protocolData.name,\r\n                    type_id: protocolData.type_id,\r\n                    split: protocolData.split,\r\n                    frequency: protocolData.frequency,\r\n                    objective: protocolData.objective,\r\n                    general_notes: protocolData.general_notes || null,\r\n                    started_at: new Date(),\r\n                    ended_at: null,\r\n                    created_at: new Date(),\r\n                    updated_at: new Date(),\r\n                })\r\n                .executeTakeFirst();\r\n\r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n\r\n            // Inserir treinos e exercícios\r\n            for (let index = 0; index < protocolData.workouts.length; index++) {\r\n                const workout = protocolData.workouts[index];\r\n                const workout_name = workout.name;\r\n\r\n                const new_workout = await trx\r\n                    .insertInto('coach_protocols_workouts')\r\n                    .values({\r\n                        protocol_id: new_protocol_id,\r\n                        name: workout_name,\r\n                    })\r\n                    .executeTakeFirst();\r\n\r\n                const new_workout_id = Number(new_workout.insertId);\r\n\r\n                if (workout.exercises && Array.isArray(workout.exercises)) {\r\n                    await trx\r\n                        .insertInto('coach_protocols_workouts_exercises')\r\n                        .values(workout.exercises.map(exercise => ({\r\n                            workout_id: new_workout_id,\r\n                            exercise_id: null, // Pode ser preenchido posteriormente com IDs de exercícios cadastrados\r\n                            name: exercise.name,\r\n                            sets: exercise.sets,\r\n                            reps: typeof exercise.reps === 'string' ? exercise.reps : exercise.reps.toString(),\r\n                            rpe: exercise.rpe || null,\r\n                            rest_seconds: exercise.rest_seconds || null,\r\n                            notes: exercise.notes || null,\r\n                        })))\r\n                        .execute();\r\n                }\r\n            }\r\n\r\n            return new_protocol_id;\r\n        });\r\n\r\n        return {\r\n            status: \"success\",\r\n            data: {\r\n                protocol_id: result,\r\n            },\r\n        };\r\n    } catch (error) {\r\n        console.error('Erro ao processar protocolo de treino:', {\r\n            message: error.message,\r\n            stack: error.stack,\r\n            input: userInfo\r\n        });\r\n        throw new HttpException({\r\n            status: \"error\",\r\n            message: ['Erro ao processar protocolo de treino.', error.message],\r\n        }, 500);\r\n    }\r\n}\r\n\r\n\r\n  \r\n\r\n    async getActiveProtocolsWorkouts(userId: number) {\r\n        const today = new Date();\r\n        const protocol = await db\r\n            .selectFrom('coach_protocols')\r\n            .selectAll()\r\n            .where('client_id', '=', userId)\r\n            .where('started_at', '<=', today)\r\n            .where('ended_at', 'is', null)\r\n            .orderBy('started_at', 'desc') // Caso existam múltiplos, pega o mais recente\r\n            .executeTakeFirst();\r\n    \r\n        if (!protocol) {\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    has_protocol: false,\r\n                }\r\n            };\r\n        }\r\n    \r\n        // Buscar todos os exercícios associados a esse protocolo\r\n        const exercises = await db\r\n            .selectFrom('coach_protocols_workouts as p')\r\n            .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')\r\n            .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')\r\n            .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')\r\n            .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')\r\n            .select([\r\n                'p.id',\r\n                'pe.id as pe_id',\r\n                'pe.exercise_id',\r\n                'p.name as workout_name',\r\n                'e.name as exercise_name',\r\n                'pe.name as exercise_name2',\r\n                's.value_option as muscle_group',\r\n                'equipment.value_option as equipment',\r\n                'e.media_url',\r\n                'pe.sets',\r\n                'pe.reps',\r\n                'pe.rpe',\r\n                'pe.rest_seconds',\r\n                'pe.notes',\r\n            ])\r\n            .where('protocol_id', '=', protocol.id)\r\n            // .where('pe.exercise_id', 'is not', null) // Evita exercícios inválidos\r\n            .execute();\r\n    \r\n        // Criar a estrutura de workouts agrupando os exercícios corretamente\r\n        const workouts = exercises.reduce((acc, curr) => {\r\n            const workoutName = curr.workout_name;\r\n    \r\n            if (!acc[workoutName]) {\r\n                acc[workoutName] = {\r\n                    name: workoutName,\r\n                    id: curr.id,\r\n                    exercises: [],\r\n                };\r\n            }\r\n    \r\n            acc[workoutName].exercises.push(curr);\r\n            return acc;\r\n        }, {});\r\n\r\n        const workoutsAndExercises = Object.values(workouts).map((workout: any) => ({\r\n            name: workout.name,\r\n            id: workout.id,\r\n            exercises: workout.exercises.map((exercise: any) => ({\r\n                id: exercise.pe_id,\r\n                exercise_id: exercise.exercise_id || null,\r\n                name: exercise.exercise_name || exercise.exercise_name2,\r\n                muscle_group: exercise.muscle_group || null,\r\n                equipment: exercise.equipment || null,\r\n                media_url: exercise.media_url || null,\r\n                sets: exercise.sets,\r\n                reps: exercise.reps,\r\n                rpe: exercise.rpe,\r\n                rest_seconds: exercise.rest_seconds,\r\n                notes: exercise.notes,\r\n            })),\r\n        }));\r\n\r\n        const workoutsCompleted = await db\r\n        .selectFrom('daily_coach_protocol')\r\n        .where('user_id', '=', userId)\r\n        .where('protocol_id', '=', protocol.id)\r\n        .select(db.fn.count<number>('id').as('workouts_completed'))\r\n        .executeTakeFirst();\r\n\r\n    \r\n        // Montar e retornar a resposta formatada\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                has_protocol: true,\r\n                id: protocol.id,\r\n                frequency: protocol.frequency,\r\n                name: protocol.name,\r\n                workouts_completed: workoutsCompleted?.workouts_completed || 0,\r\n                general_notes: protocol.general_notes,\r\n                objective: protocol.objective,\r\n                split: protocol.split,\r\n                started_at: protocol.started_at,\r\n                type_id: protocol.type_id,\r\n                workouts: workoutsAndExercises,\r\n            },\r\n        };\r\n    }\r\n\r\n    async getProtocolDietById(protocolId: number, userId: number) {\r\n        console.log(`🔍 getProtocolDietById: Buscando protocolo de dieta ID: ${protocolId} (tipo: ${typeof protocolId}) para usuário: ${userId}`);\r\n\r\n        try {\r\n            // Buscar o protocolo básico\r\n            const protocol = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n                .select([\r\n                    'p.id',\r\n                    'p.name',\r\n                    'p.objective',\r\n                    'p.initial_weight',\r\n                    'p.general_notes',\r\n                    's.value_option as type',\r\n                    'p.goal_calories as calories',\r\n                    'p.goal_protein as protein',\r\n                    'p.goal_carbs as carbs',\r\n                    'p.goal_fat as fat',\r\n                    'p.goal_water as water',\r\n                    'p.started_at',\r\n                    'p.ended_at',\r\n                    'p.type_id',\r\n                ])\r\n                .where('p.id', '=', protocolId)\r\n                .where('p.client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            console.log(`📋 Protocolo de dieta encontrado:`, protocol ? 'SIM' : 'NÃO');\r\n            if (protocol) {\r\n                console.log(`📊 Detalhes do protocolo: ID=${protocol.id}, Nome=\"${protocol.name}\", Tipo=\"${protocol.type}\"`);\r\n            }\r\n\r\n            if (!protocol) {\r\n                console.log(`❌ Protocolo de dieta ${protocolId} não encontrado para usuário ${userId}`);\r\n\r\n                // Verificar se o protocolo existe mas para outro usuário\r\n                const protocolExists = await db\r\n                    .selectFrom('nutritionist_protocols')\r\n                    .select(['id', 'client_id'])\r\n                    .where('id', '=', protocolId)\r\n                    .executeTakeFirst();\r\n\r\n                if (protocolExists) {\r\n                    console.log(`⚠️ Protocolo ${protocolId} existe mas pertence ao usuário ${protocolExists.client_id}, não ao usuário ${userId}`);\r\n                    throw new HttpException({\r\n                        status: 403,\r\n                        message: ['Acesso negado ao protocolo.'],\r\n                    }, 403);\r\n                } else {\r\n                    console.log(`❌ Protocolo ${protocolId} não existe no banco de dados`);\r\n                    throw new HttpException({\r\n                        status: 404,\r\n                        message: ['Protocolo de dieta não encontrado.'],\r\n                    }, 404);\r\n                }\r\n            }\r\n\r\n        // Buscar todas as refeições do protocolo organizadas por dia da semana\r\n        const mealRows = await db\r\n            .selectFrom('nutritionist_protocols_meals as p')\r\n            .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')\r\n            .select([\r\n                'p.id',\r\n                'p.name',\r\n                'p.day_of_week',\r\n                'p.meal_time',\r\n                'f.name as food_name',\r\n                'f.quantity',\r\n                'f.unit',\r\n                'f.calories',\r\n                'f.protein',\r\n                'f.carbs',\r\n                'f.fat',\r\n                'f.fiber',\r\n            ])\r\n            .where('p.protocol_id', '=', protocolId)\r\n            .orderBy('p.day_of_week', 'asc')\r\n            .orderBy('p.meal_time', 'asc')\r\n            .orderBy('p.id', 'asc')\r\n            .execute();\r\n\r\n        // Agrupar refeições por dia da semana\r\n        const mealsByDay = new Map();\r\n        mealRows.forEach((row) => {\r\n            const day = row.day_of_week.toLowerCase();\r\n            if (!mealsByDay.has(day)) {\r\n                mealsByDay.set(day, []);\r\n            }\r\n\r\n            let meal = mealsByDay.get(day).find((m: any) => m.id === row.id);\r\n            if (!meal) {\r\n                meal = {\r\n                    id: row.id,\r\n                    name: row.name,\r\n                    meal_time: row.meal_time,\r\n                    nutrients: {\r\n                        calories: 0,\r\n                        protein: 0,\r\n                        carbs: 0,\r\n                        fat: 0,\r\n                        fiber: 0,\r\n                    },\r\n                    foods: [],\r\n                };\r\n                mealsByDay.get(day).push(meal);\r\n            }\r\n\r\n            if (row.food_name) {\r\n                meal.foods.push({\r\n                    name: row.food_name,\r\n                    unit: row.unit,\r\n                    quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,\r\n                });\r\n                meal.nutrients.calories += parseFloat(Number(row.calories).toFixed(2)) || 0;\r\n                meal.nutrients.protein += parseFloat(Number(row.protein).toFixed(2)) || 0;\r\n                meal.nutrients.carbs += parseFloat(Number(row.carbs).toFixed(2)) || 0;\r\n                meal.nutrients.fat += parseFloat(Number(row.fat).toFixed(2)) || 0;\r\n                meal.nutrients.fiber += parseFloat(Number(row.fiber).toFixed(2)) || 0;\r\n            }\r\n        });\r\n\r\n        // Converter Map para objeto\r\n        const meals = Object.fromEntries(mealsByDay);\r\n\r\n        // Buscar suplementos do protocolo\r\n        const supplements = await db\r\n            .selectFrom('nutritionist_protocols_supplements as p')\r\n            .select(['p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])\r\n            .where('protocol_id', '=', protocolId)\r\n            .execute();\r\n\r\n            console.log(`✅ Protocolo de dieta ${protocolId} carregado com sucesso`);\r\n            console.log(`📊 Resumo: ${Object.keys(meals).length} dias com refeições, ${supplements.length} suplementos`);\r\n\r\n            return {\r\n                id: protocol.id,\r\n                type: protocol.type || 'Dieta',\r\n                name: protocol.name,\r\n                objective: protocol.objective,\r\n                general_notes: protocol.general_notes,\r\n                initial_weight: protocol.initial_weight,\r\n                started_at: protocol.started_at,\r\n                ended_at: protocol.ended_at,\r\n                status: protocol.ended_at ? 'finished' : 'active',\r\n                goals: {\r\n                    calories: parseFloat(Number(protocol.calories).toFixed(2)) || 0,\r\n                    protein: parseFloat(Number(protocol.protein).toFixed(2)) || 0,\r\n                    carbs: parseFloat(Number(protocol.carbs).toFixed(2)) || 0,\r\n                    fat: parseFloat(Number(protocol.fat).toFixed(2)) || 0,\r\n                    water: parseFloat(Number(protocol.water).toFixed(2)) || 0,\r\n                },\r\n                meals,\r\n                supplements: supplements.map((sup) => ({\r\n                    name: sup.name,\r\n                    dosage: sup.dosage,\r\n                    supplement_time: sup.supplement_time,\r\n                    notes: sup.notes,\r\n                })),\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error(`❌ Erro ao buscar protocolo de dieta ${protocolId}:`, error);\r\n            console.error(`❌ Stack trace:`, error.stack);\r\n            throw error;\r\n        }\r\n    }\r\n\r\n    async getProtocolWorkoutById(protocolId: number, userId: number) {\r\n        console.log(`🔍 Buscando protocolo ID: ${protocolId} para usuário: ${userId}`);\r\n\r\n        const protocol = await db\r\n            .selectFrom('coach_protocols')\r\n            .selectAll()\r\n            .where('id', '=', protocolId)\r\n            .where('client_id', '=', userId)\r\n            .executeTakeFirst();\r\n\r\n        console.log(`📋 Protocolo encontrado:`, protocol ? 'SIM' : 'NÃO');\r\n\r\n        if (!protocol) {\r\n            console.log(`❌ Protocolo ${protocolId} não encontrado para usuário ${userId}`);\r\n            throw new HttpException({\r\n                status: 404,\r\n                message: ['Protocolo não encontrado.'],\r\n            }, 404);\r\n        }\r\n\r\n\r\n\r\n        // Verificar se o protocolo está ativo\r\n        if (protocol.ended_at) {\r\n            console.log(`⚠️ Protocolo ${protocolId} está finalizado`);\r\n        }\r\n\r\n        // Primeiro, buscar todos os workouts do protocolo\r\n        const allWorkouts = await db\r\n            .selectFrom('coach_protocols_workouts')\r\n            .select(['id', 'name', 'protocol_id'])\r\n            .where('protocol_id', '=', protocol.id)\r\n            .execute();\r\n\r\n\r\n\r\n        // Depois, buscar todos os exercícios associados a esse protocolo\r\n        const exercises = await db\r\n            .selectFrom('coach_protocols_workouts as p')\r\n            .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')\r\n            .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')\r\n            .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')\r\n            .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')\r\n            .select([\r\n                'p.id',\r\n                'pe.id as pe_id',\r\n                'pe.exercise_id',\r\n                'p.name as workout_name',\r\n                'e.name as exercise_name',\r\n                'pe.name as exercise_name2',\r\n                's.value_option as muscle_group',\r\n                'equipment.value_option as equipment',\r\n                'e.media_url',\r\n                'pe.sets',\r\n                'pe.reps',\r\n                'pe.rpe',\r\n                'pe.rest_seconds',\r\n                'pe.notes',\r\n            ])\r\n            .where('protocol_id', '=', protocol.id)\r\n            .execute();\r\n\r\n\r\n\r\n        // Inicializar estrutura de workouts com todos os workouts encontrados\r\n        const workouts = {};\r\n        allWorkouts.forEach(workout => {\r\n            workouts[workout.name] = {\r\n                name: workout.name,\r\n                id: workout.id,\r\n                exercises: [],\r\n            };\r\n        });\r\n\r\n        // Adicionar exercícios aos workouts correspondentes\r\n        exercises.forEach(curr => {\r\n            const workoutName = curr.workout_name;\r\n            if (workouts[workoutName] && curr.pe_id) {\r\n                workouts[workoutName].exercises.push(curr);\r\n            }\r\n        });\r\n\r\n        const workoutsAndExercises = Object.values(workouts).map((workout: any) => ({\r\n            name: workout.name,\r\n            id: workout.id,\r\n            exercises: workout.exercises.map((exercise: any) => ({\r\n                id: exercise.pe_id,\r\n                exercise_id: exercise.exercise_id || null,\r\n                name: exercise.exercise_name || exercise.exercise_name2,\r\n                muscle_group: exercise.muscle_group || null,\r\n                equipment: exercise.equipment || null,\r\n                media_url: exercise.media_url || null,\r\n                sets: exercise.sets,\r\n                reps: exercise.reps,\r\n                rpe: exercise.rpe,\r\n                rest_seconds: exercise.rest_seconds,\r\n                notes: exercise.notes,\r\n            })),\r\n        }));\r\n\r\n\r\n\r\n\r\n\r\n        const workoutsCompleted = await db\r\n            .selectFrom('daily_coach_protocol')\r\n            .where('user_id', '=', userId)\r\n            .where('protocol_id', '=', protocol.id)\r\n            .select(db.fn.count<number>('id').as('workouts_completed'))\r\n            .executeTakeFirst();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                has_protocol: true,\r\n                id: protocol.id,\r\n                frequency: protocol.frequency,\r\n                name: protocol.name,\r\n                workouts_completed: workoutsCompleted?.workouts_completed || 0,\r\n                general_notes: protocol.general_notes,\r\n                objective: protocol.objective,\r\n                split: protocol.split,\r\n                started_at: protocol.started_at,\r\n                type_id: protocol.type_id,\r\n                workouts: workoutsAndExercises,\r\n            },\r\n        };\r\n    }\r\n\r\n    async updateProtocolWorkout(protocolId: number, updateData: any, userId: number) {\r\n        // Verificar se o protocolo existe e pertence ao usuário\r\n        const existingProtocol = await db\r\n            .selectFrom('coach_protocols')\r\n            .select(['id', 'client_id', 'ended_at', 'started_at'])\r\n            .where('id', '=', protocolId)\r\n            .where('client_id', '=', userId)\r\n            .executeTakeFirst();\r\n\r\n        if (!existingProtocol) {\r\n            throw new HttpException({\r\n                status: 404,\r\n                message: ['Protocolo não encontrado ou você não tem permissão para editá-lo.'],\r\n            }, 404);\r\n        }\r\n\r\n        // Verificar se o protocolo ainda está ativo (não foi finalizado)\r\n        if (existingProtocol.ended_at) {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Não é possível editar um protocolo que já foi finalizado.'],\r\n            }, 400);\r\n        }\r\n\r\n        // Validar dados básicos\r\n        if (!updateData.name || updateData.name.trim().length === 0) {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Nome do protocolo é obrigatório.'],\r\n            }, 400);\r\n        }\r\n\r\n        if (!updateData.objective || updateData.objective.trim().length === 0) {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Objetivo do protocolo é obrigatório.'],\r\n            }, 400);\r\n        }\r\n\r\n        if (!updateData.frequency || updateData.frequency < 1 || updateData.frequency > 7) {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Frequência deve ser entre 1 e 7 dias por semana.'],\r\n            }, 400);\r\n        }\r\n\r\n        try {\r\n            // Atualizar dados básicos do protocolo\r\n            await db\r\n                .updateTable('coach_protocols')\r\n                .set({\r\n                    name: updateData.name,\r\n                    type_id: updateData.type,\r\n                    split: updateData.split,\r\n                    frequency: updateData.frequency,\r\n                    objective: updateData.objective,\r\n                    general_notes: updateData.notes,\r\n                    updated_at: new Date(),\r\n                })\r\n                .where('id', '=', protocolId)\r\n                .execute();\r\n\r\n            // Se há workouts para atualizar\r\n            if (updateData.workouts && Array.isArray(updateData.workouts)) {\r\n                // Remover workouts e exercícios existentes\r\n                const existingWorkouts = await db\r\n                    .selectFrom('coach_protocols_workouts')\r\n                    .select(['id'])\r\n                    .where('protocol_id', '=', protocolId)\r\n                    .execute();\r\n\r\n                for (const workout of existingWorkouts) {\r\n                    await db\r\n                        .deleteFrom('coach_protocols_workouts_exercises')\r\n                        .where('workout_id', '=', workout.id)\r\n                        .execute();\r\n                }\r\n\r\n                await db\r\n                    .deleteFrom('coach_protocols_workouts')\r\n                    .where('protocol_id', '=', protocolId)\r\n                    .execute();\r\n\r\n                // Adicionar novos workouts e exercícios\r\n                const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n                for (const [index, workout] of updateData.workouts.entries()) {\r\n                    const workoutName = workout.name || `Treino ${letters[index]}`;\r\n\r\n                    const newWorkout = await db\r\n                        .insertInto('coach_protocols_workouts')\r\n                        .values({\r\n                            protocol_id: protocolId,\r\n                            name: workoutName,\r\n                            created_at: new Date(),\r\n                            updated_at: new Date(),\r\n                        })\r\n                        .executeTakeFirst();\r\n\r\n                    const workoutId = Number(newWorkout.insertId);\r\n\r\n                    if (workout.exercises && Array.isArray(workout.exercises)) {\r\n                        for (const exercise of workout.exercises) {\r\n                            // Verificar se é um exercício personalizado (ID começa com 'custom_')\r\n                            const isCustomExercise = typeof exercise.exercise_id === 'string' && \r\n                                exercise.exercise_id.toString().startsWith('custom_');\r\n                            \r\n                            await db\r\n                                .insertInto('coach_protocols_workouts_exercises')\r\n                                .values({\r\n                                    workout_id: workoutId,\r\n                                    // Para exercícios personalizados, definir exercise_id como null\r\n                                    exercise_id: isCustomExercise ? null : (exercise.exercise?.id || exercise.exercise_id),\r\n                                    name: exercise.name || exercise.exercise_name || exercise.exercise?.name,\r\n                                    sets: exercise.sets,\r\n                                    reps: exercise.reps,\r\n                                    rpe: exercise.rpe,\r\n                                    rest_seconds: exercise.restTime || exercise.rest_seconds,\r\n                                    notes: exercise.notes,\r\n                                })\r\n                                .execute();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n\r\n            return {\r\n                status: 'success',\r\n                message: 'Protocolo atualizado com sucesso',\r\n                data: { id: protocolId }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('Erro ao atualizar protocolo:', error);\r\n            throw new HttpException({\r\n                status: 500,\r\n                message: ['Erro interno do servidor ao atualizar protocolo.'],\r\n            }, 500);\r\n        }\r\n    }\r\n\r\n    async getUserData(userId: number) {\r\n        const user = await db\r\n          .selectFrom('users')\r\n          .where('id', '=', userId)\r\n          .select(['name', 'email', 'photo', 'height', 'weight', 'date_of_birth'])\r\n          .executeTakeFirst();\r\n          return user;\r\n      }\r\n\r\n    \r\n          async postProtocolsDiet(userId: number, createProtocolDietDto: CreateProtocolDietDto) {\r\n            console.log(`🚀 postProtocolsDiet: Iniciando criação de protocolo manual para usuário ${userId}`);\r\n            console.log(`📋 Dados recebidos:`, JSON.stringify(createProtocolDietDto, null, 2));\r\n\r\n            // Penente: Receber propriedade dos alimentos\r\n            if (await this.hasActiveProtocolDiet(userId)) {\r\n                console.error(`❌ postProtocolsDiet: Usuário ${userId} já possui protocolo ativo`);\r\n                throw new HttpException({\r\n                    status: 400,\r\n                    message: ['Você já possui um protocolo ativo.'],\r\n                }, 400);\r\n            }\r\n\r\n            console.log(`✅ postProtocolsDiet: Validação passou - criando protocolo manual para usuário ${userId}`);\r\n\r\n            const { name, type_id, objective, nutritional_goals, meals, supplements, general_notes } = createProtocolDietDto;\r\n\r\n            const userData = await this.getUserData(userId);\r\n            const initial_weight = userData?.weight || 0;\r\n    \r\n            const new_protocol = await db\r\n              .insertInto('nutritionist_protocols')\r\n              .values({\r\n                name: name,\r\n                type_id: type_id,\r\n                initial_weight: initial_weight,\r\n                objective: objective,\r\n                goal_calories: nutritional_goals.calories,\r\n                goal_protein: nutritional_goals.protein,\r\n                goal_carbs: nutritional_goals.carbs,\r\n                goal_fat: nutritional_goals.fat,\r\n                goal_water: nutritional_goals.water,\r\n                general_notes: general_notes,\r\n                started_at: new Date(),\r\n                client_id: userId,\r\n                created_at: new Date(),\r\n                updated_at: new Date(),\r\n              })\r\n              .executeTakeFirst();\r\n    \r\n            const new_protocol_id = Number(new_protocol.insertId);\r\n    \r\n    \r\n            meals.forEach(async (meal: any) => {\r\n              const new_meal = await db.insertInto('nutritionist_protocols_meals')\r\n              .values({\r\n                protocol_id: new_protocol_id,\r\n                name: meal.name,\r\n                day_of_week: meal.day_of_week,\r\n                meal_time: meal.meal_time,\r\n              })\r\n              .executeTakeFirst();\r\n    \r\n              const new_meal_id = Number(new_meal.insertId);\r\n    \r\n              meal?.foods?.forEach((food: any) => {\r\n                db.insertInto('nutritionist_protocols_meals_foods')\r\n                .values({\r\n                    meal_id: new_meal_id,\r\n                    food_id: food.food_id,\r\n                    name: food.name,\r\n                    quantity: food.quantity,\r\n                    unit: food.unit|| 'g',\r\n                    calories: food.calories || 0,\r\n                    protein: food.protein || 0,\r\n                    carbs: food.carbs || 0,\r\n                    fat: food.fat || 0,\r\n                    fiber: food.fiber || 0,\r\n                  })\r\n                  .execute();\r\n              });\r\n            });\r\n    \r\n            if (supplements && Array.isArray(supplements) && supplements.length > 0) {\r\n              await db\r\n                .insertInto('nutritionist_protocols_supplements')\r\n                .values(supplements.map((supplement) => ({\r\n                  protocol_id: new_protocol_id,\r\n                  name: supplement.name,\r\n                  dosage: supplement.dosage,\r\n                  supplement_time: supplement.supplement_time,\r\n                  notes: supplement.notes,\r\n                })))\r\n                .execute();\r\n            }\r\n    \r\n            return {\r\n              status: 'success',\r\n              data: [],\r\n            };\r\n          }\r\n\r\n          // AI\r\n          async generateProtocolPrompt(userInfo: any) {\r\n            const goalMapping = {\r\n              \"Emagrecimento\": 34,\r\n              \"Manutenção\": 35,\r\n              \"Ganho Muscular\": 36\r\n            };\r\n          \r\n            const goalId = goalMapping[userInfo.goal] || 35;\r\n            const mealFrequency = userInfo.mealFrequency || 5; // Default para 5 refeições por dia se não fornecido\r\n          \r\n            return `\r\n            # CONTEXTO OBRIGATÓRIO:\r\n            ## DADOS DO USUÁRIO:\r\n            - Altura: ${userInfo?.height || 'Não informada'} cm\r\n            - Peso Atual: ${userInfo?.weight} kg\r\n            - Peso Alvo: ${userInfo?.targetWeight || 'Não especificado'} kg\r\n            - % Gordura Atual: ${userInfo?.bodyFat ? userInfo.bodyFat + '%' : 'Não medida'}\r\n            - % Gordura Alvo: ${userInfo?.targetBodyFat ? userInfo.targetBodyFat + '%' : 'Não especificada'}\r\n            - Nível de Atividade: ${userInfo?.activityLevel}\r\n            - Objetivo: ${userInfo?.goal} (ID: ${goalId})\r\n            - Preferências: ${userInfo?.dietaryPreferences.join(', ') || 'Nenhuma preferência específica'}\r\n            - Restrições: ${userInfo?.restrictions.join(', ') || 'Nenhuma restrição informada'}\r\n            - Alimentos Preferidos: ${userInfo?.includedFoods.join(', ') || 'Nenhum favorito informado'}\r\n            - Frequência de Refeições: ${mealFrequency} refeições/dia\r\n          \r\n            ${userInfo?.goal === 'Emagrecimento' && userInfo.dietPlan ? '## PLANO ALIMENTAR:\\n' + userInfo.dietPlan : ''}\r\n          \r\n            ${userInfo?.notes ? '## COMENTÁRIOS ADICIONAIS:\\n' + userInfo.notes : ''}\r\n          \r\n            # TAREFA PRINCIPAL:\r\n            Como nutricionista IA, calcule AUTONOMAMENTE:\r\n            1. Necessidade calórica diária\r\n            2. Distribuição de macronutrientes (proteínas, carboidratos, gorduras)\r\n            3. Recomendações hídricas\r\n            4. Valores nutricionais para cada alimento\r\n            5. As somas dos macronutrientes (proteína, carboidrato, gordura) e calorias provenientes dos alimentos em TODAS as refeições diárias DEVEM bater com os valores especificados no campo \"nutritional_goals\", com margem máxima de erro de ±5% por dia.\r\n          \r\n            # DIRETRIZES PARA CÁLCULOS:\r\n            - Use a fórmula de Mifflin-St Jeor para o cálculo calórico basal\r\n            - Ajuste para objetivo (déficit/superávit calórico quando aplicável)\r\n            - Considere a distribuição ACEITÁVEL de macronutrientes (AMDR)\r\n            - Os valores nutricionais (calorias, proteína, carboidrato, gordura e fibra) DEVEM ser calculados exclusivamente com base em fontes brasileiras confiáveis (prioritariamente a Tabela TACO e dados da ANVISA)\r\n            - Não invente ou estime valores nutricionais. Se não houver valor na TACO, prefira alimentos com valores disponíveis\r\n            - Considere o peso do alimento na forma em que ele será consumido (ex: arroz cozido, frango grelhado, etc), conforme especificado na TACO\r\n          \r\n            # REQUISITO CRÍTICO:\r\n            - Crie um plano alimentar COMPLETO para TODOS OS 7 DIAS DA SEMANA: monday, tuesday, wednesday, thursday, friday, saturday, sunday\r\n            - Gere EXATAMENTE ${mealFrequency} refeições por dia para CADA dia\r\n            - NÃO corte a resposta antes de completar todos os 7 dias\r\n          \r\n            # MODELO JSON OBRIGATÓRIO:\r\n            {\r\n              \"name\": \"[Nome criativo baseado no objetivo]\",\r\n              \"type_id\": ${goalId},\r\n              \"objective\": \"[Descrição técnica com base nos dados do usuário]\",\r\n              \"nutritional_goals\": {\r\n                \"calories\": /* Calcular usando fórmulas científicas E validar com a soma das refeições do dia */,\r\n                \"protein\": /* Proteína em gramas (1.2-2.2g/kg peso) */,\r\n                \"carbs\": /* Carboidratos em gramas */,\r\n                \"fat\": /* Gorduras em gramas */,\r\n                \"water\": /* ml (35ml/kg + ajuste de atividade) */\r\n              },\r\n              \"meals\": [\r\n                {\r\n                  \"name\": \"[Nome da Refeição]\",\r\n                  \"day_of_week\": \"monday/tuesday/.../sunday\",\r\n                  \"meal_time\": \"HH:MM:SS\",\r\n                  \"foods\": [\r\n                    {\r\n                      \"name\": \"[Alimento]\",\r\n                      \"quantity\": /* Quantidade numérica SEM UNIDADE */,\r\n                      \"unit\": \"g/ml/...\",\r\n                      \"calories\": /* Valor exato calculado */,\r\n                      \"protein\": /* g */,\r\n                      \"carbs\": /* g */,\r\n                      \"fat\": /* g */,\r\n                      \"fiber\": /* g */\r\n                    }\r\n                  ]\r\n                }\r\n              ],\r\n              \"supplements\": [\r\n                {\r\n                  \"name\": \"[Nome Suplemento]\",\r\n                  \"dosage\": \"Dose específica\",\r\n                  \"supplement_time\": \"Momento de consumo\",\r\n                  \"notes\": \"Justificativa técnica\"\r\n                }\r\n              ],\r\n              \"general_notes\": \"[Recomendações personalizadas]\"\r\n            }\r\n          \r\n            # EXEMPLO MÍNIMO ESPERADO:\r\n            {\r\n              \"name\": \"Exemplo\",\r\n              \"type_id\": ${goalId},\r\n              \"objective\": \"...\",\r\n              \"nutritional_goals\": {\"calories\": 2500, \"protein\": 150, \"carbs\": 150, \"fat\": 139, \"water\": 2870},\r\n              \"meals\": [\r\n                {\"name\": \"Café da Manhã\", \"day_of_week\": \"monday\", \"meal_time\": \"07:30:00\", \"foods\": [...]},\r\n                {\"name\": \"Lanche\", \"day_of_week\": \"monday\", \"meal_time\": \"10:30:00\", \"foods\": [...]},\r\n                {\"name\": \"Almoço\", \"day_of_week\": \"monday\", \"meal_time\": \"13:00:00\", \"foods\": [...]},\r\n                {\"name\": \"Lanche da Tarde\", \"day_of_week\": \"monday\", \"meal_time\": \"16:00:00\", \"foods\": [...]},\r\n                {\"name\": \"Jantar\", \"day_of_week\": \"monday\", \"meal_time\": \"19:30:00\", \"foods\": [...]},\r\n                {\"name\": \"Café da Manhã\", \"day_of_week\": \"tuesday\", \"meal_time\": \"07:30:00\", \"foods\": [...]}\r\n                // ... continuar até sunday com ${mealFrequency} refeições por dia\r\n              ],\r\n              \"supplements\": [...],\r\n              \"general_notes\": \"...\"\r\n            }\r\n          \r\n            # REGRAS ESSENCIAIS:\r\n            1. Números SEM ASPAS ou unidades nos valores (ex: \"quantity\": 150)\r\n            2. Precisão nutricional: ±5% de margem de erro por dia\r\n            3. Variar alimentos entre dias (mantendo a consistência nutricional): ${userInfo?.foodVariation || 'Não especificado'}\r\n            4. Incluir fibra em TODOS alimentos aplicáveis\r\n            5. Usar alimentos típicos brasileiros\r\n            6. Formato 24h para horários (ex: \"14:30:00\")\r\n            7. Gerar EXATAMENTE ${mealFrequency} refeições por dia para TODOS os 7 dias\r\n          \r\n            # CRITÉRIO DE PRECISÃO NUTRICIONAL:\r\n            - Todos os valores nutricionais devem ser extraídos ou calculados com base nas informações da Tabela TACO.\r\n            - Nunca invente valores. Não arredonde de forma exagerada.\r\n            - A soma das calorias e dos macronutrientes (proteína, carboidrato, gordura) de TODAS as refeições de um dia deve corresponder aos valores em \"nutritional_goals\" com no máximo +5% de variação.\r\n            - Essa validação deve ser feita por dia. Exemplo: se o plano indica 2.400 kcal, a soma dos alimentos de segunda-feira pode variar entre 2.400 e 2.520 kcal, mas não menos ou mais que isso.\r\n            - O mesmo se aplica a cada macronutriente separadamente.\r\n            - Não inclua valores arbitrários em \"nutritional_goals\". Calcule com base no total dos alimentos e ajuste conforme necessário.\r\n          \r\n            Retorne APENAS o JSON válido SEM comentários ou markdown.\r\n            `;\r\n          }\r\n          \r\n          private extractJsonFromString(text: string): string {\r\n            const jsonStart = text.indexOf('{');\r\n            const jsonEnd = text.lastIndexOf('}') + 1;\r\n          \r\n            if (jsonStart === -1 || jsonEnd <= jsonStart) {\r\n              throw new Error('Nenhum JSON válido encontrado na resposta');\r\n            }\r\n          \r\n            const extracted = text.slice(jsonStart, jsonEnd);\r\n          \r\n            try {\r\n              return JSON.stringify(JSON.parse(extracted)); // Tenta validar diretamente\r\n            } catch {\r\n              return JSON.stringify(JSON.parse(jsonrepair(extracted))); // Repara se necessário\r\n            }\r\n          }\r\n\r\n            private extractJsonFromStringOld(text: string): string {\r\n              // Etapa 0: Normalização inicial\r\n              let sanitized = text\r\n                .replace(/[\\u0000-\\u001F\\u007F-\\u009F]/g, '') // Remove todos os caracteres de controle\r\n                .replace(/^\\uFEFF/, '') // Remove BOM\r\n                .replace(/[‘’]/g, \"'\") // Normaliza aspas simples\r\n                .replace(/[“”]/g, '\"') // Normaliza aspas duplas\r\n                .replace(/\\\\n/g, '') // Remove quebras de linha\r\n                .trim();\r\n            \r\n              // Etapa 1: Extrair o bloco JSON\r\n              const jsonStart = sanitized.indexOf('{');\r\n              const jsonEnd = sanitized.lastIndexOf('}') + 1;\r\n              \r\n              if (jsonStart === -1 || jsonEnd <= jsonStart) {\r\n                throw new Error('Nenhum JSON válido encontrado na resposta');\r\n              }\r\n              \r\n              sanitized = sanitized.slice(jsonStart, jsonEnd);\r\n            \r\n              // Etapa 2: Correções específicas para JSON corrompido\r\n              sanitized = sanitized\r\n                // Remove aspas duplicadas em nomes de propriedades\r\n                .replace(/\"{2,}/g, '\"')\r\n                // Corrige aspas em valores numéricos\r\n                .replace(/\":\\s*\"(\\d+)\"/g, '\": $1')\r\n                // Remove aspas extras antes de valores\r\n                .replace(/\"\"([^\"]+)\"\"/g, '\"$1\"')\r\n                // Corrige tempos no formato \"HH\":\"MM:SS\"\r\n                .replace(/\"(\\d{2})\":(\\d{2}):(\\d{2})\"/g, '\"$1:$2:$3\"')\r\n                // Remove espaços desnecessários\r\n                .replace(/\\s+/g, ' ')\r\n                .replace(/\\s*([{}[\\]:,])\\s*/g, '$1');\r\n            \r\n              // Etapa 3: Correções avançadas\r\n              const fixes: Array<[RegExp, string]> = [\r\n                // Garante aspas em propriedades\r\n                [/([{,])\\s*([a-zA-Z_]+)\\s*:/g, '$1\"$2\":'],\r\n                // Remove aspas em valores numéricos\r\n                [/\":\\s*\"(\\d+(?:\\.\\d+)?)\"/g, '\":$1'],\r\n                // Corrige valores booleanos\r\n                [/\":\\s*\"(true|false)\"/g, '\":$1'],\r\n                // Remove vírgulas extras\r\n                [/,\\s*}/g, '}'],\r\n                [/,\\s*]/g, ']']\r\n              ];\r\n            \r\n              fixes.forEach(([regex, replacement]) => {\r\n                sanitized = sanitized.replace(regex, replacement);\r\n              });\r\n            \r\n              // Etapa 4: Validação final\r\n              try {\r\n                const parsed = JSON.parse(sanitized);\r\n                return JSON.stringify(parsed); // Garante formato consistente\r\n              } catch (error) {\r\n                console.error('JSON após tentativas de correção:', sanitized.substring(0, 500));\r\n                throw new Error(`Falha na validação do JSON: ${error.message}`);\r\n              }\r\n            }\r\n\r\n\r\n            async postProtocolsDietAi(userId: number, userInfo: any) {\r\n              console.log(`🤖 postProtocolsDietAi: Iniciando criação de protocolo IA para usuário ${userId}`);\r\n              console.log(`📋 Dados recebidos:`, JSON.stringify(userInfo, null, 2));\r\n\r\n              if (await this.hasActiveProtocolDiet(userId)) {\r\n                console.error(`❌ postProtocolsDietAi: Usuário ${userId} já possui protocolo ativo`);\r\n                throw new HttpException({\r\n                  status: 400,\r\n                  message: ['Você já possui um protocolo ativo.'],\r\n                }, 400);\r\n              }\r\n\r\n              console.log(`✅ postProtocolsDietAi: Validação passou - criando protocolo IA para usuário ${userId}`);\r\n            \r\n              try {\r\n                const prompt = await this.generateProtocolPrompt(userInfo);\r\n                const response = await this.openai.chat.completions.create({\r\n                  model: \"gpt-4o-mini\", // Mudança para gpt-4o-mini para melhor performance\r\n                  messages: [\r\n                    {\r\n                      role: \"system\",\r\n                      content: \"You are a professional nutritionist expert in creating personalized diet protocols. Return only valid JSON following the specified format, ensuring a complete plan for all 7 days of the week.\"\r\n                    },\r\n                    { role: \"user\", content: prompt }\r\n                  ],\r\n                  temperature: 0.2, // Reduzido para mais consistência\r\n                  max_tokens: 6144, // Reduzido para otimizar performance\r\n                  response_format: { type: \"json_object\" }\r\n                });\r\n            \r\n                const aiResponseText: any = response.choices[0].message.content;\r\n                const protocolDataText = this.extractJsonFromString(aiResponseText);\r\n                const protocolData = JSON.parse(protocolDataText);\r\n            \r\n                if (!protocolData.name || !protocolData.type_id || !protocolData.nutritional_goals) {\r\n                  throw new Error('Dados essenciais ausentes no protocolo gerado');\r\n                }\r\n            \r\n                const initial_weight = userInfo.weight;\r\n            \r\n                // Iniciar transação\r\n                const result = await db.transaction().execute(async (trx) => {\r\n                  // Atualizar dados do usuário, se necessário\r\n                  let userUpdateData = {\r\n                    ...(userInfo.weight && userInfo.weight > 0 && { weight: userInfo.weight }),\r\n                    ...(userInfo.bodyFat && userInfo.bodyFat > 0 && { bodyfat: userInfo.bodyFat }), // Corrigido 'bodyfat' para 'bodyFat'\r\n                  };\r\n            \r\n                  if (Object.keys(userUpdateData).length > 0) {\r\n                    await trx\r\n                      .updateTable('users')\r\n                      .set(userUpdateData)\r\n                      .where('id', '=', userId)\r\n                      .execute();\r\n                  }\r\n            \r\n                  // Inserir protocolo\r\n                  const new_protocol = await trx\r\n                    .insertInto('nutritionist_protocols')\r\n                    .values({\r\n                      name: protocolData.name,\r\n                      type_id: protocolData.type_id,\r\n                      initial_weight,\r\n                      objective: protocolData.objective,\r\n                      goal_calories: protocolData.nutritional_goals.calories,\r\n                      goal_protein: protocolData.nutritional_goals.protein,\r\n                      goal_carbs: protocolData.nutritional_goals.carbs,\r\n                      goal_fat: protocolData.nutritional_goals.fat,\r\n                      goal_water: protocolData.nutritional_goals.water,\r\n                      general_notes: protocolData.general_notes,\r\n                      started_at: new Date(),\r\n                      client_id: userId,\r\n                      created_at: new Date(),\r\n                      updated_at: new Date(),\r\n                    })\r\n                    .executeTakeFirst();\r\n            \r\n                  const new_protocol_id = Number(new_protocol.insertId);\r\n            \r\n                  // Inserir refeições\r\n                  for (const meal of protocolData.meals || []) {\r\n                    const new_meal = await trx\r\n                      .insertInto('nutritionist_protocols_meals')\r\n                      .values({\r\n                        protocol_id: new_protocol_id,\r\n                        name: meal.name,\r\n                        day_of_week: meal.day_of_week,\r\n                        meal_time: meal.meal_time,\r\n                      })\r\n                      .executeTakeFirst();\r\n            \r\n                    const new_meal_id = Number(new_meal.insertId);\r\n            \r\n                    if (meal.foods && Array.isArray(meal.foods)) {\r\n                      await trx\r\n                        .insertInto('nutritionist_protocols_meals_foods')\r\n                        .values(meal.foods.map(food => ({\r\n                          meal_id: new_meal_id,\r\n                          food_id: null,\r\n                          name: food.name,\r\n                          quantity: food.quantity,\r\n                          unit: food.unit || 'g',\r\n                          calories: food.calories || 0,\r\n                          protein: food.protein || 0,\r\n                          carbs: food.carbs || 0,\r\n                          fat: food.fat || 0,\r\n                          fiber: food.fiber || 0,\r\n                        })))\r\n                        .execute();\r\n                    }\r\n                  }\r\n            \r\n                  // Inserir suplementos\r\n                  if (protocolData.supplements?.length > 0) {\r\n                    await trx\r\n                      .insertInto('nutritionist_protocols_supplements')\r\n                      .values(protocolData.supplements.map((supplement) => ({\r\n                        protocol_id: new_protocol_id,\r\n                        name: supplement.name,\r\n                        dosage: supplement.dosage,\r\n                        supplement_time: supplement.supplement_time,\r\n                        notes: supplement.notes,\r\n                      })))\r\n                      .execute();\r\n                  }\r\n            \r\n                  return new_protocol_id;\r\n                });\r\n            \r\n                return {\r\n                  status: \"success\",\r\n                  data: {\r\n                    protocol_id: result,\r\n                  },\r\n                };\r\n              } catch (error) {\r\n                console.error('Erro ao processar protocolo:', {\r\n                  message: error.message,\r\n                  stack: error.stack,\r\n                  input: userInfo\r\n                });\r\n                throw new HttpException({\r\n                  status: \"error\",\r\n                  message: ['Erro ao processar protocolo de dieta.', error.message],\r\n                }, 500);\r\n              }\r\n            }\r\n\r\n\r\n          \r\n\r\n          async getActiveProtocolsDiet(userId: number) {\r\n            console.log(`🔍 getActiveProtocolsDiet: Buscando protocolo ativo para usuário ${userId}`);\r\n\r\n            // APLICAR REGRA OTIMIZADA: Garantir apenas um protocolo ativo\r\n            const protocolRule = await this.ensureSingleActiveProtocol(userId);\r\n            console.log('📋 Resultado da regra:', protocolRule.message);\r\n\r\n            if (!protocolRule.activeProtocol) {\r\n              console.log('❌ Nenhum protocolo ativo encontrado');\r\n              return {\r\n                status: 'success',\r\n                data: {\r\n                  has_protocol: false,\r\n                  message: protocolRule.message\r\n                },\r\n              };\r\n            }\r\n\r\n            // Buscar dados completos do protocolo ativo único\r\n            const protocol = await db\r\n              .selectFrom('nutritionist_protocols as p')\r\n              .leftJoin('select_options as s', 's.id', 'p.type_id')\r\n              .select([\r\n                'p.id',\r\n                'p.name',\r\n                'p.objective',\r\n                'p.initial_weight',\r\n                'p.general_notes',\r\n                's.value_option as type',\r\n                'p.goal_calories as calories',\r\n                'p.goal_protein as protein',\r\n                'p.goal_carbs as carbs',\r\n                'p.goal_fat as fat',\r\n                'p.goal_water as water',\r\n                'p.started_at',\r\n                'p.ended_at'])\r\n              .where('p.id', '=', protocolRule.activeProtocol.id)\r\n              .where('p.client_id', '=', userId)\r\n              .executeTakeFirst();\r\n\r\n            if (!protocol) {\r\n              console.log('❌ Erro: protocolo ativo não encontrado após regra');\r\n              return {\r\n                status: 'success',\r\n                data: {\r\n                  has_protocol: false,\r\n                  message: 'Protocolo ativo não encontrado'\r\n                },\r\n              };\r\n            }\r\n\r\n            console.log(`✅ Protocolo ativo único confirmado: ${protocol.name} (ID: ${protocol.id})`);\r\n            if (protocolRule.deactivatedCount > 0) {\r\n              console.log(`🔄 ${protocolRule.deactivatedCount} protocolos antigos foram finalizados`);\r\n            }\r\n          \r\n            // Buscar meals e foods associados\r\n            const mealRows = await db\r\n              .selectFrom('nutritionist_protocols_meals as p')\r\n              .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')\r\n              .select([\r\n                'p.id',\r\n                'p.name',\r\n                'p.day_of_week',\r\n                'p.meal_time',\r\n                'f.name as food_name',\r\n                'f.quantity',\r\n                'f.unit',\r\n              ])\r\n              .where('protocol_id', '=', protocol.id)\r\n              .orderBy('p.meal_time', 'asc')\r\n              .orderBy('p.id', 'asc')\r\n              .execute();\r\n          \r\n            // Agrupar os meals por dia da semana\r\n            const mealsByDay = new Map<string, any[]>();\r\n            mealRows.forEach((row) => {\r\n              const day = row.day_of_week.toLowerCase(); // Padronizar como minúsculo, ex: \"monday\"\r\n              if (!mealsByDay.has(day)) {\r\n                mealsByDay.set(day, []);\r\n              }\r\n          \r\n              // Encontrar ou criar a refeição no dia\r\n              let meal = mealsByDay.get(day)?.find((m) => m.id === row.id);\r\n              if (!meal) {\r\n                meal = {\r\n                  id: row.id,\r\n                  name: row.name,\r\n                  meal_time: row.meal_time,\r\n                  foods: [],\r\n                };\r\n                mealsByDay.get(day)?.push(meal);\r\n              }\r\n          \r\n              // Adicionar alimento, se existir\r\n              if (row.food_name) {\r\n                meal.foods.push({\r\n                  name: row.food_name,\r\n                  unit: row.unit,\r\n                  quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,\r\n                });\r\n              }\r\n            });\r\n          \r\n            // Converter Map para objeto\r\n            const meals = Object.fromEntries(mealsByDay);\r\n          \r\n            // Buscar supplementos\r\n            const supplements = await db\r\n              .selectFrom('nutritionist_protocols_supplements as p')\r\n              .select(['p.id', 'p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])\r\n              .where('protocol_id', '=', protocol.id)\r\n              .execute();\r\n          \r\n            return {\r\n              status: 'success',\r\n              data: {\r\n                has_protocol: true,\r\n                id: protocol.id,\r\n                type: protocol.type,\r\n                name: protocol.name,\r\n                objective: protocol.objective,\r\n                general_notes: protocol.general_notes,\r\n                initial_weight: parseFloat(Number(protocol.initial_weight).toFixed(2)) || 0,\r\n                started_at: protocol.started_at,\r\n                ended_at: protocol.ended_at,\r\n                goals: {\r\n                  calories: parseFloat(Number(protocol.calories).toFixed(2)) || 0,\r\n                  protein: parseFloat(Number(protocol.protein).toFixed(2)) || 0,\r\n                  carbs: parseFloat(Number(protocol.carbs).toFixed(2)) || 0,\r\n                  fat: parseFloat(Number(protocol.fat).toFixed(2)) || 0,\r\n                  water: parseFloat(Number(protocol.water).toFixed(2)) || 0,\r\n                },\r\n                meals, // Agora é um objeto com dias da semana como chaves\r\n                supplements: supplements.map((sup) => ({\r\n                  name: sup.name,\r\n                  dosage: sup.dosage,\r\n                  supplement_time: sup.supplement_time,\r\n                  notes: sup.notes,\r\n                })),\r\n              },\r\n            };\r\n          }\r\n\r\n          async getActiveMealsOfDayWeek(userId: number, query: any) {\r\n            const { date } = query;\r\n            let inputDate: Date = date ? new Date(date) : new Date();\r\n            // Verifica se a data é válida, caso contrário, usa a data atual\r\n            if (isNaN(inputDate.getTime())) {\r\n                inputDate = new Date();\r\n            }\r\n\r\n            console.log('🔄 getActiveMealsOfDayWeek: Iniciando busca para usuário', userId);\r\n            console.log('📅 Data consultada:', date);\r\n\r\n            const tz = 'America/Sao_Paulo';\r\n\r\n            // Usa a data passada ou a data atual no timezone do usuário\r\n            const inputDateTz = dayjs.tz(date || undefined, tz);\r\n\r\n            // Agora sim, pega o início e fim do dia corretamente no timezone do usuário\r\n            const startOfDayUtc = inputDateTz.startOf('day').utc().toDate();\r\n            const endOfDayUtc = inputDateTz.endOf('day').utc().toDate();\r\n\r\n            // Obtém o nome do dia da semana em inglês e em minúsculas (ex: \"monday\")\r\n            const dayName = startOfDayUtc.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();\r\n            console.log('📅 Dia da semana:', dayName);\r\n\r\n            const today = dayjs().tz(tz).toDate();\r\n\r\n            // APLICAR REGRA OTIMIZADA: Garantir apenas um protocolo ativo\r\n            console.log('🔧 Aplicando regra de protocolo único ativo...');\r\n            const protocolRule = await this.ensureSingleActiveProtocol(userId);\r\n            console.log('📋 Resultado da regra:', protocolRule.message);\r\n\r\n            if (!protocolRule.activeProtocol) {\r\n                console.log('❌ Nenhum protocolo ativo encontrado para usuário', userId);\r\n                return {\r\n                    status: 'success',\r\n                    data: {\r\n                        has_protocol: false,\r\n                        message: protocolRule.message\r\n                    },\r\n                };\r\n            }\r\n\r\n            // Buscar dados completos do protocolo ativo\r\n            console.log('🔍 Buscando dados completos do protocolo ativo:', protocolRule.activeProtocol.id);\r\n\r\n            const protocolBase = await db\r\n                .selectFrom('nutritionist_protocols as p')\r\n                .select([\r\n                    'p.id',\r\n                    'p.name',\r\n                    'p.objective',\r\n                    'p.initial_weight',\r\n                    'p.general_notes',\r\n                    'p.goal_calories as calories',\r\n                    'p.goal_protein as protein',\r\n                    'p.goal_carbs as carbs',\r\n                    'p.goal_fat as fat',\r\n                    'p.goal_water as water',\r\n                    'p.started_at',\r\n                    'p.ended_at',\r\n                    'p.type_id',\r\n                ])\r\n                .where('p.id', '=', protocolRule.activeProtocol.id)\r\n                .where('p.client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            if (!protocolBase) {\r\n                console.log('❌ Erro: protocolo ativo não encontrado após regra', protocolRule.activeProtocol.id);\r\n                return {\r\n                    status: 'success',\r\n                    data: {\r\n                        has_protocol: false,\r\n                        message: 'Protocolo ativo não encontrado'\r\n                    },\r\n                };\r\n            }\r\n\r\n            // Busca o tipo do protocolo separadamente\r\n            let protocolType = 'Dieta'; // valor padrão\r\n            if (protocolBase.type_id) {\r\n                const typeResult = await db\r\n                    .selectFrom('select_options')\r\n                    .select(['value_option'])\r\n                    .where('id', '=', protocolBase.type_id)\r\n                    .executeTakeFirst();\r\n\r\n                if (typeResult && typeResult.value_option) {\r\n                    protocolType = typeResult.value_option;\r\n                }\r\n            }\r\n\r\n            const protocol = {\r\n                ...protocolBase,\r\n                type: protocolType\r\n            };\r\n\r\n            console.log('✅ Protocolo ativo único confirmado:', {\r\n                id: protocol.id,\r\n                name: protocol.name,\r\n                started_at: protocol.started_at,\r\n                type: protocol.type,\r\n                deactivated_count: protocolRule.deactivatedCount\r\n            });\r\n\r\n            // Busca as refeições para o dia específico, com LEFT JOIN para verificar se a refeição foi completada\r\n            console.log('🔍 Buscando refeições do protocolo para o dia:', dayName);\r\n\r\n            const mealRows: any = await db\r\n                .selectFrom('nutritionist_protocols_meals as p')\r\n                .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')\r\n                .leftJoin('daily_meals as d', (join) =>\r\n                    join\r\n                        .onRef('d.meal_id', '=', 'p.id') // Relaciona meal_id\r\n                        .on('d.user_id', '=', userId) // Filtra pelo usuário\r\n                        .on('d.daily_at', '>=', startOfDayUtc)\r\n                        .on('d.daily_at', '<=', endOfDayUtc) // Filtra pelo dia atual\r\n                )\r\n                .leftJoin('daily_meals_foods as df', 'df.meal_id', 'd.id')\r\n                .select([\r\n                    sql`COALESCE(d.id, p.id)`.as('id'),\r\n                    'p.id as protocol_meal_id',\r\n                    'p.name',\r\n                    'p.meal_time',\r\n                    'd.daily_at',\r\n                    sql`COALESCE(df.name, f.name)`.as('food_name'), // Se df.name for NULL, usa f.name\r\n                    sql`COALESCE(df.quantity, f.quantity)`.as('quantity'), // Se df.quantity for NULL, usa f.quantity\r\n                    sql`COALESCE(df.unit, f.unit)`.as('unit'), // Se df.unit for NULL, usa f.unit\r\n                    sql`COALESCE(df.calories, f.calories)`.as('calories'), // Se df.calories for NULL, usa f.calories\r\n                    sql`COALESCE(df.protein, f.protein)`.as('protein'), // Se df.protein for NULL, usa f.protein\r\n                    sql`COALESCE(df.carbs, f.carbs)`.as('carbs'), // Se df.carbs for NULL, usa f.carbs\r\n                    sql`COALESCE(df.fat, f.fat)`.as('fat'), // Se df.fat for NULL, usa f.fat\r\n                    sql`CASE WHEN d.id IS NOT NULL THEN TRUE ELSE FALSE END`.as('completed'), // Verifica se há registro em daily_meals\r\n                ])\r\n                .where('p.protocol_id', '=', protocol.id)\r\n                .where('p.day_of_week', '=', dayName)\r\n                .orderBy('d.daily_at', 'asc')\r\n                .orderBy('p.meal_time', 'asc')\r\n                .orderBy('p.id', 'asc')\r\n                .execute();\r\n\r\n            console.log(`📊 Encontradas ${mealRows.length} linhas de refeições do protocolo para ${dayName}`);\r\n\r\n            const mealsOutDiet = await db\r\n                .selectFrom('daily_meals as d')\r\n                .leftJoin('daily_meals_foods as df', 'df.meal_id', 'd.id')\r\n                .select([\r\n                    'd.id',\r\n                    'd.name',                    \r\n                    'd.daily_at',\r\n                    'df.name as food_name',\r\n                    'df.quantity',\r\n                    'df.unit',\r\n                    'df.calories',\r\n                    'df.protein',\r\n                    'df.carbs',\r\n                    'df.fat',\r\n                    'df.fiber',\r\n                ])\r\n                .where('d.user_id', '=', userId)\r\n                .where((eb) =>\r\n                    eb.and([\r\n                        eb(sql`DATE(d.daily_at)`, '=', sql`DATE(${startOfDayUtc})`),\r\n                        eb('d.meal_id', 'is', null),\r\n                    ])\r\n                )\r\n                .orderBy('d.daily_at', 'asc')\r\n                .execute();\r\n                \r\n            const dateTz = (date: any) => {\r\n                return dayjs.tz(date, tz).format('YYYY-MM-DD HH:mm:ss');\r\n            }\r\n            const timeTz = (date: any) => {\r\n                return dayjs.tz(date, tz).format('HH:mm:ss');\r\n            }\r\n\r\n            const mealRowsFormatted = mealRows.map((row: any) => ({\r\n                id: row.id,\r\n                protocol_meal_id: row.protocol_meal_id || null,\r\n                name: row.name,\r\n                meal_time: row.daily_at ? timeTz(row.daily_at) : row.meal_time,\r\n                food_name: row.food_name,\r\n                quantity: row.quantity,\r\n                unit: row.unit,\r\n                calories: row.calories,\r\n                protein: row.protein,\r\n                carbs: row.carbs,\r\n                fat: row.fat,\r\n                fiber: row.fiber,\r\n                completed: row.completed,\r\n            }));\r\n\r\n            console.log('📋 Refeições do protocolo formatadas:', mealRowsFormatted.length);\r\n\r\n            const mealsOutDietFormatted = mealsOutDiet.map((row) => ({\r\n                id: row.id,\r\n                name: row.name,\r\n                meal_time: timeTz(row.daily_at),\r\n                food_name: row.food_name,\r\n                quantity: row.quantity,\r\n                unit: row.unit,\r\n                calories: row.calories,\r\n                protein: row.protein,\r\n                carbs: row.carbs,\r\n                fat: row.fat,\r\n                fiber: row.fiber,\r\n                completed: true,\r\n            }));\r\n\r\n            const allMeals = [...mealRowsFormatted, ...mealsOutDietFormatted].map((row) => ({\r\n                id: row.id,\r\n                name: row.name,\r\n                meal_time: row.meal_time,\r\n                food_name: row.food_name,\r\n                quantity: row.quantity,\r\n                unit: row.unit,\r\n                calories: row.calories,\r\n                protein: row.protein,\r\n                carbs: row.carbs,\r\n                fat: row.fat,\r\n                fiber: row.fiber,\r\n                completed: row?.completed ? true : false,\r\n            }));\r\n\r\n            \r\n            const allMealsOrder = allMeals.sort((a, b) => {\r\n              return a.meal_time.localeCompare(b.meal_time);\r\n            });\r\n\r\n        \r\n            // Agrupa as refeições e seus alimentos\r\n            const mealsMap = new Map();\r\n            for (const row of allMealsOrder) {\r\n                // Usa protocol_meal_id quando disponível para agrupar corretamente refeições do protocolo\r\n                const mealKey = (row as any).protocol_meal_id || row.id;\r\n                let meal = mealsMap.get(mealKey);\r\n                if (!meal) {\r\n                    meal = {\r\n                        id: (row as any).protocol_meal_id || row.id, // Usa protocol_meal_id para refeições do protocolo\r\n                        name: row.name,\r\n                        meal_time: row.meal_time,\r\n                        nutrients: {\r\n                            calories: 0,\r\n                            protein: 0,\r\n                            carbs: 0,\r\n                            fat: 0,\r\n                            fiber: 0,\r\n                        },\r\n                        completed: row.completed ? true : false, // Adiciona o campo \"completed\"\r\n                        foods: [],\r\n                    };\r\n                    mealsMap.set(mealKey, meal);\r\n                }\r\n                if (row.food_name) {\r\n                    meal.foods.push({\r\n                        name: row.food_name,\r\n                        unit: row.unit,\r\n                        quantity: parseFloat(Number(row.quantity).toFixed(2)) || 0,\r\n                    });\r\n                    meal.nutrients.calories += parseFloat(Number(row.calories).toFixed(2)) || 0;\r\n                    meal.nutrients.protein += parseFloat(Number(row.protein).toFixed(2)) || 0;\r\n                    meal.nutrients.carbs += parseFloat(Number(row.carbs).toFixed(2)) || 0;\r\n                    meal.nutrients.fat += parseFloat(Number(row.fat).toFixed(2)) || 0;\r\n                    meal.nutrients.fiber += parseFloat(Number(row.fiber).toFixed(2)) || 0;\r\n                }\r\n            }\r\n            const meals = Array.from(mealsMap.values());\r\n\r\n            console.log(`🍽️ Total de refeições agrupadas: ${meals.length}`);\r\n            console.log('📋 Refeições finais:', meals.map(m => ({ id: m.id, name: m.name, completed: m.completed, foods_count: m.foods.length })));\r\n        \r\n            // Busca os suplementos do protocolo\r\n            const supplements = await db\r\n                .selectFrom('nutritionist_protocols_supplements as p')\r\n                .select(['p.name', 'p.dosage', 'p.supplement_time', 'p.notes'])\r\n                .where('protocol_id', '=', protocol.id)\r\n                .execute();\r\n        \r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    has_protocol: true,\r\n                    id: protocol.id,\r\n                    type: protocol.type,\r\n                    name: protocol.name,\r\n                    objective: protocol.objective,\r\n                    general_notes: protocol.general_notes,\r\n                    initial_weight: protocol.initial_weight,\r\n                    started_at: protocol.started_at,\r\n                    ended_at: protocol.ended_at,\r\n                    goals: {\r\n                        calories: protocol.calories,\r\n                        protein: protocol.protein,\r\n                        carbs: protocol.carbs,\r\n                        fat: protocol.fat,\r\n                        water: protocol.water,\r\n                    },\r\n                    meals,\r\n                    supplements: supplements.map(sup => ({\r\n                        name: sup.name,\r\n                        dosage: sup.dosage,\r\n                        supplement_time: sup.supplement_time,\r\n                        notes: sup.notes,\r\n                    })),\r\n                },\r\n            };\r\n        }\r\n\r\n\r\n          async getActiveProtocolsDietMealsDayWeek(userId: number) {\r\n            // Just return the meals for the current day of the week\r\n            const today = new Date();\r\n\r\n            // get protocol active id\r\n            const protocol = await db\r\n              .selectFrom('nutritionist_protocols as p')\r\n              .select(['p.id'])\r\n              .where('p.client_id', '=', userId)\r\n              .where('p.started_at', '<=', today)\r\n              .where('p.ended_at', 'is', null)\r\n              .orderBy('p.started_at', 'desc')\r\n              .executeTakeFirst();\r\n\r\n              if (!protocol) {\r\n                return {\r\n                  status: 'success',\r\n                  data: {\r\n                    has_protocol: false,\r\n                  },\r\n                };\r\n              }\r\n\r\n              const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();\r\n\r\n              const mealRows = await db\r\n                .selectFrom('nutritionist_protocols_meals as p')\r\n                .leftJoin('nutritionist_protocols_meals_foods as f', 'f.meal_id', 'p.id')\r\n                .leftJoin('foods as food', 'food.id', 'f.food_id')\r\n                .select([\r\n                  'p.id',\r\n                  'p.name',\r\n                  'p.day_of_week',\r\n                  'p.meal_time',\r\n                  'food.name as food_name',\r\n                  'f.quantity',\r\n                  'f.unit',\r\n                ])\r\n                .where('protocol_id', '=', protocol.id)\r\n                .where('p.day_of_week', '=', dayOfWeek)\r\n                .orderBy('p.meal_time', 'asc')\r\n                .orderBy('p.id', 'asc')\r\n                .execute();\r\n\r\n                const mealsByDay = new Map<string, any[]>();\r\n                mealRows.forEach((row) => {\r\n                  const day = row.day_of_week.toLowerCase(); // Padronizar como minúsculo, ex: \"monday\"\r\n                  if (!mealsByDay.has(day)) {\r\n                    mealsByDay.set(day, []);\r\n                  }\r\n\r\n                  // Encontrar ou criar a refeição no dia\r\n                  let meal = mealsByDay.get(day)?.find((m) => m.id === row.id);\r\n                  if (!meal) {\r\n                    meal = {\r\n                      id: row.id,\r\n                      name: row.name,\r\n                      meal_time: row.meal_time,\r\n                      foods: [],\r\n                    };\r\n                    mealsByDay.get(day)?.push(meal);\r\n                  }\r\n\r\n                  // Adicionar alimento, se existir\r\n                  if (row.food_name) {\r\n                    meal.foods.push({\r\n                      name: row.food_name,\r\n                      unit: row.unit,\r\n                      quantity: row.quantity,\r\n                    });\r\n                  }\r\n                });\r\n\r\n                const meals = Object.fromEntries(mealsByDay);\r\n\r\n                return {\r\n                  status: 'success',\r\n                  data: {\r\n                    has_protocol: true,\r\n                    meals,\r\n                  },\r\n                };\r\n          }\r\n            \r\n\r\n          async deleteProtocolDiet(id: number, userId: number) {  \r\n            const protocol = await db\r\n              .selectFrom('nutritionist_protocols')\r\n              .where('id', '=', id)\r\n              .where('client_id', '=', userId)\r\n              .select(['id'])\r\n              .executeTakeFirst();\r\n          \r\n            if (!protocol) {\r\n              return {\r\n                status: 'error',\r\n                message: 'Protocol not found',\r\n              };\r\n            }\r\n          \r\n            // Excluir protocolo\r\n            await db\r\n              .deleteFrom('nutritionist_protocols')\r\n              .where('id', '=', id)\r\n              .execute();\r\n          \r\n            return {\r\n              status: 'success',\r\n              data: [],\r\n              message: 'Protocol deleted successfully',\r\n            };\r\n          }\r\n\r\n          async deleteProtocolWorkout(id: number, userId: number) {\r\n            const protocol = await db\r\n              .selectFrom('coach_protocols')\r\n              .where('id', '=', id)\r\n              .where('client_id', '=', userId)\r\n              .select(['id'])\r\n              .executeTakeFirst();\r\n          \r\n            if (!protocol) {\r\n              return {\r\n                status: 'error',\r\n                message: 'Protocol not found',\r\n              };\r\n            }\r\n          \r\n            // Excluir protocolo\r\n            await db\r\n              .deleteFrom('coach_protocols')\r\n              .where('id', '=', id)\r\n              .execute();\r\n          \r\n            return {\r\n              status: 'success',\r\n              data: [],\r\n              message: 'Protocol deleted successfully',\r\n            };\r\n          }\r\n\r\n          // Check protocol workout\r\n\r\n          // Check protocol diet\r\n          async checkProtocolDiet(userId: number, body: any) {\r\n          const { meal_id } = body;\r\n          let { meal_name, daily_at, meal_foods } = body;\r\n\r\n          const tz = 'America/Sao_Paulo';\r\n\r\n          const today = dayjs().tz(tz).format('YYYY-MM-DD HH:mm:ss');\r\n\r\n          daily_at = (daily_at ? dayjs.tz(daily_at, tz).format('YYYY-MM-DD HH:mm:ss') : today);\r\n\r\n          // check if meal_id is already checked (apenas para refeições do protocolo)\r\n          if (meal_id) {\r\n            const isMealChecked = await db\r\n              .selectFrom('daily_meals')\r\n              .select(['id'])\r\n              .where('user_id', '=', userId)\r\n              .where('meal_id', '=', meal_id)\r\n              .where(sql`DATE(daily_at)`, '=', sql`DATE(${daily_at})`)\r\n              .executeTakeFirst();\r\n\r\n              if (isMealChecked) {\r\n                return {\r\n                  status: 'error',\r\n                  message: 'Meal already checked',\r\n                };\r\n              }\r\n          }\r\n\r\n          let sum: any = { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 };\r\n\r\n          let food: any;\r\n\r\n          if(meal_id) {\r\n          // get protocol_id meal_time calories carbs protein fat from meal and foods\r\n          food = await db\r\n            .selectFrom('nutritionist_protocols_meals_foods as p')\r\n            .leftJoin('nutritionist_protocols_meals as m', 'm.id', 'p.meal_id')\r\n            .select([\r\n              'm.protocol_id',\r\n              'p.meal_id',\r\n              'p.name',\r\n              'm.name as meal_name',\r\n              'm.meal_time',\r\n              'p.quantity',\r\n              'p.calories',\r\n              'p.carbs',\r\n              'p.protein',\r\n              'p.fat',\r\n              'p.fiber',\r\n            ])\r\n            .where('p.meal_id', '=', meal_id)\r\n            .execute();\r\n\r\n            if (!food) {\r\n              throw new HttpException('Meal not found', HttpStatus.NOT_FOUND);\r\n            }\r\n\r\n            // sum calories carbs protein fat from food x quantity\r\n            // CORREÇÃO: Verificar se os valores precisam ser convertidos\r\n            sum = food.reduce((acc: any, curr: any) => {\r\n              const quantity = Number(curr.quantity) || 0;\r\n              const calories = Number(curr.calories) || 0;\r\n              const protein = Number(curr.protein) || 0;\r\n              const carbs = Number(curr.carbs) || 0;\r\n              const fat = Number(curr.fat) || 0;\r\n              const fiber = Number(curr.fiber) || 0;\r\n\r\n              // CORREÇÃO TEMPORÁRIA: Sempre aplicar conversão por 100g\r\n              // Baseado no comentário original: \"quantity is in grams and calories carbs protein fat are in 100g\"\r\n              // Vamos assumir que TODOS os valores estão por 100g e precisam ser convertidos\r\n              const conversionFactor = quantity / 100;\r\n\r\n              console.log(`[DEBUG] Alimento: ${curr.name || 'N/A'}, Quantidade: ${quantity}g, Calorias originais: ${calories}, Fator: ${conversionFactor}, Calorias convertidas: ${calories * conversionFactor}`);\r\n\r\n              acc.calories += parseFloat((calories * conversionFactor).toFixed(2));\r\n              acc.carbs += parseFloat((carbs * conversionFactor).toFixed(2));\r\n              acc.protein += parseFloat((protein * conversionFactor).toFixed(2));\r\n              acc.fat += parseFloat((fat * conversionFactor).toFixed(2));\r\n              acc.fiber += parseFloat((fiber * conversionFactor).toFixed(2));\r\n\r\n              return acc;\r\n            }, { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 });\r\n          }\r\n          \r\n          if(!meal_id && meal_foods) {\r\n             sum = meal_foods.reduce((acc: any, curr: any) => {\r\n              acc.calories += parseFloat(Number(curr.calories).toFixed(2));\r\n              acc.carbs += parseFloat(Number(curr.carbs).toFixed(2));\r\n              acc.protein += parseFloat(Number(curr.protein).toFixed(2));\r\n              acc.fat += parseFloat(Number(curr.fat).toFixed(2));\r\n              acc.fiber += parseFloat(Number(curr.fiber).toFixed(2));\r\n              return acc;\r\n            }, { calories: 0, carbs: 0, protein: 0, fat: 0, fiber: 0 });  \r\n          }            \r\n\r\n            const mealData: any = {\r\n              user_id: userId,\r\n              protocol_id: (meal_id && food && food.length > 0) ? food[0]?.protocol_id : null,\r\n              meal_id: meal_id || null,\r\n              name: (meal_id && food && food.length > 0) ? food[0]?.meal_name : meal_name,\r\n              calories: sum.calories,\r\n              carbs: sum.carbs,\r\n              protein: sum.protein,\r\n              fat: sum.fat,\r\n              fiber: sum.fiber || 0,\r\n              daily_at,\r\n              created_at: new Date(),\r\n              updated_at: new Date(),\r\n            }\r\n\r\n            const dailyMeal = await db\r\n              .insertInto('daily_meals')\r\n              .values(mealData)\r\n              .executeTakeFirst();\r\n\r\n            const dailyMealId = Number(dailyMeal.insertId);\r\n\r\n            if(meal_foods) {\r\n              await db\r\n                .insertInto('daily_meals_foods')\r\n                .values(meal_foods.map((food: any) => ({\r\n                  meal_id: dailyMealId,\r\n                  food_id: null,\r\n                  name: food.name,\r\n                  quantity: food.quantity,\r\n                  unit: food.unit || 'g',\r\n                  calories: food.calories || 0,\r\n                  protein: food.protein || 0,\r\n                  carbs: food.carbs || 0,\r\n                  fat: food.fat || 0,\r\n                  fiber: food.fiber || 0,\r\n                })))\r\n                .execute();\r\n            }\r\n\r\n\r\n            return {\r\n              status: 'success',\r\n              data: [],\r\n              message: 'Meal checked successfully',\r\n            };\r\n          }\r\n\r\n          async uncheckProtocolDiet(userId: number, body: any) {\r\n            const { id } = body;\r\n\r\n            // daily\r\n            await db\r\n              .deleteFrom('daily_meals')\r\n              .where('id', '=', id)\r\n              .where('user_id', '=', userId)\r\n              .execute();\r\n\r\n            return {\r\n              status: 'success',\r\n              data: [],\r\n              message: 'Meal unchecked successfully',\r\n            };\r\n          }\r\n\r\n          async getDailyCheckedMeal(userId: number, query: any) {\r\n            const { date_start, date_end } = query;\r\n            // if date_start and date_end are not provided, use current date\r\n            const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n            const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();    \r\n          \r\n            const meals = await db\r\n              .selectFrom('daily_meals as d')\r\n              .leftJoin('nutritionist_protocols_meals as m', 'm.id', 'd.meal_id')\r\n              .select([\r\n                'd.id',\r\n                'd.meal_id',\r\n                'm.name',\r\n                'm.meal_time',\r\n              ])\r\n              .where('d.user_id', '=', userId)\r\n              .where('d.daily_at', '>=', startOfDay)\r\n              .where('d.daily_at', '<=', endOfDay)\r\n              .where('d.meal_id', 'is not', null)\r\n              .execute();\r\n          \r\n            return {\r\n              status: 'success',\r\n              data: meals,\r\n            };\r\n          }\r\n\r\n          async getProtocolsWorkoutExercises(userId: number) {\r\n            const today = new Date();\r\n            const protocol = await db\r\n              .selectFrom('coach_protocols as p')\r\n              .select(['p.id'])\r\n              .where('p.client_id', '=', userId)\r\n              .where('p.started_at', '<=', today)\r\n              .where('p.ended_at', 'is', null)\r\n              .orderBy('p.started_at', 'desc')\r\n              .executeTakeFirst();\r\n        \r\n            if (!protocol) {\r\n              return {\r\n                status: 'success',\r\n                data: {\r\n                  has_protocol: false,\r\n                },\r\n              };\r\n            }\r\n        \r\n            const exercises = await db\r\n              .selectFrom('coach_protocols_workouts as p')\r\n              .leftJoin('coach_protocols_workouts_exercises as pe', 'pe.workout_id', 'p.id')\r\n              .leftJoin('exercises as e', 'e.id', 'pe.exercise_id')\r\n              .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')\r\n              .leftJoin('select_options as equipment', 'equipment.id', 'e.equipment_id')\r\n              .select([\r\n                'p.id',\r\n                'pe.id as pe_id',\r\n                'pe.exercise_id',\r\n                // 'pe.split_group',\r\n                'e.name',\r\n                'pe.name as exercise_name',\r\n                's.value_option as muscle_group',\r\n                'equipment.value_option as equipment',\r\n                'e.media_url',\r\n                'pe.sets',\r\n                'pe.reps',\r\n                'pe.rpe',\r\n                'pe.rest_seconds',\r\n                'pe.notes',\r\n              ])\r\n              .where('protocol_id', '=', protocol.id)\r\n              // .where('pe.exercise_id', 'is not', null)\r\n              // .orderBy('p.split_group', 'asc')\r\n              .execute();\r\n        \r\n            // Agrupar os exercícios por split_group\r\n            const workouts = exercises.reduce((acc, curr) => {\r\n              if (!acc[curr.id]) {\r\n                acc[curr.id] = {\r\n                  id: curr.id,\r\n                  exercises: [],\r\n                };\r\n              }\r\n              acc[curr.id].exercises.push(curr);\r\n              return acc;\r\n            }, {});\r\n        \r\n            // Transforma o objeto em um array de workouts\r\n            const workoutsArray = Object.values(workouts);\r\n\r\n            const workoutsAndExercises = workoutsArray.map((workout: any) => ({\r\n              id: workout.id,\r\n              exercises: workout.exercises.map((exercise) => ({\r\n                id: exercise.pe_id,\r\n                exercise_id: exercise.exercise_id || null,\r\n                name: exercise.name || exercise.exercise_name,\r\n                muscle_group: exercise.muscle_group || null,\r\n                equipment: exercise.equipment || null,\r\n                media_url: exercise.media_url || null,\r\n                sets: exercise.sets,\r\n                reps: exercise.reps,\r\n                rpe: exercise.rpe,\r\n                rest_seconds: exercise.rest_seconds,\r\n                notes: exercise.notes,\r\n              })),\r\n            }));\r\n\r\n        \r\n            return {\r\n              status: 'success',\r\n              data: {\r\n                has_protocol: true,\r\n                id: protocol.id,\r\n                workouts: workoutsAndExercises,\r\n              },\r\n            };\r\n        }\r\n\r\n        private convertTimeToHours(time: string): number {\r\n          const [hours, minutes, seconds] = time.split(':').map(Number);\r\n          return hours + minutes / 60 + seconds / 3600;\r\n        }\r\n\r\n        async calculateCalories(workoutTime: string, met: number, weight: string | number) {\r\n          const weightKg = typeof weight === 'string' ? parseFloat(weight) : weight;\r\n          const timeInHours = this.convertTimeToHours(workoutTime);\r\n          return met * weightKg * timeInHours * 1.05;\r\n        }\r\n\r\n        async postProtocolsWorkoutDaily(userId: number, body: any) {\r\n        const { protocol_id, protocol_workout_id, workout_time, total_weight, met, series } = body;\r\n\r\n        const user = await db\r\n          .selectFrom('users')\r\n          .select(['weight'])\r\n          .where('id', '=', userId)\r\n          .executeTakeFirst();\r\n\r\n        const weight = user?.weight || 0;\r\n\r\n        const total_calories = await this.calculateCalories(workout_time, Number(met), Number(weight));\r\n          \r\n        const seriesData = series.map((serie: any) => ({\r\n          protocol_exercise_id: serie.protocol_exercise_id,\r\n          calories: serie.calories,\r\n          weight: serie.weight,\r\n          reps: serie.reps,\r\n        }));\r\n        \r\n        const dailyProtocolData = {\r\n          user_id: userId,\r\n          protocol_id,\r\n          protocol_workout_id,\r\n          met,\r\n          workout_time,\r\n          total_calories,\r\n          total_weight\r\n        };\r\n\r\n        const dailyProtocol = await db\r\n          .insertInto('daily_coach_protocol')\r\n          .values(dailyProtocolData)\r\n          .executeTakeFirst();\r\n\r\n        const dailyProtocolId = Number(dailyProtocol.insertId);\r\n\r\n        await db\r\n          .insertInto('daily_coach_protocol_series')\r\n          .values(seriesData.map((serie: any) => ({ daily_id: dailyProtocolId, ...serie })))\r\n          .execute();\r\n\r\n        return {\r\n          status: 'success',\r\n          data: [],\r\n        };\r\n      }\r\n\r\n      async getProtocolsWorkoutDaily(userId: number, query: any) {\r\n        const { page = 1, date_start, date_end } = query;\r\n        const limit = 10;\r\n        const offset = (page - 1) * limit;\r\n\r\n        // if date_start and date_end are not provided, use current date\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n      \r\n        let queryResult = db\r\n          .selectFrom('daily_coach_protocol as dcp')\r\n          .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n          .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n          .leftJoin('daily_coach_protocol_series as dcps', 'dcps.daily_id', 'dcp.id')\r\n          .leftJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcps.protocol_exercise_id')\r\n          .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')\r\n          .leftJoin('select_options as so_type', 'so_type.id', 'cp.type_id')\r\n          .leftJoin('select_options as so_muscle', 'so_muscle.id', 'e.muscle_group_id')\r\n          .select([\r\n            'dcp.created_at as date',\r\n            'cpw.name as workout_name',\r\n            'dcp.workout_time',\r\n            'dcp.total_calories',\r\n            (eb) =>\r\n              eb\r\n                .fn.count(eb.ref('dcps.protocol_exercise_id'))\r\n                .distinct()\r\n                .as('exercise_count'),\r\n            'so_type.value_option as type',\r\n            sql<string>`GROUP_CONCAT(DISTINCT so_muscle.value_option SEPARATOR ', ')`.as('muscle_groups'), // Usando sql diretamente\r\n          ]);\r\n\r\n          queryResult = queryResult.where('dcp.user_id', '=', userId);\r\n\r\n          if (date_start && date_end) {\r\n            queryResult = queryResult.where('dcp.created_at', '>=', startOfDay).where('dcp.created_at', '<=', endOfDay);\r\n          }\r\n\r\n          queryResult = queryResult.groupBy(['dcp.id', 'cpw.name', 'so_type.value_option'])\r\n          .limit(limit)\r\n          .offset(offset)\r\n          .orderBy('dcp.created_at', 'desc');\r\n\r\n          const result = await queryResult\r\n          .execute();\r\n\r\n          let queryTotalRecordsResult: any = db\r\n          .selectFrom('daily_coach_protocol as dcp')\r\n          .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n          .where('dcp.user_id', '=', userId);\r\n\r\n          if (date_start && date_end) {\r\n            queryTotalRecordsResult = queryTotalRecordsResult.where('dcp.created_at', '>=', startOfDay).where('dcp.created_at', '<=', endOfDay);\r\n          }\r\n\r\n          queryTotalRecordsResult = queryTotalRecordsResult.select(db.fn.countAll().as('total'));\r\n\r\n          const totalRecordsResult = await queryTotalRecordsResult\r\n          .executeTakeFirstOrThrow();\r\n      \r\n        const totalRecords = parseInt(totalRecordsResult.total, 10);\r\n        const hasMore = totalRecords > page * limit;\r\n      \r\n        return {\r\n          status: 'success',\r\n          data: result.map((row) => ({\r\n            date: row.date,\r\n            workout_name: row.workout_name,\r\n            total_calories: row.total_calories,\r\n            workout_time: row.workout_time,\r\n            exercise_count: Number(row.exercise_count),\r\n            type: row.type || 'N/A',\r\n            muscle_groups: row.muscle_groups || 'N/A', // Garante que não seja undefined\r\n          })),\r\n          pagination: {\r\n            total_records: totalRecords,\r\n            current_page: page,\r\n            limit,\r\n            has_more: hasMore,\r\n          },\r\n        };\r\n      }\r\n\r\n      async calculateDaysGoalSequence(userId: number): Promise<number> {\r\n        // Pegar o último dia registrado (o ponto de partida do streak)\r\n        const latestGoal = await db\r\n        .selectFrom('daily_meals_goal')\r\n        .select(['goal_date', 'goal_met'])\r\n        .where('user_id', '=', userId)\r\n        .orderBy('goal_date', 'desc')\r\n        .limit(1)\r\n        .executeTakeFirst();\r\n\r\n        // Se não há registros ou o último dia não atingiu a meta, o streak é 0\r\n        if (!latestGoal || !latestGoal.goal_met) {\r\n        return 0;\r\n        }\r\n\r\n        let streak = 1; // O último dia conta como 1, se goal_met = TRUE\r\n        const latestDate = new Date(latestGoal.goal_date);\r\n        let previousDate = new Date(latestDate);\r\n        previousDate.setDate(latestDate.getDate() - 1);\r\n\r\n        // Verificar o dia anterior para determinar se a sequência continua\r\n        while (true) {\r\n        const previousDateStr = previousDate.toISOString().split('T')[0];\r\n\r\n        const previousGoal = await db\r\n          .selectFrom('daily_meals_goal')\r\n          .select('goal_met')\r\n          .where('user_id', '=', userId)\r\n          .where('goal_date', '=', previousDateStr)\r\n          .executeTakeFirst();\r\n\r\n        // Se o dia anterior não existe ou a meta não foi atingida, a sequência para\r\n        if (!previousGoal || !previousGoal.goal_met) {\r\n          break;\r\n        }\r\n\r\n        streak++;\r\n        previousDate.setDate(previousDate.getDate() - 1); // Continuar verificando o próximo dia anterior\r\n        }\r\n\r\n        return streak;\r\n      }\r\n\r\n      async getProgressNutritionalDaysGoalSequence(userId: number) {\r\n        const daysGoalSequence = await this.calculateDaysGoalSequence(userId);\r\n\r\n        return {\r\n          status: 'success',\r\n          data: {\r\n            days_goal_sequence: daysGoalSequence,\r\n          },\r\n        };\r\n      }\r\n\r\n      async postProgressEvaluations(userId: number, body: any, files: { front?: Express.Multer.File, back?: Express.Multer.File, side?: Express.Multer.File }) {\r\n        const { weight, bf } = body;\r\n\r\n        // Debug logs\r\n        console.log('Received body:', body);\r\n        console.log('Weight:', weight, 'Type:', typeof weight);\r\n        console.log('BF:', bf, 'Type:', typeof bf);\r\n        console.log('Files:', Object.keys(files).filter(key => files[key]));\r\n\r\n        // Convert to numbers and validate\r\n        const weightNum = parseFloat(weight);\r\n        const bfNum = parseFloat(bf);\r\n\r\n        if (isNaN(weightNum) || weightNum <= 0 || isNaN(bfNum) || bfNum <= 0) {\r\n          throw new HttpException({\r\n            status: 400,\r\n            message: ['Os campos Peso e Gordura Corporal são obrigatórios e devem ser números válidos maiores que zero.'],\r\n          }, 400);\r\n        }\r\n      \r\n        let frontImage: string | null = null;\r\n        let backImage: string | null = null;\r\n        let sideImage: string | null = null;\r\n      \r\n        const uploadDir = path.join(__dirname, '..', '..', 'storage', 'media', userId.toString(), 'evaluations');\r\n      \r\n        if (!fs.existsSync(uploadDir)) {\r\n          fs.mkdirSync(uploadDir, { recursive: true });\r\n        }\r\n      \r\n        // Tipos permitidos e dimensões máximas\r\n        const typesAllowed = ['image/jpeg', 'image/png', 'image/jpg'];\r\n        const MAX_DIMENSION = 1080;\r\n      \r\n        for (const key of ['front', 'back', 'side'] as const) {\r\n          const file = files[key];\r\n      \r\n          if (file) {\r\n            if (file.size > 30 * 1024 * 1024) {\r\n              throw new HttpException({ status: 400, message: ['O arquivo deve ter no máximo 30MB.'] }, 400);\r\n            }\r\n      \r\n            if (!typesAllowed.includes(file.mimetype)) {\r\n              throw new HttpException({ status: 400, message: ['O arquivo deve ser uma imagem (jpg, jpeg, png).'] }, 400);\r\n            }\r\n      \r\n            const fileExtension = path.extname(file.originalname);\r\n            const fileName = `${randomUUID()}${fileExtension}`;\r\n            const filePath = path.join(uploadDir, fileName);\r\n      \r\n            const image = sharp(file.buffer);\r\n            const metadata = await image.metadata();\r\n      \r\n            if (metadata.width! > MAX_DIMENSION || metadata.height! > MAX_DIMENSION) {\r\n              const imageResized = await image\r\n                .resize({\r\n                  width: metadata.width! > metadata.height! ? MAX_DIMENSION : undefined,\r\n                  height: metadata.height! > metadata.width! ? MAX_DIMENSION : undefined,\r\n                  fit: 'inside',\r\n                  withoutEnlargement: true\r\n                })\r\n                .jpeg({ quality: 80 })\r\n                .toBuffer();\r\n      \r\n              await fs.promises.writeFile(filePath, imageResized);\r\n            } else {\r\n              await fs.promises.writeFile(filePath, file.buffer);\r\n            }\r\n      \r\n            if (key === 'front') frontImage = fileName;\r\n            if (key === 'back') backImage = fileName;\r\n            if (key === 'side') sideImage = fileName;\r\n          }\r\n        }\r\n      \r\n        const new_evaluation = await db\r\n          .insertInto('evaluations')\r\n          .values({\r\n            user_id: userId,\r\n            weight: weightNum,\r\n            bf: bfNum,\r\n            created_at: new Date(),\r\n            updated_at: new Date(),\r\n          })\r\n          .executeTakeFirst();\r\n\r\n        const evaluationId = Number(new_evaluation.insertId);\r\n\r\n        // Update user weight and bodyfat\r\n        let userUpdateData = {\r\n          ...(weightNum && weightNum > 0 && { weight: weightNum }),\r\n          ...(bfNum && bfNum > 0 && { bodyfat: bfNum }),\r\n        };\r\n\r\n        if (Object.keys(userUpdateData).length > 0) {\r\n          await db\r\n            .updateTable('users')\r\n            .set(userUpdateData)\r\n            .where('id', '=', userId)\r\n            .execute();\r\n        }        \r\n      \r\n        if (frontImage || backImage || sideImage) {\r\n          const imagesToInsert = [\r\n            { name: frontImage, position: 'front' },\r\n            { name: backImage, position: 'back' },\r\n            { name: sideImage, position: 'side' },\r\n          ].filter(img => img.name !== null);\r\n      \r\n          await Promise.all(\r\n            imagesToInsert.map(image =>\r\n              db.insertInto('evaluations_photos')\r\n                .values({\r\n                  evaluation_id: evaluationId,\r\n                  media_type: 'image',\r\n                  media_position: image.position as 'front' | 'back' | 'side',\r\n                  media_url: image.name!,\r\n                })\r\n                .execute()\r\n            )\r\n          );\r\n        }\r\n      \r\n        return { status: 'success', data: [] };\r\n      }\r\n\r\n      async postProgressEvaluationsMeasurements(userId: number, body: any) {\r\n        const { shoulders, chest, waist, abdomen, hips, biceps_right, biceps_left, forearm_right, forearm_left, thigh_right, thigh_left, calf_right, calf_left } = body;\r\n        const new_evaluation_measurements = await db\r\n        .insertInto('evaluations_measurements')\r\n        .values({\r\n          user_id: userId,\r\n          shoulders: shoulders,\r\n          chest: chest,\r\n          waist: waist,\r\n          abdomen: abdomen,\r\n          hips: hips,\r\n          biceps_right: biceps_right,\r\n          biceps_left: biceps_left,\r\n          forearm_right: forearm_right,\r\n          forearm_left: forearm_left,\r\n          thigh_right: thigh_right,\r\n          thigh_left: thigh_left,\r\n          calf_right: calf_right,\r\n          calf_left: calf_left,\r\n          created_at: new Date(),\r\n          updated_at: new Date(),\r\n        })\r\n        .executeTakeFirst();\r\n\r\n        return {\r\n          status: 'success',\r\n          data: [],\r\n        };\r\n\r\n      }\r\n\r\n      async getProgressEvaluations(userId: number, query: any) {\r\n        const { page = 1, limit = 5 } = query;\r\n      \r\n        const offset = (page - 1) * limit;\r\n      \r\n        // Consultando as avaliações\r\n        const evaluations = await db\r\n          .selectFrom('evaluations as e')\r\n          .leftJoin('evaluations_photos as ep', 'ep.evaluation_id', 'e.id')\r\n          .select([\r\n            'e.id',\r\n            'e.weight',\r\n            'e.bf',\r\n            'e.created_at',\r\n            'ep.media_url',\r\n            'ep.media_position',\r\n          ])\r\n          .where('e.user_id', '=', userId)\r\n          .orderBy('e.created_at', 'desc')\r\n          .limit(limit)\r\n          .offset(offset)\r\n          .execute();\r\n      \r\n        // Consultando as medições\r\n        const measurements = await db\r\n          .selectFrom('evaluations_measurements as em')\r\n          .select([\r\n            'em.user_id',\r\n            'em.shoulders',\r\n            'em.chest',\r\n            'em.waist',\r\n            'em.abdomen',\r\n            'em.hips',\r\n            'em.biceps_right',\r\n            'em.biceps_left',\r\n            'em.forearm_right',\r\n            'em.forearm_left',\r\n            'em.thigh_right',\r\n            'em.thigh_left',\r\n            'em.calf_right',\r\n            'em.calf_left',\r\n            'em.created_at',\r\n          ])\r\n          .where('em.user_id', '=', userId)\r\n          .orderBy('em.created_at', 'desc')\r\n          .limit(limit)\r\n          .offset(offset)\r\n          .execute();\r\n      \r\n        // Obtendo o total de avaliações\r\n        const totalEvaluations = await db\r\n          .selectFrom('evaluations')\r\n          .where('user_id', '=', userId)\r\n          .select(db.fn.countAll().as('total'))\r\n          .executeTakeFirst();\r\n      \r\n        // Formatando as avaliações\r\n        const formattedEvaluations = evaluations.reduce((acc: any, evaluation: any) => {\r\n          // Verifica se a avaliação já foi processada\r\n          let evaluationData: any = acc.find((item: any) => item.id === evaluation.id);\r\n      \r\n          // Se a avaliação ainda não foi processada, cria um novo item para avaliação\r\n          if (!evaluationData) {\r\n            evaluationData = {\r\n              id: evaluation.id,\r\n              type: 'evaluation', // Adiciona tipo de dado\r\n              weight: evaluation.weight,\r\n              bf: evaluation.bf,\r\n              created_at: evaluation.created_at,\r\n              photos: {\r\n                front: null,\r\n                back: null,\r\n                side: null,\r\n              },\r\n            };\r\n            acc.push(evaluationData);\r\n          }\r\n\r\n          const photo_base_url = process.env.STATIC_URL + '/storage/media/' + userId + '/evaluations/';\r\n      \r\n          // Preenche a foto baseada na posição\r\n          if (evaluation.media_position === 'front') {\r\n            evaluationData.photos.front = photo_base_url + evaluation.media_url;\r\n          } else if (evaluation.media_position === 'back') {\r\n            evaluationData.photos.back = photo_base_url + evaluation.media_url;\r\n          } else if (evaluation.media_position === 'side') {\r\n            evaluationData.photos.side = photo_base_url + evaluation.media_url;\r\n          }\r\n      \r\n          return acc;\r\n        }, []);\r\n      \r\n        // Formatando as medições\r\n        const formattedMeasurements = measurements.reduce((acc: any, measurement: any) => {\r\n          // Verifica se a medição já foi processada\r\n          let measurementData: any = acc.find((item: any) => item.created_at === measurement.created_at);\r\n      \r\n          // Se a medição ainda não foi processada, cria um novo item para medição\r\n          if (!measurementData) {\r\n            measurementData = {\r\n              id: measurement.user_id,\r\n              type: 'measurement', // Adiciona tipo de dado\r\n              shoulders: measurement.shoulders,\r\n              chest: measurement.chest,\r\n              waist: measurement.waist,\r\n              abdomen: measurement.abdomen,\r\n              hips: measurement.hips,\r\n              biceps_right: measurement.biceps_right,\r\n              biceps_left: measurement.biceps_left,\r\n              forearm_right: measurement.forearm_right,\r\n              forearm_left: measurement.forearm_left,\r\n              thigh_right: measurement.thigh_right,\r\n              thigh_left: measurement.thigh_left,\r\n              calf_right: measurement.calf_right,\r\n              calf_left: measurement.calf_left,              \r\n              created_at: measurement.created_at,\r\n            };\r\n            acc.push(measurementData);\r\n          }\r\n      \r\n          return acc;\r\n        }, []);\r\n      \r\n        // Juntando as avaliações e medições em um único array\r\n        const allData = [...formattedEvaluations, ...formattedMeasurements];\r\n      \r\n        // Ordenando tudo por data de criação para que fique organizado\r\n        allData.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());\r\n      \r\n        return {\r\n          status: 'success',\r\n          data: allData,\r\n          pagination: {\r\n            page,\r\n            limit,\r\n            total: Number(totalEvaluations?.total),\r\n          },\r\n        };\r\n      }\r\n      \r\n\r\n      async calculateCurrentStreak(userId: number): Promise<number> {\r\n        const latestGoal = await db\r\n          .selectFrom('daily_meals_goal')\r\n          .select(['goal_date', 'goal_met'])\r\n          .where('user_id', '=', userId)\r\n          .orderBy('goal_date', 'desc')\r\n          .limit(1)\r\n          .executeTakeFirst();\r\n      \r\n        if (!latestGoal || !latestGoal.goal_met) return 0;\r\n      \r\n        let streak = 1;\r\n        const latestDate = new Date(latestGoal.goal_date);\r\n        let previousDate = new Date(latestDate);\r\n        previousDate.setDate(latestDate.getDate() - 1);\r\n      \r\n        while (true) {\r\n          const previousDateStr = previousDate.toISOString().split('T')[0];\r\n          const previousGoal = await db\r\n            .selectFrom('daily_meals_goal')\r\n            .select('goal_met')\r\n            .where('user_id', '=', userId)\r\n            .where('goal_date', '=', previousDateStr)\r\n            .executeTakeFirst();\r\n      \r\n          if (!previousGoal || !previousGoal.goal_met) break;\r\n      \r\n          streak++;\r\n          previousDate.setDate(previousDate.getDate() - 1);\r\n        }\r\n      \r\n        return streak;\r\n      }\r\n\r\n      async calculateRecordStreak(userId: number): Promise<number> {\r\n        // Pegar todos os dias com meta atingida, ordenados por data\r\n        const goals = await db\r\n          .selectFrom('daily_meals_goal')\r\n          .select('goal_date')\r\n          .where('user_id', '=', userId)\r\n          .where('goal_met', '=', true)\r\n          .orderBy('goal_date', 'asc')\r\n          .execute();\r\n      \r\n        if (!goals.length) return 0;\r\n      \r\n        let maxStreak = 0;\r\n        let currentStreak = 1;\r\n      \r\n        for (let i = 1; i < goals.length; i++) {\r\n          const currentDate = new Date(goals[i].goal_date);\r\n          const previousDate = new Date(goals[i - 1].goal_date);\r\n          const diffInDays = (currentDate.getTime() - previousDate.getTime()) / (1000 * 60 * 60 * 24);\r\n      \r\n          if (diffInDays === 1) {\r\n            // Dias consecutivos\r\n            currentStreak++;\r\n          } else {\r\n            // Sequência quebrada, reiniciar\r\n            maxStreak = Math.max(maxStreak, currentStreak);\r\n            currentStreak = 1;\r\n          }\r\n        }\r\n      \r\n        // Atualizar o máximo com a última sequência\r\n        maxStreak = Math.max(maxStreak, currentStreak);\r\n      \r\n        return maxStreak;\r\n      }\r\n\r\n      async calculateWeekAttendance(userId: number, currentDate: Date): Promise<number> {\r\n        // Definir o início e fim da semana atual (segunda a domingo)\r\n        const startOfWeek = new Date(currentDate);\r\n        startOfWeek.setDate(currentDate.getDate() - (currentDate.getDay() + 6) % 7); // Segunda-feira\r\n        const endOfWeek = new Date(startOfWeek);\r\n        endOfWeek.setDate(startOfWeek.getDate() + 6); // Domingo\r\n      \r\n        const startDateStr = startOfWeek.toISOString().split('T')[0];\r\n        const endDateStr = endOfWeek.toISOString().split('T')[0];\r\n      \r\n        // Contar dias com meta atingida na semana\r\n        const successfulDays = await db\r\n          .selectFrom('daily_meals_goal')\r\n          .select(sql<number>`COUNT(DISTINCT goal_date)`.as('count'))\r\n          .where('user_id', '=', userId)\r\n          .where('goal_date', '>=', startDateStr)\r\n          .where('goal_date', '<=', endDateStr)\r\n          .where('goal_met', '=', true)\r\n          .executeTakeFirst();\r\n      \r\n        const daysWithGoalMet = successfulDays?.count || 0;\r\n        const totalDaysInWeek = 7; // Sempre 7 dias na semana\r\n      \r\n        // Calcular percentual\r\n        return Math.round((daysWithGoalMet / totalDaysInWeek) * 100);\r\n      }\r\n\r\n      async calculateMonthAttendance(userId: number, currentDate: Date): Promise<number> {\r\n        // Definir o início e fim do mês atual\r\n        const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\r\n        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);\r\n      \r\n        const startDateStr = startOfMonth.toISOString().split('T')[0];\r\n        const endDateStr = endOfMonth.toISOString().split('T')[0];\r\n      \r\n        // Contar dias com meta atingida no mês\r\n        const successfulDays = await db\r\n          .selectFrom('daily_meals_goal')\r\n          .select(sql<number>`COUNT(DISTINCT goal_date)`.as('count'))\r\n          .where('user_id', '=', userId)\r\n          .where('goal_date', '>=', startDateStr)\r\n          .where('goal_date', '<=', endDateStr)\r\n          .where('goal_met', '=', true)\r\n          .executeTakeFirst();\r\n      \r\n        const daysWithGoalMet = successfulDays?.count || 0;\r\n        const totalDaysInMonth = endOfMonth.getDate(); // Número de dias no mês\r\n      \r\n        // Calcular percentual\r\n        return Math.round((daysWithGoalMet / totalDaysInMonth) * 100);\r\n      }\r\n      \r\n      async getProgressDietWeekly(userId: number) {\r\n          // Obter o protocolo ativo do usuário\r\n          const protocol = await db\r\n            .selectFrom('nutritionist_protocols')\r\n            .where('client_id', '=', userId)\r\n            .where('ended_at', 'is', null)\r\n            .select(['id'])\r\n            .executeTakeFirst();\r\n      \r\n          if (!protocol) {\r\n            return {\r\n              status: 'success',\r\n              data: {\r\n                weekly: [],\r\n                success_rate: 0,\r\n                complete_meals: 0,\r\n                perfect_days: 0,\r\n              },\r\n            };\r\n          }\r\n      \r\n          // Obter o total de refeições esperadas por dia da semana do protocolo\r\n          const protocolMeals = await db\r\n            .selectFrom('nutritionist_protocols_meals')\r\n            .where('protocol_id', '=', protocol.id)\r\n            .select(['day_of_week'])\r\n            .groupBy('day_of_week')\r\n            .select((eb) => eb.fn.count('id').as('meal_count'))\r\n            .execute();\r\n      \r\n          // Tipagem explícita para o resultado\r\n          interface ProtocolMealCount {\r\n            day_of_week: string;\r\n            meal_count: number;\r\n          }\r\n      \r\n          const totalMealsByDay: { [key: string]: number } = {};\r\n          (protocolMeals as ProtocolMealCount[]).forEach((meal) => {\r\n            totalMealsByDay[meal.day_of_week] = meal.meal_count;\r\n          });\r\n      \r\n          // Mapear dias da semana (banco -> interface)\r\n          const dayMapping: { [key: string]: string } = {\r\n            monday: 'Seg',\r\n            tuesday: 'Ter',\r\n            wednesday: 'Qua',\r\n            thursday: 'Qui',\r\n            friday: 'Sex',\r\n            saturday: 'Sab',\r\n            sunday: 'Dom',\r\n          };\r\n      \r\n          // Obter o intervalo da semana atual (segunda a domingo)\r\n          const startOfWeek = dayjs().startOf('isoWeek').toDate();\r\n          const endOfWeek = dayjs().endOf('isoWeek').toDate();\r\n      \r\n          // Contar refeições completadas por dia\r\n          const dailyMeals = await db\r\n            .selectFrom('daily_meals')\r\n            .where('user_id', '=', userId)\r\n            .where('daily_at', '>=', startOfWeek)\r\n            .where('daily_at', '<=', endOfWeek)\r\n            .where('meal_id', 'is not', null)\r\n            .select((eb) => [sql<string>`DATE(daily_at)`.as('day')])\r\n            .execute();\r\n      \r\n          // Mapear os dias da semana\r\n          const weeklyProgress: any[] = [];\r\n          for (let i = 0; i < 7; i++) {\r\n            const currentDate = dayjs(startOfWeek).add(i, 'day').toDate();\r\n            const dayOfWeek = dayjs(currentDate).format('dddd').toLowerCase();\r\n            const mappedDay = dayMapping[dayOfWeek as keyof typeof dayMapping];\r\n            const total = totalMealsByDay[dayOfWeek] || 0;\r\n            const completed = dailyMeals.filter((meal) =>\r\n              dayjs(meal.day).isSame(currentDate, 'day')\r\n            ).length;\r\n      \r\n            weeklyProgress.push({\r\n              day: mappedDay,\r\n              completed,\r\n              total,\r\n            });\r\n          }\r\n      \r\n          // Calcular métricas\r\n          const completeMeals = weeklyProgress.reduce((acc: number, day: any) => acc + day.completed, 0);\r\n          const totalPossibleMeals = weeklyProgress.reduce((acc, day) => acc + day.total, 0);\r\n          const successRate = totalPossibleMeals > 0 ? (completeMeals / totalPossibleMeals) * 100 : 0;\r\n          const perfectDays = weeklyProgress.filter((day) => day.completed === day.total).length;\r\n      \r\n          return {\r\n            status: 'success',\r\n            data: {\r\n              weekly: weeklyProgress,\r\n              success_rate: Number(successRate.toFixed(2)),\r\n              complete_meals: completeMeals,\r\n              perfect_days: perfectDays,\r\n            },\r\n          };\r\n        }\r\n\r\n    // Novo método para progresso semanal consolidado (treinos, nutrição, sono, água)\r\n    async getWeeklyProgressConsolidated(userId: number) {\r\n      const startOfWeek = dayjs().startOf('isoWeek').toDate();\r\n      const endOfWeek = dayjs().endOf('isoWeek').toDate();\r\n\r\n      console.log('🔄 getWeeklyProgressConsolidated: Buscando dados para usuário', userId);\r\n      console.log('📅 Período:', startOfWeek.toISOString(), 'até', endOfWeek.toISOString());\r\n\r\n      try {\r\n        // 1. Dados de nutrição (já implementado)\r\n        const nutritionData = await this.getProgressDietWeekly(userId);\r\n\r\n        // 2. Dados de treinos\r\n        const workoutData = await db\r\n          .selectFrom('daily_workouts_activities')\r\n          .where('user_id', '=', userId)\r\n          .where('daily_at', '>=', startOfWeek)\r\n          .where('daily_at', '<=', endOfWeek)\r\n          .select([\r\n            sql`DATE(daily_at)`.as('day'),\r\n            sql`COUNT(*)`.as('completed'),\r\n            sql`SUM(calories)`.as('calories_burned')\r\n          ])\r\n          .groupBy(sql`DATE(daily_at)`)\r\n          .execute();\r\n\r\n        // 3. Dados de água\r\n        const waterData = await db\r\n          .selectFrom('daily_water')\r\n          .where('user_id', '=', userId)\r\n          .where('daily_at', '>=', startOfWeek)\r\n          .where('daily_at', '<=', endOfWeek)\r\n          .select([\r\n            sql`DATE(daily_at)`.as('day'),\r\n            sql`SUM(consumed)`.as('total_consumed')\r\n          ])\r\n          .groupBy(sql`DATE(daily_at)`)\r\n          .execute();\r\n\r\n            // 4. Dados de sono (mock por enquanto - pode ser integrado com wearables)\r\n            const sleepData: SleepData[] = [];\r\n            for (let i = 0; i < 7; i++) {\r\n              const currentDate = dayjs(startOfWeek).add(i, 'day');\r\n              sleepData.push({\r\n                day: currentDate.format('YYYY-MM-DD'),\r\n                hours: 7 + Math.random() * 2, // 7-9 horas\r\n                quality: Math.floor(Math.random() * 30) + 70 // 70-100%\r\n              });\r\n            }\r\n\r\n            // Mapear dados por dia da semana\r\n            const dayMapping = {\r\n              monday: 'Seg',\r\n              tuesday: 'Ter',\r\n              wednesday: 'Qua',\r\n              thursday: 'Qui',\r\n              friday: 'Sex',\r\n              saturday: 'Sáb',\r\n              sunday: 'Dom'\r\n            };\r\n\r\n            const weeklyConsolidated: WeeklyConsolidatedData[] = [];\r\n            for (let i = 0; i < 7; i++) {\r\n              const currentDate = dayjs(startOfWeek).add(i, 'day');\r\n              const dayOfWeek = currentDate.format('dddd').toLowerCase();\r\n              const mappedDay = dayMapping[dayOfWeek];\r\n              const dateStr = currentDate.format('YYYY-MM-DD');\r\n\r\n              // Encontrar dados para este dia\r\n              const nutritionDay = nutritionData.data.weekly.find(d => d.day === mappedDay) || { completed: 0, total: 0 };\r\n              const workoutDay = workoutData.find(d => dayjs(d.day).format('YYYY-MM-DD') === dateStr) || { completed: 0, calories_burned: 0 };\r\n              const waterDay = waterData.find(d => dayjs(d.day).format('YYYY-MM-DD') === dateStr) || { total_consumed: 0 };\r\n              const sleepDay = sleepData.find(d => d.day === dateStr) || { hours: 0, quality: 0 };\r\n\r\n              weeklyConsolidated.push({\r\n                day: mappedDay,\r\n                date: dateStr,\r\n                nutrition: {\r\n                  completed: nutritionDay.completed,\r\n                  total: nutritionDay.total,\r\n                  percentage: nutritionDay.total > 0 ? Math.round((nutritionDay.completed / nutritionDay.total) * 100) : 0\r\n                },\r\n                workout: {\r\n                  completed: Number(workoutDay.completed) || 0,\r\n                  calories_burned: Number(workoutDay.calories_burned) || 0,\r\n                  target: 1, // Meta de 1 treino por dia\r\n                  percentage: Number(workoutDay.completed) > 0 ? 100 : 0\r\n                },\r\n                water: {\r\n                  consumed: Number(waterDay.total_consumed) || 0,\r\n                  target: 2500, // Meta de 2.5L por dia\r\n                  percentage: Math.min(Math.round((Number(waterDay.total_consumed) || 0) / 2500 * 100), 100)\r\n                },\r\n                sleep: {\r\n                  hours: Number(sleepDay.hours) || 0,\r\n                  quality: Number(sleepDay.quality) || 0,\r\n                  target: 8, // Meta de 8 horas\r\n                  percentage: Math.min(Math.round((Number(sleepDay.hours) || 0) / 8 * 100), 100)\r\n                }\r\n              });\r\n            }\r\n\r\n            // Calcular estatísticas gerais\r\n            const totalDays = weeklyConsolidated.length;\r\n            const nutritionSuccess = weeklyConsolidated.filter(d => d.nutrition.percentage >= 80).length;\r\n            const workoutSuccess = weeklyConsolidated.filter(d => d.workout.percentage >= 100).length;\r\n            const waterSuccess = weeklyConsolidated.filter(d => d.water.percentage >= 80).length;\r\n            const sleepSuccess = weeklyConsolidated.filter(d => d.sleep.percentage >= 80).length;\r\n\r\n            return {\r\n              status: 'success',\r\n              data: {\r\n                weekly: weeklyConsolidated,\r\n                summary: {\r\n                  nutrition: {\r\n                    success_rate: Math.round((nutritionSuccess / totalDays) * 100),\r\n                    days_completed: nutritionSuccess\r\n                  },\r\n                  workout: {\r\n                    success_rate: Math.round((workoutSuccess / totalDays) * 100),\r\n                    days_completed: workoutSuccess,\r\n                    total_calories: weeklyConsolidated.reduce((sum, d) => sum + d.workout.calories_burned, 0)\r\n                  },\r\n                  water: {\r\n                    success_rate: Math.round((waterSuccess / totalDays) * 100),\r\n                    days_completed: waterSuccess,\r\n                    total_consumed: weeklyConsolidated.reduce((sum, d) => sum + d.water.consumed, 0)\r\n                  },\r\n                  sleep: {\r\n                    success_rate: Math.round((sleepSuccess / totalDays) * 100),\r\n                    days_completed: sleepSuccess,\r\n                    average_hours: weeklyConsolidated.reduce((sum, d) => sum + d.sleep.hours, 0) / totalDays,\r\n                    average_quality: weeklyConsolidated.reduce((sum, d) => sum + d.sleep.quality, 0) / totalDays\r\n                  }\r\n                }\r\n              }\r\n            };\r\n\r\n          } catch (error) {\r\n            console.error('❌ Erro ao buscar progresso semanal consolidado:', error);\r\n            return {\r\n              status: 'error',\r\n              message: 'Erro ao buscar dados de progresso semanal',\r\n              data: null\r\n            };\r\n          }\r\n        }\r\n\r\n      async getProgressDiet(userId: number) {\r\n      }\r\n\r\n      async getProgressWeightFat(userId: number, query: any) {\r\n        const { date_start, date_end } = query;\r\n\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n        let weightFatQuery = db\r\n        .selectFrom('evaluations')\r\n        .where('user_id', '=', userId);\r\n        if(date_start) {\r\n          weightFatQuery = weightFatQuery\r\n          .where('created_at', '>=', startOfDay);\r\n        }\r\n        if(date_end) {\r\n          weightFatQuery = weightFatQuery\r\n          .where('created_at', '<=', endOfDay);\r\n        }\r\n        const weightFat = await weightFatQuery\r\n        .select([\r\n            'created_at as date',\r\n            'weight',\r\n            'bf',\r\n        ])\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: weightFat,\r\n        }\r\n\r\n      }\r\n\r\n      async getProgressStrength(userId: number, query: any) {\r\n        const { date_start, date_end, filter, filter_id } = query;\r\n\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n        const strengthQuery = db\r\n        .selectFrom('daily_coach_protocol_series as dcp')\r\n        .innerJoin('daily_coach_protocol as dcp2', 'dcp2.id', 'dcp.daily_id')\r\n        .innerJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcp.protocol_exercise_id')\r\n        .innerJoin('exercises as e', 'e.id', 'cpwe.exercise_id')\r\n        .where('dcp2.user_id', '=', userId)\r\n        .where('dcp2.daily_at', '>=', startOfDay)\r\n        .where('dcp2.daily_at', '<=', endOfDay)\r\n        .select([\r\n            'dcp2.daily_at as date',\r\n            (eb) => eb.fn.sum('dcp.weight').as('weight'),\r\n        ]);\r\n\r\n        if (filter === 'muscle_group') {\r\n            strengthQuery\r\n            .innerJoin('select_options as so', 'so.id', 'e.muscle_group_id')\r\n            .where('so.id', '=', filter_id)\r\n        }\r\n        \r\n        if (filter === 'exercise') {\r\n            strengthQuery\r\n            .where('e.id', '=', filter_id)\r\n        }\r\n\r\n        const strength = await strengthQuery\r\n        .groupBy('dcp.created_at')\r\n        .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: strength,\r\n        }\r\n      }\r\n      \r\n  async getProgressNutritionalAnalysis(userId: number, query: { date_start?: string; date_end?: string }) {\r\n    const { date_start, date_end } = query;\r\n\r\n    // Definir intervalo de datas (padrão: semana atual)\r\n    const startOfWeek = date_start\r\n      ? dayjs(date_start).startOf('day').toDate()\r\n      : dayjs().startOf('week').toDate();\r\n    const endOfWeek = date_end\r\n      ? dayjs(date_end).endOf('day').toDate()\r\n      : dayjs().endOf('week').toDate();\r\n\r\n    // Consultar as refeições do usuário no intervalo de datas\r\n    const nutritionalDataDaily: any = await db\r\n      .selectFrom('daily_meals as dm')\r\n      .leftJoin('daily_meals_foods as dmf', 'dmf.meal_id', 'dm.id')\r\n      .leftJoin('foods as f', 'f.id', 'dmf.food_id')\r\n      .where('dm.user_id', '=', userId)\r\n      .where('dm.daily_at', '>=', startOfWeek)\r\n      .where('dm.daily_at', '<=', endOfWeek)\r\n      .select((eb) => [\r\n        eb.fn.sum('dmf.calories').as('total_calories'),\r\n        eb.fn.sum('dmf.carbs').as('total_carbs'),\r\n        eb.fn.sum('dmf.protein').as('total_protein'),\r\n        eb.fn.sum('dmf.fat').as('total_fat'),\r\n        eb.fn.count('dm.id').as('meal_count'),\r\n      ])\r\n      .executeTakeFirst();\r\n\r\n      // from protocol daily_nutritionist_protocol\r\n      const nutritionalData = await db\r\n      .selectFrom('daily_meals')\r\n      .where('user_id', '=', userId)\r\n      .where('daily_at', '>=', startOfWeek)\r\n      .where('daily_at', '<=', endOfWeek)\r\n      .select((eb) => [\r\n        eb.fn.sum('calories').as('total_calories'),\r\n        eb.fn.sum('carbs').as('total_carbs'),\r\n        eb.fn.sum('protein').as('total_protein'),\r\n        eb.fn.sum('fat').as('total_fat'),\r\n        eb.fn.count('id').as('meal_count'),\r\n      ])\r\n      .executeTakeFirst();\r\n\r\n\r\n    // Definir interface para o resultado da consulta\r\n    interface NutritionalSummary {\r\n      total_calories: number | null;\r\n      total_carbs: number | null;\r\n      total_protein: number | null;\r\n      total_fat: number | null;\r\n      meal_count: number;\r\n    }\r\n\r\n    // Se não houver refeições, retornar valores zerados\r\n    if (!nutritionalData || nutritionalData.meal_count === 0) {\r\n      return {\r\n        status: 'success',\r\n        data: {\r\n          weekly_averages: {\r\n            macronutrient_distribution: {\r\n              calories: 0,\r\n              calories_percent: 100,\r\n              carbs: 0,\r\n              carbs_percent: 0,\r\n              protein: 0,\r\n              protein_percent: 0,\r\n              fat: 0,\r\n              fat_percent: 0,\r\n            },\r\n          },\r\n        },\r\n      };\r\n    }\r\n\r\n    // Calcular médias semanais\r\n    const daysInRange = dayjs(endOfWeek).diff(startOfWeek, 'day') + 1;\r\n    const avgCalories = Number(nutritionalData.total_calories || 0) / daysInRange;\r\n    const avgCarbs = Number(nutritionalData.total_carbs || 0) / daysInRange;\r\n    const avgProtein = Number(nutritionalData.total_protein || 0) / daysInRange;\r\n    const avgFat = Number(nutritionalData.total_fat || 0) / daysInRange;\r\n\r\n    // Calcular calorias contribuídas por cada macronutriente\r\n    const carbsCalories = avgCarbs * 4; // 1g carboidrato = 4 kcal\r\n    const proteinCalories = avgProtein * 4; // 1g proteína = 4 kcal\r\n    const fatCalories = avgFat * 9; // 1g gordura = 9 kcal\r\n    const totalCaloriesFromMacros = carbsCalories + proteinCalories + fatCalories;\r\n\r\n    // Calcular percentuais\r\n    const carbsPercent = totalCaloriesFromMacros > 0 ? (carbsCalories / totalCaloriesFromMacros) * 100 : 0;\r\n    const proteinPercent = totalCaloriesFromMacros > 0 ? (proteinCalories / totalCaloriesFromMacros) * 100 : 0;\r\n    const fatPercent = totalCaloriesFromMacros > 0 ? (fatCalories / totalCaloriesFromMacros) * 100 : 0;\r\n\r\n    // Generate micronutrients data based on macronutrient intake\r\n    const micronutrientsData = this.generateMicronutrientsFromMacros({\r\n      calories: Math.round(avgCalories),\r\n      protein: Math.round(avgProtein),\r\n      carbs: Math.round(avgCarbs),\r\n      fat: Math.round(avgFat)\r\n    });\r\n\r\n    return {\r\n      status: 'success',\r\n      data: {\r\n        date: new Date().toISOString(),\r\n        totalIntake: micronutrientsData.totalIntake,\r\n        deficiencies: micronutrientsData.deficiencies,\r\n        excesses: micronutrientsData.excesses,\r\n        recommendations: micronutrientsData.recommendations,\r\n        overallScore: micronutrientsData.overallScore,\r\n        improvementAreas: micronutrientsData.improvementAreas,\r\n        weekly_averages: {\r\n          macronutrient_distribution: {\r\n            calories: Math.round(avgCalories),\r\n            calories_percent: 100,\r\n            carbs: Math.round(avgCarbs),\r\n            carbs_percent: Math.round(carbsPercent),\r\n            protein: Math.round(avgProtein),\r\n            protein_percent: Math.round(proteinPercent),\r\n            fat: Math.round(avgFat),\r\n            fat_percent: Math.round(fatPercent),\r\n          },\r\n        },\r\n      },\r\n    };\r\n  }\r\n\r\n\r\n  \r\n  async getProgressCaloricBalance(userId: number, query: { date_start?: string; date_end?: string }) {\r\n    const { date_start, date_end } = query;\r\n  \r\n    // Definir intervalo de datas (padrão: semana atual)\r\n    const startOfWeek = date_start\r\n      ? new Date(date_start)\r\n      : new Date();\r\n    const endOfWeek = date_end\r\n      ? new Date(date_end)\r\n      : new Date();\r\n  \r\n    startOfWeek.setHours(0, 0, 0, 0);\r\n    endOfWeek.setHours(23, 59, 59, 999);\r\n  \r\n    // Consulta principal para obter o saldo calórico\r\n    const result = await db\r\n      .selectFrom('daily_meals as dnp')\r\n      .leftJoin('daily_workouts_activities as dwa', 'dwa.user_id', 'dnp.user_id')\r\n      .where('dnp.user_id', '=', userId)\r\n      .where('dnp.daily_at', '>=', startOfWeek)\r\n      .where('dnp.daily_at', '<=', endOfWeek)\r\n      .select([\r\n        // db.fn.coalesce(db.fn.date('dnp.created_at'), '').as('date'),\r\n        'dnp.daily_at as date',\r\n        // db.fn.sum('dnp.calories').as('consumed'),\r\n        (eb) => eb.fn.sum(eb.ref('dnp.calories')).as('consumed'),\r\n        // db.fn.sum('dwa.calories').as('burned'),\r\n        (eb) => eb.fn.sum(eb.ref('dwa.calories')).as('burned'),\r\n      ])\r\n      .groupBy('date')\r\n      .orderBy('date', 'asc')\r\n      .execute();\r\n  \r\n    // Calcular médias e taxa metabólica basal\r\n    const totalConsumed = result.reduce((sum, row) => sum + Number(row.consumed || 0), 0);\r\n    const totalBurned = result.reduce((sum, row) => sum + Number(row.burned || 0), 0);\r\n    const averageConsumed = totalConsumed / result.length || 0;\r\n    const averageBurned = totalBurned / result.length || 0;\r\n  \r\n    return {\r\n      status: 'success',\r\n      data: {\r\n        chart: result.map((row: any) => ({\r\n          date: row.date.toISOString().split('T')[0],\r\n          consumed: Number(row.consumed || 0),\r\n          burned: Number(row.burned || 0),\r\n        })),\r\n        average_consumed: averageConsumed,\r\n        average_burned: averageBurned,\r\n        basal_metabolic_rate: 0, // Implementar lógica para calcular a taxa metabólica basal\r\n      },\r\n    };\r\n  }\r\n\r\n      async getProgressWorkouts(userId: number, query: any) {\r\n        const { date_start, date_end } = query;\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n        const workouts = await db\r\n        .selectFrom('daily_coach_protocol as dcp')\r\n        .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n        .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n        .leftJoin('daily_coach_protocol_series as dcps', 'dcps.daily_id', 'dcp.id')\r\n        .leftJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcps.protocol_exercise_id')\r\n        .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')\r\n        .leftJoin('select_options as so_type', 'so_type.id', 'cp.type_id')\r\n        .leftJoin('select_options as so_muscle', 'so_muscle.id', 'e.muscle_group_id')\r\n        .select([\r\n            'dcp.created_at as date',\r\n            'cpw.name as workout_name',\r\n            'dcp.workout_time',\r\n            'dcp.total_calories',\r\n            (eb) =>\r\n              eb\r\n                .fn.count(eb.ref('dcps.protocol_exercise_id'))\r\n                .distinct()\r\n                .as('exercise_count'),\r\n            'so_type.value_option as type',\r\n            sql<string>`GROUP_CONCAT(DISTINCT so_muscle.value_option SEPARATOR ', ')`.as('muscle_groups'), // Usando sql diretamente\r\n          ])\r\n        .where('dcp.user_id', '=', userId)\r\n        .where('dcp.created_at', '>=', startOfDay)\r\n        .where('dcp.created_at', '<=', endOfDay)\r\n        .groupBy(['dcp.id', 'cpw.name', 'so_type.value_option'])\r\n        .execute();\r\n\r\n        const workoutsMapped = workouts.map((item: any) => {\r\n            return {\r\n                date: item.date,\r\n                workout_name: item.workout_name,\r\n                total_calories: item.total_calories,\r\n                workout_time: item.workout_time,\r\n                exercise_count: Number(item.exercise_count),\r\n                type: item.type || 'N/A',\r\n                muscle_groups: item.muscle_groups || 'N/A', // Garante que não seja undefined\r\n            }\r\n        })\r\n\r\n        const workoutsAvarage = workoutsMapped.reduce((acc: any, item: any) => {\r\n            acc.total_calories += item.total_calories;\r\n            acc.workout_time += item.workout_time;\r\n            return acc;\r\n        }, { total_calories: 0, workout_time: 0 })\r\n\r\n        return {\r\n            status: 'success',\r\n            data: {\r\n                workouts: workoutsMapped,\r\n                minutes: workoutsAvarage.workout_time,\r\n                calories: workoutsAvarage.total_calories,\r\n                chart: workoutsMapped,\r\n            }\r\n        }\r\n      }\r\n\r\n      async getProgressWorkoutsAnalysis(userId: number, query: any) {\r\n        const { date_start, date_end } = query;\r\n      \r\n        // Definir intervalo de datas (padrão: semana atual)\r\n        const startOfWeek = date_start\r\n          ? new Date(date_start)\r\n          : new Date();\r\n        const endOfWeek = date_end\r\n          ? new Date(date_end)\r\n          : new Date();\r\n      \r\n        startOfWeek.setHours(0, 0, 0, 0);\r\n        endOfWeek.setHours(23, 59, 59, 999);\r\n      \r\n        // Consulta principal para obter os dados dos treinos\r\n        const result = await db\r\n          .selectFrom('daily_coach_protocol as dcp')\r\n          .where('dcp.user_id', '=', userId)\r\n          .where('dcp.daily_at', '>=', startOfWeek)\r\n          .where('dcp.daily_at', '<=', endOfWeek)\r\n          .select([\r\n            // db.fn.count('dcp.id').as('workouts'),\r\n            (eb) => eb.fn.count(eb.ref('dcp.id')).as('workouts'),\r\n            // db.fn.sum('dcp.total_calories').as('calories'),\r\n            (eb) => eb.fn.sum(eb.ref('dcp.total_calories')).as('calories'),\r\n            // db.fn.coalesce(db.fn.date('dcp.created_at'), '').as('date'),\r\n            'dcp.daily_at as date',\r\n            // db.fn.sum('dcp.workout_time').as('minutes'),\r\n            (eb) => eb.fn.sum(eb.ref('dcp.workout_time')).as('minutes'),\r\n          ])\r\n          .groupBy('date')\r\n          .orderBy('date', 'asc')\r\n          .execute();\r\n      \r\n        // Calcular totais\r\n        const totalWorkouts = result.reduce((sum, row) => sum + Number(row.workouts || 0), 0);\r\n        const totalMinutes = result.reduce((sum, row) => sum + Number(row.minutes || 0), 0);\r\n        const totalCalories = result.reduce((sum, row) => sum + Number(row.calories || 0), 0);\r\n      \r\n        // Montar o gráfico\r\n        const chartData = result.map((row: any) => ({\r\n          label: row.date.toISOString().split('T')[0],\r\n          training_volume: Number(row.minutes || 0),\r\n        }));\r\n      \r\n        return {\r\n          status: 'success',\r\n          data: {\r\n            workouts: totalWorkouts,\r\n            minutes: totalMinutes,\r\n            calories: totalCalories,\r\n            chart: chartData,\r\n          },\r\n        };\r\n      }\r\n\r\n      \r\n        async getProgressCompleteAnalysis(userId: number, query: { date_start?: string; date_end?: string }) {\r\n          const { date_start, date_end } = query;\r\n      \r\n          // Definir intervalo de datas (padrão: mês atual)\r\n          const startOfPeriod = date_start\r\n            ? dayjs(date_start).startOf('day').toDate()\r\n            : dayjs().startOf('month').toDate();\r\n          const endOfPeriod = date_end\r\n            ? dayjs(date_end).endOf('day').toDate()\r\n            : dayjs().endOf('month').toDate();\r\n      \r\n          // 1. Calcular horário mais frequente dos treinos\r\n          const workoutTimes = await db\r\n            .selectFrom('daily_coach_protocol')\r\n            .where('user_id', '=', userId)\r\n            .where('workout_time', '>=', startOfPeriod)\r\n            .where('workout_time', '<=', endOfPeriod)\r\n            .select((eb) => [\r\n              sql<string>`HOUR(workout_time)`.as('hour'),\r\n              eb.fn.count('id').as('count'),\r\n            ])\r\n            .groupBy('hour')\r\n            .orderBy('count', 'desc')\r\n            .executeTakeFirst();\r\n          const mostFrequentTime = workoutTimes ? `${String(workoutTimes.hour).padStart(2, '0')}:00` : '00:00';\r\n      \r\n          // 2. Calcular intervalo médio entre treinos\r\n          const allWorkouts = await db\r\n            .selectFrom('daily_coach_protocol')\r\n            .where('user_id', '=', userId)\r\n            .where('workout_time', '>=', startOfPeriod)\r\n            .where('workout_time', '<=', endOfPeriod)\r\n            .select('workout_time')\r\n            .orderBy('workout_time', 'asc')\r\n            .execute();\r\n          let averageInterval = 0;\r\n          if (allWorkouts.length > 1) {\r\n            const intervals: number[] = [];\r\n            for (let i = 1; i < allWorkouts.length; i++) {\r\n              const diff = dayjs(allWorkouts[i].workout_time).diff(allWorkouts[i - 1].workout_time, 'hour', true);\r\n              intervals.push(diff);\r\n            }\r\n            averageInterval = intervals.length > 0 ? intervals.reduce((a, b) => a + b, 0) / intervals.length : 0;\r\n          }\r\n          const averageIntervalFormatted = averageInterval.toFixed(1) + 'h';\r\n      \r\n          // 3. Calcular primeira e última refeição\r\n          const mealTimes = await db\r\n            .selectFrom('daily_meals_foods as df')\r\n            .innerJoin('daily_meals as dm', 'dm.id', 'df.meal_id')\r\n            .where('dm.user_id', '=', userId)\r\n            .where('dm.daily_at', '>=', startOfPeriod)\r\n            .where('dm.daily_at', '<=', endOfPeriod)\r\n            .select(sql<Date>`HOUR(dm.daily_at)`.as('meal_time')) // hour of daily_at\r\n            .execute();\r\n          const sortedMealTimes = mealTimes.map((m) => m.meal_time).sort((a: any, b: any) => a - b);\r\n          const firstMeal = sortedMealTimes.length > 0 ? dayjs(sortedMealTimes[0]).format('HH:mm') : '00:00';\r\n          const lastMeal = sortedMealTimes.length > 0 ? dayjs(sortedMealTimes[sortedMealTimes.length - 1]).format('HH:mm') : '00:00';\r\n      \r\n          // 4. Calcular faixas de horário dinâmicas (timeSlots) com base nos dados\r\n          const totalDays = dayjs(endOfPeriod).diff(startOfPeriod, 'day') + 1;\r\n          const mealHours = mealTimes.map((m) => dayjs(m.meal_time).hour());\r\n          const minHour = mealHours.length > 0 ? Math.min(...mealHours) : 0;\r\n          const maxHour = mealHours.length > 0 ? Math.max(...mealHours) : 23;\r\n      \r\n          // Criar faixas dinâmicas (ex.: dividir em 6 faixas entre minHour e maxHour)\r\n          const numSlots = 6; // Número de faixas desejadas (ajustável)\r\n          const range = maxHour - minHour;\r\n          const slotSize = range > 0 ? Math.ceil(range / numSlots) : 1;\r\n          const timeSlots: { time: string; start: number; end: number }[] = [];\r\n          for (let i = 0; i < numSlots; i++) {\r\n            const start = minHour + i * slotSize;\r\n            const end = Math.min(start + slotSize - 1, 23);\r\n            timeSlots.push({\r\n              time: `${String(start).padStart(2, '0')}:00`,\r\n              start,\r\n              end,\r\n            });\r\n          }\r\n      \r\n          // Calcular frequência por horário\r\n          const frequencyByTime = await Promise.all(\r\n            timeSlots.map(async (slot) => {\r\n              const daysWithMeal = await db\r\n                .selectFrom('daily_meals_foods as df')\r\n                .innerJoin('daily_meals as dm', 'dm.id', 'df.meal_id')\r\n                .where('dm.user_id', '=', userId)\r\n                .where('dm.daily_at', '>=', startOfPeriod)\r\n                .where('dm.daily_at', '<=', endOfPeriod)\r\n                .where((eb) =>\r\n                  eb.and([\r\n                    eb(sql<number>`HOUR(dm.daily_at)`, '>=', slot.start),\r\n                    eb(sql<number>`HOUR(dm.daily_at)`, '<=', slot.end),\r\n                  ])\r\n                )\r\n                .select((eb) => [sql<string>`DATE(dm.daily_at)`.as('day')])\r\n                .distinct()\r\n                .execute();\r\n              const percent = totalDays > 0 ? (daysWithMeal.length / totalDays) * 100 : 0;\r\n              return { time: slot.time, percent: Math.round(percent) };\r\n            })\r\n          );\r\n      \r\n          // 5. Buscar grupos musculares dinamicamente\r\n          const muscleGroupsData = await db\r\n            .selectFrom('select_options')\r\n            .where('area_key', '=', 'muscle_group')\r\n            .select(['id', 'value_option as name'])\r\n            .execute();\r\n          const muscleGroups = muscleGroupsData.reduce((acc: any, group: any) => {\r\n            acc[group.id] = group.name;\r\n            return acc;\r\n          }, {} as { [key: number]: string });\r\n      \r\n          // Calcular distribuição de volume por grupo muscular\r\n          const volumeDistribution = await db\r\n            .selectFrom('daily_coach_protocol_series')\r\n            .innerJoin('daily_coach_protocol', 'daily_coach_protocol.id', 'daily_coach_protocol_series.daily_id')\r\n            .innerJoin('coach_protocols_workouts_exercises' , 'coach_protocols_workouts_exercises.id', 'daily_coach_protocol_series.protocol_exercise_id')\r\n            .innerJoin('exercises', 'exercises.id', 'coach_protocols_workouts_exercises.exercise_id')\r\n            .where('daily_coach_protocol.user_id', '=', userId)\r\n            .where('daily_coach_protocol.workout_time', '>=', startOfPeriod)\r\n            .where('daily_coach_protocol.workout_time', '<=', endOfPeriod)\r\n            .select((eb: any) => [\r\n              'exercises.muscle_group_id',\r\n              eb.fn.sum('daily_coach_protocol_series.calories').as('total_calories'),\r\n            ])\r\n            .groupBy('exercises.muscle_group_id')\r\n            .execute();\r\n      \r\n          const totalCalories = volumeDistribution.reduce((sum, d) => sum + Number(d.total_calories), 0);\r\n          const volumeDistributionFormatted = volumeDistribution.map((d) => ({\r\n            muscle_group: muscleGroups[d.muscle_group_id] || 'Outros',\r\n            percent: totalCalories > 0 ? Math.round((Number(d.total_calories) / totalCalories) * 100) : 0,\r\n          }));\r\n      \r\n          // 6. Retornar os dados\r\n          return {\r\n            status: 'success',\r\n            data: {\r\n              most_frequent_time: mostFrequentTime,\r\n              average_interval: averageIntervalFormatted,\r\n              first_meal: firstMeal,\r\n              last_meal: lastMeal,\r\n              frequency_by_time: frequencyByTime,\r\n              volume_distribution: volumeDistributionFormatted,\r\n            },\r\n          };\r\n        }\r\n\r\n      async getProgressVolumeDistribution(userId: number, query: any) {\r\n        const { date_start, date_end } = query;\r\n        const startOfDay = date_start ? dayjs(date_start).startOf('day').toDate() : dayjs().startOf('day').toDate();\r\n        const endOfDay = date_end ? dayjs(date_end).endOf('day').toDate() : dayjs().endOf('day').toDate();\r\n\r\n\r\n        const volumeDistribution = await db\r\n        .selectFrom('daily_coach_protocol_series as dcp')\r\n        .innerJoin('daily_coach_protocol as dcp2', 'dcp2.id', 'dcp.daily_id')\r\n        .innerJoin('coach_protocols_workouts_exercises as cpwe', 'cpwe.id', 'dcp.protocol_exercise_id')\r\n        .innerJoin('exercises as e', 'e.id', 'cpwe.exercise_id') \r\n        .innerJoin('select_options as so', 'so.id', 'e.muscle_group_id')\r\n        .where('dcp2.user_id', '=', userId)\r\n        .where('dcp2.daily_at', '>=', startOfDay)\r\n        .where('dcp2.daily_at', '<=', endOfDay)\r\n        .select([\r\n            'so.value_option as muscle_group',\r\n            'dcp.weight',\r\n        ])\r\n        .groupBy('so.value_option')\r\n        .execute();\r\n\r\n        const volumeDistributionMapped = volumeDistribution.reduce((acc: any, item: any) => {\r\n            // label and calc percent on weight of group {label, percent}\r\n            const percent = item.weight / volumeDistribution.reduce((acc: any, item: any) => acc + item.weight, 0) * 100;\r\n            acc.push({\r\n                label: item.muscle_group,\r\n                weight: item.weight,\r\n                percent: Math.round(percent),\r\n            })\r\n            return acc;\r\n        }, [])\r\n\r\n        \r\n\r\n\r\n\r\n\r\n        return {\r\n            status: 'success',\r\n            data: volumeDistributionMapped,\r\n        }\r\n      }\r\n\r\n      async getProgressAttendance(userId: number) {\r\n        const today = dayjs().startOf('day').toDate();\r\n    \r\n        // 1. Calcular assiduidade da semana\r\n        const startOfWeek = dayjs().startOf('week').toDate();\r\n        const activeDaysWeek = await db\r\n          .selectFrom('daily_coach_protocol')\r\n          .where('user_id', '=', userId)\r\n          .where('workout_time', '>=', startOfWeek)\r\n          .where('workout_time', '<=', today)\r\n          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])\r\n          .execute();\r\n        const totalDaysWeek = dayjs(today).diff(startOfWeek, 'day') + 1;\r\n        const weekAttendance = totalDaysWeek > 0 ? (activeDaysWeek.length / totalDaysWeek) * 100 : 0;\r\n    \r\n        // 2. Calcular assiduidade do mês\r\n        const startOfMonth = dayjs().startOf('month').toDate();\r\n        const activeDaysMonth = await db\r\n          .selectFrom('daily_coach_protocol')\r\n          .where('user_id', '=', userId)\r\n          .where('workout_time', '>=', startOfMonth)\r\n          .where('workout_time', '<=', today)\r\n          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])\r\n          .execute();\r\n        const totalDaysMonth = dayjs(today).diff(startOfMonth, 'day') + 1;\r\n        const monthAttendance = totalDaysMonth > 0 ? (activeDaysMonth.length / totalDaysMonth) * 100 : 0;\r\n    \r\n        // 3. Calcular streak atual\r\n        const allActiveDays = await db\r\n          .selectFrom('daily_coach_protocol')\r\n          .where('user_id', '=', userId)\r\n          .select((eb) => [sql<string>`DATE(workout_time)`.as('day')])\r\n          .orderBy('workout_time', 'desc')\r\n          .execute();\r\n        const activeDaysSet = new Set(allActiveDays.map((d) => d.day));\r\n        let currentStreak = 0;\r\n        let currentDate = today;\r\n        while (activeDaysSet.has(dayjs(currentDate).format('YYYY-MM-DD'))) {\r\n          currentStreak++;\r\n          currentDate = dayjs(currentDate).subtract(1, 'day').toDate();\r\n        }\r\n    \r\n        // 4. Calcular recorde de streak\r\n        let maxStreak = 0;\r\n        let tempStreak = 0;\r\n        const sortedActiveDays = allActiveDays.map((d) => dayjs(d.day).toDate()).sort((a, b) => a.getTime() - b.getTime());\r\n        for (let i = 0; i < sortedActiveDays.length; i++) {\r\n          tempStreak = 1;\r\n          let j = i + 1;\r\n          while (j < sortedActiveDays.length && dayjs(sortedActiveDays[j]).diff(sortedActiveDays[j - 1], 'day') === 1) {\r\n            tempStreak++;\r\n            j++;\r\n          }\r\n          maxStreak = Math.max(maxStreak, tempStreak);\r\n          i = j - 1; // Pular dias já contados\r\n        }\r\n    \r\n        // 5. Retornar os dados\r\n        return {\r\n          status: 'success',\r\n          data: {\r\n            week: Math.round(weekAttendance),\r\n            month: Math.round(monthAttendance),\r\n            streak: currentStreak,\r\n            record_streak: maxStreak,\r\n          },\r\n        };\r\n      }\r\n\r\n      // AI\r\n      async getActiveMacroUserInfo(userId: number) {\r\n        const user = await db\r\n        .selectFrom('nutritionist_protocols as p')\r\n        .select([\r\n          'p.goal_calories as calories',\r\n          'p.goal_protein as protein',\r\n          'p.goal_carbs as carbs',\r\n          'p.goal_fat as fat',\r\n        ])\r\n        .where('p.client_id', '=', userId)\r\n        .where('p.ended_at', 'is', null)\r\n        .executeTakeFirst();\r\n\r\n        const text = `\r\n          Calorias: ${user?.calories} kcal\r\n          Proteína: ${user?.protein} g\r\n          Carboidratos: ${user?.carbs} g\r\n          Gorduras: ${user?.fat} g\r\n        `;\r\n\r\n        return text;\r\n      }     \r\n\r\n\r\n      async getPromptTextAi(text: string, userId: number) {\r\n        // Prompt otimizado e mais conciso\r\n        return `Gere 3 recomendações de alimentos baseado em: ${text}\r\n\r\nRetorne JSON válido:\r\n{ \"recommendations\": [\r\n  [{\"name\": \"alimento\", \"quantity\": 100, \"unit\": \"g\", \"calories\": 150, \"protein\": 20, \"carbs\": 10, \"fat\": 5, \"fiber\": 2}]\r\n]}\r\n\r\nRegras:\r\n1. Alimentos brasileiros típicos\r\n2. Valores nutricionais precisos\r\n3. Quantidades realistas`;\r\n      }\r\n\r\n      async getMealData(meal_id: number) {\r\n        const mealData = await db\r\n        .selectFrom('nutritionist_protocols_meals as m')\r\n        .leftJoin('nutritionist_protocols_meals_foods as mf', 'mf.meal_id', 'm.id')\r\n        .where('m.id', '=', meal_id)\r\n        .select([\r\n            'm.id as meal_id',\r\n            'm.name as meal_name',\r\n            'mf.name as food_name',\r\n            'mf.quantity as food_quantity',\r\n            'mf.unit as food_unit',\r\n            'mf.calories as food_calories',\r\n            'mf.protein as food_protein',\r\n            'mf.carbs as food_carbs',\r\n            'mf.fat as food_fat',\r\n            'mf.fiber as food_fiber',\r\n        ])\r\n        .execute();\r\n        \r\n        const mealText = mealData.map((item: any) => {\r\n            return `\r\n            Refeição: ${item.meal_name}\r\n            Alimento: ${item.food_name}\r\n            Quantidade: ${item.food_quantity} ${item.food_unit}\r\n            Calorias: ${item.food_calories}\r\n            Proteína: ${item.food_protein}\r\n            Carboidratos: ${item.food_carbs}\r\n            Gorduras: ${item.food_fat}\r\n            Fibra: ${item.food_fiber}\r\n            `;\r\n        }).join('\\n');\r\n\r\n        return mealText;\r\n      }\r\n\r\n      async getPromptTextReplacementAi(mealData: any) {\r\n        const { meal_id } = mealData;\r\n\r\n        const mealText = await this.getMealData(meal_id);\r\n\r\n        return `\r\n          Com base na refeição abaixo, gere 3 recomendações de refeições com alimentos alternativos, mantendo a mesma quantidade e valores nutricionais.\r\n\r\n          # REFEIÇÃO:\r\n          ${mealText}\r\n\r\n          Retorne APENAS o JSON válido SEM comentários ou markdown.\r\n\r\n          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS\r\n\r\n          # MODELO JSON OBRIGATÓRIO:\r\n          { recommendations: [\r\n          [\r\n            {\r\n              \"name\": \"[Nome do alimento]\",\r\n              \"quantity\": /* Quantidade numérica SEM UNIDADE */,\r\n              \"unit\": \"g/ml/...\",\r\n              \"calories\": /* Valor exato calculado */,\r\n              \"protein\": /* g */,\r\n              \"carbs\": /* g */,\r\n              \"fat\": /* g */,\r\n              \"fiber\": /* g */\r\n            },...\r\n          ],...\r\n          ]}\r\n\r\n          # REGRAS ESSENCIAIS:\r\n          1. Números SEM ASPAS ou unidades nos valores (ex: \"quantity\": 150)\r\n          2. Precisão nutricional: ±5% de margem de erro\r\n          3. Usar alimentos típicos brasileiros          \r\n        `;\r\n      }\r\n\r\n      async getPromptTextRecognitionAi(text: string, userId: number) {\r\n        return `Identifique alimentos em: ${text}\r\n\r\nRetorne JSON:\r\n{ \"recommendations\": [\r\n  [{\"name\": \"alimento\", \"quantity\": 100, \"unit\": \"g\", \"calories\": 150, \"protein\": 20, \"carbs\": 10, \"fat\": 5, \"fiber\": 2}]\r\n]}`;\r\n      }\r\n\r\n      async getPromptImage() {\r\n        return `\r\n          Com base na imagem, identifique os alimentos e suas quantidades.\r\n\r\n          Retorne APENAS o JSON válido SEM comentários ou markdown.\r\n\r\n          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS\r\n\r\n          # MODELO JSON OBRIGATÓRIO:\r\n          { recommendations: [\r\n          [\r\n            {\r\n              \"name\": \"[Nome do alimento]\",\r\n              \"quantity\": /* Quantidade numérica SEM UNIDADE */,\r\n              \"unit\": \"g/ml/...\",\r\n              \"calories\": /* Valor exato calculado */,\r\n              \"protein\": /* g */,\r\n              \"carbs\": /* g */,\r\n              \"fat\": /* g */,\r\n              \"fiber\": /* g */\r\n            },...\r\n          ],...\r\n          ]}\r\n\r\n          # REGRAS ESSENCIAIS:\r\n          1. Números SEM ASPAS ou unidades nos valores (ex: \"quantity\": 150)\r\n          2. Precisão nutricional: ±5% de margem de erro\r\n          3. Usar alimentos típicos brasileiros     \r\n        `;\r\n      }\r\n\r\n      async getPromptAudio() {\r\n        return `\r\n          Com base na transcrição do áudio, identifique os alimentos e suas quantidades.\r\n\r\n          Retorne APENAS o JSON válido SEM comentários ou markdown.\r\n\r\n          OBRIGATÓRIO: O JSON DEVE SEGUIR O FORMATO EXATO DE ESTRUTURA ABAIXO, NÃO ACRESCENTE OU DIMINUA OS CAMPOS\r\n\r\n          # MODELO JSON OBRIGATÓRIO:\r\n          { recommendations: [\r\n          [\r\n            {\r\n              \"name\": \"[Nome do alimento]\",\r\n              \"quantity\": /* Quantidade numérica SEM UNIDADE */,\r\n              \"unit\": \"g/ml/...\",\r\n              \"calories\": /* Valor exato calculado */,\r\n              \"protein\": /* g */,\r\n              \"carbs\": /* g */,\r\n              \"fat\": /* g */,\r\n              \"fiber\": /* g */\r\n            },...\r\n          ],...\r\n          ]}\r\n\r\n          # REGRAS ESSENCIAIS:\r\n          1. Números SEM ASPAS ou unidades nos valores (ex: \"quantity\": 150)\r\n          2. Precisão nutricional: ±5% de margem de erro\r\n          3. Usar alimentos típicos brasileiros     \r\n        `;\r\n      }\r\n\r\n\r\n      async processTextAi(text: string, mealData: any, userId: number) {\r\n        if (!text || typeof text !== 'string' || text.trim() === '') {\r\n          if(mealData.meal_type !== 'replacement') {\r\n            throw new Error('Texto está indefinido ou vazio');\r\n          }\r\n        }\r\n        \r\n        let prompt: any;\r\n        switch(mealData.meal_type) {\r\n          case 'new':\r\n            prompt = await this.getPromptTextAi(text, userId);\r\n            break;\r\n          case 'recognition':\r\n            prompt = await this.getPromptTextRecognitionAi(text, userId);\r\n            break;\r\n          case 'replacement':\r\n            prompt = await this.getPromptTextReplacementAi(mealData);\r\n            break;\r\n          default:\r\n            throw new Error('Tipo de refeição inválido');\r\n        }\r\n\r\n        const response: any = await this.openai.chat.completions.create({\r\n          model: \"gpt-4o-mini\", // Modelo otimizado para velocidade\r\n          messages: [\r\n            {\r\n              role: \"system\",\r\n              content: \"Nutricionista expert. Retorne apenas JSON válido.\"\r\n            },\r\n            { role: \"user\", content: prompt }\r\n          ],\r\n          temperature: 0.1, // Reduzido para máxima consistência e velocidade\r\n          max_tokens: 1024, // Drasticamente reduzido para otimizar performance\r\n          response_format: { type: \"json_object\" }\r\n        });\r\n    \r\n        \r\n        const aiResponseText: any = response.choices[0].message.content;\r\n\r\n        return aiResponseText;\r\n      }\r\n\r\n      async processImageAi(base64: string) {\r\n        // Validação de entrada\r\n        if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {\r\n          throw new Error('Base64 da imagem está indefinido ou vazio');\r\n        }\r\n\r\n        // gpt-4o , gpt-4o-mini or gpt-4-turbo\r\n\r\n\r\n      const completion = await this.openai.chat.completions.create({\r\n          model: \"gpt-4o-mini\",\r\n          messages: [\r\n            { role: \"system\", content: \"Você é um assistente que analisa imagens e fornece respostas detalhadas.\" },\r\n            {\r\n              role: \"user\",\r\n              content: [\r\n                  {\r\n                      type: \"image_url\",\r\n                      image_url: {\r\n                          url: base64,\r\n                      },\r\n                  },\r\n                  {\r\n                      type: \"text\",\r\n                      text: await this.getPromptImage(),\r\n                  },\r\n              ],\r\n          }],\r\n      });\r\n\r\n      return completion.choices[0].message.content;\r\n\r\n\r\n      /*\r\n      // Validação de formato Base64 e tipo de mídia\r\n      const base64Match = base64.match(/^data:(image\\/\\w+);base64,(.+)$/);\r\n      if (!base64Match || base64Match.length < 3) {\r\n          throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');\r\n      }\r\n\r\n      const [, mimeType, dataStr] = base64Match;\r\n      const validImageTypes = ['image/png', 'image/jpeg', 'image/gif', 'image/webp'];\r\n\r\n      if (!validImageTypes.includes(mimeType)) {\r\n          throw new Error(`Tipo de imagem não suportado: ${mimeType}`);\r\n      }\r\n\r\n      // Conversão e validação dos dados Base64\r\n      let buffer;\r\n      try {\r\n          buffer = Buffer.from(dataStr, 'base64');\r\n      } catch (error) {\r\n          throw new Error('Dados Base64 inválidos');\r\n      }\r\n\r\n      // Criar o diretório se não existir\r\n      const uploadDir = path.join('__temp');\r\n      await fs.promises.mkdir(uploadDir, { recursive: true });\r\n    \r\n      // Gerar nome aleatório para o arquivo\r\n      const fileExt = mimeType.split('/')[1];\r\n      const randomName = `image_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;\r\n      const filePath = path.join(uploadDir, randomName);\r\n\r\n      try {\r\n          await fsPromises.writeFile(filePath, buffer);\r\n      } catch (error) {\r\n          throw new Error(`Erro ao salvar arquivo de imagem: ${error.message}`);\r\n      }\r\n\r\n      const fileUrl = process.env.STATIC_URL + '/temp/' + randomName;\r\n      // const fileUrl = 'https://static.mysnapfit.com.br/temp/image_1743474479214-xdwhn1klom.jpeg';\r\n\r\n        const response: any = await this.openai.chat.completions.create({\r\n          model: 'gpt-4o',\r\n          messages: [\r\n            {\r\n              role: 'system',\r\n              content: 'Você é um assistente que analisa imagens e fornece respostas detalhadas.',\r\n            },\r\n            {\r\n              role: 'user',\r\n              content: [\r\n                {\r\n                  type: 'image_url',\r\n                  image_url: { url: fileUrl },\r\n                },\r\n                {\r\n                  type: 'text',\r\n                  text: await this.getPromptImage(),\r\n                },\r\n              ],\r\n            },\r\n          ],\r\n        });\r\n\r\n        // Remover o arquivo temporário após o processamento\r\n        fs.unlinkSync(filePath);\r\n\r\n        const aiResponseText: any = response.choices[0].message.content;\r\n\r\n        console.log(aiResponseText);\r\n\r\n        return aiResponseText;\r\n        */\r\n      }\r\n\r\n      async processAudioAi(base64: string, userId: number) {\r\n        try {\r\n          // Validar entrada\r\n          if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {\r\n            throw new Error('Base64 do áudio está indefinido ou vazio');\r\n          }\r\n      \r\n          // Validar formato Base64\r\n          const base64Match = base64.match(/^data:(audio\\/\\w+);base64,(.+)$/);\r\n          if (!base64Match || base64Match.length < 3) {\r\n            throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');\r\n          }\r\n      \r\n          const [, mimeType, dataStr] = base64Match;\r\n          const openAiAudiosAllowedTypes = ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm'];\r\n      \r\n          const fileExt = mimeType.split('/')[1];\r\n      \r\n          if (!openAiAudiosAllowedTypes.includes(fileExt)) {\r\n            throw new Error(`Tipo de áudio não suportado: ${mimeType}. Formatos aceitos: ${openAiAudiosAllowedTypes.join(', ')}`);\r\n          }\r\n      \r\n          // Converter Base64 para Buffer\r\n          let buffer: Buffer;\r\n          try {\r\n            buffer = Buffer.from(dataStr, 'base64');\r\n          } catch (error) {\r\n            throw new Error('Dados Base64 inválidos');\r\n          }\r\n      \r\n          // Criar diretório temporário\r\n          const uploadDir = path.join('__temp');\r\n          await fsExtra.ensureDir(uploadDir);\r\n      \r\n          // Gerar nome aleatório para o arquivo\r\n          const randomName = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;\r\n          const filePath = path.join(uploadDir, randomName);\r\n      \r\n          // Salvar arquivo\r\n          await fs.promises.writeFile(filePath, buffer);\r\n\r\n          // Testar se o arquivo foi salvo corretamente\r\n          if (!fs.existsSync(filePath) || fs.statSync(filePath).size === 0) {\r\n            throw new Error('Arquivo salvo está vazio ou inválido.');\r\n          }\r\n\r\n          // console.log(`Arquivo salvo em: ${filePath}`);\r\n\r\n      \r\n          // Enviar para a API da OpenAI\r\n          /*\r\n          const response = await this.openai.audio.transcriptions.create({\r\n            file: fs.createReadStream(filePath),\r\n            model: 'gpt-4o-transcribe',            \r\n          });\r\n          */\r\n          \r\n          const fileBlob = new Blob([buffer], { type: mimeType });\r\n          const file = new File([fileBlob], randomName, { type: mimeType });   \r\n          \r\n          \r\n          const form = new FormData();\r\n          form.append('file', file);\r\n          form.append('model', 'whisper-1');\r\n          \r\n          const config = {\r\n              headers: {\r\n                  'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,\r\n                  'Content-Type': 'multipart/form-data',\r\n                  // ...axios.defaults.headers.common,\r\n              },\r\n          };\r\n          const response = await axios.post('https://api.openai.com/v1/audio/transcriptions', form, config);\r\n\r\n      \r\n          // Remover arquivo temporário após o processamento\r\n          await fs.promises.unlink(filePath);\r\n      \r\n          const transcription = response.data.text;\r\n      \r\n          const mealData = {\r\n            meal_type: 'recognition',\r\n          };\r\n      \r\n          return await this.processTextAi(transcription, mealData, userId);\r\n        } catch (error) {\r\n          console.error('Erro ao processar áudio:', error);\r\n          return { error: 'Falha ao processar o áudio' };\r\n        }\r\n\r\n      }\r\n\r\n      async processAudioAiOld(base64: string, userId: number) {\r\n        try {\r\n          // Validate input\r\n        if (!base64 || typeof base64 !== 'string' || base64.trim() === '') {\r\n          throw new Error('Base64 do áudio está indefinido ou vazio');\r\n      }\r\n\r\n\r\n\r\n      // Split and validate Base64 format\r\n      const base64Match = base64.match(/^data:(audio\\/\\w+);base64,(.+)$/);\r\n      if (!base64Match || base64Match.length < 3) {\r\n          throw new Error('Formato Base64 inválido ou tipo de mídia não suportado');\r\n      }\r\n\r\n\r\n      const [, mimeType, dataStr] = base64Match;\r\n      const validAudioTypes = ['audio/mp3', 'audio/mpeg', 'audio/webm', 'audio/wav', 'audio/ogg'];\r\n      \r\n\r\n      if (!validAudioTypes.includes(mimeType)) {\r\n          throw new Error(`Tipo de áudio não suportado: ${mimeType}`);\r\n      }\r\n\r\n      // Convert and validate Base64 data\r\n      let buffer: Buffer;\r\n\r\n      try {\r\n          buffer = Buffer.from(dataStr, 'base64');\r\n      } catch (error) {\r\n          throw new Error('Dados Base64 inválidos');\r\n      }\r\n\r\n\r\n\r\n      // Criar o diretório se não existir\r\n      const uploadDir = path.join('__temp');\r\n      await fs.promises.mkdir(uploadDir, { recursive: true });\r\n    \r\n      // Gerar nome aleatório para o arquivo\r\n      const fileExt = mimeType.split('/')[1];\r\n      const randomName = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;\r\n      const filePath = path.join(uploadDir, randomName);\r\n\r\n      let needsConversion = false;\r\n\r\n      // Write file asynchronously\r\n\r\n      try {\r\n          await fsPromises.writeFile(filePath, buffer);\r\n      } catch (error) {\r\n          throw new Error(`Erro ao salvar arquivo de áudio: ${error.message}`);\r\n      }\r\n\r\n          const openAiAudiosAllowedTypes = ['flac', 'm4a', 'mp3', 'mp4', 'mpeg', 'mpga', 'oga', 'ogg', 'wav', 'webm'];\r\n          let randomName2 = '';\r\n          let filePath2 = '';\r\n\r\n          if (!openAiAudiosAllowedTypes.includes(fileExt)) {\r\n            // convert to webm\r\n            randomName2 = `audio_${Date.now()}-${Math.random().toString(36).substring(2)}.webm`;\r\n            const convertedFilePath = path.join(uploadDir, randomName2);\r\n            await sharp(buffer).toFile(convertedFilePath);\r\n            filePath2 = convertedFilePath;\r\n          }\r\n\r\n          const getFileUrl = (!needsConversion) ? filePath : filePath2;\r\n\r\n          // const fileUrl = process.env.STATIC_URL + '/temp/' + randomName;\r\n          // const fileUrl = 'https://static.mysnapfit.com.br/temp/audio_1743464604190-vhjxfkuazep.webm';\r\n      \r\n          // Enviar para a OpenAI Whisper API\r\n          const response = await this.openai.audio.transcriptions.create({\r\n            file: fs.createReadStream(getFileUrl),\r\n            model: 'gpt-4o-transcribe',\r\n          });\r\n      \r\n          // Remover o arquivo temporário após o processamento\r\n          fs.unlinkSync(filePath);\r\n          if (needsConversion) {\r\n            fs.unlinkSync(filePath2);\r\n          }\r\n      \r\n          const transcription = response.text;\r\n\r\n          const mealData = {\r\n            meal_type: 'recognition',\r\n          }\r\n          \r\n          return await this.processTextAi(transcription, mealData, userId);\r\n        } catch (error) {\r\n          console.error('Erro ao processar áudio:', error);\r\n          return { error: 'Falha ao processar o áudio' };\r\n        }\r\n      }\r\n\r\n\r\n      async getFoodsSuggestions(userId: number, query: any, body: any) {\r\n        console.log('🔄 getFoodsSuggestions chamado:', { userId, query, body });\r\n\r\n        const mealType = query.meal_type || 'new'; // new, recognition, replacement\r\n        const mealId = query.meal_id || null;\r\n\r\n        console.log('🔄 Parâmetros processados:', { mealType, mealId });\r\n\r\n        const mealData = {\r\n          meal_type: mealType,\r\n          meal_id: mealId,\r\n        }\r\n\r\n        const getType = body.type;\r\n        const content = body.content;\r\n        let response: any;\r\n\r\n        console.log('🔄 Processando tipo:', getType);\r\n\r\n        if (getType === 'text') {\r\n          response = await this.processTextAi(content, mealData, userId);\r\n        } else if (getType === 'image') {\r\n          response = await this.processImageAi(content);\r\n        } else if (getType === 'audio') {\r\n          response = await this.processAudioAi(content, userId);\r\n        } else {\r\n          throw new Error('Tipo de mídia não suportado');\r\n        }\r\n\r\n        console.log('🔄 Resposta da IA:', response);\r\n\r\n        // const textJson = JSON.parse(response);\r\n        const textJson = JSON.parse(this.extractJsonFromString(response));\r\n\r\n        console.log('🔄 JSON extraído:', textJson);\r\n\r\n        let recommendations = textJson?.recommendations || [];\r\n\r\n        console.log('🔄 Recommendations antes do processamento:', recommendations);\r\n\r\n        if(Array.isArray(recommendations) && recommendations?.length > 0) {\r\n          recommendations = recommendations.map((item: any) => {\r\n            // Verificar se item é um array antes de chamar map\r\n            if (!Array.isArray(item)) {\r\n              console.warn('⚠️ Item não é um array:', item);\r\n              return item;\r\n            }\r\n            return item.map((item2: any) => {\r\n              return {\r\n                ...item2,\r\n                quantity: parseFloat(Number(item2.quantity).toFixed(2)),\r\n                calories: parseFloat(Number(item2.calories).toFixed(2)),\r\n                protein: parseFloat(Number(item2.protein).toFixed(2)),\r\n                carbs: parseFloat(Number(item2.carbs).toFixed(2)),\r\n                fat: parseFloat(Number(item2.fat).toFixed(2)),\r\n                fiber: parseFloat(Number(item2.fiber).toFixed(2)),\r\n              }\r\n            })\r\n          })\r\n        }\r\n\r\n        console.log('🔄 Recommendations após processamento:', recommendations);\r\n\r\n        const result = {\r\n          status: 'success',\r\n          data: recommendations || [],\r\n        };\r\n\r\n        console.log('🔄 Resultado final:', result);\r\n\r\n        return result;\r\n      }\r\n\r\n      // User Options\r\n      async getUserOptions(userId: number) {\r\n        let affiliate: any = false;\r\n        let admin: any = false;\r\n\r\n        // check if is affiliate\r\n        const isAffiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .select('status')\r\n        .executeTakeFirst();\r\n\r\n        if (isAffiliate) {\r\n          affiliate = isAffiliate.status;\r\n\r\n          if(affiliate === 'inactive') {\r\n            affiliate = false;\r\n          }\r\n        }\r\n\r\n        // check if is admin\r\n        const isAdmin = await db.selectFrom('users_roles')\r\n        .where('user_id', '=', userId)\r\n        .where('role_id', '=', 1)\r\n        .select('user_id')\r\n        .executeTakeFirst();\r\n\r\n        if (isAdmin) {\r\n          admin = true;\r\n        }\r\n\r\n        return {\r\n          status: 'success',\r\n          data: {\r\n            affiliate,\r\n            admin,\r\n          },\r\n        };        \r\n      }\r\n\r\n\r\n      // Affiliates\r\n      async createAffiliate(body: any, userId: number) {\r\n        const { aff_ref } = body;\r\n\r\n        // check if user is already an affiliate\r\n        const isAffiliate = await db.selectFrom('affiliates')\r\n        .where('user_id', '=', userId)\r\n        .select('id')\r\n        .executeTakeFirst();\r\n\r\n        if (isAffiliate) {\r\n          return {\r\n            status: 'success',\r\n            data: [],\r\n          };\r\n        }\r\n        \r\n        let aff_id = null;\r\n        if (aff_ref) {\r\n          const affData: any = await db.selectFrom('affiliate_links')\r\n          .where('invite', '=', aff_ref.toLowerCase().trim())\r\n          .where('link_type', '=', 'signup_affiliate')\r\n          .select(['user_id'])\r\n          .executeTakeFirst();\r\n    \r\n          if (affData) {\r\n            aff_id = affData.user_id;\r\n          }\r\n        }\r\n\r\n        const affiliateData: any = {\r\n          status: 'pending',\r\n          user_id: userId,\r\n          ref_user_id: aff_id,\r\n          invite: aff_ref ? aff_ref.toLowerCase().trim() : null,\r\n          is_master: null,\r\n          created_at: new Date(),\r\n          updated_at: new Date(),\r\n        }\r\n\r\n        const newAffiliate = await db.insertInto('affiliates')\r\n        .values(affiliateData)\r\n        .executeTakeFirstOrThrow();\r\n\r\n        return {\r\n          status: 'success',\r\n          data: [],\r\n        }\r\n      }\r\n\r\n      // Plans\r\n      // Checkout\r\n      async subscribePlan(userId: number, planId: number) {\r\n        const plan = await db.selectFrom('plans_payments_providers')\r\n        .where('id', '=', planId)\r\n        .select(['payment_provider_external_id'])\r\n        .executeTakeFirstOrThrow();\r\n\r\n        const stripePlanId = plan.payment_provider_external_id;\r\n\r\n        // user data\r\n        const user = await db.selectFrom('users')\r\n        .where('id', '=', userId)\r\n        .select(['email', 'stripeId'])\r\n        .executeTakeFirstOrThrow();\r\n\r\n        // create or retreive customer by stripeId\r\n        let customerId = user.stripeId;\r\n\r\n        if (!customerId) {\r\n          const customer = await this.stripe.customers.create(\r\n            {\r\n              // @ts-ignore\r\n              email: user.email,\r\n              metadata: {\r\n                userId: userId.toString(),\r\n              },\r\n            }\r\n          );\r\n\r\n          customerId = customer.id;\r\n\r\n          await db.updateTable('users')\r\n          .set({\r\n            stripeId: customerId,\r\n          })\r\n          .where('id', '=', userId)\r\n          .execute();\r\n        }\r\n\r\n        // @ts-ignore\r\n        const session = await this.stripe.checkout.sessions.create({\r\n          payment_method_types: ['card'],\r\n          mode: 'subscription',\r\n          client_reference_id: userId.toString(),\r\n          customer: customerId,\r\n          success_url: process.env.STRIPE_SUCCESS_URL,\r\n          cancel_url: process.env.STRIPE_CANCEL_URL,\r\n          line_items: [\r\n            {\r\n              price: stripePlanId,\r\n              quantity: 1,\r\n            },\r\n          ],\r\n        });\r\n\r\n        return {\r\n          status: 'success',\r\n          data: {\r\n            url: session.url,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Plans\r\n      async getAllPlans(userId: number) {\r\n        // check if user has active subscription\r\n        const hasActiveSubscription = await db.selectFrom('users_subscriptions')\r\n        .where('user_id', '=', userId)\r\n        .where('status', '=', 'active')\r\n        .select('plan_id')\r\n        .executeTakeFirst();\r\n\r\n        // Get plans from web\r\n        const plans = await db.selectFrom('plans as p1')\r\n        .leftJoin('plans_payments_providers as p2', 'p1.id', 'p2.plan_id')\r\n        .leftJoin('payment_providers as p3', 'p2.payment_provider_id', 'p3.id')\r\n        .select([\r\n          'p1.id', 'p1.is_active', 'p1.name', 'p1.description',\r\n          sql`COALESCE(p2.price, p1.price)`.as('price'),\r\n          sql`COALESCE(p2.currency, p1.currency)`.as('currency'),\r\n          sql`COALESCE(p2.snaptokens, p1.snaptokens)`.as('snaptokens'),\r\n          'p2.id as config_id',\r\n        ])\r\n        .where('p2.platform', '=', 'web')\r\n        .where('p1.deleted_at', 'is', null)\r\n        .where('p2.deleted_at', 'is', null)\r\n        .execute();\r\n        \r\n        // return plans and selected if has active subscription\r\n        return {\r\n          status: 'success',\r\n          data: plans.map((plan: any) => {\r\n            return {\r\n              ...plan,\r\n              selected: hasActiveSubscription ? plan.id === hasActiveSubscription.plan_id : false,\r\n            };\r\n          }),\r\n        };\r\n      }\r\n\r\n    // Subscription methods\r\n    async getMySubscriptions(userId: number) {\r\n        const subscriptions = await db\r\n            .selectFrom('users_subscriptions as us')\r\n            .leftJoin('plans as p', 'p.id', 'us.plan_id')\r\n            .leftJoin('payment_providers as pp', 'pp.id', 'us.plan_payment_provider_id')\r\n            .where('us.user_id', '=', userId)\r\n            .select([\r\n                'us.id',\r\n                'us.status',\r\n                'us.start_date',\r\n                'us.end_date',\r\n                'us.next_billing_date',\r\n                'us.cancel_at_period_end',\r\n                'us.is_trial',\r\n                'us.trial_start_date',\r\n                'us.trial_end_date',\r\n                'us.price',\r\n                'us.currency',\r\n                'p.name as plan_name',\r\n                'p.description as plan_description',\r\n                'pp.name as payment_provider',\r\n            ])\r\n            .orderBy('us.created_at', 'desc')\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: subscriptions,\r\n        };\r\n    }\r\n\r\n    async cancelSubscription(subscriptionId: number, userId: number) {\r\n        // Verificar se a assinatura pertence ao usuário\r\n        const subscription = await db\r\n            .selectFrom('users_subscriptions')\r\n            .where('id', '=', subscriptionId)\r\n            .where('user_id', '=', userId)\r\n            .select(['id', 'status', 'payment_provider_external_id'])\r\n            .executeTakeFirst();\r\n\r\n        if (!subscription) {\r\n            throw new HttpException({\r\n                status: 404,\r\n                message: ['Assinatura não encontrada.'],\r\n            }, 404);\r\n        }\r\n\r\n        if (subscription.status === 'canceled') {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Assinatura já está cancelada.'],\r\n            }, 400);\r\n        }\r\n\r\n        // Atualizar status para cancelamento no final do período\r\n        await db\r\n            .updateTable('users_subscriptions')\r\n            .set({\r\n                cancel_at_period_end: true,\r\n                updated_at: new Date(),\r\n            })\r\n            .where('id', '=', subscriptionId)\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            message: 'Assinatura será cancelada no final do período atual.',\r\n        };\r\n    }\r\n\r\n    async cancelSubscriptionImmediately(subscriptionId: number, userId: number) {\r\n        // Verificar se a assinatura pertence ao usuário\r\n        const subscription = await db\r\n            .selectFrom('users_subscriptions')\r\n            .where('id', '=', subscriptionId)\r\n            .where('user_id', '=', userId)\r\n            .select(['id', 'status'])\r\n            .executeTakeFirst();\r\n\r\n        if (!subscription) {\r\n            throw new HttpException({\r\n                status: 404,\r\n                message: ['Assinatura não encontrada.'],\r\n            }, 404);\r\n        }\r\n\r\n        if (subscription.status === 'canceled') {\r\n            throw new HttpException({\r\n                status: 400,\r\n                message: ['Assinatura já está cancelada.'],\r\n            }, 400);\r\n        }\r\n\r\n        // Cancelar imediatamente\r\n        await db\r\n            .updateTable('users_subscriptions')\r\n            .set({\r\n                status: 'canceled',\r\n                end_date: new Date(),\r\n                updated_at: new Date(),\r\n            })\r\n            .where('id', '=', subscriptionId)\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            message: 'Assinatura cancelada imediatamente.',\r\n        };\r\n    }\r\n\r\n    // Transaction methods\r\n    async getMyTransactions(userId: number) {\r\n        const transactions = await db\r\n            .selectFrom('transactions as t')\r\n            .leftJoin('payment_providers as pp', 'pp.id', 't.payment_provider_id')\r\n            .where('t.user_id', '=', userId)\r\n            .select([\r\n                't.id',\r\n                't.provider_transaction_id',\r\n                't.amount',\r\n                't.currency',\r\n                't.status',\r\n                't.source_type',\r\n                't.source_id',\r\n                't.created_at',\r\n                'pp.name as payment_provider',\r\n            ])\r\n            .orderBy('t.created_at', 'desc')\r\n            .execute();\r\n\r\n        return {\r\n            status: 'success',\r\n            data: transactions,\r\n        };\r\n    }\r\n\r\n    async getMyTransactionDetails(transactionId: number, userId: number) {\r\n        const transaction = await db\r\n            .selectFrom('transactions as t')\r\n            .leftJoin('payment_providers as pp', 'pp.id', 't.payment_provider_id')\r\n            .leftJoin('users_subscriptions as us', 'us.id', 't.source_id')\r\n            .leftJoin('plans as p', 'p.id', 'us.plan_id')\r\n            .where('t.id', '=', transactionId)\r\n            .where('t.user_id', '=', userId)\r\n            .select([\r\n                't.id',\r\n                't.provider_transaction_id',\r\n                't.amount',\r\n                't.currency',\r\n                't.status',\r\n                't.source_type',\r\n                't.source_id',\r\n                't.created_at',\r\n                'pp.name as payment_provider',\r\n                'p.name as plan_name',\r\n                'p.description as plan_description',\r\n                'us.start_date as subscription_start_date',\r\n                'us.end_date as subscription_end_date',\r\n            ])\r\n            .executeTakeFirst();\r\n\r\n        if (!transaction) {\r\n            throw new HttpException({\r\n                status: 404,\r\n                message: ['Transação não encontrada.'],\r\n            }, 404);\r\n        }\r\n\r\n        return {\r\n            status: 'success',\r\n            data: transaction,\r\n        };\r\n    }\r\n\r\n    // Endpoint geral de progresso (compatibilidade com frontend)\r\n    async getProgress(userId: number, query: any) {\r\n        try {\r\n            // Buscar dados de avaliações recentes da tabela 'evaluations'\r\n            const evaluations = await db\r\n                .selectFrom('evaluations')\r\n                .where('user_id', '=', userId)\r\n                .orderBy('created_at', 'desc')\r\n                .limit(10)\r\n                .select([\r\n                    'id',\r\n                    'weight',\r\n                    'bf as body_fat_percentage',\r\n                    'created_at'\r\n                ])\r\n                .execute();\r\n\r\n            // Buscar fotos das avaliações\r\n            const evaluationIds = evaluations.map(e => e.id);\r\n            let photos: any[] = [];\r\n            if (evaluationIds.length > 0) {\r\n                photos = await db\r\n                    .selectFrom('evaluations_photos')\r\n                    .where('evaluation_id', 'in', evaluationIds)\r\n                    .select([\r\n                        'evaluation_id',\r\n                        'media_position',\r\n                        'media_url'\r\n                    ])\r\n                    .execute();\r\n            }\r\n\r\n            // Transformar dados para formato esperado pelo frontend\r\n            const progressData = evaluations.map(evaluation => {\r\n                const evalPhotos = photos.filter(p => p.evaluation_id === evaluation.id);\r\n                const photoMap: any = {};\r\n                evalPhotos.forEach(photo => {\r\n                    photoMap[photo.media_position] = photo.media_url;\r\n                });\r\n\r\n                return {\r\n                    weight: evaluation.weight,\r\n                    bodyFat: evaluation.body_fat_percentage,\r\n                    date: evaluation.created_at,\r\n                    photos: photoMap\r\n                };\r\n            });\r\n\r\n            return {\r\n                status: 'success',\r\n                data: progressData\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting progress data:', error);\r\n            return {\r\n                status: 'success',\r\n                data: []\r\n            };\r\n        }\r\n    }\r\n\r\n    // Análise nutricional semanal\r\n    async getAnalyticsNutritionWeekly(userId: number, query: any) {\r\n        try {\r\n            // Buscar dados nutricionais dos últimos 7 dias\r\n            const startOfDay = dayjs().subtract(7, 'days').startOf('day').toDate();\r\n            const endOfDay = dayjs().endOf('day').toDate();\r\n\r\n            // Buscar dados das refeições diárias\r\n            const mealsData = await db\r\n                .selectFrom('daily_meals')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .select([\r\n                    'daily_at as date',\r\n                    'calories',\r\n                    'protein',\r\n                    'carbs',\r\n                    'fat'\r\n                ])\r\n                .orderBy('daily_at', 'asc')\r\n                .execute();\r\n\r\n            return {\r\n                status: 'success',\r\n                data: mealsData\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting weekly nutrition analytics:', error);\r\n            return {\r\n                status: 'success',\r\n                data: []\r\n            };\r\n        }\r\n    }\r\n\r\n    // Análise de treinos semanal\r\n    async getAnalyticsWorkoutsWeekly(userId: number, query: any) {\r\n        try {\r\n            // Buscar dados de treinos dos últimos 7 dias\r\n            const startOfDay = dayjs().subtract(7, 'days').startOf('day').toDate();\r\n            const endOfDay = dayjs().endOf('day').toDate();\r\n\r\n            const workoutData = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .select([\r\n                    'daily_at as date',\r\n                    'calories as caloriesBurned'\r\n                ])\r\n                .orderBy('daily_at', 'asc')\r\n                .execute();\r\n\r\n            return {\r\n                status: 'success',\r\n                data: workoutData\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting weekly workout analytics:', error);\r\n            return {\r\n                status: 'success',\r\n                data: []\r\n            };\r\n        }\r\n    }\r\n\r\n    // Evolução de força\r\n    async getStrengthEvolution(userId: number, query: any) {\r\n        try {\r\n            // Por enquanto, retornar dados vazios até implementarmos a consulta correta\r\n            // TODO: Implementar consulta real quando a estrutura do banco estiver clara\r\n            return {\r\n                status: 'success',\r\n                data: {}\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting strength evolution:', error);\r\n            return {\r\n                status: 'success',\r\n                data: {}\r\n            };\r\n        }\r\n    }\r\n\r\n    // Aderência semanal\r\n    async getProgressAdherence(userId: number, query: any) {\r\n        try {\r\n            const { week } = query;\r\n\r\n            // If week is provided, calculate start and end dates for that week\r\n            let startOfDay: Date;\r\n            let endOfDay: Date;\r\n\r\n            if (week) {\r\n                const weekDate = dayjs(week);\r\n                startOfDay = weekDate.startOf('week').toDate();\r\n                endOfDay = weekDate.endOf('week').toDate();\r\n            } else {\r\n                // Default to current week\r\n                startOfDay = dayjs().startOf('week').toDate();\r\n                endOfDay = dayjs().endOf('week').toDate();\r\n            }\r\n\r\n            // For string date comparisons (goal_date is string type)\r\n            const startDateString = dayjs(startOfDay).format('YYYY-MM-DD');\r\n            const endDateString = dayjs(endOfDay).format('YYYY-MM-DD');\r\n\r\n            // Get diet adherence data from daily_meals_goal table\r\n            const dietGoalData = await db\r\n                .selectFrom('daily_meals_goal')\r\n                .where('user_id', '=', userId)\r\n                .where('goal_date', '>=', startDateString)\r\n                .where('goal_date', '<=', endDateString)\r\n                .select([\r\n                    'goal_date as date',\r\n                    'meals_completed',\r\n                    'meals'\r\n                ])\r\n                .execute();\r\n\r\n            // Get workout adherence data - count activities per day\r\n            const workoutData = await db\r\n                .selectFrom('daily_workouts_activities')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .select([\r\n                    'daily_at as date',\r\n                    db.fn.count('id').as('activities_count')\r\n                ])\r\n                .groupBy('daily_at')\r\n                .execute();\r\n\r\n            // Get coach protocol data for workout targets\r\n            const coachProtocolData = await db\r\n                .selectFrom('daily_coach_protocol')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .select([\r\n                    'daily_at as date',\r\n                    db.fn.count('id').as('planned_workouts')\r\n                ])\r\n                .groupBy('daily_at')\r\n                .execute();\r\n\r\n            // Calculate adherence percentages\r\n            const totalDietMeals = dietGoalData.reduce((sum, day) => sum + (day.meals || 0), 0);\r\n            const completedDietMeals = dietGoalData.reduce((sum, day) => sum + (day.meals_completed || 0), 0);\r\n            const dietPercentage = totalDietMeals > 0 ? (completedDietMeals / totalDietMeals) * 100 : 0;\r\n\r\n            const totalWorkoutActivities = workoutData.reduce((sum, day) => sum + Number(day.activities_count || 0), 0);\r\n            const totalPlannedWorkouts = coachProtocolData.reduce((sum, day) => sum + Number(day.planned_workouts || 0), 0);\r\n            const workoutPercentage = totalPlannedWorkouts > 0 ? (totalWorkoutActivities / totalPlannedWorkouts) * 100 :\r\n                                    totalWorkoutActivities > 0 ? 100 : 0; // If no planned workouts but activities exist, 100%\r\n\r\n            // Get water data from daily_water table\r\n            const waterData = await db\r\n                .selectFrom('daily_water')\r\n                .where('user_id', '=', userId)\r\n                .where('daily_at', '>=', startOfDay)\r\n                .where('daily_at', '<=', endOfDay)\r\n                .select([\r\n                    'daily_at as date',\r\n                    'consumed as glasses_consumed',\r\n                    'consumed as glasses_goal'\r\n                ])\r\n                .execute();\r\n\r\n            const totalWaterGlasses = waterData.reduce((sum, day) => sum + (day.glasses_goal || 8), 0);\r\n            const completedWaterGlasses = waterData.reduce((sum, day) => sum + (day.glasses_consumed || 0), 0);\r\n            const waterPercentage = totalWaterGlasses > 0 ? (completedWaterGlasses / totalWaterGlasses) * 100 : 0;\r\n\r\n            const overallPercentage = (dietPercentage + workoutPercentage + waterPercentage) / 3;\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    diet: {\r\n                        completed: completedDietMeals,\r\n                        total: totalDietMeals,\r\n                        percentage: Math.round(dietPercentage * 10) / 10\r\n                    },\r\n                    workout: {\r\n                        completed: totalWorkoutActivities,\r\n                        total: totalPlannedWorkouts || totalWorkoutActivities, // Use planned workouts or actual activities as total\r\n                        percentage: Math.round(workoutPercentage * 10) / 10\r\n                    },\r\n                    water: {\r\n                        completed: completedWaterGlasses,\r\n                        total: totalWaterGlasses,\r\n                        percentage: Math.round(waterPercentage * 10) / 10\r\n                    },\r\n                    overall: {\r\n                        percentage: Math.round(overallPercentage * 10) / 10\r\n                    }\r\n                }\r\n            };\r\n        } catch (error) {\r\n            console.error('Error getting progress adherence:', error);\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    diet: { completed: 0, total: 0, percentage: 0 },\r\n                    workout: { completed: 0, total: 0, percentage: 0 },\r\n                    water: { completed: 0, total: 0, percentage: 0 },\r\n                    overall: { percentage: 0 }\r\n                }\r\n            };\r\n        }\r\n    }\r\n\r\n    // WORKOUT SESSION METHODS\r\n\r\n    /**\r\n     * Start a workout session from an active protocol\r\n     */\r\n    async startWorkout(userId: number, body: any) {\r\n        try {\r\n            const { protocolId, templateId, exercises, date } = body;\r\n\r\n            console.log('🏋️ startWorkout: Starting workout session for user:', userId);\r\n            console.log('📋 Protocol ID:', protocolId);\r\n            console.log('📅 Date:', date);\r\n\r\n            // Get active protocol if protocolId not provided\r\n            let workoutProtocolId = protocolId;\r\n            if (!workoutProtocolId) {\r\n                const activeProtocol = await this.getActiveProtocolsWorkouts(userId);\r\n                if (activeProtocol.data.has_protocol) {\r\n                    workoutProtocolId = activeProtocol.data.id;\r\n                } else {\r\n                    throw new Error('No active workout protocol found');\r\n                }\r\n            }\r\n\r\n            // Ensure protocolId is a number\r\n            workoutProtocolId = Number(workoutProtocolId);\r\n            if (isNaN(workoutProtocolId)) {\r\n                throw new Error('Invalid protocol ID');\r\n            }\r\n\r\n            // Verify protocol exists and belongs to user\r\n            const protocol = await db\r\n                .selectFrom('coach_protocols')\r\n                .selectAll()\r\n                .where('id', '=', workoutProtocolId)\r\n                .where('client_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            if (!protocol) {\r\n                throw new Error('Workout protocol not found');\r\n            }\r\n\r\n            // Get the first workout from the protocol if no specific workout selected\r\n            const protocolWorkouts = await db\r\n                .selectFrom('coach_protocols_workouts')\r\n                .selectAll()\r\n                .where('protocol_id', '=', workoutProtocolId)\r\n                .orderBy('id', 'asc')\r\n                .execute();\r\n\r\n            if (protocolWorkouts.length === 0) {\r\n                throw new Error('No workouts found in protocol');\r\n            }\r\n\r\n            // Use the first workout for now (can be enhanced to select specific workout)\r\n            const selectedWorkout = protocolWorkouts[0];\r\n\r\n            // Create workout session record\r\n            const workoutSession = await db\r\n                .insertInto('daily_coach_protocol')\r\n                .values({\r\n                    user_id: userId,\r\n                    protocol_id: workoutProtocolId,\r\n                    protocol_workout_id: selectedWorkout.id,\r\n                    workout_time: new Date(), // Set to current time when workout starts\r\n                    total_calories: 0, // Will be calculated when completed\r\n                    total_weight: 0, // Will be calculated when completed\r\n                    met: 6.0, // Default MET value for strength training\r\n                    daily_at: new Date(date),\r\n                    created_at: new Date(),\r\n                    updated_at: new Date()\r\n                })\r\n                .executeTakeFirst();\r\n\r\n            const sessionId = Number(workoutSession.insertId);\r\n\r\n            console.log('✅ Workout session created with ID:', sessionId);\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    sessionId,\r\n                    protocolId: workoutProtocolId,\r\n                    workoutId: selectedWorkout.id,\r\n                    workoutName: selectedWorkout.name,\r\n                    message: 'Workout session started successfully'\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error starting workout:', error);\r\n            throw new HttpException({\r\n                status: 'error',\r\n                message: [error.message || 'Failed to start workout session']\r\n            }, 400);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get workouts by date\r\n     */\r\n    async getWorkoutsByDate(userId: number, query: any) {\r\n        try {\r\n            const { date } = query;\r\n            const targetDate = date ? new Date(date) : new Date();\r\n\r\n            console.log('📅 getWorkoutsByDate: Getting workouts for date:', targetDate);\r\n\r\n            const workouts = await db\r\n                .selectFrom('daily_coach_protocol as dcp')\r\n                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n                .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n                .select([\r\n                    'dcp.id as session_id',\r\n                    'dcp.protocol_id',\r\n                    'dcp.protocol_workout_id',\r\n                    'cpw.name as workout_name',\r\n                    'cp.name as protocol_name',\r\n                    'dcp.workout_time',\r\n                    'dcp.total_calories',\r\n                    'dcp.total_weight',\r\n                    'dcp.daily_at',\r\n                    'dcp.created_at'\r\n                ])\r\n                .where('dcp.user_id', '=', userId)\r\n                .where('dcp.daily_at', '>=', dayjs(targetDate).startOf('day').toDate())\r\n                .where('dcp.daily_at', '<=', dayjs(targetDate).endOf('day').toDate())\r\n                .orderBy('dcp.created_at', 'desc')\r\n                .execute();\r\n\r\n            return {\r\n                status: 'success',\r\n                data: workouts\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error getting workouts by date:', error);\r\n            return {\r\n                status: 'success',\r\n                data: []\r\n            };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get workout history with pagination\r\n     */\r\n    async getWorkoutHistory(userId: number, query: any) {\r\n        try {\r\n            const { period = 'month', page = 1, limit = 10 } = query;\r\n            const offset = (page - 1) * limit;\r\n\r\n            console.log('📚 getWorkoutHistory: Getting workout history for period:', period);\r\n\r\n            // Calculate date range based on period\r\n            let startDate: Date;\r\n            let endDate = new Date();\r\n\r\n            switch (period) {\r\n                case 'week':\r\n                case '7d':\r\n                    startDate = dayjs().subtract(7, 'days').startOf('day').toDate();\r\n                    break;\r\n                case 'month':\r\n                case '30d':\r\n                    startDate = dayjs().subtract(30, 'days').startOf('day').toDate();\r\n                    break;\r\n                case '3months':\r\n                case '90d':\r\n                    startDate = dayjs().subtract(90, 'days').startOf('day').toDate();\r\n                    break;\r\n                default:\r\n                    startDate = dayjs().subtract(30, 'days').startOf('day').toDate();\r\n            }\r\n\r\n            const workouts = await db\r\n                .selectFrom('daily_coach_protocol as dcp')\r\n                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n                .innerJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n                .select([\r\n                    'dcp.id as session_id',\r\n                    'dcp.protocol_id',\r\n                    'dcp.protocol_workout_id',\r\n                    'cpw.name as workout_name',\r\n                    'cp.name as protocol_name',\r\n                    'dcp.workout_time',\r\n                    'dcp.total_calories',\r\n                    'dcp.total_weight',\r\n                    'dcp.daily_at as date',\r\n                    'dcp.created_at',\r\n                    'cp.objective',\r\n                    'cp.split'\r\n                ])\r\n                .where('dcp.user_id', '=', userId)\r\n                .where('dcp.daily_at', '>=', startDate)\r\n                .where('dcp.daily_at', '<=', endDate)\r\n                // Show all workouts - let frontend handle completion status\r\n                // .where('dcp.total_calories', '>', 0) // Temporarily removed to show all workouts\r\n                .orderBy('dcp.daily_at', 'desc')\r\n                .limit(limit)\r\n                .offset(offset)\r\n                .execute();\r\n\r\n            // Get total count for pagination\r\n            const totalResult = await db\r\n                .selectFrom('daily_coach_protocol as dcp')\r\n                .select(db.fn.countAll().as('total'))\r\n                .where('dcp.user_id', '=', userId)\r\n                .where('dcp.daily_at', '>=', startDate)\r\n                .where('dcp.daily_at', '<=', endDate)\r\n                // Count all workouts - let frontend handle filtering\r\n                // .where('dcp.total_calories', '>', 0) // Temporarily removed\r\n                .executeTakeFirst();\r\n\r\n            const total = Number(totalResult?.total || 0);\r\n\r\n            // Calculate summary statistics\r\n            const totalWorkouts = workouts.length;\r\n            const totalVolume = workouts.reduce((sum, w) => sum + (w.total_weight || 0), 0);\r\n            const averageIntensity = totalWorkouts > 0 ?\r\n                workouts.reduce((sum, w) => sum + (w.total_calories || 0), 0) / totalWorkouts : 0;\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    workouts: workouts.map(w => {\r\n                        // Calculate duration in minutes from workout_time to now (if completed)\r\n                        let durationMinutes = 45; // Default duration\r\n                        if (w.workout_time && w.total_calories > 0) {\r\n                            // If workout is completed, assume it took 45 minutes (we can enhance this later)\r\n                            durationMinutes = 45;\r\n                        }\r\n\r\n                        return {\r\n                            ...w,\r\n                            // More robust completion logic - consider multiple factors\r\n                            completed: (w.total_calories && w.total_calories > 0) || (w.total_weight && w.total_weight > 0) || w.workout_time !== null,\r\n                            duration: durationMinutes, // Duration in minutes\r\n                            workout_start_time: w.workout_time // Keep original time for reference\r\n                        };\r\n                    }),\r\n                    totalWorkouts,\r\n                    totalVolume,\r\n                    averageIntensity,\r\n                    pagination: {\r\n                        page,\r\n                        limit,\r\n                        total,\r\n                        totalPages: Math.ceil(total / limit)\r\n                    }\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error getting workout history:', error);\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    workouts: [],\r\n                    totalWorkouts: 0,\r\n                    totalVolume: 0,\r\n                    averageIntensity: 0,\r\n                    pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }\r\n                }\r\n            };\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Get workout session details by session ID\r\n     */\r\n    async getWorkoutSession(userId: number, sessionId: string) {\r\n        try {\r\n\r\n\r\n            // Get session details with joins\r\n            const session = await db\r\n                .selectFrom('daily_coach_protocol as dcp')\r\n                .innerJoin('coach_protocols as cp', 'cp.id', 'dcp.protocol_id')\r\n                .leftJoin('coach_protocols_workouts as cpw', 'cpw.id', 'dcp.protocol_workout_id')\r\n                .select([\r\n                    'dcp.id as session_id',\r\n                    'dcp.protocol_id',\r\n                    'dcp.protocol_workout_id as workout_id',\r\n                    'cpw.name as workout_name',\r\n                    'cp.name as protocol_name',\r\n                    'dcp.workout_time',\r\n                    'dcp.total_calories',\r\n                    'dcp.total_weight',\r\n                    'dcp.daily_at',\r\n                    'dcp.created_at',\r\n                    'cp.objective',\r\n                    'cp.split'\r\n                ])\r\n                .where('dcp.id', '=', Number(sessionId))\r\n                .where('dcp.user_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            if (!session) {\r\n                throw new Error('Workout session not found');\r\n            }\r\n\r\n            // Get exercises with proper joins (handles both linked exercises and custom exercises)\r\n            const exercises = await db\r\n                .selectFrom('coach_protocols_workouts_exercises as cpwe')\r\n                .leftJoin('exercises as e', 'e.id', 'cpwe.exercise_id')\r\n                .leftJoin('select_options as s', 's.id', 'e.muscle_group_id')\r\n                .select([\r\n                    'cpwe.id',\r\n                    'e.name as exercise_name',\r\n                    'cpwe.name as custom_name',\r\n                    'cpwe.sets',\r\n                    'cpwe.reps',\r\n                    'cpwe.rpe',\r\n                    'cpwe.rest_seconds',\r\n                    's.value_option as muscle_group',\r\n                    'cpwe.notes'\r\n                ])\r\n                .where('cpwe.workout_id', '=', session.workout_id)\r\n                .execute();\r\n\r\n            // Format the response to match what ActiveWorkoutPage expects\r\n            const workoutData = {\r\n                id: session.workout_id,\r\n                name: session.workout_name,\r\n                muscle_groups: exercises.map(e => e.muscle_group).filter((v, i, a) => a.indexOf(v) === i).join(', '),\r\n                exercises: exercises.map(exercise => ({\r\n                    id: exercise.id,\r\n                    name: exercise.exercise_name || exercise.custom_name || 'Exercício sem nome',\r\n                    sets: exercise.sets,\r\n                    reps: exercise.reps,\r\n                    rpe: exercise.rpe,\r\n                    rest_seconds: exercise.rest_seconds,\r\n                    muscle_group: exercise.muscle_group,\r\n                    notes: exercise.notes\r\n                }))\r\n            };\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    session: {\r\n                        id: session.session_id,\r\n                        protocol_id: session.protocol_id,\r\n                        workout_id: session.workout_id,\r\n                        workout_name: session.workout_name,\r\n                        protocol_name: session.protocol_name,\r\n                        workout_time: session.workout_time,\r\n                        total_calories: session.total_calories,\r\n                        total_weight: session.total_weight,\r\n                        date: session.daily_at,\r\n                        created_at: session.created_at,\r\n                        objective: session.objective,\r\n                        split: session.split\r\n                    },\r\n                    workout: workoutData,\r\n                    // Format as protocol structure for compatibility\r\n                    has_protocol: true,\r\n                    id: session.protocol_id,\r\n                    name: session.protocol_name,\r\n                    workouts: [workoutData]\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error getting workout session:', error);\r\n            throw new HttpException({\r\n                status: 'error',\r\n                message: [error.message || 'Failed to get workout session']\r\n            }, 400);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Complete a workout session\r\n     */\r\n    async completeWorkout(userId: number, workoutId: string, completionData?: any) {\r\n        try {\r\n            console.log('✅ completeWorkout: Completing workout session:', workoutId);\r\n            console.log('📊 completeWorkout: Completion data:', completionData);\r\n\r\n            // Validate workoutId\r\n            if (!workoutId || isNaN(Number(workoutId))) {\r\n                throw new Error('Invalid workout ID provided');\r\n            }\r\n\r\n            // Find the workout session\r\n            const session = await db\r\n                .selectFrom('daily_coach_protocol')\r\n                .selectAll()\r\n                .where('id', '=', Number(workoutId))\r\n                .where('user_id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            if (!session) {\r\n                throw new Error(`Workout session not found for ID: ${workoutId} and user: ${userId}`);\r\n            }\r\n\r\n            console.log('📋 completeWorkout: Found session:', {\r\n                id: session.id,\r\n                protocol_id: session.protocol_id,\r\n                workout_time: session.workout_time,\r\n                user_id: session.user_id\r\n            });\r\n\r\n            // Calculate workout duration\r\n            let workoutDuration = 45; // Default 45 minutes\r\n\r\n            // Use duration from completion data if provided\r\n            if (completionData?.duration && completionData.duration > 0) {\r\n                workoutDuration = Math.round(completionData.duration / 60); // Convert seconds to minutes\r\n                console.log('📊 Using duration from completion data:', workoutDuration, 'minutes');\r\n            } else if (session.workout_time && session.workout_time.getTime() > new Date('2020-01-01').getTime()) {\r\n                // Only calculate from workout_time if it's a reasonable date (after 2020)\r\n                workoutDuration = Math.round((new Date().getTime() - session.workout_time.getTime()) / (1000 * 60));\r\n                console.log('📊 Calculated duration from workout_time:', workoutDuration, 'minutes');\r\n            } else {\r\n                console.log('📊 Using default duration:', workoutDuration, 'minutes');\r\n            }\r\n\r\n            // Get user weight for calorie calculation\r\n            const user = await db\r\n                .selectFrom('users')\r\n                .select(['weight'])\r\n                .where('id', '=', userId)\r\n                .executeTakeFirst();\r\n\r\n            const userWeight = user?.weight || 70; // Default 70kg if not set\r\n\r\n            // Calculate calories burned (MET * weight * time in hours)\r\n            const met = session.met || 6.0; // Default MET for strength training\r\n            const caloriesBurned = Math.round(met * userWeight * (Number(workoutDuration) / 60));\r\n\r\n            // Update the session with completion data\r\n            console.log('💪 completeWorkout: Updating session with completion data');\r\n            console.log('📊 Workout duration:', workoutDuration, 'minutes');\r\n            console.log('🔥 Calories burned:', caloriesBurned);\r\n\r\n            await db\r\n                .updateTable('daily_coach_protocol')\r\n                .set({\r\n                    total_calories: caloriesBurned,\r\n                    updated_at: new Date()\r\n                    // Keep the original workout_time as the start time\r\n                    // Don't modify workout_time since it represents when the workout started\r\n                })\r\n                .where('id', '=', Number(workoutId))\r\n                .where('user_id', '=', userId)\r\n                .execute();\r\n\r\n            console.log('✅ completeWorkout: Session updated successfully');\r\n\r\n            console.log('✅ Workout session completed successfully');\r\n\r\n            return {\r\n                status: 'success',\r\n                data: {\r\n                    sessionId: workoutId,\r\n                    duration: workoutDuration,\r\n                    caloriesBurned,\r\n                    message: 'Workout completed successfully'\r\n                }\r\n            };\r\n\r\n        } catch (error) {\r\n            console.error('❌ Error completing workout:', error);\r\n            throw new HttpException({\r\n                status: 'error',\r\n                message: [error.message || 'Failed to complete workout session']\r\n            }, 400);\r\n        }\r\n    }\r\n\r\n    // Helper method to generate micronutrients data from macronutrients\r\n    private generateMicronutrientsFromMacros(macroData: any) {\r\n        const { calories, protein, carbs, fat } = macroData;\r\n\r\n        // Generate realistic micronutrient data based on macronutrient intake\r\n        const micronutrients = [\r\n            {\r\n                micronutrientId: 'vitamin_d',\r\n                name: 'Vitamina D',\r\n                currentIntake: Math.max(5, Math.min(15, calories / 200)), // 5-15 mcg based on calories\r\n                recommendedIntake: 10,\r\n                unit: 'mcg',\r\n                status: calories > 1800 ? 'adequate' : 'deficient',\r\n                percentage: Math.min(100, (calories / 2000) * 80)\r\n            },\r\n            {\r\n                micronutrientId: 'iron',\r\n                name: 'Ferro',\r\n                currentIntake: Math.max(8, Math.min(18, protein / 10)), // Based on protein intake\r\n                recommendedIntake: 14,\r\n                unit: 'mg',\r\n                status: protein > 100 ? 'adequate' : 'low',\r\n                percentage: Math.min(100, (protein / 120) * 90)\r\n            },\r\n            {\r\n                micronutrientId: 'vitamin_b12',\r\n                name: 'Vitamina B12',\r\n                currentIntake: Math.max(1.5, Math.min(3, protein / 50)), // Based on protein\r\n                recommendedIntake: 2.4,\r\n                unit: 'mcg',\r\n                status: protein > 80 ? 'adequate' : 'deficient',\r\n                percentage: Math.min(100, (protein / 100) * 85)\r\n            },\r\n            {\r\n                micronutrientId: 'calcium',\r\n                name: 'Cálcio',\r\n                currentIntake: Math.max(600, Math.min(1200, calories * 0.5)), // Based on overall intake\r\n                recommendedIntake: 1000,\r\n                unit: 'mg',\r\n                status: calories > 2000 ? 'adequate' : 'low',\r\n                percentage: Math.min(100, (calories / 2200) * 95)\r\n            },\r\n            {\r\n                micronutrientId: 'vitamin_c',\r\n                name: 'Vitamina C',\r\n                currentIntake: Math.max(40, Math.min(120, carbs / 2)), // Based on carbs (fruits/vegetables)\r\n                recommendedIntake: 90,\r\n                unit: 'mg',\r\n                status: carbs > 150 ? 'adequate' : 'low',\r\n                percentage: Math.min(100, (carbs / 180) * 88)\r\n            },\r\n            {\r\n                micronutrientId: 'omega_3',\r\n                name: 'Ômega-3',\r\n                currentIntake: Math.max(0.8, Math.min(2.5, fat / 30)), // Based on fat intake\r\n                recommendedIntake: 1.6,\r\n                unit: 'g',\r\n                status: fat > 50 ? 'adequate' : 'deficient',\r\n                percentage: Math.min(100, (fat / 60) * 92)\r\n            }\r\n        ];\r\n\r\n        const deficiencies = micronutrients.filter(m => m.status === 'deficient' || m.status === 'low');\r\n        const excesses = micronutrients.filter(m => m.status === 'excess');\r\n\r\n        const recommendations = deficiencies.map(micro => ({\r\n            micronutrientId: micro.micronutrientId,\r\n            recommendation: `Aumentar consumo de ${micro.name}`,\r\n            foods: this.getRecommendedFoods(micro.micronutrientId),\r\n            priority: micro.status === 'deficient' ? 'high' : 'medium'\r\n        }));\r\n\r\n        const overallScore = Math.round(\r\n            micronutrients.reduce((sum, micro) => sum + micro.percentage, 0) / micronutrients.length\r\n        );\r\n\r\n        return {\r\n            totalIntake: micronutrients,\r\n            deficiencies,\r\n            excesses,\r\n            recommendations,\r\n            overallScore,\r\n            improvementAreas: deficiencies.map(d => d.name).slice(0, 3)\r\n        };\r\n    }\r\n\r\n    private getRecommendedFoods(micronutrientId: string): string[] {\r\n        const foodMap: { [key: string]: string[] } = {\r\n            'vitamin_d': ['Salmão', 'Sardinha', 'Ovos', 'Cogumelos'],\r\n            'iron': ['Carne vermelha', 'Feijão', 'Espinafre', 'Lentilha'],\r\n            'vitamin_b12': ['Carne', 'Peixe', 'Ovos', 'Laticínios'],\r\n            'calcium': ['Leite', 'Queijo', 'Iogurte', 'Brócolis'],\r\n            'vitamin_c': ['Laranja', 'Morango', 'Kiwi', 'Pimentão'],\r\n            'omega_3': ['Salmão', 'Sardinha', 'Nozes', 'Linhaça']\r\n        };\r\n\r\n        return foodMap[micronutrientId] || ['Alimentos variados'];\r\n    }\r\n}\r\n"], "names": ["UsersService", "dayjs", "require", "timezone", "utc", "isoWeek", "extend", "create", "createClientDto", "userId", "role", "checkUser", "db", "selectFrom", "where", "email", "select", "executeTakeFirst", "HttpException", "status", "message", "roles", "role_id", "hashedPassword", "bcrypt", "hash", "password", "new_client", "insertInto", "values", "user_id", "Number", "insertId", "execute", "client_id", "data", "id", "getMe", "user", "leftJoin", "groupBy", "dataFormat", "name", "username", "phone", "photo", "height", "parseFloat", "toFixed", "weight", "<PERSON><PERSON>t", "goal", "date_of_birth", "getGoals", "goals", "orderBy", "updateMe", "body", "goal_id", "undefined", "length", "existingUser", "updateData", "updated_at", "Date", "updateTable", "set", "softDeleteAccount", "deleted_at", "deleteFrom", "updateMePhoto", "file", "maxFileSize", "size", "allowedMimeTypes", "includes", "mimetype", "uploadDir", "path", "join", "toString", "fs", "promises", "mkdir", "recursive", "fileExtension", "randomName", "now", "Math", "random", "substring", "filePath", "image", "sharp", "buffer", "metadata", "MAX_DIMENSION", "width", "resizeOptions", "fit", "withoutEnlargement", "resize", "jpeg", "quality", "toFile", "photoUrl", "filename", "getDailyNutritionalSummary", "query", "date_start", "date_end", "tz", "localStart", "startOf", "localEnd", "endOf", "startOfDay", "toDate", "endOfDay", "eb", "or", "water", "fn", "sum", "as", "daily_nutritionist_protocol", "dailyCoachProtocol", "dailyWorkoutsActivities", "caloriesProtocolBurned", "total_calories", "caloriesWorkoutsBurned", "calories", "total_calories_burned", "sum_daily_nutritionist_protocol", "reduce", "acc", "curr", "max", "protein", "carbs", "fat", "total", "calories_remaining", "goal_calories", "dataNutritionalSummary", "consumed", "burned", "remaining", "goal_protein", "goal_fat", "goal_carbs", "fiber", "goal_water", "getDaily", "date", "waterConsumed", "activity", "device_source", "steps", "active", "minutes", "heart", "default_min", "default_max", "nutritional_summary", "getFirstName", "names", "split", "getAssessments", "assessmentsData", "assessments", "days_met_goals", "attendance", "weekly", "monthly", "streak", "record_streak", "weekly_diet_progress", "day", "completed", "success_rate", "complete_meals", "perfect_days", "postDailyWater", "dailyWaterDto", "daily_at", "dailyAt", "daily", "created_at", "getDailyWorkoutsActivities", "getDailyWorkoutsActivitiesByDate", "minutesToHHMMSS", "hours", "floor", "remainingMinutes", "seconds", "postDailyWorkoutsActivities", "dailyWorkoutsActivitiesDto", "duration", "workoutsActivities", "calculated_calories", "activity_id", "activity_time", "hasActiveProtocolWorkout", "today", "console", "log", "toISOString", "protocol", "selectAll", "started_at", "ended_at", "hasActiveProtocolDiet", "type", "type_id", "warn", "orphanProtocols", "orphan", "fixOrphanProtocols", "results", "orphansFound", "orphansFixed", "consistencyRestored", "details", "push", "action", "validationCount", "count", "displayCount", "error", "ensureSingleActiveProtocol", "activeProtocols", "activeProtocol", "deactivatedCount", "mostRecentProtocol", "protocolsToDeactivate", "slice", "updateError", "getProtocolsDietHistory", "options", "page", "limit", "offset", "protocols", "processedProtocols", "map", "objective", "duration_days", "getTime", "filteredProtocols", "filter", "p", "totalResult", "activeCount", "finishedCount", "result", "pagination", "totalPages", "ceil", "hasNext", "has<PERSON>rev", "stats", "finished", "avgDuration", "topTypes", "filters", "startDate", "endDate", "protocolsCount", "stack", "getProtocolsDietStats", "totalProtocols", "finishedProtocols", "t", "finishProtocolDiet", "protocolId", "Error", "updatedProtocol", "duplicateProtocolDiet", "newStartDate", "originalProtocol", "newProtocol", "initial_weight", "general_notes", "newProtocolId", "originalMeals", "mealsCount", "meal", "newMeal", "protocol_id", "day_of_week", "meal_time", "newMealId", "originalFoods", "food", "meal_id", "food_id", "quantity", "unit", "originalSupplements", "supplementsCount", "supplement", "dosage", "supplement_time", "notes", "originalId", "postProtocolsWorkout", "createProtocolWorkoutDto", "frequency", "workouts", "new_protocol", "new_protocol_id", "Array", "isArray", "letters", "index", "workout", "exercises", "workout_name", "new_workout", "new_workout_id", "exercise", "exercise_id", "workout_id", "sets", "reps", "rpe", "rest_seconds", "finishProtocolWorkout", "duplicateProtocolWorkout", "originalWorkouts", "newWorkout", "newWorkoutId", "originalExercises", "original_protocol_id", "debugProtocolsRaw", "allProtocols", "tables", "coach_protocols", "debugWorkoutsRaw", "allWorkouts", "completedWorkouts", "w", "recentWorkouts", "workoutDate", "thirtyDaysAgo", "setDate", "getDate", "totalWorkouts", "completedWorkoutsData", "recentWorkoutsData", "daily_coach_protocol", "getProtocolsWorkoutHistory", "formattedProtocols", "getProtocolsRawDebug", "generateWorkoutProtocolPrompt", "userInfo", "goalMapping", "goalId", "timeAvailable", "bodyFat", "activityLevel", "experience", "splitPreference", "equipment", "targetMuscles", "restrictions", "preferences", "postProtocolsWorkoutAi", "prompt", "response", "openai", "chat", "completions", "model", "messages", "content", "temperature", "max_tokens", "response_format", "aiResponseText", "choices", "protocolDataText", "extractJsonFromString", "protocolData", "JSON", "parse", "transaction", "trx", "input", "getActiveProtocolsWorkouts", "has_protocol", "workoutName", "workoutsAndExercises", "Object", "pe_id", "exercise_name", "exercise_name2", "muscle_group", "media_url", "workoutsCompleted", "workouts_completed", "getProtocolDietById", "protocolExists", "mealRows", "mealsByDay", "Map", "for<PERSON>ach", "row", "toLowerCase", "has", "get", "find", "m", "nutrients", "foods", "food_name", "meals", "fromEntries", "supplements", "keys", "sup", "getProtocolWorkoutById", "updateProtocolWorkout", "existingProtocol", "trim", "existingWorkouts", "entries", "workoutId", "isCustomExercise", "startsWith", "restTime", "getUserData", "postProtocolsDiet", "createProtocolDietDto", "stringify", "nutritional_goals", "userData", "new_meal", "new_meal_id", "generateProtocolPrompt", "mealFrequency", "targetWeight", "targetBodyFat", "dietaryPreferences", "includedFoods", "dietPlan", "foodVariation", "text", "jsonStart", "indexOf", "jsonEnd", "lastIndexOf", "extracted", "jsonrepair", "extractJsonFromStringOld", "sanitized", "replace", "fixes", "regex", "replacement", "parsed", "postProtocolsDietAi", "userUpdateData", "getActiveProtocolsDiet", "protocolRule", "getActiveMealsOfDayWeek", "inputDate", "isNaN", "inputDateTz", "startOfDayUtc", "endOfDayUtc", "day<PERSON><PERSON>", "toLocaleDateString", "weekday", "protocolBase", "protocolType", "typeResult", "value_option", "deactivated_count", "onRef", "on", "sql", "mealsOutDiet", "and", "dateTz", "format", "timeTz", "mealRowsFormatted", "protocol_meal_id", "mealsOutDietFormatted", "allMeals", "allMealsOrder", "sort", "a", "b", "localeCompare", "mealsMap", "<PERSON><PERSON><PERSON>", "from", "foods_count", "getActiveProtocolsDietMealsDayWeek", "dayOfWeek", "deleteProtocolDiet", "deleteProtocolWorkout", "checkProtocolDiet", "meal_name", "meal_foods", "isMealChecked", "HttpStatus", "NOT_FOUND", "conversionFactor", "mealData", "dailyMeal", "dailyMealId", "uncheckProtocolDiet", "getDailyCheckedMeal", "getProtocolsWorkoutExercises", "workoutsArray", "convertTimeToHours", "time", "calculateCalories", "workoutTime", "met", "weightKg", "timeInHours", "postProtocolsWorkoutDaily", "protocol_workout_id", "workout_time", "total_weight", "series", "seriesData", "serie", "protocol_exercise_id", "dailyProtocolData", "dailyProtocol", "dailyProtocolId", "daily_id", "getProtocolsWorkoutDaily", "query<PERSON><PERSON>ult", "innerJoin", "ref", "distinct", "queryTotalRecordsResult", "countAll", "totalRecordsResult", "executeTakeFirstOrThrow", "totalRecords", "parseInt", "hasMore", "exercise_count", "muscle_groups", "total_records", "current_page", "has_more", "calculateDaysGoalSequence", "latestGoal", "goal_met", "latestDate", "goal_date", "previousDate", "previousDateStr", "previousGoal", "getProgressNutritionalDaysGoalSequence", "daysGoalSequence", "days_goal_sequence", "postProgressEvaluations", "files", "bf", "key", "weightNum", "bfNum", "frontImage", "backImage", "sideImage", "__dirname", "existsSync", "mkdirSync", "typesAllowed", "extname", "originalname", "fileName", "randomUUID", "imageResized", "<PERSON><PERSON><PERSON><PERSON>", "writeFile", "new_evaluation", "evaluationId", "imagesToInsert", "position", "img", "Promise", "all", "evaluation_id", "media_type", "media_position", "postProgressEvaluationsMeasurements", "shoulders", "chest", "waist", "abdomen", "hips", "biceps_right", "biceps_left", "forearm_right", "forearm_left", "thigh_right", "thigh_left", "calf_right", "calf_left", "new_evaluation_measurements", "getProgressEvaluations", "evaluations", "measurements", "totalEvaluations", "formattedEvaluations", "evaluation", "evaluationData", "item", "photos", "front", "back", "side", "photo_base_url", "process", "env", "STATIC_URL", "formattedMeasurements", "measurement", "measurementData", "allData", "calculateCurrentStreak", "calculateRecordStreak", "maxStreak", "currentStreak", "i", "currentDate", "diffInDays", "calculateWeekAttendance", "startOfWeek", "getDay", "endOfWeek", "startDateStr", "endDateStr", "successfulDays", "daysWithGoalMet", "totalDaysInWeek", "round", "calculateMonthAttendance", "startOfMonth", "getFullYear", "getMonth", "endOfMonth", "totalDaysInMonth", "getProgressDietWeekly", "protocolMeals", "totalMealsByDay", "meal_count", "dayMapping", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "dailyMeals", "weeklyProgress", "add", "mappedDay", "isSame", "completeMeals", "totalPossibleMeals", "successRate", "perfectDays", "getWeeklyProgressConsolidated", "nutritionData", "workoutData", "waterData", "sleepData", "weeklyConsolidated", "dateStr", "nutritionDay", "d", "workoutDay", "calories_burned", "waterDay", "total_consumed", "sleepDay", "nutrition", "percentage", "target", "min", "sleep", "totalDays", "nutritionSuccess", "workoutSuccess", "waterSuccess", "sleepSuccess", "summary", "days_completed", "average_hours", "average_quality", "getProgressDiet", "getProgressWeightFat", "weightFatQuery", "weightFat", "getProgressStrength", "filter_id", "strengthQuery", "strength", "getProgressNutritionalAnalysis", "nutritionalDataDaily", "nutritionalData", "weekly_averages", "macronutrient_distribution", "calories_percent", "carbs_percent", "protein_percent", "fat_percent", "daysInRange", "diff", "avgCalories", "avgCarbs", "total_carbs", "avgProtein", "total_protein", "avgFat", "total_fat", "carbsCalories", "proteinCalories", "fatCalories", "totalCaloriesFromMacros", "carbsPercent", "proteinPercent", "fatPercent", "micronutrientsData", "generateMicronutrientsFromMacros", "totalIntake", "deficiencies", "excesses", "recommendations", "overallScore", "improvementAreas", "getProgressCaloricBalance", "setHours", "totalConsumed", "totalBurned", "averageConsumed", "averageBurned", "chart", "average_consumed", "average_burned", "basal_metabolic_rate", "getProgressWorkouts", "workoutsMapped", "workoutsAvarage", "getProgressWorkoutsAnalysis", "totalMinutes", "totalCalories", "chartData", "label", "training_volume", "getProgressCompleteAnalysis", "startOfPeriod", "endOfPeriod", "workoutTimes", "mostFrequentTime", "String", "hour", "padStart", "averageInterval", "intervals", "averageIntervalFormatted", "mealTimes", "sortedMealTimes", "firstMeal", "lastMeal", "mealHours", "minHour", "maxHour", "numSlots", "range", "slotSize", "timeSlots", "start", "end", "frequencyByTime", "slot", "daysWithMeal", "percent", "muscleGroupsData", "muscleGroups", "group", "volumeDistribution", "volumeDistributionFormatted", "muscle_group_id", "most_frequent_time", "average_interval", "first_meal", "last_meal", "frequency_by_time", "volume_distribution", "getProgressVolumeDistribution", "volumeDistributionMapped", "getProgressAttendance", "activeDaysWeek", "totalDaysWeek", "weekAttendance", "activeDaysMonth", "totalDaysMonth", "monthAttendance", "allActiveDays", "activeDaysSet", "Set", "subtract", "tempStreak", "sortedActiveDays", "j", "week", "month", "getActiveMacroUserInfo", "getPromptTextAi", "getMealData", "mealText", "food_quantity", "food_unit", "food_calories", "food_protein", "food_carbs", "food_fat", "food_fiber", "getPromptTextReplacementAi", "getPromptTextRecognitionAi", "getPromptImage", "getPromptAudio", "processTextAi", "meal_type", "processImageAi", "base64", "completion", "image_url", "url", "processAudioAi", "base64Match", "match", "mimeType", "dataStr", "openAiAudiosAllowedTypes", "fileExt", "<PERSON><PERSON><PERSON>", "fsExtra", "ensureDir", "statSync", "fileBlob", "Blob", "File", "form", "FormData", "append", "config", "headers", "OPENAI_API_KEY", "axios", "post", "unlink", "transcription", "processAudioAiOld", "validAudioTypes", "needsConversion", "fsPromises", "randomName2", "filePath2", "convertedFilePath", "getFileUrl", "audio", "transcriptions", "createReadStream", "unlinkSync", "getFoodsSuggestions", "mealType", "mealId", "getType", "textJson", "item2", "getUserOptions", "affiliate", "admin", "isAffiliate", "isAdmin", "createAffiliate", "aff_ref", "aff_id", "affData", "affiliateData", "ref_user_id", "invite", "is_master", "newAffiliate", "subscribePlan", "planId", "plan", "stripePlanId", "payment_provider_external_id", "customerId", "stripeId", "customer", "stripe", "customers", "session", "checkout", "sessions", "payment_method_types", "mode", "client_reference_id", "success_url", "STRIPE_SUCCESS_URL", "cancel_url", "STRIPE_CANCEL_URL", "line_items", "price", "getAllPlans", "hasActiveSubscription", "plans", "selected", "plan_id", "getMySubscriptions", "subscriptions", "cancelSubscription", "subscriptionId", "subscription", "cancel_at_period_end", "cancelSubscriptionImmediately", "end_date", "getMyTransactions", "transactions", "getMyTransactionDetails", "transactionId", "getProgress", "evaluationIds", "e", "progressData", "evalPhotos", "photoMap", "body_fat_percentage", "getAnalyticsNutritionWeekly", "mealsData", "getAnalyticsWorkoutsWeekly", "getStrengthEvolution", "getProgressAdherence", "weekDate", "startDateString", "endDateString", "dietGoalData", "coachProtocolData", "totalDietMeals", "completedDietMeals", "meals_completed", "dietPercentage", "totalWorkoutActivities", "activities_count", "totalPlannedWorkouts", "planned_workouts", "workoutPercentage", "totalWaterGlasses", "glasses_goal", "completedWaterGlasses", "glasses_consumed", "waterPercentage", "overallPercentage", "diet", "overall", "startWorkout", "templateId", "workoutProtocolId", "protocolWorkouts", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "workoutSession", "sessionId", "getWorkoutsByDate", "targetDate", "getWorkoutHistory", "period", "totalVolume", "averageIntensity", "durationMinutes", "workout_start_time", "getWorkoutSession", "v", "custom_name", "session_id", "protocol_name", "completeWorkout", "completionData", "workoutDuration", "userWeight", "caloriesBurned", "macroData", "micronutrients", "micronutrientId", "currentIntake", "recommendedIntake", "micro", "recommendation", "getRecommendedFoods", "priority", "foodMap", "constructor", "Stripe", "STRIPE_SK", "OpenAI", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;+BA0EaA;;;eAAAA;;;wBA1EyC;0BACnC;kEAEK;wBAEJ;4DAkDA;8DACE;8DACJ;wBACS;+DAGR;kEAMS;iEACH;8DACP;4BACS;wBACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAjEvB,QAAQ;AACR,oCAAoC;AACpC,6BAA6B;AAC7B,kCAAkC;AAClC,4CAA4C;AAC5C,MAAMC,QAAQC,QAAQ;AACtB,MAAMC,WAAWD,QAAQ;AACzB,MAAME,MAAMF,QAAQ;AACpB,MAAMG,UAAUH,QAAQ;AACxBD,MAAMK,MAAM,CAACF;AACbH,MAAMK,MAAM,CAACH;AACbF,MAAMK,MAAM,CAACD;AAyDN,IAAA,AAAML,eAAN,MAAMA;IAgBT,MAAMO,OAAOC,eAAgC,EAAEC,MAAc,EAAEC,IAAY,EAAE;QACzE,MAAMC,YAAY,MAAMC,YAAE,CACrBC,UAAU,CAAC,SACXC,KAAK,CAAC,SAAS,KAAKN,gBAAgBO,KAAK,EACzCC,MAAM,CAAC,MACPC,gBAAgB;QAEjB,IAAIN,WAAW;YACX,MAAM,IAAIO,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA+B;YAC7C,GAAG;QACP;QAGJ,MAAMC,QAAQ;YACV,SAAS;YACT,SAAS;YACT,gBAAgB;YAChB,QAAQ;QACZ;QAEA,MAAMC,UAAkBD,KAAK,CAACX,KAAK;QAEnC,MAAMa,iBAAiB,MAAMC,UAAOC,IAAI,CAACjB,gBAAgBkB,QAAQ,EAAE;QAGnE,MAAMC,aAAa,MAAMf,YAAE,CAC1BgB,UAAU,CAAC,SACXC,MAAM,CAAC;YAAC,GAAGrB,eAAe;YAAEkB,UAAUH;QAAc,GACpDN,gBAAgB;QAEjB,cAAc;QACd,MAAML,YAAE,CACPgB,UAAU,CAAC,eACXC,MAAM,CAAC;YACJC,SAASC,OAAOJ,WAAWK,QAAQ;YACnCV,SAASS,OAAOT;QACpB,GACCW,OAAO;QAER,SAAS;QACT,MAAMrB,YAAE,CACPgB,UAAU,CAAC,WACXC,MAAM,CAAC;YACJK,WAAWH,OAAOJ,WAAWK,QAAQ;YACrCF,SAASC,OAAOtB;YAChBa,SAASS,OAAOT;QACpB,GACCW,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAM;gBACFC,IAAIL,OAAOJ,WAAWK,QAAQ;YAClC;QACJ;IACJ;IAEA,MAAMK,MAAM5B,MAAc,EAAE;QACxB,MAAM6B,OAAY,MAAM1B,YAAE,CACrBC,UAAU,CAAC,cACXC,KAAK,CAAC,QAAQ,KAAKL,QACnB8B,QAAQ,CAAC,0BAA0B,WAAW,aAC9CvB,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA,kBAAkB;YAClB,kBAAkB;YAClB;YACA;YACA;YACA;YACA;SACH,EACAwB,OAAO,CAAC,QACRvB,gBAAgB;QAEjB,MAAMwB,aAAa;YACfL,IAAIE,KAAKF,EAAE;YACXM,MAAMJ,KAAKI,IAAI;YACf3B,OAAOuB,KAAKvB,KAAK;YACjB4B,UAAUL,KAAKK,QAAQ;YACvBC,OAAON,KAAKM,KAAK;YACjBC,OAAOP,KAAKO,KAAK;YACjBC,QAAQC,WAAWhB,OAAOO,KAAKQ,MAAM,EAAEE,OAAO,CAAC,OAAO;YACtDC,QAAQF,WAAWhB,OAAOO,KAAKW,MAAM,EAAED,OAAO,CAAC,OAAO;YACtDE,SAASH,WAAWhB,OAAOO,KAAKY,OAAO,EAAEF,OAAO,CAAC,OAAO;YACxDG,MAAMb,KAAKa,IAAI;YACfC,eAAed,KAAKc,aAAa;QACrC;QAEJ,OAAO;YACHjC,QAAQ;YACRgB,MAAMM;QACV;IACJ;IAEA,MAAMY,WAAW;QACf,MAAMC,QAAQ,MAAM1C,YAAE,CACnBC,UAAU,CAAC,kBACXG,MAAM,CAAC;YAAC;YAAM;SAAuB,EACrCF,KAAK,CAAC,YAAY,KAAK,SACvByC,OAAO,CAAC,cACRtB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAMmB;QACR;IACF;IAEA,MAAME,SAAS/C,MAAc,EAAEgD,IAAS,EAAE;QACxC,MAAM,EACJf,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLE,MAAM,EACNG,MAAM,EACNC,OAAO,EACPQ,OAAO,EACPN,aAAa,EACbP,KAAK,EACN,GAAGY;QAEJ,mDAAmD;QACnD,IAAIX,WAAWa,aAAcb,CAAAA,SAAS,OAAOA,SAAS,GAAE,GAAI;YAC1D,MAAM,IAAI5B,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAwC;YACpD,GAAG;QACL;QAEA,IAAI6B,WAAWU,aAAcV,CAAAA,SAAS,MAAMA,SAAS,GAAE,GAAI;YACzD,MAAM,IAAI/B,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACjD,GAAG;QACL;QAEA,gCAAgC;QAChC,IAAIuB,aAAagB,WAAW;YAC1B,IAAIhB,SAASiB,MAAM,GAAG,KAAKjB,SAASiB,MAAM,GAAG,IAAI;gBAC/C,MAAM,IAAI1C,qBAAa,CAAC;oBACtBC,QAAQ;oBACRC,SAAS;wBAAC;qBAA6C;gBACzD,GAAG;YACL;YAEA,kCAAkC;YAClC,MAAMyC,eAAe,MAAMjD,YAAE,CAC1BC,UAAU,CAAC,SACXC,KAAK,CAAC,YAAY,KAAK6B,UACvB7B,KAAK,CAAC,MAAM,MAAML,QAClBO,MAAM,CAAC,MACPC,gBAAgB;YAEnB,IAAI4C,cAAc;gBAChB,MAAM,IAAI3C,qBAAa,CAAC;oBACtBC,QAAQ;oBACRC,SAAS;wBAAC;qBAAgC;gBAC5C,GAAG;YACL;QACF;QAEA,6DAA6D;QAC7D,MAAM0C,aAAkB;YACtBC,YAAY,IAAIC;QAClB;QAEA,IAAItB,SAASiB,WAAWG,WAAWpB,IAAI,GAAGA;QAC1C,IAAIC,aAAagB,WAAWG,WAAWnB,QAAQ,GAAGA;QAClD,IAAIC,UAAUe,WAAWG,WAAWlB,KAAK,GAAGA;QAC5C,IAAIE,WAAWa,WAAWG,WAAWhB,MAAM,GAAGA;QAC9C,IAAIG,WAAWU,WAAWG,WAAWb,MAAM,GAAGA;QAC9C,IAAIC,YAAYS,WAAWG,WAAWZ,OAAO,GAAGA;QAChD,IAAIQ,YAAYC,WAAWG,WAAWJ,OAAO,GAAGA;QAChD,IAAIN,kBAAkBO,WAAWG,WAAWV,aAAa,GAAG,IAAIY,KAAKZ;QACrE,IAAIP,UAAUc,WAAWG,WAAWjB,KAAK,GAAGA;QAE5C,MAAMjC,YAAE,CACLqD,WAAW,CAAC,SACZC,GAAG,CAACJ,YACJhD,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM;gBACJf,SAAS;YACX;QACF;IACF;IAEA,MAAM+C,kBAAkB1D,MAAc,EAAE;QACtC,kDAAkD;QAClD,MAAM6B,OAAO,MAAM1B,YAAE,CAClBC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAKL,QACjBO,MAAM,CAAC;YAAC;YAAM;SAAa,EAC3BC,gBAAgB;QAEnB,IAAI,CAACqB,MAAM;YACT,MAAM,IAAIpB,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA0B;YACtC,GAAG;QACL;QAEA,IAAIkB,KAAK8B,UAAU,EAAE;YACnB,MAAM,IAAIlD,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAuC;YACnD,GAAG;QACL;QAEA,2CAA2C;QAC3C,MAAMR,YAAE,CACLqD,WAAW,CAAC,SACZC,GAAG,CAAC;YACHE,YAAY,IAAIJ;YAChBD,YAAY,IAAIC;QAClB,GACClD,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;QAEV,kCAAkC;QAClC,MAAMrB,YAAE,CACLyD,UAAU,CAAC,cACXvD,KAAK,CAAC,WAAW,KAAKL,QACtBwB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM;gBACJf,SAAS;YACX;QACF;IACF;IAEA,MAAMkD,cAAc7D,MAAc,EAAE8D,IAAyB,EAAE;QAC7D,kBAAkB;QAClB,MAAMC,cAAc,KAAK,OAAO,MAAM,QAAQ;QAC9C,IAAID,KAAKE,IAAI,GAAGD,aAAa;YAC3B,MAAM,IAAItD,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACjD,GAAG;QACL;QAEA,0BAA0B;QAC1B,MAAMsD,mBAAmB;YAAC;YAAc;YAAa;YAAa;SAAa;QAC/E,IAAI,CAACA,iBAAiBC,QAAQ,CAACJ,KAAKK,QAAQ,GAAG;YAC7C,MAAM,IAAI1D,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA8D;YAC1E,GAAG;QACL;QAEA,mCAAmC;QACnC,MAAMyD,YAAYC,MAAKC,IAAI,CAAC,SAAS,SAAStE,OAAOuE,QAAQ,IAAI;QACjE,MAAMC,IAAGC,QAAQ,CAACC,KAAK,CAACN,WAAW;YAAEO,WAAW;QAAK;QAErD,sCAAsC;QACtC,MAAMC,gBAAgBd,KAAKK,QAAQ,KAAK,cAAc,QAAQ;QAC9D,MAAMU,aAAa,CAAC,QAAQ,EAAEtB,KAAKuB,GAAG,GAAG,CAAC,EAAEC,KAAKC,MAAM,GAAGT,QAAQ,CAAC,IAAIU,SAAS,CAAC,GAAG,CAAC,EAAEL,eAAe;QACtG,MAAMM,WAAWb,MAAKC,IAAI,CAACF,WAAWS;QAEtC,6CAA6C;QAC7C,MAAMM,QAAaC,IAAAA,cAAK,EAACtB,KAAKuB,MAAM;QACpC,MAAMC,WAAgB,MAAMH,MAAMG,QAAQ;QAE1C,iCAAiC;QACjC,MAAMC,gBAAgB;QACtB,IAAID,SAASE,KAAK,GAAGD,iBAAiBD,SAASjD,MAAM,GAAGkD,eAAe;YACrE,8CAA8C;YAC9C,MAAME,gBAAqC;gBACzCD,OAAOF,SAASE,KAAK,GAAGF,SAASjD,MAAM,GAAGkD,gBAAgBrC;gBAC1Db,QAAQiD,SAASjD,MAAM,GAAGiD,SAASE,KAAK,GAAGD,gBAAgBrC;gBAC3DwC,KAAK;gBACLC,oBAAoB,KAAK,oCAAoC;YAC/D;YAEA,0BAA0B;YAC1B,MAAMR,MACHS,MAAM,CAACH,eACPI,IAAI,CAAC;gBAAEC,SAAS;YAAG,GAAG,iCAAiC;aACvDC,MAAM,CAACb;QACZ,OAAO;YACL,sCAAsC;YACtC,MAAMC,MACHU,IAAI,CAAC;gBAAEC,SAAS;YAAG,GACnBC,MAAM,CAACb;QACZ;QAEA,yCAAyC;QACzC,MAAMc,WAAW,CAAC,aAAa,EAAEhG,OAAO,SAAS,EAAE6E,YAAY;QAE/D,oDAAoD;QACpD,MAAM1E,YAAE,CACLqD,WAAW,CAAC,SACZC,GAAG,CAAC;YACHrB,OAAO4D;YACP1C,YAAY,IAAIC;QAClB,GACClD,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRC,SAAS;YACTe,MAAM;gBACJU,OAAO4D;gBACPC,UAAUpB;YACZ;QACF;IACF;IAGA,MAAMqB,2BAA2BC,KAAU,EAAEnG,MAAc,EAAE;QAC3D,MAAM,EAAEoG,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,MAAMG,KAAK;QAEX,MAAMC,aAAa/G,MAAM8G,EAAE,CAACF,cAAclD,WAAWoD,IAAIE,OAAO,CAAC;QACjE,MAAMC,WAAWjH,MAAM8G,EAAE,CAACD,YAAYnD,WAAWoD,IAAII,KAAK,CAAC;QAE3D,MAAMC,aAAaJ,WAAW5G,GAAG,GAAGiH,MAAM;QAC1C,MAAMC,WAAWJ,SAAS9G,GAAG,GAAGiH,MAAM;QAEpC;;;;;;;;QAQA,GAEA,MAAM/D,QAAa,MAAM1C,YAAE,CAC1BC,UAAU,CAAC,0BACXG,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;SACH,EACAF,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,CAACyG,KAAOA,GAAGC,EAAE,CAAC;gBACjBD,GAAG,cAAc,MAAMH;gBACvBG,GAAG,cAAc,MAAMD;aAC1B,GACAxG,KAAK,CAAC,YAAY,MAAM,MACxByC,OAAO,CAAC,cAAc,QACtBtC,gBAAgB;QAEjB,iBAAiB;QACjB,MAAMwG,QAAa,MAAM7G,YAAE,CAC1BC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,SACzB,eAAe;SACdtG,MAAM,CAAC;YACJJ,YAAE,CAAC8G,EAAE,CAACC,GAAG,CAAC,YAAYC,EAAE,CAAC;SAC5B,EACApF,OAAO,CAAC,WACRvB,gBAAgB;QAEjB,8BAA8B;QAC9B,MAAM4G,8BAA8B,MAAMjH,YAAE,CAC3CC,UAAU,CAAC,oBACXC,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,cAAc,MAAMsG,YAC1BtG,KAAK,CAAC,cAAc,MAAMwG,UAC1BtG,MAAM,CAAC;YACJ;YACA;YACA;YACA;SACH,EACAiB,OAAO;QAER;;;;;;;;;;;;;;;;QAgBA,GAEA,uBAAuB;QACvB,MAAM6F,qBAA0B,MAAMlH,YAAE,CACvCC,UAAU,CAAC,wBACXG,MAAM,CAAC;YACJJ,YAAE,CAAC8G,EAAE,CAACC,GAAG,CAAC,kBAAkBC,EAAE,CAAC;SAClC,EACA9G,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBrG,gBAAgB;QAEjB,MAAM8G,0BAA+B,MAAMnH,YAAE,CAC5CC,UAAU,CAAC,6BACXG,MAAM,CAAC;YACJJ,YAAE,CAAC8G,EAAE,CAACC,GAAG,CAAC,YAAYC,EAAE,CAAC;SAC5B,EACA9G,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBrG,gBAAgB;QAEjB,MAAM+G,yBAAyBjG,OAAO+F,oBAAoBG;QAC1D,MAAMC,yBAAyBnG,OAAOgG,yBAAyBI;QAC/D,MAAMC,wBAAwBJ,yBAAyBE;QAEvD,2DAA2D;QAC3D,MAAMG,kCAAkCR,4BAA4BS,MAAM,CAAC,CAACC,KAAKC;YAC7E,0EAA0E;YAC1E,MAAML,WAAW3C,KAAKiD,GAAG,CAAC,GAAG1G,OAAOyG,KAAKL,QAAQ,KAAK;YACtD,MAAMO,UAAUlD,KAAKiD,GAAG,CAAC,GAAG1G,OAAOyG,KAAKE,OAAO,KAAK;YACpD,MAAMC,QAAQnD,KAAKiD,GAAG,CAAC,GAAG1G,OAAOyG,KAAKG,KAAK,KAAK;YAChD,MAAMC,MAAMpD,KAAKiD,GAAG,CAAC,GAAG1G,OAAOyG,KAAKI,GAAG,KAAK;YAE5CL,IAAIJ,QAAQ,IAAIA;YAChBI,IAAIG,OAAO,IAAIA;YACfH,IAAII,KAAK,IAAIA;YACbJ,IAAIK,GAAG,IAAIA;YACX,OAAOL;QACX,GAAG;YAAEJ,UAAU;YAAGO,SAAS;YAAGC,OAAO;YAAGC,KAAK;QAAE;QAE/C;;;;;;;;;;;;;;;QAeA,GACD,MAAMC,QAAQR;QAEb,+FAA+F;QAC/F,IAAIR,4BAA4BjE,MAAM,KAAK,KACtCiF,MAAMV,QAAQ,KAAK,KAAKU,MAAMH,OAAO,KAAK,KAAKG,MAAMF,KAAK,KAAK,KAAKE,MAAMD,GAAG,KAAK,GAAI;YACvFC,MAAMV,QAAQ,GAAG;YACjBU,MAAMH,OAAO,GAAG;YAChBG,MAAMF,KAAK,GAAG;YACdE,MAAMD,GAAG,GAAG;QAChB;QAEA,MAAME,qBAA0B,AAAC/G,CAAAA,OAAOuB,OAAOyF,kBAAkB,CAAA,IAAMhH,CAAAA,OAAO8G,MAAMV,QAAQ,IAAIpG,OAAOqG,sBAAqB;QAE5H,MAAMY,yBAAyB;YAC3Bb,UAAU;gBACNc,UAAUlG,WAAWhB,OAAO8G,MAAMV,QAAQ,EAAEnF,OAAO,CAAC,OAAO;gBAC3DkG,QAAQnG,WAAWhB,OAAOqG,uBAAuBpF,OAAO,CAAC,OAAO;gBAChEmG,WAAWL,qBAAqB,IAAI/F,WAAWhB,OAAO+G,oBAAoB9F,OAAO,CAAC,MAAM;gBACxFG,MAAMJ,WAAWhB,OAAOuB,OAAOyF,eAAe/F,OAAO,CAAC,OAAO;YACjE;YACA0F,SAAS;gBACLA,SAAS3F,WAAWhB,OAAO8G,MAAMH,OAAO,EAAE1F,OAAO,CAAC,OAAO;gBACzDG,MAAMJ,WAAWhB,OAAOuB,OAAO8F,cAAcpG,OAAO,CAAC,OAAO;YAChE;YACA4F,KAAK;gBACDA,KAAK7F,WAAWhB,OAAO8G,MAAMD,GAAG,EAAE5F,OAAO,CAAC,OAAO;gBACjDG,MAAMJ,WAAWhB,OAAOuB,OAAO+F,UAAUrG,OAAO,CAAC,OAAO;YAC5D;YACA2F,OAAO;gBACHA,OAAO5F,WAAWhB,OAAO8G,MAAMF,KAAK,EAAE3F,OAAO,CAAC,OAAO;gBACrDG,MAAMJ,WAAWhB,OAAOuB,OAAOgG,YAAYtG,OAAO,CAAC,OAAO;YAC9D;YACAuG,OAAO;gBACHA,OAAO;gBACPpG,MAAM;YACV;YACAsE,OAAO;gBACHA,OAAO1E,WAAWhB,OAAO0F,OAAOwB,UAAUjG,OAAO,CAAC,OAAO;gBACzDG,MAAMJ,WAAWhB,OAAOuB,OAAOkG,YAAYxG,OAAO,CAAC,OAAO;YAC9D;QACJ;QAEA,OAAO;YACH7B,QAAQ;YACRgB,MAAM6G;QACV;IACJ;IAGA,MAAMS,SAAS7C,KAAU,EAAEnG,MAAc,EAAE;QACvC,MAAM,EAAEiJ,IAAI,EAAE,GAAG9C;QAEjB,MAAMQ,aAAanH,MAAMyJ,MAAMzC,OAAO,CAAC,OAAOI,MAAM;QACpD,MAAMC,WAAWrH,MAAMyJ,MAAMvC,KAAK,CAAC,OAAOE,MAAM;QAEhD,yBAAyB;QACzB,MAAMI,QAAQ,MAAM7G,YAAE,CACbC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,SACzB,eAAe;SACdtG,MAAM,CAAC;YACJJ,YAAE,CAAC8G,EAAE,CAACC,GAAG,CAAC,YAAYC,EAAE,CAAC;SAC5B,EACApF,OAAO,CAAC,WACRvB,gBAAgB;QAEzB,MAAM0I,gBAAgBlC,OAAOwB,YAAY;QAGzC,OAAO;YACH9H,QAAQ;YACRgB,MAAM;gBACFyH,UAAU;oBACNC,eAAe;oBACfC,OAAO;wBACHA,OAAO;wBACP3G,MAAM;oBACV;oBACA4G,QAAQ;wBACJC,SAAS;wBACT7G,MAAM;oBACV;oBACAgF,UAAU;wBACNA,UAAU;wBACVhF,MAAM;oBACV;gBACA;gBACA8G,OAAO;oBACHA,OAAO;oBACPC,aAAa;oBACbC,aAAa;gBACjB;gBACAC,qBAAqB;oBACjBjC,UAAU;wBACNc,UAAU;wBACVC,QAAQ;wBACRC,WAAW;wBACXhG,MAAM;oBACV;oBACAuF,SAAS;wBACLA,SAAS;wBACTvF,MAAM;oBACV;oBACAyF,KAAK;wBACDA,KAAK;wBACLzF,MAAM;oBACV;oBACAwF,OAAO;wBACHA,OAAO;wBACPxF,MAAM;oBACV;oBACAoG,OAAO;wBACHA,OAAO;wBACPpG,MAAM;oBACV;gBACJ;gBACAsE,OAAO;oBACHA,OAAOkC;oBACPxG,MAAM;gBACV;YACJ;QAEJ;IAEJ;IAEAkH,aAAa3H,IAAY,EAAE;QACvB,MAAM4H,QAAQ5H,KAAK6H,KAAK,CAAC;QACzB,OAAOD,KAAK,CAAC,EAAE;IACnB;IAEA,MAAME,eAAe/J,MAAc,EAAE;QACrC,MAAM6B,OAAO,MAAM1B,YAAE,CACpBC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAKL,QACjBO,MAAM,CAAC;YACN;SACD,EACAwB,OAAO,CAAC,MACRvB,gBAAgB;QAEjB,MAAMwJ,kBAAkB;YACpB/H,MAAMJ,MAAMI,OAAO,IAAI,CAAC2H,YAAY,CAAC/H,KAAKI,IAAI,IAAI;YAClDgI,aAAa;gBACTC,gBAAgB;gBAChBC,YAAY;oBACRC,QAAQ;oBACRC,SAAS;oBACTC,QAAQ;oBACRC,eAAe;gBACnB;gBACAC,sBAAsB;oBAClBJ,QAAQ;wBACJ;4BACIK,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;wBACA;4BACIqC,KAAK;4BACLC,WAAW;4BACXtC,OAAO;wBACX;qBACH;oBACDuC,cAAc;oBACdC,gBAAgB;oBAChBC,cAAc;gBAClB;YACR;QACA;QAEA,OAAO;YACHnK,QAAQ;YACRgB,MAAMsI;QACV;IAEJ;IAEA,QAAQ;IACR,MAAMc,eAAezJ,OAAe,EAAE0J,aAA4B,EAAE;QAChE,MAAM,EAAEvC,QAAQ,EAAEwC,QAAQ,EAAE,GAAGD;QAE/B,MAAME,UAAUzL,MAAMwL,UAAUxE,OAAO,CAAC,OAAOI,MAAM;QAErD,MAAMsE,QAAQ,MAAM/K,YAAE,CACrBgB,UAAU,CAAC,eACXC,MAAM,CAAC;YACJC;YACAmH;YACAwC,UAAUC;YACVE,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QACpB,GACC/B,OAAO;QAER,OAAO;YACH,UAAU;YACV,QAAQ,EAAE;QACd;IACJ;IAEA,MAAM4J,6BAA6B;QAC/B,MAAM9D,0BAA0B,MAAMnH,YAAE,CACvCC,UAAU,CAAC,uBACXG,MAAM,CAAC;YAAC;YAAM;SAAO,EACrBuC,OAAO,CAAC,cAAc,OACtBtB,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAM4F;QACV;IACJ;IAEA,4BAA4B;IAC5B,MAAM+D,iCAAiCrL,MAAc,EAAEmG,KAAU,EAAE;QAC/D,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QACjC,gEAAgE;QAChE,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAE/F,MAAMU,0BAA0B,MAAMnH,YAAE,CACvCC,UAAU,CAAC,mCACX0B,QAAQ,CAAC,4BAA4B,QAAQ,kBAC7CzB,KAAK,CAAC,cAAc,KAAKL,QACzBK,KAAK,CAAC,eAAe,MAAMsG,YAC3BtG,KAAK,CAAC,eAAe,MAAMwG,UAC3BtG,MAAM,CAAC;YACJ;YACA;YACA;YACA;SACH,EACAiB,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAM4F;QACV;IACJ;IAEA,sBAAsB;IACtBgE,gBAAgB/B,OAAe,EAAU;QACrC,MAAMgC,QAAQxG,KAAKyG,KAAK,CAACjC,UAAU;QACnC,MAAMkC,mBAAmBlC,UAAU;QACnC,MAAMmC,UAAU;QAChB,OAAO,GAAGH,MAAM,CAAC,EAAEE,iBAAiB,CAAC,EAAEC,SAAS;IACpD;IAEA,MAAMC,4BAA4B3L,MAAc,EAAE4L,0BAAsD,EAAE;QACtG,MAAM,EAAEjK,EAAE,EAAEkK,QAAQ,EAAE,GAAGD;QAEzB,MAAME,qBAAqB,MAAM3L,YAAE,CAClCC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKsB,IACjBpB,MAAM,CAAC;YAAC;SAAW,EACnBC,gBAAgB;QAEjB,IAAI,CAACsL,oBAAoB;YACrB,OAAO;gBACHpL,QAAQ;gBACRC,SAAS;YACb;QACJ;QAEA,sBAAsB;QACtB,MAAMoL,sBAAsBF,WAAYC,CAAAA,mBAAmBpE,QAAQ,GAAC,EAAC;QAErE,MAAMJ,0BAA0B,MAAMnH,YAAE,CACvCgB,UAAU,CAAC,6BACXC,MAAM,CAAC;YACJC,SAASrB;YACTgM,aAAarK;YACbsK,eAAe,IAAI,CAACX,eAAe,CAACO;YACpCnE,UAAUqE;QACd,GACCvK,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAM,EAAE;QACZ;IACJ;IAEA,MAAMwK,yBAAyBlM,MAAc,EAAE;QAC3C,MAAMmM,QAAQ,IAAI5I;QAElB6I,QAAQC,GAAG,CAAC,CAAC,sEAAsE,EAAErM,QAAQ;QAC7FoM,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEF,MAAMG,WAAW,IAAI;QAEnD,MAAMC,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,cAAc,MAAM8L,OAC1B9L,KAAK,CAAC,YAAY,MAAM,MACxByC,OAAO,CAAC,cAAc,QAAQ,8CAA8C;SAC5EtC,gBAAgB;QAErB,IAAI+L,UAAU;YACVH,QAAQC,GAAG,CAAC,CAAC,6DAA6D,EAAEE,SAAS5K,EAAE,CAAC,QAAQ,EAAE4K,SAAStK,IAAI,EAAE;YACjHmK,QAAQC,GAAG,CAAC,CAAC,wBAAwB,EAAEE,SAASE,UAAU,CAAC,WAAW,EAAEF,SAASG,QAAQ,EAAE;YAC3F,OAAO;QACX,OAAO;YACHN,QAAQC,GAAG,CAAC,CAAC,6DAA6D,CAAC;YAC3E,OAAO;QACX;IACJ;IAEA,MAAMM,sBAAsB3M,MAAc,EAAE;QACxC,MAAMmM,QAAQ,IAAI5I;QAElB6I,QAAQC,GAAG,CAAC,CAAC,mEAAmE,EAAErM,QAAQ;QAC1FoM,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEF,MAAMG,WAAW,IAAI;QAEnD,uFAAuF;QACvF,MAAMC,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;YAAC;YAAQ;YAAU;YAAgB;YAAc;YAA0B;SAAY,EAC9FF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1ByC,OAAO,CAAC,gBAAgB,QACxBtC,gBAAgB;QAErB,IAAI+L,UAAU;YACVH,QAAQC,GAAG,CAAC,CAAC,0DAA0D,EAAEE,SAAS5K,EAAE,CAAC,QAAQ,EAAE4K,SAAStK,IAAI,EAAE;YAC9GmK,QAAQC,GAAG,CAAC,CAAC,wBAAwB,EAAEE,SAASE,UAAU,CAAC,WAAW,EAAEF,SAASG,QAAQ,CAAC,OAAO,EAAEH,SAASK,IAAI,IAAI,WAAW,UAAU,EAAEL,SAASM,OAAO,EAAE;YAE7J,2EAA2E;YAC3E,IAAI,CAACN,SAASK,IAAI,EAAE;gBAChBR,QAAQU,IAAI,CAAC,CAAC,oCAAoC,EAAEP,SAAS5K,EAAE,CAAC,mDAAmD,CAAC;YACxH;YACA,OAAO;QACX,OAAO;YACHyK,QAAQC,GAAG,CAAC,CAAC,0DAA0D,CAAC;YAExE,2CAA2C;YAC3C,MAAMU,kBAAkB,MAAM5M,YAAE,CAC3BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;gBAAC;gBAAQ;gBAAU;gBAAa;aAAyB,EAChEF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1BA,KAAK,CAAC,QAAQ,MAAM,MAAM,kCAAkC;aAC5DmB,OAAO;YAEZ,IAAIuL,gBAAgB5J,MAAM,GAAG,GAAG;gBAC5BiJ,QAAQU,IAAI,CAAC,CAAC,sCAAsC,EAAEC,gBAAgB5J,MAAM,CAAC,sCAAsC,CAAC,EAAE4J;gBAEtH,2DAA2D;gBAC3D,KAAK,MAAMC,UAAUD,gBAAiB;oBAClC,MAAM5M,YAAE,CACHqD,WAAW,CAAC,0BACZC,GAAG,CAAC;wBAAEiJ,UAAUP;wBAAO7I,YAAY6I;oBAAM,GACzC9L,KAAK,CAAC,MAAM,KAAK2M,OAAOrL,EAAE,EAC1BH,OAAO;oBAEZ4K,QAAQC,GAAG,CAAC,CAAC,0CAA0C,EAAEW,OAAOrL,EAAE,CAAC,2BAA2B,CAAC;gBACnG;YACJ;YAEA,OAAO;QACX;IACJ;IAEA,uDAAuD;IACvD,MAAMsL,mBAAmBjN,MAAc,EAAE;QACrCoM,QAAQC,GAAG,CAAC,CAAC,uDAAuD,EAAErM,QAAQ;QAE9E,MAAMmM,QAAQ,IAAI5I;QAClB,MAAM2J,UAAU;YACZC,cAAc;YACdC,cAAc;YACdC,qBAAqB;YACrBC,SAAS,EAAE;QACf;QAEA,IAAI;YACA,mCAAmC;YACnC,MAAMP,kBAAkB,MAAM5M,YAAE,CAC3BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;gBAAC;gBAAQ;gBAAU;gBAAa;gBAAgB;aAAa,EACpEF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,cAAc,MAAM,MAC1BA,KAAK,CAAC,QAAQ,MAAM,MAAM,mBAAmB;aAC7CmB,OAAO;YAEZ0L,QAAQC,YAAY,GAAGJ,gBAAgB5J,MAAM;YAC7CiJ,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEU,gBAAgB5J,MAAM,CAAC,kBAAkB,CAAC;YAExE,IAAI4J,gBAAgB5J,MAAM,GAAG,GAAG;gBAC5B,iCAAiC;gBACjC,KAAK,MAAM6J,UAAUD,gBAAiB;oBAClC,MAAM5M,YAAE,CACHqD,WAAW,CAAC,0BACZC,GAAG,CAAC;wBACDiJ,UAAUP;wBACV7I,YAAY6I;oBAChB,GACC9L,KAAK,CAAC,MAAM,KAAK2M,OAAOrL,EAAE,EAC1BH,OAAO;oBAEZ0L,QAAQE,YAAY;oBACnBF,QAAQI,OAAO,CAAWC,IAAI,CAAC;wBAC5B5L,IAAIqL,OAAOrL,EAAE;wBACbM,MAAM+K,OAAO/K,IAAI;wBACjB4K,SAASG,OAAOH,OAAO;wBACvBW,QAAQ;oBACZ;oBAEApB,QAAQC,GAAG,CAAC,CAAC,iCAAiC,EAAEW,OAAOrL,EAAE,CAAC,QAAQ,EAAEqL,OAAO/K,IAAI,EAAE;gBACrF;YACJ;YAEA,0CAA0C;YAC1C,MAAMwL,kBAAkB,MAAMtN,YAAE,CAC3BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1BA,KAAK,CAAC,QAAQ,UAAU,MACxBG,gBAAgB;YAErB,MAAMmN,eAAe,MAAMxN,YAAE,CACxBC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1BG,gBAAgB;YAErB0M,QAAQG,mBAAmB,GAAG/L,OAAOmM,iBAAiBC,SAAS,OAAOpM,OAAOqM,cAAcD,SAAS;YAEpGtB,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEoB,iBAAiBC,SAAS,EAAE,WAAW,EAAEC,cAAcD,SAAS,GAAG;YAC7GtB,QAAQC,GAAG,CAAC,CAAC,2BAA2B,EAAEa,QAAQG,mBAAmB,EAAE;YAEvE,OAAOH;QAEX,EAAE,OAAOU,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAEA;YAC1D,MAAMA;QACV;IACJ;IAEA,4DAA4D;IAC5D,MAAMC,2BAA2B7N,MAAc,EAI5C;QACCoM,QAAQC,GAAG,CAAC,CAAC,0EAA0E,EAAErM,QAAQ;QAEjG,IAAI;YACA,sDAAsD;YACtD,MAAM8N,kBAAkB,MAAM3N,YAAE,CAC3BC,UAAU,CAAC,0BACXG,MAAM,CAAC;gBAAC;gBAAM;gBAAQ;gBAAc;aAAa,EACjDF,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,YAAY,MAAM,MACxByC,OAAO,CAAC,cAAc,QAAQ,wBAAwB;aACtDtB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEyB,gBAAgB3K,MAAM,CAAC,kBAAkB,CAAC;YAExE,IAAI2K,gBAAgB3K,MAAM,KAAK,GAAG;gBAC9BiJ,QAAQC,GAAG,CAAC,CAAC,mCAAmC,CAAC;gBACjD,OAAO;oBACH0B,gBAAgB;oBAChBC,kBAAkB;oBAClBrN,SAAS;gBACb;YACJ;YAEA,IAAImN,gBAAgB3K,MAAM,KAAK,GAAG;gBAC9BiJ,QAAQC,GAAG,CAAC,CAAC,wCAAwC,EAAEyB,eAAe,CAAC,EAAE,CAAC7L,IAAI,CAAC,MAAM,EAAE6L,eAAe,CAAC,EAAE,CAACnM,EAAE,CAAC,CAAC,CAAC;gBAC/G,OAAO;oBACHoM,gBAAgBD,eAAe,CAAC,EAAE;oBAClCE,kBAAkB;oBAClBrN,SAAS;gBACb;YACJ;YAEA,6DAA6D;YAC7D,MAAMsN,qBAAqBH,eAAe,CAAC,EAAE,EAAE,iDAAiD;YAChG,MAAMI,wBAAwBJ,gBAAgBK,KAAK,CAAC,IAAI,kBAAkB;YAE1E/B,QAAQC,GAAG,CAAC,CAAC,4CAA4C,EAAEyB,gBAAgB3K,MAAM,CAAC,CAAC,CAAC;YACpFiJ,QAAQC,GAAG,CAAC,CAAC,kBAAkB,EAAE4B,mBAAmBhM,IAAI,CAAC,MAAM,EAAEgM,mBAAmBtM,EAAE,CAAC,WAAW,EAAEsM,mBAAmBxB,UAAU,CAAC,CAAC,CAAC;YACpIL,QAAQC,GAAG,CAAC,CAAC,cAAc,EAAE6B,sBAAsB/K,MAAM,CAAC,wBAAwB,CAAC;YAEnF,oCAAoC;YACpC,MAAM2B,MAAM,IAAIvB;YAChB,IAAIyK,mBAAmB;YAEvB,KAAK,MAAMzB,YAAY2B,sBAAuB;gBAC1C,IAAI;oBACA,MAAM/N,YAAE,CACHqD,WAAW,CAAC,0BACZC,GAAG,CAAC;wBACDiJ,UAAU5H;wBACVxB,YAAYwB;oBAChB,GACCzE,KAAK,CAAC,MAAM,KAAKkM,SAAS5K,EAAE,EAC5BtB,KAAK,CAAC,aAAa,KAAKL,QACxBwB,OAAO;oBAEZ4K,QAAQC,GAAG,CAAC,CAAC,yBAAyB,EAAEE,SAAStK,IAAI,CAAC,MAAM,EAAEsK,SAAS5K,EAAE,CAAC,CAAC,CAAC;oBAC5EqM;gBACJ,EAAE,OAAOI,aAAa;oBAClBhC,QAAQwB,KAAK,CAAC,CAAC,8BAA8B,EAAErB,SAAS5K,EAAE,CAAC,CAAC,CAAC,EAAEyM;gBACnE;YACJ;YAEAhC,QAAQC,GAAG,CAAC,CAAC,8BAA8B,EAAE2B,iBAAiB,wCAAwC,CAAC;YAEvG,OAAO;gBACHD,gBAAgBE;gBAChBD;gBACArN,SAAS,CAAC,sCAAsC,EAAEqN,iBAAiB,sCAAsC,CAAC;YAC9G;QAEJ,EAAE,OAAOJ,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,2CAA2C,CAAC,EAAEA;YAC7D,MAAMA;QACV;IACJ;IAEA,+DAA+D;IAC/D,MAAMS,wBAAwBrO,MAAc,EAAEsO,UAO1C,CAAC,CAAC,EAAE;QACJlC,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAErM,QAAQ,EAAEsO;QAElF,IAAI;YACA,MAAM,EACFC,OAAO,CAAC,EACRC,QAAQ,EAAE,EACV9N,SAAS,KAAK,EACjB,GAAG4N;YAEJ,MAAMG,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5BpC,QAAQC,GAAG,CAAC,CAAC,gCAAgC,EAAEkC,KAAK,QAAQ,EAAEC,MAAM,SAAS,EAAEC,OAAO,SAAS,EAAE/N,QAAQ;YAEzG,8BAA8B;YAC9B0L,QAAQC,GAAG,CAAC,CAAC,yDAAyD,CAAC;YAEvE,MAAMqC,YAAY,MAAMvO,YAAE,CACrBC,UAAU,CAAC,0BACXG,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,aAAa,KAAKL,QACxB8C,OAAO,CAAC,cAAc,QACtB0L,KAAK,CAACA,OACNC,MAAM,CAACA,QACPjN,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,sCAAsC,EAAEqC,UAAUvL,MAAM,CAAC,WAAW,CAAC;YAElF,wCAAwC;YACxC,MAAMwL,qBAAqBD,UAAUE,GAAG,CAACrC,CAAAA,WAAa,CAAA;oBAClD5K,IAAI4K,SAAS5K,EAAE;oBACfM,MAAMsK,SAAStK,IAAI,IAAI;oBACvB4M,WAAWtC,SAASsC,SAAS,IAAI;oBACjCpC,YAAYF,SAASE,UAAU;oBAC/BC,UAAUH,SAASG,QAAQ;oBAC3BvB,YAAYoB,SAASpB,UAAU;oBAC/B7C,eAAeiE,SAASjE,aAAa,IAAI;oBACzCK,cAAc4D,SAAS5D,YAAY,IAAI;oBACvCE,YAAY0D,SAAS1D,UAAU,IAAI;oBACnCD,UAAU2D,SAAS3D,QAAQ,IAAI;oBAC/BgE,MAAM;oBACNlM,QAAQ6L,SAASG,QAAQ,GAAG,aAAa;oBACzCoC,eAAevC,SAASG,QAAQ,IAAIH,SAASE,UAAU,GACjD1H,KAAKyG,KAAK,CAAC,AAAC,CAAA,IAAIjI,KAAKgJ,SAASG,QAAQ,EAAEqC,OAAO,KAAK,IAAIxL,KAAKgJ,SAASE,UAAU,EAAEsC,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC,KAClH;gBACV,CAAA;YAEA,mCAAmC;YACnC,MAAMC,oBAAoBtO,WAAW,QAC/BiO,qBACAA,mBAAmBM,MAAM,CAACC,CAAAA,IAAKA,EAAExO,MAAM,KAAKA;YAElD0L,QAAQC,GAAG,CAAC,CAAC,qCAAqC,EAAE2C,kBAAkB7L,MAAM,EAAE;YAE9E,uBAAuB;YACvB,MAAMgM,cAAc,MAAMhP,YAAE,CACvBC,UAAU,CAAC,0BACXG,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;YAErB,MAAM4H,QAAQ9G,OAAO6N,aAAa/G,SAAS;YAE3CgE,QAAQC,GAAG,CAAC,CAAC,iCAAiC,EAAEjE,OAAO;YAEvD,uBAAuB;YACvB,MAAMgH,cAAcT,mBAAmBM,MAAM,CAACC,CAAAA,IAAKA,EAAExO,MAAM,KAAK,UAAUyC,MAAM;YAChF,MAAMkM,gBAAgBV,mBAAmBM,MAAM,CAACC,CAAAA,IAAKA,EAAExO,MAAM,KAAK,YAAYyC,MAAM;YAEpF,MAAMmM,SAAS;gBACXZ,WAAWM;gBACXO,YAAY;oBACRhB;oBACAC;oBACApG;oBACAoH,YAAYzK,KAAK0K,IAAI,CAACrH,QAAQoG;oBAC9BkB,SAASnB,OAAOxJ,KAAK0K,IAAI,CAACrH,QAAQoG;oBAClCmB,SAASpB,OAAO;gBACpB;gBACAqB,OAAO;oBACHxH,OAAOA;oBACPkB,QAAQ8F;oBACRS,UAAUR;oBACVS,aAAa;oBACbC,UAAU;wBAAC;4BAAEnD,MAAM;4BAASc,OAAOtF;wBAAM;qBAAE;gBAC/C;gBACA4H,SAAS;oBACLtP;oBACAuP,WAAW3B,QAAQ2B,SAAS;oBAC5BC,SAAS5B,QAAQ4B,OAAO;oBACxBtD,MAAM0B,QAAQ1B,IAAI;gBACtB;YACJ;YAEAR,QAAQC,GAAG,CAAC,CAAC,mCAAmC,CAAC,EAAE;gBAC/C8D,gBAAgBb,OAAOZ,SAAS,CAACvL,MAAM;gBACvCiF,OAAOkH,OAAOC,UAAU,CAACnH,KAAK;gBAC9BmG,MAAMe,OAAOC,UAAU,CAAChB,IAAI;gBAC5Ba;gBACAC;YACJ;YAEA,OAAOC;QAEX,EAAE,OAAO1B,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,sDAAsD,EAAE5N,OAAO,CAAC,CAAC,EAAE4N;YAClFxB,QAAQwB,KAAK,CAAC,CAAC,cAAc,CAAC,EAAEA,MAAMwC,KAAK;YAC3ChE,QAAQwB,KAAK,CAAC,CAAC,uBAAuB,CAAC,EAAE;gBAAE5N;gBAAQsO;YAAQ;YAE3D,8CAA8C;YAC9C,MAAM,IAAI7N,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAkD;gBAC5DiN,OAAOA,MAAMjN,OAAO;YACxB,GAAG;QACP;IACJ;IAEA,sCAAsC;IACtC,MAAM0P,sBAAsBrQ,MAAc,EAAE;QACxC,IAAI;YACA,MAAMmM,QAAQ,IAAI5I;YAElB,6BAA6B;YAC7B,MAAM+M,iBAAiB,MAAMnQ,YAAE,CAC1BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,QAAQ,UAAU,MACxBG,gBAAgB;YAErB,MAAMsN,kBAAkB,MAAM3N,YAAE,CAC3BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,cAAc,MAAM,MAC1BA,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,QAAQ,UAAU,MACxBG,gBAAgB;YAErB,MAAM+P,oBAAoB,MAAMpQ,YAAE,CAC7BC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,UAC5B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,cAAc,UAAU,MAC9BA,KAAK,CAAC,QAAQ,UAAU,MACxBG,gBAAgB;YAErB,oBAAoB;YACpB,MAAMuP,WAAW,MAAM5P,YAAE,CACpBC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;gBACJ;gBACAJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC;aACxB,EACA9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,QAAQ,UAAU,MACxB0B,OAAO,CAAC,kBACRe,OAAO,CAAC,SAAS,QACjB0L,KAAK,CAAC,GACNhN,OAAO;YAEZ,OAAO;gBACH4G,OAAO9G,OAAOgP,gBAAgB5C,SAAS;gBACvCpE,QAAQhI,OAAOwM,iBAAiBJ,SAAS;gBACzCmC,UAAUvO,OAAOiP,mBAAmB7C,SAAS;gBAC7CoC,aAAa;gBACbC,UAAUA,SAASnB,GAAG,CAAC4B,CAAAA,IAAM,CAAA;wBACzB5D,MAAM4D,EAAE5D,IAAI;wBACZc,OAAOpM,OAAOkP,EAAE9C,KAAK;oBACzB,CAAA;YACJ;QAEJ,EAAE,OAAOE,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEA;YAClD,OAAO;gBACHxF,OAAO;gBACPkB,QAAQ;gBACRuG,UAAU;gBACVC,aAAa;gBACbC,UAAU,EAAE;YAChB;QACJ;IACJ;IAEA,+BAA+B;IAC/B,MAAMU,mBAAmBzQ,MAAc,EAAE0Q,UAAkB,EAAE;QACzDtE,QAAQC,GAAG,CAAC,CAAC,6CAA6C,EAAEqE,WAAW,cAAc,EAAE1Q,QAAQ;QAE/F,IAAI;YACA,MAAMmM,QAAQ,IAAI5I;YAElB,wDAAwD;YACxD,MAAMgJ,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,0BACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,YAAY,MAAM,MACxBG,gBAAgB;YAErB,IAAI,CAAC+L,UAAU;gBACX,MAAM,IAAIoE,MAAM;YACpB;YAEA,sBAAsB;YACtB,MAAMC,kBAAkB,MAAMzQ,YAAE,CAC3BqD,WAAW,CAAC,0BACZC,GAAG,CAAC;gBACDiJ,UAAUP;gBACV7I,YAAY6I;YAChB,GACC9L,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;YAErB4L,QAAQC,GAAG,CAAC,CAAC,YAAY,EAAEqE,WAAW,uBAAuB,CAAC;YAE9D,OAAO;gBACH/O,IAAI+O;gBACJzO,MAAMsK,SAAStK,IAAI;gBACnByK,UAAUP;gBACV2C,eAAe/J,KAAKyG,KAAK,CAAC,AAACW,CAAAA,MAAM4C,OAAO,KAAK,IAAIxL,KAAKgJ,SAASE,UAAU,EAAEsC,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;YAC/G;QAEJ,EAAE,OAAOnB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,8BAA8B,CAAC,EAAEA;YAChD,MAAMA;QACV;IACJ;IAEA,oDAAoD;IACpD,MAAMiD,sBAAsB7Q,MAAc,EAAE0Q,UAAkB,EAAEI,YAAqB,EAAE;QACnF1E,QAAQC,GAAG,CAAC,CAAC,+CAA+C,EAAEqE,WAAW,cAAc,EAAE1Q,QAAQ;QAEjG,IAAI;YACA,8CAA8C;YAC9C,IAAI,MAAM,IAAI,CAAC2M,qBAAqB,CAAC3M,SAAS;gBAC1C,MAAM,IAAI2Q,MAAM;YACpB;YAEA,4BAA4B;YAC5B,MAAMI,mBAAmB,MAAM5Q,YAAE,CAC5BC,UAAU,CAAC,0BACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;YAErB,IAAI,CAACuQ,kBAAkB;gBACnB,MAAM,IAAIJ,MAAM;YACpB;YAEA,MAAMV,YAAYa,eAAe,IAAIvN,KAAKuN,gBAAgB,IAAIvN;YAC9D,MAAM4I,QAAQ,IAAI5I;YAElB,kEAAkE;YAClE,MAAMyN,cAAc,MAAM7Q,YAAE,CACvBgB,UAAU,CAAC,0BACXC,MAAM,CAAC;gBACJK,WAAWzB;gBACXiC,MAAM,GAAG8O,iBAAiB9O,IAAI,CAAC,QAAQ,CAAC;gBACxC4K,SAASkE,iBAAiBlE,OAAO;gBACjCgC,WAAWkC,iBAAiBlC,SAAS;gBACrCoC,gBAAgBF,iBAAiBE,cAAc;gBAC/CC,eAAeH,iBAAiBG,aAAa;gBAC7C5I,eAAeyI,iBAAiBzI,aAAa;gBAC7CK,cAAcoI,iBAAiBpI,YAAY;gBAC3CE,YAAYkI,iBAAiBlI,UAAU;gBACvCD,UAAUmI,iBAAiBnI,QAAQ;gBACnCG,YAAYgI,iBAAiBhI,UAAU;gBACvC0D,YAAYwD;gBACZ9E,YAAYgB;gBACZ7I,YAAY6I;YAChB,GACC3L,gBAAgB;YAErB,MAAM2Q,gBAAgB7P,OAAO0P,YAAYzP,QAAQ;YACjD6K,QAAQC,GAAG,CAAC,CAAC,4CAA4C,EAAE8E,eAAe;YAE1E,2CAA2C;YAC3C,MAAMC,gBAAgB,MAAMjR,YAAE,CACzBC,UAAU,CAAC,gCACXoM,SAAS,GACTnM,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;YAEZ,IAAI6P,aAAa;YACjB,KAAK,MAAMC,QAAQF,cAAe;gBAC9B,MAAMG,UAAU,MAAMpR,YAAE,CACnBgB,UAAU,CAAC,gCACXC,MAAM,CAAC;oBACJoQ,aAAaL;oBACblP,MAAMqP,KAAKrP,IAAI;oBACfwP,aAAaH,KAAKG,WAAW;oBAC7BC,WAAWJ,KAAKI,SAAS;oBACzBvG,YAAYgB;oBACZ7I,YAAY6I;gBAChB,GACC3L,gBAAgB;gBAErB,MAAMmR,YAAYrQ,OAAOiQ,QAAQhQ,QAAQ;gBAEzC,iCAAiC;gBACjC,MAAMqQ,gBAAgB,MAAMzR,YAAE,CACzBC,UAAU,CAAC,sCACXoM,SAAS,GACTnM,KAAK,CAAC,WAAW,KAAKiR,KAAK3P,EAAE,EAC7BH,OAAO;gBAEZ,KAAK,MAAMqQ,QAAQD,cAAe;oBAC9B,MAAMzR,YAAE,CACHgB,UAAU,CAAC,sCACXC,MAAM,CAAC;wBACJ0Q,SAASH;wBACTI,SAASF,KAAKE,OAAO;wBACrB9P,MAAM4P,KAAK5P,IAAI;wBACf+P,UAAUH,KAAKG,QAAQ;wBACvBC,MAAMJ,KAAKI,IAAI;wBACfvK,UAAUmK,KAAKnK,QAAQ;wBACvBO,SAAS4J,KAAK5J,OAAO;wBACrBC,OAAO2J,KAAK3J,KAAK;wBACjBC,KAAK0J,KAAK1J,GAAG;wBACbW,OAAO+I,KAAK/I,KAAK;wBACjBqC,YAAYgB;wBACZ7I,YAAY6I;oBAChB,GACC3K,OAAO;gBAChB;gBACA6P;YACJ;YAEA,6CAA6C;YAC7C,MAAMa,sBAAsB,MAAM/R,YAAE,CAC/BC,UAAU,CAAC,sCACXoM,SAAS,GACTnM,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;YAEZ,IAAI2Q,mBAAmB;YACvB,KAAK,MAAMC,cAAcF,oBAAqB;gBAC1C,MAAM/R,YAAE,CACHgB,UAAU,CAAC,sCACXC,MAAM,CAAC;oBACJoQ,aAAaL;oBACblP,MAAMmQ,WAAWnQ,IAAI;oBACrBoQ,QAAQD,WAAWC,MAAM;oBACzBC,iBAAiBF,WAAWE,eAAe;oBAC3CC,OAAOH,WAAWG,KAAK;oBACvBpH,YAAYgB;oBACZ7I,YAAY6I;gBAChB,GACC3K,OAAO;gBACZ2Q;YACJ;YAEA/F,QAAQC,GAAG,CAAC,CAAC,uBAAuB,EAAEgF,WAAW,aAAa,EAAEc,iBAAiB,qBAAqB,CAAC;YAEvG,OAAO;gBACHxQ,IAAIwP;gBACJlP,MAAM,GAAG8O,iBAAiB9O,IAAI,CAAC,QAAQ,CAAC;gBACxCuQ,YAAY9B;gBACZjE,YAAYwD;gBACZoB;gBACAc;YACJ;QAEJ,EAAE,OAAOvE,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAEA;YAC/C,MAAMA;QACV;IACJ;IAEA,MAAM6E,qBAAqBzS,MAAc,EAAE0S,wBAAkD,EAAE;QAC7F,MAAM,EAAEzQ,IAAI,EAAE4K,OAAO,EAAE/C,KAAK,EAAE6I,SAAS,EAAE9D,SAAS,EAAEqC,aAAa,EAAE0B,QAAQ,EAAE,GAAGF;QAEhF,IAAI,MAAM,IAAI,CAACxG,wBAAwB,CAAClM,SAAS;YAC7C,MAAM,IAAIS,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACnD,GAAG;QACP;QAEA,MAAMkS,eAAe,MAAM1S,YAAE,CACxBgB,UAAU,CAAC,mBACXC,MAAM,CAAC;YACJK,WAAWzB;YACXiC;YACA4K;YACA/C;YACA6I;YACA9D;YACAqC;YACAzE,YAAY,IAAIlJ;YAChBmJ,UAAU;YACVvB,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QACpB,GACC/C,gBAAgB;QAErB,MAAMsS,kBAAkBxR,OAAOuR,aAAatR,QAAQ;QAEpD,IAAIwR,MAAMC,OAAO,CAACJ,WAAW;YACzB,MAAMK,UAAU;YAEhB,sCAAsC;YACtC,IAAK,IAAIC,QAAQ,GAAGA,QAAQN,SAASzP,MAAM,EAAE+P,QAAS;gBAClD,MAAMC,UAAeP,QAAQ,CAACM,MAAM;gBACpC,IAAIC,SAASC,aAAaL,MAAMC,OAAO,CAACG,QAAQC,SAAS,GAAG;oBACxD,MAAMC,eAAeJ,OAAO,CAACC,MAAM;oBACnC,MAAMI,cAAc,MAAMnT,YAAE,CACvBgB,UAAU,CAAC,4BACXC,MAAM,CAAC;wBACJoQ,aAAasB;wBACb7Q,MAAM,CAAC,OAAO,EAAEoR,cAAc;oBAClC,GACC7S,gBAAgB;oBAErB,MAAM+S,iBAAiBjS,OAAOgS,YAAY/R,QAAQ;oBAElD,yCAAyC;oBACzC,KAAK,MAAMiS,YAAYL,QAAQC,SAAS,CAAE;wBACtC,IAAII,SAASC,WAAW,EAAE;4BACtB,MAAMtT,YAAE,CACHgB,UAAU,CAAC,sCACXC,MAAM,CAAC;gCACJsS,YAAYH;gCACZE,aAAaD,SAASC,WAAW;gCACjCE,MAAMH,SAASG,IAAI;gCACnBC,MAAMJ,SAASI,IAAI;gCACnBC,KAAKL,SAASK,GAAG;gCACjBC,cAAcN,SAASM,YAAY;gCACnCvB,OAAOiB,SAASjB,KAAK,IAAI;4BAC7B,GACC/Q,OAAO;wBAChB;oBACJ;gBACJ;YACJ;QACJ;QAEA,OAAO;YACHd,QAAQ;YACRgB,MAAM,EAAE;QACZ;IACJ;IAEA,+EAA+E;IAC/E,8BAA8B;IAC9B,+EAA+E;IAE/E;;GAEC,GACD,MAAMqS,sBAAsB/T,MAAc,EAAE0Q,UAAkB,EAAE;QAC5DtE,QAAQC,GAAG,CAAC,CAAC,gDAAgD,EAAEqE,WAAW,cAAc,EAAE1Q,QAAQ;QAElG,IAAI;YACA,MAAMmM,QAAQ,IAAI5I;YAElB,wDAAwD;YACxD,MAAMgJ,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,YAAY,MAAM,MACxBG,gBAAgB;YAErB,IAAI,CAAC+L,UAAU;gBACX,MAAM,IAAIoE,MAAM;YACpB;YAEA,wBAAwB;YACxB,MAAMxQ,YAAE,CACHqD,WAAW,CAAC,mBACZC,GAAG,CAAC;gBACDiJ,UAAUP;gBACV7I,YAAY6I;YAChB,GACC9L,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBwB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEqE,WAAW,uBAAuB,CAAC;YAExE,OAAO;gBACH/O,IAAI+O;gBACJzO,MAAMsK,SAAStK,IAAI;gBACnByK,UAAUP;gBACV2C,eAAe/J,KAAKyG,KAAK,CAAC,AAACW,CAAAA,MAAM4C,OAAO,KAAK,IAAIxL,KAAKgJ,SAASE,UAAU,EAAEsC,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;YAC/G;QAEJ,EAAE,OAAOnB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,wCAAwC,CAAC,EAAEA;YAC1D,MAAMA;QACV;IACJ;IAEA;;GAEC,GACD,MAAMoG,yBAAyBhU,MAAc,EAAE0Q,UAAkB,EAAEI,YAAqB,EAAE;QACtF1E,QAAQC,GAAG,CAAC,CAAC,kDAAkD,EAAEqE,WAAW,cAAc,EAAE1Q,QAAQ;QAEpG,IAAI;YACA,8CAA8C;YAC9C,IAAI,MAAM,IAAI,CAACkM,wBAAwB,CAAClM,SAAS;gBAC7C,MAAM,IAAI2Q,MAAM;YACpB;YAEA,4BAA4B;YAC5B,MAAMI,mBAAmB,MAAM5Q,YAAE,CAC5BC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;YAErB,IAAI,CAACuQ,kBAAkB;gBACnB,MAAM,IAAIJ,MAAM;YACpB;YAEA,MAAMV,YAAYa,eAAe,IAAIvN,KAAKuN,gBAAgB,IAAIvN;YAC9D,MAAM4I,QAAQ,IAAI5I;YAElB,uBAAuB;YACvB,MAAMyN,cAAc,MAAM7Q,YAAE,CACvBgB,UAAU,CAAC,mBACXC,MAAM,CAAC;gBACJK,WAAWzB;gBACXiC,MAAM,GAAG8O,iBAAiB9O,IAAI,CAAC,cAAc,CAAC;gBAC9C4M,WAAWkC,iBAAiBlC,SAAS;gBACrChC,SAASkE,iBAAiBlE,OAAO;gBACjC/C,OAAOiH,iBAAiBjH,KAAK;gBAC7B6I,WAAW5B,iBAAiB4B,SAAS;gBACrClG,YAAYwD;gBACZvD,UAAU;gBACVvB,YAAYgB;gBACZ7I,YAAY6I;YAChB,GACC3L,gBAAgB;YAErB,MAAM2Q,gBAAgB7P,OAAO0P,YAAYzP,QAAQ;YAEjD,iCAAiC;YACjC,MAAM0S,mBAAmB,MAAM9T,YAAE,CAC5BC,UAAU,CAAC,4BACXoM,SAAS,GACTnM,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;YAEZ,KAAK,MAAM2R,WAAWc,iBAAkB;gBACpC,MAAMC,aAAa,MAAM/T,YAAE,CACtBgB,UAAU,CAAC,4BACXC,MAAM,CAAC;oBACJoQ,aAAaL;oBACblP,MAAMkR,QAAQlR,IAAI;oBAClBkJ,YAAYgB;oBACZ7I,YAAY6I;gBAChB,GACC3L,gBAAgB;gBAErB,MAAM2T,eAAe7S,OAAO4S,WAAW3S,QAAQ;gBAE/C,iCAAiC;gBACjC,MAAM6S,oBAAoB,MAAMjU,YAAE,CAC7BC,UAAU,CAAC,sCACXoM,SAAS,GACTnM,KAAK,CAAC,cAAc,KAAK8S,QAAQxR,EAAE,EACnCH,OAAO;gBAEZ,KAAK,MAAMgS,YAAYY,kBAAmB;oBACtC,MAAMjU,YAAE,CACHgB,UAAU,CAAC,sCACXC,MAAM,CAAC;wBACJsS,YAAYS;wBACZV,aAAaD,SAASC,WAAW;wBACjCE,MAAMH,SAASG,IAAI;wBACnBC,MAAMJ,SAASI,IAAI;wBACnBrB,OAAOiB,SAASjB,KAAK;wBACrBpH,YAAYgB;wBACZ7I,YAAY6I;oBAChB,GACC3K,OAAO;gBAChB;YACJ;YAEA4K,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEqE,WAAW,iCAAiC,EAAES,eAAe;YAElG,OAAO;gBACHxP,IAAIwP;gBACJlP,MAAM,GAAG8O,iBAAiB9O,IAAI,CAAC,cAAc,CAAC;gBAC9CwK,YAAYwD;gBACZoE,sBAAsB3D;YAC1B;QAEJ,EAAE,OAAO9C,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAEA;YACzD,MAAMA;QACV;IACJ;IAIA,+EAA+E;IAC/E,4CAA4C;IAC5C,+EAA+E;IAE/E;;GAEC,GACD,MAAM0G,kBAAkBtU,MAAc,EAAE;QACpCoM,QAAQC,GAAG,CAAC,CAAC,yCAAyC,EAAErM,QAAQ;QAEhE,IAAI;YACA,wCAAwC;YACxC,MAAMuU,eAAe,MAAMpU,YAAE,CACxBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,aAAa,KAAKL,QACxBwB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEkI,aAAapR,MAAM,CAAC,yBAAyB,EAAEnD,QAAQ;YAE5F,OAAO;gBACHA;gBACAsQ,gBAAgBiE,aAAapR,MAAM;gBACnCuL,WAAW6F;gBACXzG,iBAAiByG,aAAatF,MAAM,CAACC,CAAAA,IAAK,CAACA,EAAExC,QAAQ;gBACrD6D,mBAAmBgE,aAAatF,MAAM,CAACC,CAAAA,IAAKA,EAAExC,QAAQ;gBACtD8H,QAAQ;oBACJC,iBAAiBF,aAAapR,MAAM;gBACxC;YACJ;QACJ,EAAE,OAAOyK,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,uCAAuC,CAAC,EAAEA;YACzD,MAAMA;QACV;IACJ;IAEA;;GAEC,GACD,MAAM8G,iBAAiB1U,MAAc,EAAE;QACnCoM,QAAQC,GAAG,CAAC,CAAC,wCAAwC,EAAErM,QAAQ;QAE/D,IAAI;YACA,kDAAkD;YAClD,MAAM2U,cAAc,MAAMxU,YAAE,CACvBC,UAAU,CAAC,wBACXoM,SAAS,GACTnM,KAAK,CAAC,WAAW,KAAKL,QACtBwB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,sBAAsB,EAAEsI,YAAYxR,MAAM,CAAC,mCAAmC,EAAEnD,QAAQ;YAErG,MAAM4U,oBAAoBD,YAAY1F,MAAM,CAAC4F,CAAAA,IAAKA,EAAErN,cAAc,GAAG;YACrE,MAAMsN,iBAAiBH,YAAY1F,MAAM,CAAC4F,CAAAA;gBACtC,IAAI,CAACA,EAAE7J,QAAQ,EAAE,OAAO;gBACxB,MAAM+J,cAAc,IAAIxR,KAAKsR,EAAE7J,QAAQ;gBACvC,MAAMgK,gBAAgB,IAAIzR;gBAC1ByR,cAAcC,OAAO,CAACD,cAAcE,OAAO,KAAK;gBAChD,OAAOH,eAAeC;YAC1B;YAEA,OAAO;gBACHhV;gBACAmV,eAAeR,YAAYxR,MAAM;gBACjCyR,mBAAmBA,kBAAkBzR,MAAM;gBAC3C2R,gBAAgBA,eAAe3R,MAAM;gBACrCyP,UAAU+B;gBACVS,uBAAuBR;gBACvBS,oBAAoBP;gBACpBN,QAAQ;oBACJc,sBAAsBX,YAAYxR,MAAM;gBAC5C;YACJ;QACJ,EAAE,OAAOyK,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,oCAAoC,CAAC,EAAEA;YACtD,MAAMA;QACV;IACJ;IAEA;;GAEC,GACD,MAAM2H,2BAA2BvV,MAAc,EAAEsO,OAAY,EAAE;QAC3DlC,QAAQC,GAAG,CAAC,CAAC,+DAA+D,EAAErM,QAAQ,EAAEsO;QAExF,IAAI;YACA,MAAM,EAAEC,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAE9N,SAAS,KAAK,EAAE,GAAG4N;YACjD,MAAMG,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5B,aAAa;YACb,IAAIrI,QAAQhG,YAAE,CACTC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,aAAa,KAAKL,QACxB8C,OAAO,CAAC,cAAc;YAE3B,sBAAsB;YACtB,IAAIpC,WAAW,OAAO;gBAClB,IAAIA,WAAW,UAAU;oBACrByF,QAAQA,MAAM9F,KAAK,CAAC,YAAY,MAAM;gBAC1C,OAAO,IAAIK,WAAW,YAAY;oBAC9ByF,QAAQA,MAAM9F,KAAK,CAAC,YAAY,UAAU;gBAC9C;YACJ;YAEA,kBAAkB;YAClB,MAAM8O,cAAc,MAAMhJ,MACrB5F,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAS,MAAMvG,EAAE,CAAC,UACpC3G,gBAAgB;YACrB,MAAM4H,QAAQ9G,OAAO6N,aAAa/G,SAAS;YAE3C,wBAAwB;YACxB,MAAMsG,YAAY,MAAMvI,MACnBqI,KAAK,CAACA,OACNC,MAAM,CAACA,QACPjN,OAAO;YAEZ,kBAAkB;YAClB,MAAMoO,QAAQ;gBACVxH;gBACAkB,QAAQoF,UAAUO,MAAM,CAACC,CAAAA,IAAK,CAACA,EAAExC,QAAQ,EAAEvJ,MAAM;gBACjD0M,UAAUnB,UAAUO,MAAM,CAACC,CAAAA,IAAKA,EAAExC,QAAQ,EAAEvJ,MAAM;gBAClD2M,aAAa;gBACbC,UAAU,EAAE;YAChB;YAEA,gCAAgC;YAChC,MAAMyF,qBAAqB9G,UAAUE,GAAG,CAACrC,CAAAA,WAAa,CAAA;oBAClD5K,IAAI4K,SAAS5K,EAAE;oBACfM,MAAMsK,SAAStK,IAAI;oBACnB4M,WAAWtC,SAASsC,SAAS;oBAC7BpC,YAAYF,SAASE,UAAU;oBAC/BC,UAAUH,SAASG,QAAQ;oBAC3BvB,YAAYoB,SAASpB,UAAU;oBAC/B7H,YAAYiJ,SAASjJ,UAAU;oBAC/BsJ,MAAML,SAASM,OAAO,EAAEtI,cAAc;oBACtC7D,QAAQ6L,SAASG,QAAQ,GAAG,aAAa;oBACzCoC,eAAevC,SAASG,QAAQ,GAC5B3H,KAAKyG,KAAK,CAAC,AAAC,CAAA,IAAIjI,KAAKgJ,SAASG,QAAQ,EAAEqC,OAAO,KAAK,IAAIxL,KAAKgJ,SAASE,UAAU,EAAEsC,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC,KAClHhK,KAAKyG,KAAK,CAAC,AAAC,CAAA,IAAIjI,OAAOwL,OAAO,KAAK,IAAIxL,KAAKgJ,SAASE,UAAU,EAAEsC,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;oBACrGjF,OAAOyC,SAASzC,KAAK;oBACrB6I,WAAWpG,SAASoG,SAAS;gBACjC,CAAA;YAEA,MAAMpD,aAAa;gBACfhB;gBACAC;gBACApG;gBACAoH,YAAYzK,KAAK0K,IAAI,CAACrH,QAAQoG;gBAC9BkB,SAASnB,OAAOxJ,KAAK0K,IAAI,CAACrH,QAAQoG;gBAClCmB,SAASpB,OAAO;YACpB;YAEAnC,QAAQC,GAAG,CAAC,CAAC,0CAA0C,EAAEqC,UAAUvL,MAAM,CAAC,WAAW,CAAC;YAEtF,OAAO;gBACHuL,WAAW8G;gBACXjG;gBACAK;gBACAI,SAAS;oBACLtP;oBACAuP,WAAW3B,QAAQ2B,SAAS;oBAC5BC,SAAS5B,QAAQ4B,OAAO;oBACxBtD,MAAM0B,QAAQ1B,IAAI;gBACtB;YACJ;QAEJ,EAAE,OAAOgB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,mDAAmD,CAAC,EAAEA;YACrE,MAAMA;QACV;IACJ;IAEA;;GAEC,GACD,MAAM6H,qBAAqBzV,MAAc,EAAE;QACvCoM,QAAQC,GAAG,CAAC,CAAC,yDAAyD,EAAErM,QAAQ;QAEhF,IAAI;YACA,wCAAwC;YACxC,MAAM0O,YAAY,MAAMvO,YAAE,CACrBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,aAAa,KAAKL,QACxB8C,OAAO,CAAC,cAAc,QACtBtB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC,CAAC,qCAAqC,EAAEqC,UAAUvL,MAAM,CAAC,WAAW,CAAC;YAEjF,OAAO;gBACHiF,OAAOsG,UAAUvL,MAAM;gBACvBuL,WAAWA,UAAUE,GAAG,CAACM,CAAAA,IAAM,CAAA;wBAC3BvN,IAAIuN,EAAEvN,EAAE;wBACRM,MAAMiN,EAAEjN,IAAI;wBACZwK,YAAYyC,EAAEzC,UAAU;wBACxBC,UAAUwC,EAAExC,QAAQ;wBACpBhM,QAAQwO,EAAExC,QAAQ,GAAG,aAAa;wBAClCmC,WAAWK,EAAEL,SAAS;wBACtB1D,YAAY+D,EAAE/D,UAAU;oBAC5B,CAAA;YACJ;QAEJ,EAAE,OAAOyC,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAEA;YAClD,MAAMA;QACV;IACJ;IAEA,aAAa;IACb,MAAM8H,8BAA8BC,QAAa,EAAE;QACjD,MAAMC,cAAc;YAClB,eAAe;YACf,SAAS;YACT,eAAe;YACf,iBAAiB;QACrB;QAEE,MAAMC,SAASD,WAAW,CAACD,SAASjT,IAAI,CAAC,IAAI,GAAG,2BAA2B;QAC3E,MAAMiQ,YAAYgD,SAASG,aAAa,GAAG/Q,KAAKyG,KAAK,CAACmK,SAASG,aAAa,GAAG,KAAK,KAAK,GAAG,iDAAiD;QAE7I,OAAO,CAAC;;;cAGE,EAAEH,UAAUtT,UAAU,gBAAgB;YACxC,EAAEsT,UAAUnT,UAAU,gBAAgB;iBACjC,EAAEmT,UAAUI,UAAUJ,SAASI,OAAO,GAAG,MAAM,aAAa;0BACnD,EAAEJ,UAAUK,cAAc;gBACpC,EAAEL,UAAUjT,KAAK,MAAM,EAAEmT,OAAO;2CACL,EAAEF,UAAUM,cAAc,mBAAmB;uBACjE,EAAEN,UAAUO,mBAAmB,mBAAmB;mCACtC,EAAEP,UAAUG,iBAAiB,GAAG;gCACnC,EAAEH,UAAUQ,WAAW7R,KAAK,SAAS,gCAAgC;8BACvE,EAAEqR,UAAUS,eAAe9R,KAAK,SAAS,6BAA6B;kBAClF,EAAEqR,UAAUU,cAAc/R,KAAK,SAAS,8BAA8B;oBACpE,EAAEqR,UAAUW,aAAahS,KAAK,SAAS,iCAAiC;;IAExF,EAAEqR,UAAUpD,QAAQ,iCAAiCoD,SAASpD,KAAK,GAAG,GAAG;;;;4BAIjD,EAAEoD,UAAUjT,KAAK;6BAChB,EAAEiT,UAAUM,WAAW;iCACnB,EAAEN,UAAUG,cAAc;iCAC1B,EAAEH,UAAUQ,WAAW7R,KAAK,MAAM;+BACpC,EAAEqR,UAAUS,eAAe9R,KAAK,MAAM;;;;;;;;;;;;;;;;;;mBAkBlD,EAAEuR,OAAO;;qBAEP,EAAElD,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDA6DiB,EAAEgD,UAAUG,iBAAiB,GAAG;;;IAG9E,CAAC;IACL;IAEA,MAAMS,uBAAuBvW,MAAc,EAAE2V,QAAa,EAAE;QACxD,IAAI,MAAM,IAAI,CAACzJ,wBAAwB,CAAClM,SAAS;YAC7C,MAAM,IAAIS,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACnD,GAAG;QACP;QAEA,IAAI;YACA,MAAM6V,SAAS,MAAM,IAAI,CAACd,6BAA6B,CAACC;YACxD,MAAMc,WAAW,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC9W,MAAM,CAAC;gBACvD+W,OAAO;gBACPC,UAAU;oBACN;wBACI7W,MAAM;wBACN8W,SAAS;oBACb;oBACA;wBAAE9W,MAAM;wBAAQ8W,SAASP;oBAAO;iBACnC;gBACDQ,aAAa;gBACbC,YAAY;gBACZC,iBAAiB;oBAAEtK,MAAM;gBAAc;YAC3C;YAEA,MAAMuK,iBAAsBV,SAASW,OAAO,CAAC,EAAE,CAACzW,OAAO,CAACoW,OAAO;YAC/D,MAAMM,mBAAmB,IAAI,CAACC,qBAAqB,CAACH;YACpD,MAAMI,eAAeC,KAAKC,KAAK,CAACJ;YAEhC,IAAI,CAACE,aAAatV,IAAI,IAAI,CAACsV,aAAa1K,OAAO,IAAI,CAAC0K,aAAa3E,QAAQ,EAAE;gBACvE,MAAM,IAAIjC,MAAM;YACpB;YAEA,oBAAoB;YACpB,MAAMrB,SAAS,MAAMnP,YAAE,CAACuX,WAAW,GAAGlW,OAAO,CAAC,OAAOmW;gBACjD,oBAAoB;gBACpB,MAAM9E,eAAe,MAAM8E,IACtBxW,UAAU,CAAC,mBACXC,MAAM,CAAC;oBACJK,WAAWzB;oBACXiC,MAAMsV,aAAatV,IAAI;oBACvB4K,SAAS0K,aAAa1K,OAAO;oBAC7B/C,OAAOyN,aAAazN,KAAK;oBACzB6I,WAAW4E,aAAa5E,SAAS;oBACjC9D,WAAW0I,aAAa1I,SAAS;oBACjCqC,eAAeqG,aAAarG,aAAa,IAAI;oBAC7CzE,YAAY,IAAIlJ;oBAChBmJ,UAAU;oBACVvB,YAAY,IAAI5H;oBAChBD,YAAY,IAAIC;gBACpB,GACC/C,gBAAgB;gBAErB,MAAMsS,kBAAkBxR,OAAOuR,aAAatR,QAAQ;gBAEpD,+BAA+B;gBAC/B,IAAK,IAAI2R,QAAQ,GAAGA,QAAQqE,aAAa3E,QAAQ,CAACzP,MAAM,EAAE+P,QAAS;oBAC/D,MAAMC,UAAUoE,aAAa3E,QAAQ,CAACM,MAAM;oBAC5C,MAAMG,eAAeF,QAAQlR,IAAI;oBAEjC,MAAMqR,cAAc,MAAMqE,IACrBxW,UAAU,CAAC,4BACXC,MAAM,CAAC;wBACJoQ,aAAasB;wBACb7Q,MAAMoR;oBACV,GACC7S,gBAAgB;oBAErB,MAAM+S,iBAAiBjS,OAAOgS,YAAY/R,QAAQ;oBAElD,IAAI4R,QAAQC,SAAS,IAAIL,MAAMC,OAAO,CAACG,QAAQC,SAAS,GAAG;wBACvD,MAAMuE,IACDxW,UAAU,CAAC,sCACXC,MAAM,CAAC+R,QAAQC,SAAS,CAACxE,GAAG,CAAC4E,CAAAA,WAAa,CAAA;gCACvCE,YAAYH;gCACZE,aAAa;gCACbxR,MAAMuR,SAASvR,IAAI;gCACnB0R,MAAMH,SAASG,IAAI;gCACnBC,MAAM,OAAOJ,SAASI,IAAI,KAAK,WAAWJ,SAASI,IAAI,GAAGJ,SAASI,IAAI,CAACrP,QAAQ;gCAChFsP,KAAKL,SAASK,GAAG,IAAI;gCACrBC,cAAcN,SAASM,YAAY,IAAI;gCACvCvB,OAAOiB,SAASjB,KAAK,IAAI;4BAC7B,CAAA,IACC/Q,OAAO;oBAChB;gBACJ;gBAEA,OAAOsR;YACX;YAEA,OAAO;gBACHpS,QAAQ;gBACRgB,MAAM;oBACF8P,aAAalC;gBACjB;YACJ;QACJ,EAAE,OAAO1B,OAAO;YACZxB,QAAQwB,KAAK,CAAC,0CAA0C;gBACpDjN,SAASiN,MAAMjN,OAAO;gBACtByP,OAAOxC,MAAMwC,KAAK;gBAClBwH,OAAOjC;YACX;YACA,MAAM,IAAIlV,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;oBAA0CiN,MAAMjN,OAAO;iBAAC;YACtE,GAAG;QACP;IACJ;IAKI,MAAMkX,2BAA2B7X,MAAc,EAAE;QAC7C,MAAMmM,QAAQ,IAAI5I;QAClB,MAAMgJ,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,cAAc,MAAM8L,OAC1B9L,KAAK,CAAC,YAAY,MAAM,MACxByC,OAAO,CAAC,cAAc,QAAQ,8CAA8C;SAC5EtC,gBAAgB;QAErB,IAAI,CAAC+L,UAAU;YACX,OAAO;gBACH7L,QAAQ;gBACRgB,MAAM;oBACFoW,cAAc;gBAClB;YACJ;QACJ;QAEA,yDAAyD;QACzD,MAAM1E,YAAY,MAAMjT,YAAE,CACrBC,UAAU,CAAC,iCACX0B,QAAQ,CAAC,4CAA4C,iBAAiB,QACtEA,QAAQ,CAAC,kBAAkB,QAAQ,kBACnCA,QAAQ,CAAC,uBAAuB,QAAQ,qBACxCA,QAAQ,CAAC,+BAA+B,gBAAgB,kBACxDvB,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,CACtC,yEAAyE;SACxEH,OAAO;QAEZ,qEAAqE;QACrE,MAAMoR,WAAWQ,UAAUvL,MAAM,CAAC,CAACC,KAAKC;YACpC,MAAMgQ,cAAchQ,KAAKsL,YAAY;YAErC,IAAI,CAACvL,GAAG,CAACiQ,YAAY,EAAE;gBACnBjQ,GAAG,CAACiQ,YAAY,GAAG;oBACf9V,MAAM8V;oBACNpW,IAAIoG,KAAKpG,EAAE;oBACXyR,WAAW,EAAE;gBACjB;YACJ;YAEAtL,GAAG,CAACiQ,YAAY,CAAC3E,SAAS,CAAC7F,IAAI,CAACxF;YAChC,OAAOD;QACX,GAAG,CAAC;QAEJ,MAAMkQ,uBAAuBC,OAAO7W,MAAM,CAACwR,UAAUhE,GAAG,CAAC,CAACuE,UAAkB,CAAA;gBACxElR,MAAMkR,QAAQlR,IAAI;gBAClBN,IAAIwR,QAAQxR,EAAE;gBACdyR,WAAWD,QAAQC,SAAS,CAACxE,GAAG,CAAC,CAAC4E,WAAmB,CAAA;wBACjD7R,IAAI6R,SAAS0E,KAAK;wBAClBzE,aAAaD,SAASC,WAAW,IAAI;wBACrCxR,MAAMuR,SAAS2E,aAAa,IAAI3E,SAAS4E,cAAc;wBACvDC,cAAc7E,SAAS6E,YAAY,IAAI;wBACvClC,WAAW3C,SAAS2C,SAAS,IAAI;wBACjCmC,WAAW9E,SAAS8E,SAAS,IAAI;wBACjC3E,MAAMH,SAASG,IAAI;wBACnBC,MAAMJ,SAASI,IAAI;wBACnBC,KAAKL,SAASK,GAAG;wBACjBC,cAAcN,SAASM,YAAY;wBACnCvB,OAAOiB,SAASjB,KAAK;oBACzB,CAAA;YACJ,CAAA;QAEA,MAAMgG,oBAAoB,MAAMpY,YAAE,CACjCC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCpB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAS,MAAMvG,EAAE,CAAC,uBACpC3G,gBAAgB;QAGjB,yCAAyC;QACzC,OAAO;YACHE,QAAQ;YACRgB,MAAM;gBACFoW,cAAc;gBACdnW,IAAI4K,SAAS5K,EAAE;gBACfgR,WAAWpG,SAASoG,SAAS;gBAC7B1Q,MAAMsK,SAAStK,IAAI;gBACnBuW,oBAAoBD,mBAAmBC,sBAAsB;gBAC7DtH,eAAe3E,SAAS2E,aAAa;gBACrCrC,WAAWtC,SAASsC,SAAS;gBAC7B/E,OAAOyC,SAASzC,KAAK;gBACrB2C,YAAYF,SAASE,UAAU;gBAC/BI,SAASN,SAASM,OAAO;gBACzB+F,UAAUoF;YACd;QACJ;IACJ;IAEA,MAAMS,oBAAoB/H,UAAkB,EAAE1Q,MAAc,EAAE;QAC1DoM,QAAQC,GAAG,CAAC,CAAC,wDAAwD,EAAEqE,WAAW,QAAQ,EAAE,OAAOA,WAAW,gBAAgB,EAAE1Q,QAAQ;QAExI,IAAI;YACA,4BAA4B;YAC5B,MAAMuM,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,QAAQ,KAAKqQ,YACnBrQ,KAAK,CAAC,eAAe,KAAKL,QAC1BQ,gBAAgB;YAErB4L,QAAQC,GAAG,CAAC,CAAC,iCAAiC,CAAC,EAAEE,WAAW,QAAQ;YACpE,IAAIA,UAAU;gBACVH,QAAQC,GAAG,CAAC,CAAC,6BAA6B,EAAEE,SAAS5K,EAAE,CAAC,QAAQ,EAAE4K,SAAStK,IAAI,CAAC,SAAS,EAAEsK,SAASK,IAAI,CAAC,CAAC,CAAC;YAC/G;YAEA,IAAI,CAACL,UAAU;gBACXH,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEqE,WAAW,6BAA6B,EAAE1Q,QAAQ;gBAEtF,yDAAyD;gBACzD,MAAM0Y,iBAAiB,MAAMvY,YAAE,CAC1BC,UAAU,CAAC,0BACXG,MAAM,CAAC;oBAAC;oBAAM;iBAAY,EAC1BF,KAAK,CAAC,MAAM,KAAKqQ,YACjBlQ,gBAAgB;gBAErB,IAAIkY,gBAAgB;oBAChBtM,QAAQC,GAAG,CAAC,CAAC,aAAa,EAAEqE,WAAW,gCAAgC,EAAEgI,eAAejX,SAAS,CAAC,iBAAiB,EAAEzB,QAAQ;oBAC7H,MAAM,IAAIS,qBAAa,CAAC;wBACpBC,QAAQ;wBACRC,SAAS;4BAAC;yBAA8B;oBAC5C,GAAG;gBACP,OAAO;oBACHyL,QAAQC,GAAG,CAAC,CAAC,YAAY,EAAEqE,WAAW,6BAA6B,CAAC;oBACpE,MAAM,IAAIjQ,qBAAa,CAAC;wBACpBC,QAAQ;wBACRC,SAAS;4BAAC;yBAAqC;oBACnD,GAAG;gBACP;YACJ;YAEJ,uEAAuE;YACvE,MAAMgY,WAAW,MAAMxY,YAAE,CACpBC,UAAU,CAAC,qCACX0B,QAAQ,CAAC,2CAA2C,aAAa,QACjEvB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,iBAAiB,KAAKqQ,YAC5B5N,OAAO,CAAC,iBAAiB,OACzBA,OAAO,CAAC,eAAe,OACvBA,OAAO,CAAC,QAAQ,OAChBtB,OAAO;YAEZ,sCAAsC;YACtC,MAAMoX,aAAa,IAAIC;YACvBF,SAASG,OAAO,CAAC,CAACC;gBACd,MAAMtO,MAAMsO,IAAItH,WAAW,CAACuH,WAAW;gBACvC,IAAI,CAACJ,WAAWK,GAAG,CAACxO,MAAM;oBACtBmO,WAAWnV,GAAG,CAACgH,KAAK,EAAE;gBAC1B;gBAEA,IAAI6G,OAAOsH,WAAWM,GAAG,CAACzO,KAAK0O,IAAI,CAAC,CAACC,IAAWA,EAAEzX,EAAE,KAAKoX,IAAIpX,EAAE;gBAC/D,IAAI,CAAC2P,MAAM;oBACPA,OAAO;wBACH3P,IAAIoX,IAAIpX,EAAE;wBACVM,MAAM8W,IAAI9W,IAAI;wBACdyP,WAAWqH,IAAIrH,SAAS;wBACxB2H,WAAW;4BACP3R,UAAU;4BACVO,SAAS;4BACTC,OAAO;4BACPC,KAAK;4BACLW,OAAO;wBACX;wBACAwQ,OAAO,EAAE;oBACb;oBACAV,WAAWM,GAAG,CAACzO,KAAK8C,IAAI,CAAC+D;gBAC7B;gBAEA,IAAIyH,IAAIQ,SAAS,EAAE;oBACfjI,KAAKgI,KAAK,CAAC/L,IAAI,CAAC;wBACZtL,MAAM8W,IAAIQ,SAAS;wBACnBtH,MAAM8G,IAAI9G,IAAI;wBACdD,UAAU1P,WAAWhB,OAAOyX,IAAI/G,QAAQ,EAAEzP,OAAO,CAAC,OAAO;oBAC7D;oBACA+O,KAAK+H,SAAS,CAAC3R,QAAQ,IAAIpF,WAAWhB,OAAOyX,IAAIrR,QAAQ,EAAEnF,OAAO,CAAC,OAAO;oBAC1E+O,KAAK+H,SAAS,CAACpR,OAAO,IAAI3F,WAAWhB,OAAOyX,IAAI9Q,OAAO,EAAE1F,OAAO,CAAC,OAAO;oBACxE+O,KAAK+H,SAAS,CAACnR,KAAK,IAAI5F,WAAWhB,OAAOyX,IAAI7Q,KAAK,EAAE3F,OAAO,CAAC,OAAO;oBACpE+O,KAAK+H,SAAS,CAAClR,GAAG,IAAI7F,WAAWhB,OAAOyX,IAAI5Q,GAAG,EAAE5F,OAAO,CAAC,OAAO;oBAChE+O,KAAK+H,SAAS,CAACvQ,KAAK,IAAIxG,WAAWhB,OAAOyX,IAAIjQ,KAAK,EAAEvG,OAAO,CAAC,OAAO;gBACxE;YACJ;YAEA,4BAA4B;YAC5B,MAAMiX,QAAQvB,OAAOwB,WAAW,CAACb;YAEjC,kCAAkC;YAClC,MAAMc,cAAc,MAAMvZ,YAAE,CACvBC,UAAU,CAAC,2CACXG,MAAM,CAAC;gBAAC;gBAAU;gBAAY;gBAAqB;aAAU,EAC7DF,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;YAER4K,QAAQC,GAAG,CAAC,CAAC,qBAAqB,EAAEqE,WAAW,sBAAsB,CAAC;YACtEtE,QAAQC,GAAG,CAAC,CAAC,WAAW,EAAE4L,OAAO0B,IAAI,CAACH,OAAOrW,MAAM,CAAC,qBAAqB,EAAEuW,YAAYvW,MAAM,CAAC,YAAY,CAAC;YAE3G,OAAO;gBACHxB,IAAI4K,SAAS5K,EAAE;gBACfiL,MAAML,SAASK,IAAI,IAAI;gBACvB3K,MAAMsK,SAAStK,IAAI;gBACnB4M,WAAWtC,SAASsC,SAAS;gBAC7BqC,eAAe3E,SAAS2E,aAAa;gBACrCD,gBAAgB1E,SAAS0E,cAAc;gBACvCxE,YAAYF,SAASE,UAAU;gBAC/BC,UAAUH,SAASG,QAAQ;gBAC3BhM,QAAQ6L,SAASG,QAAQ,GAAG,aAAa;gBACzC7J,OAAO;oBACH6E,UAAUpF,WAAWhB,OAAOiL,SAAS7E,QAAQ,EAAEnF,OAAO,CAAC,OAAO;oBAC9D0F,SAAS3F,WAAWhB,OAAOiL,SAAStE,OAAO,EAAE1F,OAAO,CAAC,OAAO;oBAC5D2F,OAAO5F,WAAWhB,OAAOiL,SAASrE,KAAK,EAAE3F,OAAO,CAAC,OAAO;oBACxD4F,KAAK7F,WAAWhB,OAAOiL,SAASpE,GAAG,EAAE5F,OAAO,CAAC,OAAO;oBACpDyE,OAAO1E,WAAWhB,OAAOiL,SAASvF,KAAK,EAAEzE,OAAO,CAAC,OAAO;gBAC5D;gBACAiX;gBACAE,aAAaA,YAAY9K,GAAG,CAAC,CAACgL,MAAS,CAAA;wBACnC3X,MAAM2X,IAAI3X,IAAI;wBACdoQ,QAAQuH,IAAIvH,MAAM;wBAClBC,iBAAiBsH,IAAItH,eAAe;wBACpCC,OAAOqH,IAAIrH,KAAK;oBACpB,CAAA;YACJ;QAEJ,EAAE,OAAO3E,OAAO;YACZxB,QAAQwB,KAAK,CAAC,CAAC,oCAAoC,EAAE8C,WAAW,CAAC,CAAC,EAAE9C;YACpExB,QAAQwB,KAAK,CAAC,CAAC,cAAc,CAAC,EAAEA,MAAMwC,KAAK;YAC3C,MAAMxC;QACV;IACJ;IAEA,MAAMiM,uBAAuBnJ,UAAkB,EAAE1Q,MAAc,EAAE;QAC7DoM,QAAQC,GAAG,CAAC,CAAC,0BAA0B,EAAEqE,WAAW,eAAe,EAAE1Q,QAAQ;QAE7E,MAAMuM,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;QAErB4L,QAAQC,GAAG,CAAC,CAAC,wBAAwB,CAAC,EAAEE,WAAW,QAAQ;QAE3D,IAAI,CAACA,UAAU;YACXH,QAAQC,GAAG,CAAC,CAAC,YAAY,EAAEqE,WAAW,6BAA6B,EAAE1Q,QAAQ;YAC7E,MAAM,IAAIS,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA4B;YAC1C,GAAG;QACP;QAIA,sCAAsC;QACtC,IAAI4L,SAASG,QAAQ,EAAE;YACnBN,QAAQC,GAAG,CAAC,CAAC,aAAa,EAAEqE,WAAW,gBAAgB,CAAC;QAC5D;QAEA,kDAAkD;QAClD,MAAMiE,cAAc,MAAMxU,YAAE,CACvBC,UAAU,CAAC,4BACXG,MAAM,CAAC;YAAC;YAAM;YAAQ;SAAc,EACpCF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCH,OAAO;QAIZ,iEAAiE;QACjE,MAAM4R,YAAY,MAAMjT,YAAE,CACrBC,UAAU,CAAC,iCACX0B,QAAQ,CAAC,4CAA4C,iBAAiB,QACtEA,QAAQ,CAAC,kBAAkB,QAAQ,kBACnCA,QAAQ,CAAC,uBAAuB,QAAQ,qBACxCA,QAAQ,CAAC,+BAA+B,gBAAgB,kBACxDvB,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCH,OAAO;QAIZ,sEAAsE;QACtE,MAAMoR,WAAW,CAAC;QAClB+B,YAAYmE,OAAO,CAAC3F,CAAAA;YAChBP,QAAQ,CAACO,QAAQlR,IAAI,CAAC,GAAG;gBACrBA,MAAMkR,QAAQlR,IAAI;gBAClBN,IAAIwR,QAAQxR,EAAE;gBACdyR,WAAW,EAAE;YACjB;QACJ;QAEA,oDAAoD;QACpDA,UAAU0F,OAAO,CAAC/Q,CAAAA;YACd,MAAMgQ,cAAchQ,KAAKsL,YAAY;YACrC,IAAIT,QAAQ,CAACmF,YAAY,IAAIhQ,KAAKmQ,KAAK,EAAE;gBACrCtF,QAAQ,CAACmF,YAAY,CAAC3E,SAAS,CAAC7F,IAAI,CAACxF;YACzC;QACJ;QAEA,MAAMiQ,uBAAuBC,OAAO7W,MAAM,CAACwR,UAAUhE,GAAG,CAAC,CAACuE,UAAkB,CAAA;gBACxElR,MAAMkR,QAAQlR,IAAI;gBAClBN,IAAIwR,QAAQxR,EAAE;gBACdyR,WAAWD,QAAQC,SAAS,CAACxE,GAAG,CAAC,CAAC4E,WAAmB,CAAA;wBACjD7R,IAAI6R,SAAS0E,KAAK;wBAClBzE,aAAaD,SAASC,WAAW,IAAI;wBACrCxR,MAAMuR,SAAS2E,aAAa,IAAI3E,SAAS4E,cAAc;wBACvDC,cAAc7E,SAAS6E,YAAY,IAAI;wBACvClC,WAAW3C,SAAS2C,SAAS,IAAI;wBACjCmC,WAAW9E,SAAS8E,SAAS,IAAI;wBACjC3E,MAAMH,SAASG,IAAI;wBACnBC,MAAMJ,SAASI,IAAI;wBACnBC,KAAKL,SAASK,GAAG;wBACjBC,cAAcN,SAASM,YAAY;wBACnCvB,OAAOiB,SAASjB,KAAK;oBACzB,CAAA;YACJ,CAAA;QAMA,MAAMgG,oBAAoB,MAAMpY,YAAE,CAC7BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCpB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAS,MAAMvG,EAAE,CAAC,uBACpC3G,gBAAgB;QAErB,OAAO;YACHE,QAAQ;YACRgB,MAAM;gBACFoW,cAAc;gBACdnW,IAAI4K,SAAS5K,EAAE;gBACfgR,WAAWpG,SAASoG,SAAS;gBAC7B1Q,MAAMsK,SAAStK,IAAI;gBACnBuW,oBAAoBD,mBAAmBC,sBAAsB;gBAC7DtH,eAAe3E,SAAS2E,aAAa;gBACrCrC,WAAWtC,SAASsC,SAAS;gBAC7B/E,OAAOyC,SAASzC,KAAK;gBACrB2C,YAAYF,SAASE,UAAU;gBAC/BI,SAASN,SAASM,OAAO;gBACzB+F,UAAUoF;YACd;QACJ;IACJ;IAEA,MAAM8B,sBAAsBpJ,UAAkB,EAAErN,UAAe,EAAErD,MAAc,EAAE;QAC7E,wDAAwD;QACxD,MAAM+Z,mBAAmB,MAAM5Z,YAAE,CAC5BC,UAAU,CAAC,mBACXG,MAAM,CAAC;YAAC;YAAM;YAAa;YAAY;SAAa,EACpDF,KAAK,CAAC,MAAM,KAAKqQ,YACjBrQ,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;QAErB,IAAI,CAACuZ,kBAAkB;YACnB,MAAM,IAAItZ,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAoE;YAClF,GAAG;QACP;QAEA,iEAAiE;QACjE,IAAIoZ,iBAAiBrN,QAAQ,EAAE;YAC3B,MAAM,IAAIjM,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA4D;YAC1E,GAAG;QACP;QAEA,wBAAwB;QACxB,IAAI,CAAC0C,WAAWpB,IAAI,IAAIoB,WAAWpB,IAAI,CAAC+X,IAAI,GAAG7W,MAAM,KAAK,GAAG;YACzD,MAAM,IAAI1C,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAmC;YACjD,GAAG;QACP;QAEA,IAAI,CAAC0C,WAAWwL,SAAS,IAAIxL,WAAWwL,SAAS,CAACmL,IAAI,GAAG7W,MAAM,KAAK,GAAG;YACnE,MAAM,IAAI1C,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAuC;YACrD,GAAG;QACP;QAEA,IAAI,CAAC0C,WAAWsP,SAAS,IAAItP,WAAWsP,SAAS,GAAG,KAAKtP,WAAWsP,SAAS,GAAG,GAAG;YAC/E,MAAM,IAAIlS,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAmD;YACjE,GAAG;QACP;QAEA,IAAI;YACA,uCAAuC;YACvC,MAAMR,YAAE,CACHqD,WAAW,CAAC,mBACZC,GAAG,CAAC;gBACDxB,MAAMoB,WAAWpB,IAAI;gBACrB4K,SAASxJ,WAAWuJ,IAAI;gBACxB9C,OAAOzG,WAAWyG,KAAK;gBACvB6I,WAAWtP,WAAWsP,SAAS;gBAC/B9D,WAAWxL,WAAWwL,SAAS;gBAC/BqC,eAAe7N,WAAWkP,KAAK;gBAC/BjP,YAAY,IAAIC;YACpB,GACClD,KAAK,CAAC,MAAM,KAAKqQ,YACjBlP,OAAO;YAEZ,gCAAgC;YAChC,IAAI6B,WAAWuP,QAAQ,IAAIG,MAAMC,OAAO,CAAC3P,WAAWuP,QAAQ,GAAG;gBAC3D,2CAA2C;gBAC3C,MAAMqH,mBAAmB,MAAM9Z,YAAE,CAC5BC,UAAU,CAAC,4BACXG,MAAM,CAAC;oBAAC;iBAAK,EACbF,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;gBAEZ,KAAK,MAAM2R,WAAW8G,iBAAkB;oBACpC,MAAM9Z,YAAE,CACHyD,UAAU,CAAC,sCACXvD,KAAK,CAAC,cAAc,KAAK8S,QAAQxR,EAAE,EACnCH,OAAO;gBAChB;gBAEA,MAAMrB,YAAE,CACHyD,UAAU,CAAC,4BACXvD,KAAK,CAAC,eAAe,KAAKqQ,YAC1BlP,OAAO;gBAEZ,wCAAwC;gBACxC,MAAMyR,UAAU;gBAChB,KAAK,MAAM,CAACC,OAAOC,QAAQ,IAAI9P,WAAWuP,QAAQ,CAACsH,OAAO,GAAI;oBAC1D,MAAMnC,cAAc5E,QAAQlR,IAAI,IAAI,CAAC,OAAO,EAAEgR,OAAO,CAACC,MAAM,EAAE;oBAE9D,MAAMgB,aAAa,MAAM/T,YAAE,CACtBgB,UAAU,CAAC,4BACXC,MAAM,CAAC;wBACJoQ,aAAad;wBACbzO,MAAM8V;wBACN5M,YAAY,IAAI5H;wBAChBD,YAAY,IAAIC;oBACpB,GACC/C,gBAAgB;oBAErB,MAAM2Z,YAAY7Y,OAAO4S,WAAW3S,QAAQ;oBAE5C,IAAI4R,QAAQC,SAAS,IAAIL,MAAMC,OAAO,CAACG,QAAQC,SAAS,GAAG;wBACvD,KAAK,MAAMI,YAAYL,QAAQC,SAAS,CAAE;4BACtC,sEAAsE;4BACtE,MAAMgH,mBAAmB,OAAO5G,SAASC,WAAW,KAAK,YACrDD,SAASC,WAAW,CAAClP,QAAQ,GAAG8V,UAAU,CAAC;4BAE/C,MAAMla,YAAE,CACHgB,UAAU,CAAC,sCACXC,MAAM,CAAC;gCACJsS,YAAYyG;gCACZ,gEAAgE;gCAChE1G,aAAa2G,mBAAmB,OAAQ5G,SAASA,QAAQ,EAAE7R,MAAM6R,SAASC,WAAW;gCACrFxR,MAAMuR,SAASvR,IAAI,IAAIuR,SAAS2E,aAAa,IAAI3E,SAASA,QAAQ,EAAEvR;gCACpE0R,MAAMH,SAASG,IAAI;gCACnBC,MAAMJ,SAASI,IAAI;gCACnBC,KAAKL,SAASK,GAAG;gCACjBC,cAAcN,SAAS8G,QAAQ,IAAI9G,SAASM,YAAY;gCACxDvB,OAAOiB,SAASjB,KAAK;4BACzB,GACC/Q,OAAO;wBAChB;oBACJ;gBACJ;YACJ;YAEA,OAAO;gBACHd,QAAQ;gBACRC,SAAS;gBACTe,MAAM;oBAAEC,IAAI+O;gBAAW;YAC3B;QAEJ,EAAE,OAAO9C,OAAO;YACZxB,QAAQwB,KAAK,CAAC,gCAAgCA;YAC9C,MAAM,IAAInN,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAmD;YACjE,GAAG;QACP;IACJ;IAEA,MAAM4Z,YAAYva,MAAc,EAAE;QAC9B,MAAM6B,OAAO,MAAM1B,YAAE,CAClBC,UAAU,CAAC,SACXC,KAAK,CAAC,MAAM,KAAKL,QACjBO,MAAM,CAAC;YAAC;YAAQ;YAAS;YAAS;YAAU;YAAU;SAAgB,EACtEC,gBAAgB;QACjB,OAAOqB;IACX;IAGI,MAAM2Y,kBAAkBxa,MAAc,EAAEya,qBAA4C,EAAE;QACpFrO,QAAQC,GAAG,CAAC,CAAC,yEAAyE,EAAErM,QAAQ;QAChGoM,QAAQC,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAEmL,KAAKkD,SAAS,CAACD,uBAAuB,MAAM;QAE/E,6CAA6C;QAC7C,IAAI,MAAM,IAAI,CAAC9N,qBAAqB,CAAC3M,SAAS;YAC1CoM,QAAQwB,KAAK,CAAC,CAAC,6BAA6B,EAAE5N,OAAO,0BAA0B,CAAC;YAChF,MAAM,IAAIS,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACnD,GAAG;QACP;QAEAyL,QAAQC,GAAG,CAAC,CAAC,8EAA8E,EAAErM,QAAQ;QAErG,MAAM,EAAEiC,IAAI,EAAE4K,OAAO,EAAEgC,SAAS,EAAE8L,iBAAiB,EAAEnB,KAAK,EAAEE,WAAW,EAAExI,aAAa,EAAE,GAAGuJ;QAE3F,MAAMG,WAAW,MAAM,IAAI,CAACL,WAAW,CAACva;QACxC,MAAMiR,iBAAiB2J,UAAUpY,UAAU;QAE3C,MAAMqQ,eAAe,MAAM1S,YAAE,CAC1BgB,UAAU,CAAC,0BACXC,MAAM,CAAC;YACNa,MAAMA;YACN4K,SAASA;YACToE,gBAAgBA;YAChBpC,WAAWA;YACXvG,eAAeqS,kBAAkBjT,QAAQ;YACzCiB,cAAcgS,kBAAkB1S,OAAO;YACvCY,YAAY8R,kBAAkBzS,KAAK;YACnCU,UAAU+R,kBAAkBxS,GAAG;YAC/BY,YAAY4R,kBAAkB3T,KAAK;YACnCkK,eAAeA;YACfzE,YAAY,IAAIlJ;YAChB9B,WAAWzB;YACXmL,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QAClB,GACC/C,gBAAgB;QAEnB,MAAMsS,kBAAkBxR,OAAOuR,aAAatR,QAAQ;QAGpDiY,MAAMV,OAAO,CAAC,OAAOxH;YACnB,MAAMuJ,WAAW,MAAM1a,YAAE,CAACgB,UAAU,CAAC,gCACpCC,MAAM,CAAC;gBACNoQ,aAAasB;gBACb7Q,MAAMqP,KAAKrP,IAAI;gBACfwP,aAAaH,KAAKG,WAAW;gBAC7BC,WAAWJ,KAAKI,SAAS;YAC3B,GACClR,gBAAgB;YAEjB,MAAMsa,cAAcxZ,OAAOuZ,SAAStZ,QAAQ;YAE5C+P,MAAMgI,OAAOR,QAAQ,CAACjH;gBACpB1R,YAAE,CAACgB,UAAU,CAAC,sCACbC,MAAM,CAAC;oBACJ0Q,SAASgJ;oBACT/I,SAASF,KAAKE,OAAO;oBACrB9P,MAAM4P,KAAK5P,IAAI;oBACf+P,UAAUH,KAAKG,QAAQ;oBACvBC,MAAMJ,KAAKI,IAAI,IAAG;oBAClBvK,UAAUmK,KAAKnK,QAAQ,IAAI;oBAC3BO,SAAS4J,KAAK5J,OAAO,IAAI;oBACzBC,OAAO2J,KAAK3J,KAAK,IAAI;oBACrBC,KAAK0J,KAAK1J,GAAG,IAAI;oBACjBW,OAAO+I,KAAK/I,KAAK,IAAI;gBACvB,GACCtH,OAAO;YACZ;QACF;QAEA,IAAIkY,eAAe3G,MAAMC,OAAO,CAAC0G,gBAAgBA,YAAYvW,MAAM,GAAG,GAAG;YACvE,MAAMhD,YAAE,CACLgB,UAAU,CAAC,sCACXC,MAAM,CAACsY,YAAY9K,GAAG,CAAC,CAACwD,aAAgB,CAAA;oBACvCZ,aAAasB;oBACb7Q,MAAMmQ,WAAWnQ,IAAI;oBACrBoQ,QAAQD,WAAWC,MAAM;oBACzBC,iBAAiBF,WAAWE,eAAe;oBAC3CC,OAAOH,WAAWG,KAAK;gBACzB,CAAA,IACC/Q,OAAO;QACZ;QAEA,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;QACV;IACF;IAEA,KAAK;IACL,MAAMqZ,uBAAuBpF,QAAa,EAAE;QAC1C,MAAMC,cAAc;YAClB,iBAAiB;YACjB,cAAc;YACd,kBAAkB;QACpB;QAEA,MAAMC,SAASD,WAAW,CAACD,SAASjT,IAAI,CAAC,IAAI;QAC7C,MAAMsY,gBAAgBrF,SAASqF,aAAa,IAAI,GAAG,oDAAoD;QAEvG,OAAO,CAAC;;;sBAGE,EAAErF,UAAUtT,UAAU,gBAAgB;0BAClC,EAAEsT,UAAUnT,OAAO;yBACpB,EAAEmT,UAAUsF,gBAAgB,mBAAmB;+BACzC,EAAEtF,UAAUI,UAAUJ,SAASI,OAAO,GAAG,MAAM,aAAa;8BAC7D,EAAEJ,UAAUuF,gBAAgBvF,SAASuF,aAAa,GAAG,MAAM,mBAAmB;kCAC1E,EAAEvF,UAAUK,cAAc;wBACpC,EAAEL,UAAUjT,KAAK,MAAM,EAAEmT,OAAO;4BAC5B,EAAEF,UAAUwF,mBAAmB7W,KAAK,SAAS,iCAAiC;0BAChF,EAAEqR,UAAUU,aAAa/R,KAAK,SAAS,8BAA8B;oCAC3D,EAAEqR,UAAUyF,cAAc9W,KAAK,SAAS,4BAA4B;uCACjE,EAAE0W,cAAc;;YAE3C,EAAErF,UAAUjT,SAAS,mBAAmBiT,SAAS0F,QAAQ,GAAG,0BAA0B1F,SAAS0F,QAAQ,GAAG,GAAG;;YAE7G,EAAE1F,UAAUpD,QAAQ,iCAAiCoD,SAASpD,KAAK,GAAG,GAAG;;;;;;;;;;;;;;;;;;;;8BAoBvD,EAAEyI,cAAc;;;;;;yBAMrB,EAAEnF,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yBA0CT,EAAEA,OAAO;;;;;;;;;;gDAUc,EAAEmF,cAAc;;;;;;;;;kFASkB,EAAErF,UAAU2F,iBAAiB,mBAAmB;;;;gCAIlG,EAAEN,cAAc;;;;;;;;;;;YAWpC,CAAC;IACH;IAEQ1D,sBAAsBiE,IAAY,EAAU;QAClD,MAAMC,YAAYD,KAAKE,OAAO,CAAC;QAC/B,MAAMC,UAAUH,KAAKI,WAAW,CAAC,OAAO;QAExC,IAAIH,cAAc,CAAC,KAAKE,WAAWF,WAAW;YAC5C,MAAM,IAAI7K,MAAM;QAClB;QAEA,MAAMiL,YAAYL,KAAKpN,KAAK,CAACqN,WAAWE;QAExC,IAAI;YACF,OAAOlE,KAAKkD,SAAS,CAAClD,KAAKC,KAAK,CAACmE,aAAa,4BAA4B;QAC5E,EAAE,OAAM;YACN,OAAOpE,KAAKkD,SAAS,CAAClD,KAAKC,KAAK,CAACoE,IAAAA,sBAAU,EAACD,cAAc,uBAAuB;QACnF;IACF;IAEUE,yBAAyBP,IAAY,EAAU;QACrD,gCAAgC;QAChC,IAAIQ,YAAYR,KACbS,OAAO,CAAC,iCAAiC,IAAI,yCAAyC;SACtFA,OAAO,CAAC,WAAW,IAAI,aAAa;SACpCA,OAAO,CAAC,SAAS,KAAK,0BAA0B;SAChDA,OAAO,CAAC,SAAS,KAAK,yBAAyB;SAC/CA,OAAO,CAAC,QAAQ,IAAI,0BAA0B;SAC9ChC,IAAI;QAEP,gCAAgC;QAChC,MAAMwB,YAAYO,UAAUN,OAAO,CAAC;QACpC,MAAMC,UAAUK,UAAUJ,WAAW,CAAC,OAAO;QAE7C,IAAIH,cAAc,CAAC,KAAKE,WAAWF,WAAW;YAC5C,MAAM,IAAI7K,MAAM;QAClB;QAEAoL,YAAYA,UAAU5N,KAAK,CAACqN,WAAWE;QAEvC,sDAAsD;QACtDK,YAAYA,SACV,mDAAmD;SAClDC,OAAO,CAAC,UAAU,IACnB,qCAAqC;SACpCA,OAAO,CAAC,iBAAiB,QAC1B,uCAAuC;SACtCA,OAAO,CAAC,gBAAgB,OACzB,yCAAyC;SACxCA,OAAO,CAAC,+BAA+B,aACxC,gCAAgC;SAC/BA,OAAO,CAAC,QAAQ,KAChBA,OAAO,CAAC,sBAAsB;QAEjC,+BAA+B;QAC/B,MAAMC,QAAiC;YACrC,gCAAgC;YAChC;gBAAC;gBAA8B;aAAU;YACzC,oCAAoC;YACpC;gBAAC;gBAA2B;aAAO;YACnC,4BAA4B;YAC5B;gBAAC;gBAAwB;aAAO;YAChC,yBAAyB;YACzB;gBAAC;gBAAU;aAAI;YACf;gBAAC;gBAAU;aAAI;SAChB;QAEDA,MAAMnD,OAAO,CAAC,CAAC,CAACoD,OAAOC,YAAY;YACjCJ,YAAYA,UAAUC,OAAO,CAACE,OAAOC;QACvC;QAEA,2BAA2B;QAC3B,IAAI;YACF,MAAMC,SAAS5E,KAAKC,KAAK,CAACsE;YAC1B,OAAOvE,KAAKkD,SAAS,CAAC0B,SAAS,8BAA8B;QAC/D,EAAE,OAAOxO,OAAO;YACdxB,QAAQwB,KAAK,CAAC,qCAAqCmO,UAAU9W,SAAS,CAAC,GAAG;YAC1E,MAAM,IAAI0L,MAAM,CAAC,4BAA4B,EAAE/C,MAAMjN,OAAO,EAAE;QAChE;IACF;IAGA,MAAM0b,oBAAoBrc,MAAc,EAAE2V,QAAa,EAAE;QACvDvJ,QAAQC,GAAG,CAAC,CAAC,uEAAuE,EAAErM,QAAQ;QAC9FoM,QAAQC,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAEmL,KAAKkD,SAAS,CAAC/E,UAAU,MAAM;QAElE,IAAI,MAAM,IAAI,CAAChJ,qBAAqB,CAAC3M,SAAS;YAC5CoM,QAAQwB,KAAK,CAAC,CAAC,+BAA+B,EAAE5N,OAAO,0BAA0B,CAAC;YAClF,MAAM,IAAIS,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAqC;YACjD,GAAG;QACL;QAEAyL,QAAQC,GAAG,CAAC,CAAC,4EAA4E,EAAErM,QAAQ;QAEnG,IAAI;YACF,MAAMwW,SAAS,MAAM,IAAI,CAACuE,sBAAsB,CAACpF;YACjD,MAAMc,WAAW,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC9W,MAAM,CAAC;gBACzD+W,OAAO;gBACPC,UAAU;oBACR;wBACE7W,MAAM;wBACN8W,SAAS;oBACX;oBACA;wBAAE9W,MAAM;wBAAQ8W,SAASP;oBAAO;iBACjC;gBACDQ,aAAa;gBACbC,YAAY;gBACZC,iBAAiB;oBAAEtK,MAAM;gBAAc;YACzC;YAEA,MAAMuK,iBAAsBV,SAASW,OAAO,CAAC,EAAE,CAACzW,OAAO,CAACoW,OAAO;YAC/D,MAAMM,mBAAmB,IAAI,CAACC,qBAAqB,CAACH;YACpD,MAAMI,eAAeC,KAAKC,KAAK,CAACJ;YAEhC,IAAI,CAACE,aAAatV,IAAI,IAAI,CAACsV,aAAa1K,OAAO,IAAI,CAAC0K,aAAaoD,iBAAiB,EAAE;gBAClF,MAAM,IAAIhK,MAAM;YAClB;YAEA,MAAMM,iBAAiB0E,SAASnT,MAAM;YAEtC,oBAAoB;YACpB,MAAM8M,SAAS,MAAMnP,YAAE,CAACuX,WAAW,GAAGlW,OAAO,CAAC,OAAOmW;gBACnD,4CAA4C;gBAC5C,IAAI2E,iBAAiB;oBACnB,GAAI3G,SAASnT,MAAM,IAAImT,SAASnT,MAAM,GAAG,KAAK;wBAAEA,QAAQmT,SAASnT,MAAM;oBAAC,CAAC;oBACzE,GAAImT,SAASI,OAAO,IAAIJ,SAASI,OAAO,GAAG,KAAK;wBAAEtT,SAASkT,SAASI,OAAO;oBAAC,CAAC;gBAC/E;gBAEA,IAAIkC,OAAO0B,IAAI,CAAC2C,gBAAgBnZ,MAAM,GAAG,GAAG;oBAC1C,MAAMwU,IACHnU,WAAW,CAAC,SACZC,GAAG,CAAC6Y,gBACJjc,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;gBACZ;gBAEA,oBAAoB;gBACpB,MAAMqR,eAAe,MAAM8E,IACxBxW,UAAU,CAAC,0BACXC,MAAM,CAAC;oBACNa,MAAMsV,aAAatV,IAAI;oBACvB4K,SAAS0K,aAAa1K,OAAO;oBAC7BoE;oBACApC,WAAW0I,aAAa1I,SAAS;oBACjCvG,eAAeiP,aAAaoD,iBAAiB,CAACjT,QAAQ;oBACtDiB,cAAc4O,aAAaoD,iBAAiB,CAAC1S,OAAO;oBACpDY,YAAY0O,aAAaoD,iBAAiB,CAACzS,KAAK;oBAChDU,UAAU2O,aAAaoD,iBAAiB,CAACxS,GAAG;oBAC5CY,YAAYwO,aAAaoD,iBAAiB,CAAC3T,KAAK;oBAChDkK,eAAeqG,aAAarG,aAAa;oBACzCzE,YAAY,IAAIlJ;oBAChB9B,WAAWzB;oBACXmL,YAAY,IAAI5H;oBAChBD,YAAY,IAAIC;gBAClB,GACC/C,gBAAgB;gBAEnB,MAAMsS,kBAAkBxR,OAAOuR,aAAatR,QAAQ;gBAEpD,oBAAoB;gBACpB,KAAK,MAAM+P,QAAQiG,aAAaiC,KAAK,IAAI,EAAE,CAAE;oBAC3C,MAAMqB,WAAW,MAAMlD,IACpBxW,UAAU,CAAC,gCACXC,MAAM,CAAC;wBACNoQ,aAAasB;wBACb7Q,MAAMqP,KAAKrP,IAAI;wBACfwP,aAAaH,KAAKG,WAAW;wBAC7BC,WAAWJ,KAAKI,SAAS;oBAC3B,GACClR,gBAAgB;oBAEnB,MAAMsa,cAAcxZ,OAAOuZ,SAAStZ,QAAQ;oBAE5C,IAAI+P,KAAKgI,KAAK,IAAIvG,MAAMC,OAAO,CAAC1B,KAAKgI,KAAK,GAAG;wBAC3C,MAAM3B,IACHxW,UAAU,CAAC,sCACXC,MAAM,CAACkQ,KAAKgI,KAAK,CAAC1K,GAAG,CAACiD,CAAAA,OAAS,CAAA;gCAC9BC,SAASgJ;gCACT/I,SAAS;gCACT9P,MAAM4P,KAAK5P,IAAI;gCACf+P,UAAUH,KAAKG,QAAQ;gCACvBC,MAAMJ,KAAKI,IAAI,IAAI;gCACnBvK,UAAUmK,KAAKnK,QAAQ,IAAI;gCAC3BO,SAAS4J,KAAK5J,OAAO,IAAI;gCACzBC,OAAO2J,KAAK3J,KAAK,IAAI;gCACrBC,KAAK0J,KAAK1J,GAAG,IAAI;gCACjBW,OAAO+I,KAAK/I,KAAK,IAAI;4BACvB,CAAA,IACCtH,OAAO;oBACZ;gBACF;gBAEA,sBAAsB;gBACtB,IAAI+V,aAAamC,WAAW,EAAEvW,SAAS,GAAG;oBACxC,MAAMwU,IACHxW,UAAU,CAAC,sCACXC,MAAM,CAACmW,aAAamC,WAAW,CAAC9K,GAAG,CAAC,CAACwD,aAAgB,CAAA;4BACpDZ,aAAasB;4BACb7Q,MAAMmQ,WAAWnQ,IAAI;4BACrBoQ,QAAQD,WAAWC,MAAM;4BACzBC,iBAAiBF,WAAWE,eAAe;4BAC3CC,OAAOH,WAAWG,KAAK;wBACzB,CAAA,IACC/Q,OAAO;gBACZ;gBAEA,OAAOsR;YACT;YAEA,OAAO;gBACLpS,QAAQ;gBACRgB,MAAM;oBACJ8P,aAAalC;gBACf;YACF;QACF,EAAE,OAAO1B,OAAO;YACdxB,QAAQwB,KAAK,CAAC,gCAAgC;gBAC5CjN,SAASiN,MAAMjN,OAAO;gBACtByP,OAAOxC,MAAMwC,KAAK;gBAClBwH,OAAOjC;YACT;YACA,MAAM,IAAIlV,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;oBAAyCiN,MAAMjN,OAAO;iBAAC;YACnE,GAAG;QACL;IACF;IAKF,MAAM4b,uBAAuBvc,MAAc,EAAE;QAC3CoM,QAAQC,GAAG,CAAC,CAAC,iEAAiE,EAAErM,QAAQ;QAExF,8DAA8D;QAC9D,MAAMwc,eAAe,MAAM,IAAI,CAAC3O,0BAA0B,CAAC7N;QAC3DoM,QAAQC,GAAG,CAAC,0BAA0BmQ,aAAa7b,OAAO;QAE1D,IAAI,CAAC6b,aAAazO,cAAc,EAAE;YAChC3B,QAAQC,GAAG,CAAC;YACZ,OAAO;gBACL3L,QAAQ;gBACRgB,MAAM;oBACJoW,cAAc;oBACdnX,SAAS6b,aAAa7b,OAAO;gBAC/B;YACF;QACF;QAEA,kDAAkD;QAClD,MAAM4L,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,+BACX0B,QAAQ,CAAC,uBAAuB,QAAQ,aACxCvB,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SAAa,EACdF,KAAK,CAAC,QAAQ,KAAKmc,aAAazO,cAAc,CAACpM,EAAE,EACjDtB,KAAK,CAAC,eAAe,KAAKL,QAC1BQ,gBAAgB;QAEnB,IAAI,CAAC+L,UAAU;YACbH,QAAQC,GAAG,CAAC;YACZ,OAAO;gBACL3L,QAAQ;gBACRgB,MAAM;oBACJoW,cAAc;oBACdnX,SAAS;gBACX;YACF;QACF;QAEAyL,QAAQC,GAAG,CAAC,CAAC,oCAAoC,EAAEE,SAAStK,IAAI,CAAC,MAAM,EAAEsK,SAAS5K,EAAE,CAAC,CAAC,CAAC;QACvF,IAAI6a,aAAaxO,gBAAgB,GAAG,GAAG;YACrC5B,QAAQC,GAAG,CAAC,CAAC,GAAG,EAAEmQ,aAAaxO,gBAAgB,CAAC,qCAAqC,CAAC;QACxF;QAEA,kCAAkC;QAClC,MAAM2K,WAAW,MAAMxY,YAAE,CACtBC,UAAU,CAAC,qCACX0B,QAAQ,CAAC,2CAA2C,aAAa,QACjEvB,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCmB,OAAO,CAAC,eAAe,OACvBA,OAAO,CAAC,QAAQ,OAChBtB,OAAO;QAEV,qCAAqC;QACrC,MAAMoX,aAAa,IAAIC;QACvBF,SAASG,OAAO,CAAC,CAACC;YAChB,MAAMtO,MAAMsO,IAAItH,WAAW,CAACuH,WAAW,IAAI,0CAA0C;YACrF,IAAI,CAACJ,WAAWK,GAAG,CAACxO,MAAM;gBACxBmO,WAAWnV,GAAG,CAACgH,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,IAAI6G,OAAOsH,WAAWM,GAAG,CAACzO,MAAM0O,KAAK,CAACC,IAAMA,EAAEzX,EAAE,KAAKoX,IAAIpX,EAAE;YAC3D,IAAI,CAAC2P,MAAM;gBACTA,OAAO;oBACL3P,IAAIoX,IAAIpX,EAAE;oBACVM,MAAM8W,IAAI9W,IAAI;oBACdyP,WAAWqH,IAAIrH,SAAS;oBACxB4H,OAAO,EAAE;gBACX;gBACAV,WAAWM,GAAG,CAACzO,MAAM8C,KAAK+D;YAC5B;YAEA,iCAAiC;YACjC,IAAIyH,IAAIQ,SAAS,EAAE;gBACjBjI,KAAKgI,KAAK,CAAC/L,IAAI,CAAC;oBACdtL,MAAM8W,IAAIQ,SAAS;oBACnBtH,MAAM8G,IAAI9G,IAAI;oBACdD,UAAU1P,WAAWhB,OAAOyX,IAAI/G,QAAQ,EAAEzP,OAAO,CAAC,OAAO;gBAC3D;YACF;QACF;QAEA,4BAA4B;QAC5B,MAAMiX,QAAQvB,OAAOwB,WAAW,CAACb;QAEjC,sBAAsB;QACtB,MAAMc,cAAc,MAAMvZ,YAAE,CACzBC,UAAU,CAAC,2CACXG,MAAM,CAAC;YAAC;YAAQ;YAAU;YAAY;YAAqB;SAAU,EACrEF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCH,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM;gBACJoW,cAAc;gBACdnW,IAAI4K,SAAS5K,EAAE;gBACfiL,MAAML,SAASK,IAAI;gBACnB3K,MAAMsK,SAAStK,IAAI;gBACnB4M,WAAWtC,SAASsC,SAAS;gBAC7BqC,eAAe3E,SAAS2E,aAAa;gBACrCD,gBAAgB3O,WAAWhB,OAAOiL,SAAS0E,cAAc,EAAE1O,OAAO,CAAC,OAAO;gBAC1EkK,YAAYF,SAASE,UAAU;gBAC/BC,UAAUH,SAASG,QAAQ;gBAC3B7J,OAAO;oBACL6E,UAAUpF,WAAWhB,OAAOiL,SAAS7E,QAAQ,EAAEnF,OAAO,CAAC,OAAO;oBAC9D0F,SAAS3F,WAAWhB,OAAOiL,SAAStE,OAAO,EAAE1F,OAAO,CAAC,OAAO;oBAC5D2F,OAAO5F,WAAWhB,OAAOiL,SAASrE,KAAK,EAAE3F,OAAO,CAAC,OAAO;oBACxD4F,KAAK7F,WAAWhB,OAAOiL,SAASpE,GAAG,EAAE5F,OAAO,CAAC,OAAO;oBACpDyE,OAAO1E,WAAWhB,OAAOiL,SAASvF,KAAK,EAAEzE,OAAO,CAAC,OAAO;gBAC1D;gBACAiX;gBACAE,aAAaA,YAAY9K,GAAG,CAAC,CAACgL,MAAS,CAAA;wBACrC3X,MAAM2X,IAAI3X,IAAI;wBACdoQ,QAAQuH,IAAIvH,MAAM;wBAClBC,iBAAiBsH,IAAItH,eAAe;wBACpCC,OAAOqH,IAAIrH,KAAK;oBAClB,CAAA;YACF;QACF;IACF;IAEA,MAAMkK,wBAAwBzc,MAAc,EAAEmG,KAAU,EAAE;QACxD,MAAM,EAAE8C,IAAI,EAAE,GAAG9C;QACjB,IAAIuW,YAAkBzT,OAAO,IAAI1F,KAAK0F,QAAQ,IAAI1F;QAClD,gEAAgE;QAChE,IAAIoZ,MAAMD,UAAU3N,OAAO,KAAK;YAC5B2N,YAAY,IAAInZ;QACpB;QAEA6I,QAAQC,GAAG,CAAC,4DAA4DrM;QACxEoM,QAAQC,GAAG,CAAC,uBAAuBpD;QAEnC,MAAM3C,KAAK;QAEX,4DAA4D;QAC5D,MAAMsW,cAAcpd,MAAM8G,EAAE,CAAC2C,QAAQ/F,WAAWoD;QAEhD,4EAA4E;QAC5E,MAAMuW,gBAAgBD,YAAYpW,OAAO,CAAC,OAAO7G,GAAG,GAAGiH,MAAM;QAC7D,MAAMkW,cAAcF,YAAYlW,KAAK,CAAC,OAAO/G,GAAG,GAAGiH,MAAM;QAEzD,yEAAyE;QACzE,MAAMmW,UAAUF,cAAcG,kBAAkB,CAAC,SAAS;YAAEC,SAAS;QAAO,GAAGjE,WAAW;QAC1F5M,QAAQC,GAAG,CAAC,qBAAqB0Q;QAEjC,MAAM5Q,QAAQ3M,QAAQ8G,EAAE,CAACA,IAAIM,MAAM;QAEnC,8DAA8D;QAC9DwF,QAAQC,GAAG,CAAC;QACZ,MAAMmQ,eAAe,MAAM,IAAI,CAAC3O,0BAA0B,CAAC7N;QAC3DoM,QAAQC,GAAG,CAAC,0BAA0BmQ,aAAa7b,OAAO;QAE1D,IAAI,CAAC6b,aAAazO,cAAc,EAAE;YAC9B3B,QAAQC,GAAG,CAAC,oDAAoDrM;YAChE,OAAO;gBACHU,QAAQ;gBACRgB,MAAM;oBACFoW,cAAc;oBACdnX,SAAS6b,aAAa7b,OAAO;gBACjC;YACJ;QACJ;QAEA,4CAA4C;QAC5CyL,QAAQC,GAAG,CAAC,mDAAmDmQ,aAAazO,cAAc,CAACpM,EAAE;QAE7F,MAAMub,eAAe,MAAM/c,YAAE,CACxBC,UAAU,CAAC,+BACXG,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAF,KAAK,CAAC,QAAQ,KAAKmc,aAAazO,cAAc,CAACpM,EAAE,EACjDtB,KAAK,CAAC,eAAe,KAAKL,QAC1BQ,gBAAgB;QAErB,IAAI,CAAC0c,cAAc;YACf9Q,QAAQC,GAAG,CAAC,qDAAqDmQ,aAAazO,cAAc,CAACpM,EAAE;YAC/F,OAAO;gBACHjB,QAAQ;gBACRgB,MAAM;oBACFoW,cAAc;oBACdnX,SAAS;gBACb;YACJ;QACJ;QAEA,0CAA0C;QAC1C,IAAIwc,eAAe,SAAS,eAAe;QAC3C,IAAID,aAAarQ,OAAO,EAAE;YACtB,MAAMuQ,aAAa,MAAMjd,YAAE,CACtBC,UAAU,CAAC,kBACXG,MAAM,CAAC;gBAAC;aAAe,EACvBF,KAAK,CAAC,MAAM,KAAK6c,aAAarQ,OAAO,EACrCrM,gBAAgB;YAErB,IAAI4c,cAAcA,WAAWC,YAAY,EAAE;gBACvCF,eAAeC,WAAWC,YAAY;YAC1C;QACJ;QAEA,MAAM9Q,WAAW;YACb,GAAG2Q,YAAY;YACftQ,MAAMuQ;QACV;QAEA/Q,QAAQC,GAAG,CAAC,uCAAuC;YAC/C1K,IAAI4K,SAAS5K,EAAE;YACfM,MAAMsK,SAAStK,IAAI;YACnBwK,YAAYF,SAASE,UAAU;YAC/BG,MAAML,SAASK,IAAI;YACnB0Q,mBAAmBd,aAAaxO,gBAAgB;QACpD;QAEA,sGAAsG;QACtG5B,QAAQC,GAAG,CAAC,kDAAkD0Q;QAE9D,MAAMpE,WAAgB,MAAMxY,YAAE,CACzBC,UAAU,CAAC,qCACX0B,QAAQ,CAAC,2CAA2C,aAAa,QACjEA,QAAQ,CAAC,oBAAoB,CAACwC,OAC3BA,KACKiZ,KAAK,CAAC,aAAa,KAAK,QAAQ,oBAAoB;aACpDC,EAAE,CAAC,aAAa,KAAKxd,QAAQ,sBAAsB;aACnDwd,EAAE,CAAC,cAAc,MAAMX,eACvBW,EAAE,CAAC,cAAc,MAAMV,aAAa,wBAAwB;UAEpEhb,QAAQ,CAAC,2BAA2B,cAAc,QAClDvB,MAAM,CAAC;YACJkd,IAAAA,WAAG,CAAA,CAAC,oBAAoB,CAAC,CAACtW,EAAE,CAAC;YAC7B;YACA;YACA;YACA;YACAsW,IAAAA,WAAG,CAAA,CAAC,yBAAyB,CAAC,CAACtW,EAAE,CAAC;YAClCsW,IAAAA,WAAG,CAAA,CAAC,iCAAiC,CAAC,CAACtW,EAAE,CAAC;YAC1CsW,IAAAA,WAAG,CAAA,CAAC,yBAAyB,CAAC,CAACtW,EAAE,CAAC;YAClCsW,IAAAA,WAAG,CAAA,CAAC,iCAAiC,CAAC,CAACtW,EAAE,CAAC;YAC1CsW,IAAAA,WAAG,CAAA,CAAC,+BAA+B,CAAC,CAACtW,EAAE,CAAC;YACxCsW,IAAAA,WAAG,CAAA,CAAC,2BAA2B,CAAC,CAACtW,EAAE,CAAC;YACpCsW,IAAAA,WAAG,CAAA,CAAC,uBAAuB,CAAC,CAACtW,EAAE,CAAC;YAChCsW,IAAAA,WAAG,CAAA,CAAC,mDAAmD,CAAC,CAACtW,EAAE,CAAC;SAC/D,EACA9G,KAAK,CAAC,iBAAiB,KAAKkM,SAAS5K,EAAE,EACvCtB,KAAK,CAAC,iBAAiB,KAAK0c,SAC5Bja,OAAO,CAAC,cAAc,OACtBA,OAAO,CAAC,eAAe,OACvBA,OAAO,CAAC,QAAQ,OAChBtB,OAAO;QAEZ4K,QAAQC,GAAG,CAAC,CAAC,eAAe,EAAEsM,SAASxV,MAAM,CAAC,uCAAuC,EAAE4Z,SAAS;QAEhG,MAAMW,eAAe,MAAMvd,YAAE,CACxBC,UAAU,CAAC,oBACX0B,QAAQ,CAAC,2BAA2B,cAAc,QAClDvB,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAF,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,CAACyG,KACJA,GAAG6W,GAAG,CAAC;gBACH7W,GAAG2W,IAAAA,WAAG,CAAA,CAAC,gBAAgB,CAAC,EAAE,KAAKA,IAAAA,WAAG,CAAA,CAAC,KAAK,EAAEZ,cAAc,CAAC,CAAC;gBAC1D/V,GAAG,aAAa,MAAM;aACzB,GAEJhE,OAAO,CAAC,cAAc,OACtBtB,OAAO;QAEZ,MAAMoc,SAAS,CAAC3U;YACZ,OAAOzJ,MAAM8G,EAAE,CAAC2C,MAAM3C,IAAIuX,MAAM,CAAC;QACrC;QACA,MAAMC,SAAS,CAAC7U;YACZ,OAAOzJ,MAAM8G,EAAE,CAAC2C,MAAM3C,IAAIuX,MAAM,CAAC;QACrC;QAEA,MAAME,oBAAoBpF,SAAS/J,GAAG,CAAC,CAACmK,MAAc,CAAA;gBAClDpX,IAAIoX,IAAIpX,EAAE;gBACVqc,kBAAkBjF,IAAIiF,gBAAgB,IAAI;gBAC1C/b,MAAM8W,IAAI9W,IAAI;gBACdyP,WAAWqH,IAAI/N,QAAQ,GAAG8S,OAAO/E,IAAI/N,QAAQ,IAAI+N,IAAIrH,SAAS;gBAC9D6H,WAAWR,IAAIQ,SAAS;gBACxBvH,UAAU+G,IAAI/G,QAAQ;gBACtBC,MAAM8G,IAAI9G,IAAI;gBACdvK,UAAUqR,IAAIrR,QAAQ;gBACtBO,SAAS8Q,IAAI9Q,OAAO;gBACpBC,OAAO6Q,IAAI7Q,KAAK;gBAChBC,KAAK4Q,IAAI5Q,GAAG;gBACZW,OAAOiQ,IAAIjQ,KAAK;gBAChB4B,WAAWqO,IAAIrO,SAAS;YAC5B,CAAA;QAEA0B,QAAQC,GAAG,CAAC,yCAAyC0R,kBAAkB5a,MAAM;QAE7E,MAAM8a,wBAAwBP,aAAa9O,GAAG,CAAC,CAACmK,MAAS,CAAA;gBACrDpX,IAAIoX,IAAIpX,EAAE;gBACVM,MAAM8W,IAAI9W,IAAI;gBACdyP,WAAWoM,OAAO/E,IAAI/N,QAAQ;gBAC9BuO,WAAWR,IAAIQ,SAAS;gBACxBvH,UAAU+G,IAAI/G,QAAQ;gBACtBC,MAAM8G,IAAI9G,IAAI;gBACdvK,UAAUqR,IAAIrR,QAAQ;gBACtBO,SAAS8Q,IAAI9Q,OAAO;gBACpBC,OAAO6Q,IAAI7Q,KAAK;gBAChBC,KAAK4Q,IAAI5Q,GAAG;gBACZW,OAAOiQ,IAAIjQ,KAAK;gBAChB4B,WAAW;YACf,CAAA;QAEA,MAAMwT,WAAW;eAAIH;eAAsBE;SAAsB,CAACrP,GAAG,CAAC,CAACmK,MAAS,CAAA;gBAC5EpX,IAAIoX,IAAIpX,EAAE;gBACVM,MAAM8W,IAAI9W,IAAI;gBACdyP,WAAWqH,IAAIrH,SAAS;gBACxB6H,WAAWR,IAAIQ,SAAS;gBACxBvH,UAAU+G,IAAI/G,QAAQ;gBACtBC,MAAM8G,IAAI9G,IAAI;gBACdvK,UAAUqR,IAAIrR,QAAQ;gBACtBO,SAAS8Q,IAAI9Q,OAAO;gBACpBC,OAAO6Q,IAAI7Q,KAAK;gBAChBC,KAAK4Q,IAAI5Q,GAAG;gBACZW,OAAOiQ,IAAIjQ,KAAK;gBAChB4B,WAAWqO,KAAKrO,YAAY,OAAO;YACvC,CAAA;QAGA,MAAMyT,gBAAgBD,SAASE,IAAI,CAAC,CAACC,GAAGC;YACtC,OAAOD,EAAE3M,SAAS,CAAC6M,aAAa,CAACD,EAAE5M,SAAS;QAC9C;QAGA,uCAAuC;QACvC,MAAM8M,WAAW,IAAI3F;QACrB,KAAK,MAAME,OAAOoF,cAAe;YAC7B,0FAA0F;YAC1F,MAAMM,UAAU,AAAC1F,IAAYiF,gBAAgB,IAAIjF,IAAIpX,EAAE;YACvD,IAAI2P,OAAOkN,SAAStF,GAAG,CAACuF;YACxB,IAAI,CAACnN,MAAM;gBACPA,OAAO;oBACH3P,IAAI,AAACoX,IAAYiF,gBAAgB,IAAIjF,IAAIpX,EAAE;oBAC3CM,MAAM8W,IAAI9W,IAAI;oBACdyP,WAAWqH,IAAIrH,SAAS;oBACxB2H,WAAW;wBACP3R,UAAU;wBACVO,SAAS;wBACTC,OAAO;wBACPC,KAAK;wBACLW,OAAO;oBACX;oBACA4B,WAAWqO,IAAIrO,SAAS,GAAG,OAAO;oBAClC4O,OAAO,EAAE;gBACb;gBACAkF,SAAS/a,GAAG,CAACgb,SAASnN;YAC1B;YACA,IAAIyH,IAAIQ,SAAS,EAAE;gBACfjI,KAAKgI,KAAK,CAAC/L,IAAI,CAAC;oBACZtL,MAAM8W,IAAIQ,SAAS;oBACnBtH,MAAM8G,IAAI9G,IAAI;oBACdD,UAAU1P,WAAWhB,OAAOyX,IAAI/G,QAAQ,EAAEzP,OAAO,CAAC,OAAO;gBAC7D;gBACA+O,KAAK+H,SAAS,CAAC3R,QAAQ,IAAIpF,WAAWhB,OAAOyX,IAAIrR,QAAQ,EAAEnF,OAAO,CAAC,OAAO;gBAC1E+O,KAAK+H,SAAS,CAACpR,OAAO,IAAI3F,WAAWhB,OAAOyX,IAAI9Q,OAAO,EAAE1F,OAAO,CAAC,OAAO;gBACxE+O,KAAK+H,SAAS,CAACnR,KAAK,IAAI5F,WAAWhB,OAAOyX,IAAI7Q,KAAK,EAAE3F,OAAO,CAAC,OAAO;gBACpE+O,KAAK+H,SAAS,CAAClR,GAAG,IAAI7F,WAAWhB,OAAOyX,IAAI5Q,GAAG,EAAE5F,OAAO,CAAC,OAAO;gBAChE+O,KAAK+H,SAAS,CAACvQ,KAAK,IAAIxG,WAAWhB,OAAOyX,IAAIjQ,KAAK,EAAEvG,OAAO,CAAC,OAAO;YACxE;QACJ;QACA,MAAMiX,QAAQzG,MAAM2L,IAAI,CAACF,SAASpd,MAAM;QAExCgL,QAAQC,GAAG,CAAC,CAAC,kCAAkC,EAAEmN,MAAMrW,MAAM,EAAE;QAC/DiJ,QAAQC,GAAG,CAAC,wBAAwBmN,MAAM5K,GAAG,CAACwK,CAAAA,IAAM,CAAA;gBAAEzX,IAAIyX,EAAEzX,EAAE;gBAAEM,MAAMmX,EAAEnX,IAAI;gBAAEyI,WAAW0O,EAAE1O,SAAS;gBAAEiU,aAAavF,EAAEE,KAAK,CAACnW,MAAM;YAAC,CAAA;QAElI,oCAAoC;QACpC,MAAMuW,cAAc,MAAMvZ,YAAE,CACvBC,UAAU,CAAC,2CACXG,MAAM,CAAC;YAAC;YAAU;YAAY;YAAqB;SAAU,EAC7DF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCH,OAAO;QAEZ,OAAO;YACHd,QAAQ;YACRgB,MAAM;gBACFoW,cAAc;gBACdnW,IAAI4K,SAAS5K,EAAE;gBACfiL,MAAML,SAASK,IAAI;gBACnB3K,MAAMsK,SAAStK,IAAI;gBACnB4M,WAAWtC,SAASsC,SAAS;gBAC7BqC,eAAe3E,SAAS2E,aAAa;gBACrCD,gBAAgB1E,SAAS0E,cAAc;gBACvCxE,YAAYF,SAASE,UAAU;gBAC/BC,UAAUH,SAASG,QAAQ;gBAC3B7J,OAAO;oBACH6E,UAAU6E,SAAS7E,QAAQ;oBAC3BO,SAASsE,SAAStE,OAAO;oBACzBC,OAAOqE,SAASrE,KAAK;oBACrBC,KAAKoE,SAASpE,GAAG;oBACjBnB,OAAOuF,SAASvF,KAAK;gBACzB;gBACAwS;gBACAE,aAAaA,YAAY9K,GAAG,CAACgL,CAAAA,MAAQ,CAAA;wBACjC3X,MAAM2X,IAAI3X,IAAI;wBACdoQ,QAAQuH,IAAIvH,MAAM;wBAClBC,iBAAiBsH,IAAItH,eAAe;wBACpCC,OAAOqH,IAAIrH,KAAK;oBACpB,CAAA;YACJ;QACJ;IACJ;IAGE,MAAMqM,mCAAmC5e,MAAc,EAAE;QACvD,wDAAwD;QACxD,MAAMmM,QAAQ,IAAI5I;QAElB,yBAAyB;QACzB,MAAMgJ,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,+BACXG,MAAM,CAAC;YAAC;SAAO,EACfF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1ByC,OAAO,CAAC,gBAAgB,QACxBtC,gBAAgB;QAEjB,IAAI,CAAC+L,UAAU;YACb,OAAO;gBACL7L,QAAQ;gBACRgB,MAAM;oBACJoW,cAAc;gBAChB;YACF;QACF;QAEA,MAAM+G,YAAY1S,MAAM6Q,kBAAkB,CAAC,SAAS;YAAEC,SAAS;QAAO,GAAGjE,WAAW;QAEpF,MAAML,WAAW,MAAMxY,YAAE,CACtBC,UAAU,CAAC,qCACX0B,QAAQ,CAAC,2CAA2C,aAAa,QACjEA,QAAQ,CAAC,iBAAiB,WAAW,aACrCvB,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCtB,KAAK,CAAC,iBAAiB,KAAKwe,WAC5B/b,OAAO,CAAC,eAAe,OACvBA,OAAO,CAAC,QAAQ,OAChBtB,OAAO;QAER,MAAMoX,aAAa,IAAIC;QACvBF,SAASG,OAAO,CAAC,CAACC;YAChB,MAAMtO,MAAMsO,IAAItH,WAAW,CAACuH,WAAW,IAAI,0CAA0C;YACrF,IAAI,CAACJ,WAAWK,GAAG,CAACxO,MAAM;gBACxBmO,WAAWnV,GAAG,CAACgH,KAAK,EAAE;YACxB;YAEA,uCAAuC;YACvC,IAAI6G,OAAOsH,WAAWM,GAAG,CAACzO,MAAM0O,KAAK,CAACC,IAAMA,EAAEzX,EAAE,KAAKoX,IAAIpX,EAAE;YAC3D,IAAI,CAAC2P,MAAM;gBACTA,OAAO;oBACL3P,IAAIoX,IAAIpX,EAAE;oBACVM,MAAM8W,IAAI9W,IAAI;oBACdyP,WAAWqH,IAAIrH,SAAS;oBACxB4H,OAAO,EAAE;gBACX;gBACAV,WAAWM,GAAG,CAACzO,MAAM8C,KAAK+D;YAC5B;YAEA,iCAAiC;YACjC,IAAIyH,IAAIQ,SAAS,EAAE;gBACjBjI,KAAKgI,KAAK,CAAC/L,IAAI,CAAC;oBACdtL,MAAM8W,IAAIQ,SAAS;oBACnBtH,MAAM8G,IAAI9G,IAAI;oBACdD,UAAU+G,IAAI/G,QAAQ;gBACxB;YACF;QACF;QAEA,MAAMwH,QAAQvB,OAAOwB,WAAW,CAACb;QAEjC,OAAO;YACLlY,QAAQ;YACRgB,MAAM;gBACJoW,cAAc;gBACd0B;YACF;QACF;IACN;IAGA,MAAMsF,mBAAmBnd,EAAU,EAAE3B,MAAc,EAAE;QACnD,MAAMuM,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,0BACXC,KAAK,CAAC,MAAM,KAAKsB,IACjBtB,KAAK,CAAC,aAAa,KAAKL,QACxBO,MAAM,CAAC;YAAC;SAAK,EACbC,gBAAgB;QAEnB,IAAI,CAAC+L,UAAU;YACb,OAAO;gBACL7L,QAAQ;gBACRC,SAAS;YACX;QACF;QAEA,oBAAoB;QACpB,MAAMR,YAAE,CACLyD,UAAU,CAAC,0BACXvD,KAAK,CAAC,MAAM,KAAKsB,IACjBH,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;YACRf,SAAS;QACX;IACF;IAEA,MAAMoe,sBAAsBpd,EAAU,EAAE3B,MAAc,EAAE;QACtD,MAAMuM,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,mBACXC,KAAK,CAAC,MAAM,KAAKsB,IACjBtB,KAAK,CAAC,aAAa,KAAKL,QACxBO,MAAM,CAAC;YAAC;SAAK,EACbC,gBAAgB;QAEnB,IAAI,CAAC+L,UAAU;YACb,OAAO;gBACL7L,QAAQ;gBACRC,SAAS;YACX;QACF;QAEA,oBAAoB;QACpB,MAAMR,YAAE,CACLyD,UAAU,CAAC,mBACXvD,KAAK,CAAC,MAAM,KAAKsB,IACjBH,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;YACRf,SAAS;QACX;IACF;IAEA,yBAAyB;IAEzB,sBAAsB;IACtB,MAAMqe,kBAAkBhf,MAAc,EAAEgD,IAAS,EAAE;QACnD,MAAM,EAAE8O,OAAO,EAAE,GAAG9O;QACpB,IAAI,EAAEic,SAAS,EAAEjU,QAAQ,EAAEkU,UAAU,EAAE,GAAGlc;QAE1C,MAAMsD,KAAK;QAEX,MAAM6F,QAAQ3M,QAAQ8G,EAAE,CAACA,IAAIuX,MAAM,CAAC;QAEpC7S,WAAYA,WAAWxL,MAAM8G,EAAE,CAAC0E,UAAU1E,IAAIuX,MAAM,CAAC,yBAAyB1R;QAE9E,2EAA2E;QAC3E,IAAI2F,SAAS;YACX,MAAMqN,gBAAgB,MAAMhf,YAAE,CAC3BC,UAAU,CAAC,eACXG,MAAM,CAAC;gBAAC;aAAK,EACbF,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,WAAW,KAAKyR,SACtBzR,KAAK,CAACod,IAAAA,WAAG,CAAA,CAAC,cAAc,CAAC,EAAE,KAAKA,IAAAA,WAAG,CAAA,CAAC,KAAK,EAAEzS,SAAS,CAAC,CAAC,EACtDxK,gBAAgB;YAEjB,IAAI2e,eAAe;gBACjB,OAAO;oBACLze,QAAQ;oBACRC,SAAS;gBACX;YACF;QACJ;QAEA,IAAIuG,MAAW;YAAEQ,UAAU;YAAGQ,OAAO;YAAGD,SAAS;YAAGE,KAAK;YAAGW,OAAO;QAAE;QAErE,IAAI+I;QAEJ,IAAGC,SAAS;YACZ,2EAA2E;YAC3ED,OAAO,MAAM1R,YAAE,CACZC,UAAU,CAAC,2CACX0B,QAAQ,CAAC,qCAAqC,QAAQ,aACtDvB,MAAM,CAAC;gBACN;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,EACAF,KAAK,CAAC,aAAa,KAAKyR,SACxBtQ,OAAO;YAER,IAAI,CAACqQ,MAAM;gBACT,MAAM,IAAIpR,qBAAa,CAAC,kBAAkB2e,kBAAU,CAACC,SAAS;YAChE;YAEA,sDAAsD;YACtD,6DAA6D;YAC7DnY,MAAM2K,KAAKhK,MAAM,CAAC,CAACC,KAAUC;gBAC3B,MAAMiK,WAAW1Q,OAAOyG,KAAKiK,QAAQ,KAAK;gBAC1C,MAAMtK,WAAWpG,OAAOyG,KAAKL,QAAQ,KAAK;gBAC1C,MAAMO,UAAU3G,OAAOyG,KAAKE,OAAO,KAAK;gBACxC,MAAMC,QAAQ5G,OAAOyG,KAAKG,KAAK,KAAK;gBACpC,MAAMC,MAAM7G,OAAOyG,KAAKI,GAAG,KAAK;gBAChC,MAAMW,QAAQxH,OAAOyG,KAAKe,KAAK,KAAK;gBAEpC,yDAAyD;gBACzD,oGAAoG;gBACpG,+EAA+E;gBAC/E,MAAMwW,mBAAmBtN,WAAW;gBAEpC5F,QAAQC,GAAG,CAAC,CAAC,kBAAkB,EAAEtE,KAAK9F,IAAI,IAAI,MAAM,cAAc,EAAE+P,SAAS,uBAAuB,EAAEtK,SAAS,SAAS,EAAE4X,iBAAiB,wBAAwB,EAAE5X,WAAW4X,kBAAkB;gBAElMxX,IAAIJ,QAAQ,IAAIpF,WAAW,AAACoF,CAAAA,WAAW4X,gBAAe,EAAG/c,OAAO,CAAC;gBACjEuF,IAAII,KAAK,IAAI5F,WAAW,AAAC4F,CAAAA,QAAQoX,gBAAe,EAAG/c,OAAO,CAAC;gBAC3DuF,IAAIG,OAAO,IAAI3F,WAAW,AAAC2F,CAAAA,UAAUqX,gBAAe,EAAG/c,OAAO,CAAC;gBAC/DuF,IAAIK,GAAG,IAAI7F,WAAW,AAAC6F,CAAAA,MAAMmX,gBAAe,EAAG/c,OAAO,CAAC;gBACvDuF,IAAIgB,KAAK,IAAIxG,WAAW,AAACwG,CAAAA,QAAQwW,gBAAe,EAAG/c,OAAO,CAAC;gBAE3D,OAAOuF;YACT,GAAG;gBAAEJ,UAAU;gBAAGQ,OAAO;gBAAGD,SAAS;gBAAGE,KAAK;gBAAGW,OAAO;YAAE;QAC3D;QAEA,IAAG,CAACgJ,WAAWoN,YAAY;YACxBhY,MAAMgY,WAAWrX,MAAM,CAAC,CAACC,KAAUC;gBAClCD,IAAIJ,QAAQ,IAAIpF,WAAWhB,OAAOyG,KAAKL,QAAQ,EAAEnF,OAAO,CAAC;gBACzDuF,IAAII,KAAK,IAAI5F,WAAWhB,OAAOyG,KAAKG,KAAK,EAAE3F,OAAO,CAAC;gBACnDuF,IAAIG,OAAO,IAAI3F,WAAWhB,OAAOyG,KAAKE,OAAO,EAAE1F,OAAO,CAAC;gBACvDuF,IAAIK,GAAG,IAAI7F,WAAWhB,OAAOyG,KAAKI,GAAG,EAAE5F,OAAO,CAAC;gBAC/CuF,IAAIgB,KAAK,IAAIxG,WAAWhB,OAAOyG,KAAKe,KAAK,EAAEvG,OAAO,CAAC;gBACnD,OAAOuF;YACT,GAAG;gBAAEJ,UAAU;gBAAGQ,OAAO;gBAAGD,SAAS;gBAAGE,KAAK;gBAAGW,OAAO;YAAE;QAC3D;QAEE,MAAMyW,WAAgB;YACpBle,SAASrB;YACTwR,aAAa,AAACM,WAAWD,QAAQA,KAAK1O,MAAM,GAAG,IAAK0O,IAAI,CAAC,EAAE,EAAEL,cAAc;YAC3EM,SAASA,WAAW;YACpB7P,MAAM,AAAC6P,WAAWD,QAAQA,KAAK1O,MAAM,GAAG,IAAK0O,IAAI,CAAC,EAAE,EAAEoN,YAAYA;YAClEvX,UAAUR,IAAIQ,QAAQ;YACtBQ,OAAOhB,IAAIgB,KAAK;YAChBD,SAASf,IAAIe,OAAO;YACpBE,KAAKjB,IAAIiB,GAAG;YACZW,OAAO5B,IAAI4B,KAAK,IAAI;YACpBkC;YACAG,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QAClB;QAEA,MAAMic,YAAY,MAAMrf,YAAE,CACvBgB,UAAU,CAAC,eACXC,MAAM,CAACme,UACP/e,gBAAgB;QAEnB,MAAMif,cAAcne,OAAOke,UAAUje,QAAQ;QAE7C,IAAG2d,YAAY;YACb,MAAM/e,YAAE,CACLgB,UAAU,CAAC,qBACXC,MAAM,CAAC8d,WAAWtQ,GAAG,CAAC,CAACiD,OAAe,CAAA;oBACrCC,SAAS2N;oBACT1N,SAAS;oBACT9P,MAAM4P,KAAK5P,IAAI;oBACf+P,UAAUH,KAAKG,QAAQ;oBACvBC,MAAMJ,KAAKI,IAAI,IAAI;oBACnBvK,UAAUmK,KAAKnK,QAAQ,IAAI;oBAC3BO,SAAS4J,KAAK5J,OAAO,IAAI;oBACzBC,OAAO2J,KAAK3J,KAAK,IAAI;oBACrBC,KAAK0J,KAAK1J,GAAG,IAAI;oBACjBW,OAAO+I,KAAK/I,KAAK,IAAI;gBACvB,CAAA,IACCtH,OAAO;QACZ;QAGA,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;YACRf,SAAS;QACX;IACF;IAEA,MAAM+e,oBAAoB1f,MAAc,EAAEgD,IAAS,EAAE;QACnD,MAAM,EAAErB,EAAE,EAAE,GAAGqB;QAEf,QAAQ;QACR,MAAM7C,YAAE,CACLyD,UAAU,CAAC,eACXvD,KAAK,CAAC,MAAM,KAAKsB,IACjBtB,KAAK,CAAC,WAAW,KAAKL,QACtBwB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;YACRf,SAAS;QACX;IACF;IAEA,MAAMgf,oBAAoB3f,MAAc,EAAEmG,KAAU,EAAE;QACpD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QACjC,gEAAgE;QAChE,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAE/F,MAAM4S,QAAQ,MAAMrZ,YAAE,CACnBC,UAAU,CAAC,oBACX0B,QAAQ,CAAC,qCAAqC,QAAQ,aACtDvB,MAAM,CAAC;YACN;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,cAAc,MAAMsG,YAC1BtG,KAAK,CAAC,cAAc,MAAMwG,UAC1BxG,KAAK,CAAC,aAAa,UAAU,MAC7BmB,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM8X;QACR;IACF;IAEA,MAAMoG,6BAA6B5f,MAAc,EAAE;QACjD,MAAMmM,QAAQ,IAAI5I;QAClB,MAAMgJ,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,wBACXG,MAAM,CAAC;YAAC;SAAO,EACfF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B9L,KAAK,CAAC,cAAc,MAAM,MAC1ByC,OAAO,CAAC,gBAAgB,QACxBtC,gBAAgB;QAEnB,IAAI,CAAC+L,UAAU;YACb,OAAO;gBACL7L,QAAQ;gBACRgB,MAAM;oBACJoW,cAAc;gBAChB;YACF;QACF;QAEA,MAAM1E,YAAY,MAAMjT,YAAE,CACvBC,UAAU,CAAC,iCACX0B,QAAQ,CAAC,4CAA4C,iBAAiB,QACtEA,QAAQ,CAAC,kBAAkB,QAAQ,kBACnCA,QAAQ,CAAC,uBAAuB,QAAQ,qBACxCA,QAAQ,CAAC,+BAA+B,gBAAgB,kBACxDvB,MAAM,CAAC;YACN;YACA;YACA;YACA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,CACtC,2CAA2C;QAC3C,mCAAmC;SAClCH,OAAO;QAEV,wCAAwC;QACxC,MAAMoR,WAAWQ,UAAUvL,MAAM,CAAC,CAACC,KAAKC;YACtC,IAAI,CAACD,GAAG,CAACC,KAAKpG,EAAE,CAAC,EAAE;gBACjBmG,GAAG,CAACC,KAAKpG,EAAE,CAAC,GAAG;oBACbA,IAAIoG,KAAKpG,EAAE;oBACXyR,WAAW,EAAE;gBACf;YACF;YACAtL,GAAG,CAACC,KAAKpG,EAAE,CAAC,CAACyR,SAAS,CAAC7F,IAAI,CAACxF;YAC5B,OAAOD;QACT,GAAG,CAAC;QAEJ,8CAA8C;QAC9C,MAAM+X,gBAAgB5H,OAAO7W,MAAM,CAACwR;QAEpC,MAAMoF,uBAAuB6H,cAAcjR,GAAG,CAAC,CAACuE,UAAkB,CAAA;gBAChExR,IAAIwR,QAAQxR,EAAE;gBACdyR,WAAWD,QAAQC,SAAS,CAACxE,GAAG,CAAC,CAAC4E,WAAc,CAAA;wBAC9C7R,IAAI6R,SAAS0E,KAAK;wBAClBzE,aAAaD,SAASC,WAAW,IAAI;wBACrCxR,MAAMuR,SAASvR,IAAI,IAAIuR,SAAS2E,aAAa;wBAC7CE,cAAc7E,SAAS6E,YAAY,IAAI;wBACvClC,WAAW3C,SAAS2C,SAAS,IAAI;wBACjCmC,WAAW9E,SAAS8E,SAAS,IAAI;wBACjC3E,MAAMH,SAASG,IAAI;wBACnBC,MAAMJ,SAASI,IAAI;wBACnBC,KAAKL,SAASK,GAAG;wBACjBC,cAAcN,SAASM,YAAY;wBACnCvB,OAAOiB,SAASjB,KAAK;oBACvB,CAAA;YACF,CAAA;QAGA,OAAO;YACL7R,QAAQ;YACRgB,MAAM;gBACJoW,cAAc;gBACdnW,IAAI4K,SAAS5K,EAAE;gBACfiR,UAAUoF;YACZ;QACF;IACJ;IAEQ8H,mBAAmBC,IAAY,EAAU;QAC/C,MAAM,CAACxU,OAAOhC,SAASmC,QAAQ,GAAGqU,KAAKjW,KAAK,CAAC,KAAK8E,GAAG,CAACtN;QACtD,OAAOiK,QAAQhC,UAAU,KAAKmC,UAAU;IAC1C;IAEA,MAAMsU,kBAAkBC,WAAmB,EAAEC,GAAW,EAAE1d,MAAuB,EAAE;QACjF,MAAM2d,WAAW,OAAO3d,WAAW,WAAWF,WAAWE,UAAUA;QACnE,MAAM4d,cAAc,IAAI,CAACN,kBAAkB,CAACG;QAC5C,OAAOC,MAAMC,WAAWC,cAAc;IACxC;IAEA,MAAMC,0BAA0BrgB,MAAc,EAAEgD,IAAS,EAAE;QAC3D,MAAM,EAAEwO,WAAW,EAAE8O,mBAAmB,EAAEC,YAAY,EAAEC,YAAY,EAAEN,GAAG,EAAEO,MAAM,EAAE,GAAGzd;QAEtF,MAAMnB,OAAO,MAAM1B,YAAE,CAClBC,UAAU,CAAC,SACXG,MAAM,CAAC;YAAC;SAAS,EACjBF,KAAK,CAAC,MAAM,KAAKL,QACjBQ,gBAAgB;QAEnB,MAAMgC,SAASX,MAAMW,UAAU;QAE/B,MAAMgF,iBAAiB,MAAM,IAAI,CAACwY,iBAAiB,CAACO,cAAcjf,OAAO4e,MAAM5e,OAAOkB;QAEtF,MAAMke,aAAaD,OAAO7R,GAAG,CAAC,CAAC+R,QAAgB,CAAA;gBAC7CC,sBAAsBD,MAAMC,oBAAoB;gBAChDlZ,UAAUiZ,MAAMjZ,QAAQ;gBACxBlF,QAAQme,MAAMne,MAAM;gBACpBoR,MAAM+M,MAAM/M,IAAI;YAClB,CAAA;QAEA,MAAMiN,oBAAoB;YACxBxf,SAASrB;YACTwR;YACA8O;YACAJ;YACAK;YACA/Y;YACAgZ;QACF;QAEA,MAAMM,gBAAgB,MAAM3gB,YAAE,CAC3BgB,UAAU,CAAC,wBACXC,MAAM,CAACyf,mBACPrgB,gBAAgB;QAEnB,MAAMugB,kBAAkBzf,OAAOwf,cAAcvf,QAAQ;QAErD,MAAMpB,YAAE,CACLgB,UAAU,CAAC,+BACXC,MAAM,CAACsf,WAAW9R,GAAG,CAAC,CAAC+R,QAAgB,CAAA;gBAAEK,UAAUD;gBAAiB,GAAGJ,KAAK;YAAC,CAAA,IAC7Enf,OAAO;QAEV,OAAO;YACLd,QAAQ;YACRgB,MAAM,EAAE;QACV;IACF;IAEA,MAAMuf,yBAAyBjhB,MAAc,EAAEmG,KAAU,EAAE;QACzD,MAAM,EAAEoI,OAAO,CAAC,EAAEnI,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAC3C,MAAMqI,QAAQ;QACd,MAAMC,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,gEAAgE;QAChE,MAAM7H,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAG/F,IAAIsa,cAAc/gB,YAAE,CACjBC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5CA,SAAS,CAAC,mCAAmC,UAAU,2BACvDrf,QAAQ,CAAC,uCAAuC,iBAAiB,UACjEA,QAAQ,CAAC,8CAA8C,WAAW,6BAClEA,QAAQ,CAAC,kBAAkB,QAAQ,oBACnCA,QAAQ,CAAC,6BAA6B,cAAc,cACpDA,QAAQ,CAAC,+BAA+B,gBAAgB,qBACxDvB,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA,CAACuG,KACCA,GACGG,EAAE,CAACyG,KAAK,CAAC5G,GAAGsa,GAAG,CAAC,8BAChBC,QAAQ,GACRla,EAAE,CAAC;YACR;YACAsW,IAAAA,WAAG,CAAQ,CAAC,4DAA4D,CAAC,CAACtW,EAAE,CAAC;SAC9E;QAED+Z,cAAcA,YAAY7gB,KAAK,CAAC,eAAe,KAAKL;QAEpD,IAAIoG,cAAcC,UAAU;YAC1B6a,cAAcA,YAAY7gB,KAAK,CAAC,kBAAkB,MAAMsG,YAAYtG,KAAK,CAAC,kBAAkB,MAAMwG;QACpG;QAEAqa,cAAcA,YAAYnf,OAAO,CAAC;YAAC;YAAU;YAAY;SAAuB,EAC/EyM,KAAK,CAACA,OACNC,MAAM,CAACA,QACP3L,OAAO,CAAC,kBAAkB;QAE3B,MAAMwM,SAAS,MAAM4R,YACpB1f,OAAO;QAER,IAAI8f,0BAA+BnhB,YAAE,CACpCC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5C9gB,KAAK,CAAC,eAAe,KAAKL;QAE3B,IAAIoG,cAAcC,UAAU;YAC1Bib,0BAA0BA,wBAAwBjhB,KAAK,CAAC,kBAAkB,MAAMsG,YAAYtG,KAAK,CAAC,kBAAkB,MAAMwG;QAC5H;QAEAya,0BAA0BA,wBAAwB/gB,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACsa,QAAQ,GAAGpa,EAAE,CAAC;QAE7E,MAAMqa,qBAAqB,MAAMF,wBAChCG,uBAAuB;QAE1B,MAAMC,eAAeC,SAASH,mBAAmBpZ,KAAK,EAAE;QACxD,MAAMwZ,UAAUF,eAAenT,OAAOC;QAEtC,OAAO;YACL9N,QAAQ;YACRgB,MAAM4N,OAAOV,GAAG,CAAC,CAACmK,MAAS,CAAA;oBACzB9P,MAAM8P,IAAI9P,IAAI;oBACdoK,cAAc0F,IAAI1F,YAAY;oBAC9B7L,gBAAgBuR,IAAIvR,cAAc;oBAClC+Y,cAAcxH,IAAIwH,YAAY;oBAC9BsB,gBAAgBvgB,OAAOyX,IAAI8I,cAAc;oBACzCjV,MAAMmM,IAAInM,IAAI,IAAI;oBAClBkV,eAAe/I,IAAI+I,aAAa,IAAI;gBACtC,CAAA;YACAvS,YAAY;gBACVwS,eAAeL;gBACfM,cAAczT;gBACdC;gBACAyT,UAAUL;YACZ;QACF;IACF;IAEA,MAAMM,0BAA0BliB,MAAc,EAAmB;QAC/D,+DAA+D;QAC/D,MAAMmiB,aAAa,MAAMhiB,YAAE,CAC1BC,UAAU,CAAC,oBACXG,MAAM,CAAC;YAAC;YAAa;SAAW,EAChCF,KAAK,CAAC,WAAW,KAAKL,QACtB8C,OAAO,CAAC,aAAa,QACrB0L,KAAK,CAAC,GACNhO,gBAAgB;QAEjB,uEAAuE;QACvE,IAAI,CAAC2hB,cAAc,CAACA,WAAWC,QAAQ,EAAE;YACzC,OAAO;QACP;QAEA,IAAI9X,SAAS,GAAG,gDAAgD;QAChE,MAAM+X,aAAa,IAAI9e,KAAK4e,WAAWG,SAAS;QAChD,IAAIC,eAAe,IAAIhf,KAAK8e;QAC5BE,aAAatN,OAAO,CAACoN,WAAWnN,OAAO,KAAK;QAE5C,mEAAmE;QACnE,MAAO,KAAM;YACb,MAAMsN,kBAAkBD,aAAajW,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;YAEhE,MAAM2Y,eAAe,MAAMtiB,YAAE,CAC1BC,UAAU,CAAC,oBACXG,MAAM,CAAC,YACPF,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,aAAa,KAAKmiB,iBACxBhiB,gBAAgB;YAEnB,4EAA4E;YAC5E,IAAI,CAACiiB,gBAAgB,CAACA,aAAaL,QAAQ,EAAE;gBAC3C;YACF;YAEA9X;YACAiY,aAAatN,OAAO,CAACsN,aAAarN,OAAO,KAAK,IAAI,+CAA+C;QACjG;QAEA,OAAO5K;IACT;IAEA,MAAMoY,uCAAuC1iB,MAAc,EAAE;QAC3D,MAAM2iB,mBAAmB,MAAM,IAAI,CAACT,yBAAyB,CAACliB;QAE9D,OAAO;YACLU,QAAQ;YACRgB,MAAM;gBACJkhB,oBAAoBD;YACtB;QACF;IACF;IAEA,MAAME,wBAAwB7iB,MAAc,EAAEgD,IAAS,EAAE8f,KAA8F,EAAE;QACvJ,MAAM,EAAEtgB,MAAM,EAAEugB,EAAE,EAAE,GAAG/f;QAEvB,aAAa;QACboJ,QAAQC,GAAG,CAAC,kBAAkBrJ;QAC9BoJ,QAAQC,GAAG,CAAC,WAAW7J,QAAQ,SAAS,OAAOA;QAC/C4J,QAAQC,GAAG,CAAC,OAAO0W,IAAI,SAAS,OAAOA;QACvC3W,QAAQC,GAAG,CAAC,UAAU4L,OAAO0B,IAAI,CAACmJ,OAAO7T,MAAM,CAAC+T,CAAAA,MAAOF,KAAK,CAACE,IAAI;QAEjE,kCAAkC;QAClC,MAAMC,YAAY3gB,WAAWE;QAC7B,MAAM0gB,QAAQ5gB,WAAWygB;QAEzB,IAAIpG,MAAMsG,cAAcA,aAAa,KAAKtG,MAAMuG,UAAUA,SAAS,GAAG;YACpE,MAAM,IAAIziB,qBAAa,CAAC;gBACtBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAmG;YAC/G,GAAG;QACL;QAEA,IAAIwiB,aAA4B;QAChC,IAAIC,YAA2B;QAC/B,IAAIC,YAA2B;QAE/B,MAAMjf,YAAYC,MAAKC,IAAI,CAACgf,WAAW,MAAM,MAAM,WAAW,SAAStjB,OAAOuE,QAAQ,IAAI;QAE1F,IAAI,CAACC,IAAG+e,UAAU,CAACnf,YAAY;YAC7BI,IAAGgf,SAAS,CAACpf,WAAW;gBAAEO,WAAW;YAAK;QAC5C;QAEA,uCAAuC;QACvC,MAAM8e,eAAe;YAAC;YAAc;YAAa;SAAY;QAC7D,MAAMle,gBAAgB;QAEtB,KAAK,MAAMyd,OAAO;YAAC;YAAS;YAAQ;SAAO,CAAW;YACpD,MAAMlf,OAAOgf,KAAK,CAACE,IAAI;YAEvB,IAAIlf,MAAM;gBACR,IAAIA,KAAKE,IAAI,GAAG,KAAK,OAAO,MAAM;oBAChC,MAAM,IAAIvD,qBAAa,CAAC;wBAAEC,QAAQ;wBAAKC,SAAS;4BAAC;yBAAqC;oBAAC,GAAG;gBAC5F;gBAEA,IAAI,CAAC8iB,aAAavf,QAAQ,CAACJ,KAAKK,QAAQ,GAAG;oBACzC,MAAM,IAAI1D,qBAAa,CAAC;wBAAEC,QAAQ;wBAAKC,SAAS;4BAAC;yBAAkD;oBAAC,GAAG;gBACzG;gBAEA,MAAMiE,gBAAgBP,MAAKqf,OAAO,CAAC5f,KAAK6f,YAAY;gBACpD,MAAMC,WAAW,GAAGC,IAAAA,kBAAU,MAAKjf,eAAe;gBAClD,MAAMM,WAAWb,MAAKC,IAAI,CAACF,WAAWwf;gBAEtC,MAAMze,QAAQC,IAAAA,cAAK,EAACtB,KAAKuB,MAAM;gBAC/B,MAAMC,WAAW,MAAMH,MAAMG,QAAQ;gBAErC,IAAIA,SAASE,KAAK,GAAID,iBAAiBD,SAASjD,MAAM,GAAIkD,eAAe;oBACvE,MAAMue,eAAe,MAAM3e,MACxBS,MAAM,CAAC;wBACNJ,OAAOF,SAASE,KAAK,GAAIF,SAASjD,MAAM,GAAIkD,gBAAgBrC;wBAC5Db,QAAQiD,SAASjD,MAAM,GAAIiD,SAASE,KAAK,GAAID,gBAAgBrC;wBAC7DwC,KAAK;wBACLC,oBAAoB;oBACtB,GACCE,IAAI,CAAC;wBAAEC,SAAS;oBAAG,GACnBie,QAAQ;oBAEX,MAAMvf,IAAGC,QAAQ,CAACuf,SAAS,CAAC9e,UAAU4e;gBACxC,OAAO;oBACL,MAAMtf,IAAGC,QAAQ,CAACuf,SAAS,CAAC9e,UAAUpB,KAAKuB,MAAM;gBACnD;gBAEA,IAAI2d,QAAQ,SAASG,aAAaS;gBAClC,IAAIZ,QAAQ,QAAQI,YAAYQ;gBAChC,IAAIZ,QAAQ,QAAQK,YAAYO;YAClC;QACF;QAEA,MAAMK,iBAAiB,MAAM9jB,YAAE,CAC5BgB,UAAU,CAAC,eACXC,MAAM,CAAC;YACNC,SAASrB;YACTwC,QAAQygB;YACRF,IAAIG;YACJ/X,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QAClB,GACC/C,gBAAgB;QAEnB,MAAM0jB,eAAe5iB,OAAO2iB,eAAe1iB,QAAQ;QAEnD,iCAAiC;QACjC,IAAI+a,iBAAiB;YACnB,GAAI2G,aAAaA,YAAY,KAAK;gBAAEzgB,QAAQygB;YAAU,CAAC;YACvD,GAAIC,SAASA,QAAQ,KAAK;gBAAEzgB,SAASygB;YAAM,CAAC;QAC9C;QAEA,IAAIjL,OAAO0B,IAAI,CAAC2C,gBAAgBnZ,MAAM,GAAG,GAAG;YAC1C,MAAMhD,YAAE,CACLqD,WAAW,CAAC,SACZC,GAAG,CAAC6Y,gBACJjc,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;QACZ;QAEA,IAAI2hB,cAAcC,aAAaC,WAAW;YACxC,MAAMc,iBAAiB;gBACrB;oBAAEliB,MAAMkhB;oBAAYiB,UAAU;gBAAQ;gBACtC;oBAAEniB,MAAMmhB;oBAAWgB,UAAU;gBAAO;gBACpC;oBAAEniB,MAAMohB;oBAAWe,UAAU;gBAAO;aACrC,CAACnV,MAAM,CAACoV,CAAAA,MAAOA,IAAIpiB,IAAI,KAAK;YAE7B,MAAMqiB,QAAQC,GAAG,CACfJ,eAAevV,GAAG,CAACzJ,CAAAA,QACjBhF,YAAE,CAACgB,UAAU,CAAC,sBACXC,MAAM,CAAC;oBACNojB,eAAeN;oBACfO,YAAY;oBACZC,gBAAgBvf,MAAMif,QAAQ;oBAC9B9L,WAAWnT,MAAMlD,IAAI;gBACvB,GACCT,OAAO;QAGhB;QAEA,OAAO;YAAEd,QAAQ;YAAWgB,MAAM,EAAE;QAAC;IACvC;IAEA,MAAMijB,oCAAoC3kB,MAAc,EAAEgD,IAAS,EAAE;QACnE,MAAM,EAAE4hB,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,IAAI,EAAEC,YAAY,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,EAAEC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAEC,SAAS,EAAE,GAAGxiB;QAC3J,MAAMyiB,8BAA8B,MAAMtlB,YAAE,CAC3CgB,UAAU,CAAC,4BACXC,MAAM,CAAC;YACNC,SAASrB;YACT4kB,WAAWA;YACXC,OAAOA;YACPC,OAAOA;YACPC,SAASA;YACTC,MAAMA;YACNC,cAAcA;YACdC,aAAaA;YACbC,eAAeA;YACfC,cAAcA;YACdC,aAAaA;YACbC,YAAYA;YACZC,YAAYA;YACZC,WAAWA;YACXra,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QAClB,GACC/C,gBAAgB;QAEjB,OAAO;YACLE,QAAQ;YACRgB,MAAM,EAAE;QACV;IAEF;IAEA,MAAMgkB,uBAAuB1lB,MAAc,EAAEmG,KAAU,EAAE;QACvD,MAAM,EAAEoI,OAAO,CAAC,EAAEC,QAAQ,CAAC,EAAE,GAAGrI;QAEhC,MAAMsI,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;QAE5B,4BAA4B;QAC5B,MAAMmX,cAAc,MAAMxlB,YAAE,CACzBC,UAAU,CAAC,oBACX0B,QAAQ,CAAC,4BAA4B,oBAAoB,QACzDvB,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,aAAa,KAAKL,QACxB8C,OAAO,CAAC,gBAAgB,QACxB0L,KAAK,CAACA,OACNC,MAAM,CAACA,QACPjN,OAAO;QAEV,0BAA0B;QAC1B,MAAMokB,eAAe,MAAMzlB,YAAE,CAC1BC,UAAU,CAAC,kCACXG,MAAM,CAAC;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,cAAc,KAAKL,QACzB8C,OAAO,CAAC,iBAAiB,QACzB0L,KAAK,CAACA,OACNC,MAAM,CAACA,QACPjN,OAAO;QAEV,gCAAgC;QAChC,MAAMqkB,mBAAmB,MAAM1lB,YAAE,CAC9BC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACsa,QAAQ,GAAGpa,EAAE,CAAC,UAC3B3G,gBAAgB;QAEnB,2BAA2B;QAC3B,MAAMslB,uBAAuBH,YAAY9d,MAAM,CAAC,CAACC,KAAUie;YACzD,4CAA4C;YAC5C,IAAIC,iBAAsBle,IAAIqR,IAAI,CAAC,CAAC8M,OAAcA,KAAKtkB,EAAE,KAAKokB,WAAWpkB,EAAE;YAE3E,4EAA4E;YAC5E,IAAI,CAACqkB,gBAAgB;gBACnBA,iBAAiB;oBACfrkB,IAAIokB,WAAWpkB,EAAE;oBACjBiL,MAAM;oBACNpK,QAAQujB,WAAWvjB,MAAM;oBACzBugB,IAAIgD,WAAWhD,EAAE;oBACjB5X,YAAY4a,WAAW5a,UAAU;oBACjC+a,QAAQ;wBACNC,OAAO;wBACPC,MAAM;wBACNC,MAAM;oBACR;gBACF;gBACAve,IAAIyF,IAAI,CAACyY;YACX;YAEA,MAAMM,iBAAiBC,QAAQC,GAAG,CAACC,UAAU,GAAG,oBAAoBzmB,SAAS;YAE7E,qCAAqC;YACrC,IAAI+lB,WAAWrB,cAAc,KAAK,SAAS;gBACzCsB,eAAeE,MAAM,CAACC,KAAK,GAAGG,iBAAiBP,WAAWzN,SAAS;YACrE,OAAO,IAAIyN,WAAWrB,cAAc,KAAK,QAAQ;gBAC/CsB,eAAeE,MAAM,CAACE,IAAI,GAAGE,iBAAiBP,WAAWzN,SAAS;YACpE,OAAO,IAAIyN,WAAWrB,cAAc,KAAK,QAAQ;gBAC/CsB,eAAeE,MAAM,CAACG,IAAI,GAAGC,iBAAiBP,WAAWzN,SAAS;YACpE;YAEA,OAAOxQ;QACT,GAAG,EAAE;QAEL,yBAAyB;QACzB,MAAM4e,wBAAwBd,aAAa/d,MAAM,CAAC,CAACC,KAAU6e;YAC3D,0CAA0C;YAC1C,IAAIC,kBAAuB9e,IAAIqR,IAAI,CAAC,CAAC8M,OAAcA,KAAK9a,UAAU,KAAKwb,YAAYxb,UAAU;YAE7F,wEAAwE;YACxE,IAAI,CAACyb,iBAAiB;gBACpBA,kBAAkB;oBAChBjlB,IAAIglB,YAAYtlB,OAAO;oBACvBuL,MAAM;oBACNgY,WAAW+B,YAAY/B,SAAS;oBAChCC,OAAO8B,YAAY9B,KAAK;oBACxBC,OAAO6B,YAAY7B,KAAK;oBACxBC,SAAS4B,YAAY5B,OAAO;oBAC5BC,MAAM2B,YAAY3B,IAAI;oBACtBC,cAAc0B,YAAY1B,YAAY;oBACtCC,aAAayB,YAAYzB,WAAW;oBACpCC,eAAewB,YAAYxB,aAAa;oBACxCC,cAAcuB,YAAYvB,YAAY;oBACtCC,aAAasB,YAAYtB,WAAW;oBACpCC,YAAYqB,YAAYrB,UAAU;oBAClCC,YAAYoB,YAAYpB,UAAU;oBAClCC,WAAWmB,YAAYnB,SAAS;oBAChCra,YAAYwb,YAAYxb,UAAU;gBACpC;gBACArD,IAAIyF,IAAI,CAACqZ;YACX;YAEA,OAAO9e;QACT,GAAG,EAAE;QAEL,sDAAsD;QACtD,MAAM+e,UAAU;eAAIf;eAAyBY;SAAsB;QAEnE,+DAA+D;QAC/DG,QAAQzI,IAAI,CAAC,CAACC,GAAGC,IAAM,IAAI/a,KAAK+a,EAAEnT,UAAU,EAAE4D,OAAO,KAAK,IAAIxL,KAAK8a,EAAElT,UAAU,EAAE4D,OAAO;QAExF,OAAO;YACLrO,QAAQ;YACRgB,MAAMmlB;YACNtX,YAAY;gBACVhB;gBACAC;gBACApG,OAAO9G,OAAOukB,kBAAkBzd;YAClC;QACF;IACF;IAGA,MAAM0e,uBAAuB9mB,MAAc,EAAmB;QAC5D,MAAMmiB,aAAa,MAAMhiB,YAAE,CACxBC,UAAU,CAAC,oBACXG,MAAM,CAAC;YAAC;YAAa;SAAW,EAChCF,KAAK,CAAC,WAAW,KAAKL,QACtB8C,OAAO,CAAC,aAAa,QACrB0L,KAAK,CAAC,GACNhO,gBAAgB;QAEnB,IAAI,CAAC2hB,cAAc,CAACA,WAAWC,QAAQ,EAAE,OAAO;QAEhD,IAAI9X,SAAS;QACb,MAAM+X,aAAa,IAAI9e,KAAK4e,WAAWG,SAAS;QAChD,IAAIC,eAAe,IAAIhf,KAAK8e;QAC5BE,aAAatN,OAAO,CAACoN,WAAWnN,OAAO,KAAK;QAE5C,MAAO,KAAM;YACX,MAAMsN,kBAAkBD,aAAajW,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;YAChE,MAAM2Y,eAAe,MAAMtiB,YAAE,CAC1BC,UAAU,CAAC,oBACXG,MAAM,CAAC,YACPF,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,aAAa,KAAKmiB,iBACxBhiB,gBAAgB;YAEnB,IAAI,CAACiiB,gBAAgB,CAACA,aAAaL,QAAQ,EAAE;YAE7C9X;YACAiY,aAAatN,OAAO,CAACsN,aAAarN,OAAO,KAAK;QAChD;QAEA,OAAO5K;IACT;IAEA,MAAMyc,sBAAsB/mB,MAAc,EAAmB;QAC3D,4DAA4D;QAC5D,MAAM6C,QAAQ,MAAM1C,YAAE,CACnBC,UAAU,CAAC,oBACXG,MAAM,CAAC,aACPF,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,KAAK,MACvByC,OAAO,CAAC,aAAa,OACrBtB,OAAO;QAEV,IAAI,CAACqB,MAAMM,MAAM,EAAE,OAAO;QAE1B,IAAI6jB,YAAY;QAChB,IAAIC,gBAAgB;QAEpB,IAAK,IAAIC,IAAI,GAAGA,IAAIrkB,MAAMM,MAAM,EAAE+jB,IAAK;YACrC,MAAMC,cAAc,IAAI5jB,KAAKV,KAAK,CAACqkB,EAAE,CAAC5E,SAAS;YAC/C,MAAMC,eAAe,IAAIhf,KAAKV,KAAK,CAACqkB,IAAI,EAAE,CAAC5E,SAAS;YACpD,MAAM8E,aAAa,AAACD,CAAAA,YAAYpY,OAAO,KAAKwT,aAAaxT,OAAO,EAAC,IAAM,CAAA,OAAO,KAAK,KAAK,EAAC;YAEzF,IAAIqY,eAAe,GAAG;gBACpB,oBAAoB;gBACpBH;YACF,OAAO;gBACL,gCAAgC;gBAChCD,YAAYjiB,KAAKiD,GAAG,CAACgf,WAAWC;gBAChCA,gBAAgB;YAClB;QACF;QAEA,4CAA4C;QAC5CD,YAAYjiB,KAAKiD,GAAG,CAACgf,WAAWC;QAEhC,OAAOD;IACT;IAEA,MAAMK,wBAAwBrnB,MAAc,EAAEmnB,WAAiB,EAAmB;QAChF,6DAA6D;QAC7D,MAAMG,cAAc,IAAI/jB,KAAK4jB;QAC7BG,YAAYrS,OAAO,CAACkS,YAAYjS,OAAO,KAAK,AAACiS,CAAAA,YAAYI,MAAM,KAAK,CAAA,IAAK,IAAI,gBAAgB;QAC7F,MAAMC,YAAY,IAAIjkB,KAAK+jB;QAC3BE,UAAUvS,OAAO,CAACqS,YAAYpS,OAAO,KAAK,IAAI,UAAU;QAExD,MAAMuS,eAAeH,YAAYhb,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5D,MAAM4d,aAAaF,UAAUlb,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;QAExD,0CAA0C;QAC1C,MAAM6d,iBAAiB,MAAMxnB,YAAE,CAC5BC,UAAU,CAAC,oBACXG,MAAM,CAACkd,IAAAA,WAAG,CAAQ,CAAC,yBAAyB,CAAC,CAACtW,EAAE,CAAC,UACjD9G,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,aAAa,MAAMonB,cACzBpnB,KAAK,CAAC,aAAa,MAAMqnB,YACzBrnB,KAAK,CAAC,YAAY,KAAK,MACvBG,gBAAgB;QAEnB,MAAMonB,kBAAkBD,gBAAgBja,SAAS;QACjD,MAAMma,kBAAkB,GAAG,0BAA0B;QAErD,sBAAsB;QACtB,OAAO9iB,KAAK+iB,KAAK,CAAC,AAACF,kBAAkBC,kBAAmB;IAC1D;IAEA,MAAME,yBAAyB/nB,MAAc,EAAEmnB,WAAiB,EAAmB;QACjF,sCAAsC;QACtC,MAAMa,eAAe,IAAIzkB,KAAK4jB,YAAYc,WAAW,IAAId,YAAYe,QAAQ,IAAI;QACjF,MAAMC,aAAa,IAAI5kB,KAAK4jB,YAAYc,WAAW,IAAId,YAAYe,QAAQ,KAAK,GAAG;QAEnF,MAAMT,eAAeO,aAAa1b,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7D,MAAM4d,aAAaS,WAAW7b,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;QAEzD,uCAAuC;QACvC,MAAM6d,iBAAiB,MAAMxnB,YAAE,CAC5BC,UAAU,CAAC,oBACXG,MAAM,CAACkd,IAAAA,WAAG,CAAQ,CAAC,yBAAyB,CAAC,CAACtW,EAAE,CAAC,UACjD9G,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,aAAa,MAAMonB,cACzBpnB,KAAK,CAAC,aAAa,MAAMqnB,YACzBrnB,KAAK,CAAC,YAAY,KAAK,MACvBG,gBAAgB;QAEnB,MAAMonB,kBAAkBD,gBAAgBja,SAAS;QACjD,MAAM0a,mBAAmBD,WAAWjT,OAAO,IAAI,wBAAwB;QAEvE,sBAAsB;QACtB,OAAOnQ,KAAK+iB,KAAK,CAAC,AAACF,kBAAkBQ,mBAAoB;IAC3D;IAEA,MAAMC,sBAAsBroB,MAAc,EAAE;QACxC,qCAAqC;QACrC,MAAMuM,WAAW,MAAMpM,YAAE,CACtBC,UAAU,CAAC,0BACXC,KAAK,CAAC,aAAa,KAAKL,QACxBK,KAAK,CAAC,YAAY,MAAM,MACxBE,MAAM,CAAC;YAAC;SAAK,EACbC,gBAAgB;QAEnB,IAAI,CAAC+L,UAAU;YACb,OAAO;gBACL7L,QAAQ;gBACRgB,MAAM;oBACJ0I,QAAQ,EAAE;oBACVO,cAAc;oBACdC,gBAAgB;oBAChBC,cAAc;gBAChB;YACF;QACF;QAEA,sEAAsE;QACtE,MAAMyd,gBAAgB,MAAMnoB,YAAE,CAC3BC,UAAU,CAAC,gCACXC,KAAK,CAAC,eAAe,KAAKkM,SAAS5K,EAAE,EACrCpB,MAAM,CAAC;YAAC;SAAc,EACtBwB,OAAO,CAAC,eACRxB,MAAM,CAAC,CAACuG,KAAOA,GAAGG,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC,eACpC3F,OAAO;QAQV,MAAM+mB,kBAA6C,CAAC;QACnDD,cAAsCxP,OAAO,CAAC,CAACxH;YAC9CiX,eAAe,CAACjX,KAAKG,WAAW,CAAC,GAAGH,KAAKkX,UAAU;QACrD;QAEA,6CAA6C;QAC7C,MAAMC,aAAwC;YAC5CC,QAAQ;YACRC,SAAS;YACTC,WAAW;YACXC,UAAU;YACVC,QAAQ;YACRC,UAAU;YACVC,QAAQ;QACV;QAEA,wDAAwD;QACxD,MAAM1B,cAAc9nB,QAAQgH,OAAO,CAAC,WAAWI,MAAM;QACrD,MAAM4gB,YAAYhoB,QAAQkH,KAAK,CAAC,WAAWE,MAAM;QAEjD,uCAAuC;QACvC,MAAMqiB,aAAa,MAAM9oB,YAAE,CACxBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMinB,aACxBjnB,KAAK,CAAC,YAAY,MAAMmnB,WACxBnnB,KAAK,CAAC,WAAW,UAAU,MAC3BE,MAAM,CAAC,CAACuG,KAAO;gBAAC2W,IAAAA,WAAG,CAAQ,CAAC,cAAc,CAAC,CAACtW,EAAE,CAAC;aAAO,EACtD3F,OAAO;QAEV,2BAA2B;QAC3B,MAAM0nB,iBAAwB,EAAE;QAChC,IAAK,IAAIhC,IAAI,GAAGA,IAAI,GAAGA,IAAK;YAC1B,MAAMC,cAAc3nB,MAAM8nB,aAAa6B,GAAG,CAACjC,GAAG,OAAOtgB,MAAM;YAC3D,MAAMiY,YAAYrf,MAAM2nB,aAAatJ,MAAM,CAAC,QAAQ7E,WAAW;YAC/D,MAAMoQ,YAAYX,UAAU,CAAC5J,UAAqC;YAClE,MAAMzW,QAAQmgB,eAAe,CAAC1J,UAAU,IAAI;YAC5C,MAAMnU,YAAYue,WAAWha,MAAM,CAAC,CAACqC,OACnC9R,MAAM8R,KAAK7G,GAAG,EAAE4e,MAAM,CAAClC,aAAa,QACpChkB,MAAM;YAER+lB,eAAe3b,IAAI,CAAC;gBAClB9C,KAAK2e;gBACL1e;gBACAtC;YACF;QACF;QAEA,oBAAoB;QACpB,MAAMkhB,gBAAgBJ,eAAerhB,MAAM,CAAC,CAACC,KAAa2C,MAAa3C,MAAM2C,IAAIC,SAAS,EAAE;QAC5F,MAAM6e,qBAAqBL,eAAerhB,MAAM,CAAC,CAACC,KAAK2C,MAAQ3C,MAAM2C,IAAIrC,KAAK,EAAE;QAChF,MAAMohB,cAAcD,qBAAqB,IAAI,AAACD,gBAAgBC,qBAAsB,MAAM;QAC1F,MAAME,cAAcP,eAAeja,MAAM,CAAC,CAACxE,MAAQA,IAAIC,SAAS,KAAKD,IAAIrC,KAAK,EAAEjF,MAAM;QAEtF,OAAO;YACLzC,QAAQ;YACRgB,MAAM;gBACJ0I,QAAQ8e;gBACRve,cAAcrJ,OAAOkoB,YAAYjnB,OAAO,CAAC;gBACzCqI,gBAAgB0e;gBAChBze,cAAc4e;YAChB;QACF;IACF;IAEJ,iFAAiF;IACjF,MAAMC,8BAA8B1pB,MAAc,EAAE;QAClD,MAAMsnB,cAAc9nB,QAAQgH,OAAO,CAAC,WAAWI,MAAM;QACrD,MAAM4gB,YAAYhoB,QAAQkH,KAAK,CAAC,WAAWE,MAAM;QAEjDwF,QAAQC,GAAG,CAAC,iEAAiErM;QAC7EoM,QAAQC,GAAG,CAAC,eAAeib,YAAYhb,WAAW,IAAI,OAAOkb,UAAUlb,WAAW;QAElF,IAAI;YACF,yCAAyC;YACzC,MAAMqd,gBAAgB,MAAM,IAAI,CAACtB,qBAAqB,CAACroB;YAEvD,sBAAsB;YACtB,MAAM4pB,cAAc,MAAMzpB,YAAE,CACzBC,UAAU,CAAC,6BACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMinB,aACxBjnB,KAAK,CAAC,YAAY,MAAMmnB,WACxBjnB,MAAM,CAAC;gBACNkd,IAAAA,WAAG,CAAA,CAAC,cAAc,CAAC,CAACtW,EAAE,CAAC;gBACvBsW,IAAAA,WAAG,CAAA,CAAC,QAAQ,CAAC,CAACtW,EAAE,CAAC;gBACjBsW,IAAAA,WAAG,CAAA,CAAC,aAAa,CAAC,CAACtW,EAAE,CAAC;aACvB,EACApF,OAAO,CAAC0b,IAAAA,WAAG,CAAA,CAAC,cAAc,CAAC,EAC3Bjc,OAAO;YAEV,mBAAmB;YACnB,MAAMqoB,YAAY,MAAM1pB,YAAE,CACvBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMinB,aACxBjnB,KAAK,CAAC,YAAY,MAAMmnB,WACxBjnB,MAAM,CAAC;gBACNkd,IAAAA,WAAG,CAAA,CAAC,cAAc,CAAC,CAACtW,EAAE,CAAC;gBACvBsW,IAAAA,WAAG,CAAA,CAAC,aAAa,CAAC,CAACtW,EAAE,CAAC;aACvB,EACApF,OAAO,CAAC0b,IAAAA,WAAG,CAAA,CAAC,cAAc,CAAC,EAC3Bjc,OAAO;YAEN,0EAA0E;YAC1E,MAAMsoB,YAAyB,EAAE;YACjC,IAAK,IAAI5C,IAAI,GAAGA,IAAI,GAAGA,IAAK;gBAC1B,MAAMC,cAAc3nB,MAAM8nB,aAAa6B,GAAG,CAACjC,GAAG;gBAC9C4C,UAAUvc,IAAI,CAAC;oBACb9C,KAAK0c,YAAYtJ,MAAM,CAAC;oBACxBtS,OAAO,IAAIxG,KAAKC,MAAM,KAAK;oBAC3Bc,SAASf,KAAKyG,KAAK,CAACzG,KAAKC,MAAM,KAAK,MAAM,GAAG,UAAU;gBACzD;YACF;YAEA,iCAAiC;YACjC,MAAMyjB,aAAa;gBACjBC,QAAQ;gBACRC,SAAS;gBACTC,WAAW;gBACXC,UAAU;gBACVC,QAAQ;gBACRC,UAAU;gBACVC,QAAQ;YACV;YAEA,MAAMe,qBAA+C,EAAE;YACvD,IAAK,IAAI7C,IAAI,GAAGA,IAAI,GAAGA,IAAK;gBAC1B,MAAMC,cAAc3nB,MAAM8nB,aAAa6B,GAAG,CAACjC,GAAG;gBAC9C,MAAMrI,YAAYsI,YAAYtJ,MAAM,CAAC,QAAQ7E,WAAW;gBACxD,MAAMoQ,YAAYX,UAAU,CAAC5J,UAAU;gBACvC,MAAMmL,UAAU7C,YAAYtJ,MAAM,CAAC;gBAEnC,gCAAgC;gBAChC,MAAMoM,eAAeN,cAAcjoB,IAAI,CAAC0I,MAAM,CAAC+O,IAAI,CAAC+Q,CAAAA,IAAKA,EAAEzf,GAAG,KAAK2e,cAAc;oBAAE1e,WAAW;oBAAGtC,OAAO;gBAAE;gBAC1G,MAAM+hB,aAAaP,YAAYzQ,IAAI,CAAC+Q,CAAAA,IAAK1qB,MAAM0qB,EAAEzf,GAAG,EAAEoT,MAAM,CAAC,kBAAkBmM,YAAY;oBAAEtf,WAAW;oBAAG0f,iBAAiB;gBAAE;gBAC9H,MAAMC,WAAWR,UAAU1Q,IAAI,CAAC+Q,CAAAA,IAAK1qB,MAAM0qB,EAAEzf,GAAG,EAAEoT,MAAM,CAAC,kBAAkBmM,YAAY;oBAAEM,gBAAgB;gBAAE;gBAC3G,MAAMC,WAAWT,UAAU3Q,IAAI,CAAC+Q,CAAAA,IAAKA,EAAEzf,GAAG,KAAKuf,YAAY;oBAAEze,OAAO;oBAAGzF,SAAS;gBAAE;gBAElFikB,mBAAmBxc,IAAI,CAAC;oBACtB9C,KAAK2e;oBACLngB,MAAM+gB;oBACNQ,WAAW;wBACT9f,WAAWuf,aAAavf,SAAS;wBACjCtC,OAAO6hB,aAAa7hB,KAAK;wBACzBqiB,YAAYR,aAAa7hB,KAAK,GAAG,IAAIrD,KAAK+iB,KAAK,CAAC,AAACmC,aAAavf,SAAS,GAAGuf,aAAa7hB,KAAK,GAAI,OAAO;oBACzG;oBACA+K,SAAS;wBACPzI,WAAWpJ,OAAO6oB,WAAWzf,SAAS,KAAK;wBAC3C0f,iBAAiB9oB,OAAO6oB,WAAWC,eAAe,KAAK;wBACvDM,QAAQ;wBACRD,YAAYnpB,OAAO6oB,WAAWzf,SAAS,IAAI,IAAI,MAAM;oBACvD;oBACA1D,OAAO;wBACLwB,UAAUlH,OAAO+oB,SAASC,cAAc,KAAK;wBAC7CI,QAAQ;wBACRD,YAAY1lB,KAAK4lB,GAAG,CAAC5lB,KAAK+iB,KAAK,CAAC,AAACxmB,CAAAA,OAAO+oB,SAASC,cAAc,KAAK,CAAA,IAAK,OAAO,MAAM;oBACxF;oBACAM,OAAO;wBACLrf,OAAOjK,OAAOipB,SAAShf,KAAK,KAAK;wBACjCzF,SAASxE,OAAOipB,SAASzkB,OAAO,KAAK;wBACrC4kB,QAAQ;wBACRD,YAAY1lB,KAAK4lB,GAAG,CAAC5lB,KAAK+iB,KAAK,CAAC,AAACxmB,CAAAA,OAAOipB,SAAShf,KAAK,KAAK,CAAA,IAAK,IAAI,MAAM;oBAC5E;gBACF;YACF;YAEA,+BAA+B;YAC/B,MAAMsf,YAAYd,mBAAmB5mB,MAAM;YAC3C,MAAM2nB,mBAAmBf,mBAAmB9a,MAAM,CAACib,CAAAA,IAAKA,EAAEM,SAAS,CAACC,UAAU,IAAI,IAAItnB,MAAM;YAC5F,MAAM4nB,iBAAiBhB,mBAAmB9a,MAAM,CAACib,CAAAA,IAAKA,EAAE/W,OAAO,CAACsX,UAAU,IAAI,KAAKtnB,MAAM;YACzF,MAAM6nB,eAAejB,mBAAmB9a,MAAM,CAACib,CAAAA,IAAKA,EAAEljB,KAAK,CAACyjB,UAAU,IAAI,IAAItnB,MAAM;YACpF,MAAM8nB,eAAelB,mBAAmB9a,MAAM,CAACib,CAAAA,IAAKA,EAAEU,KAAK,CAACH,UAAU,IAAI,IAAItnB,MAAM;YAEpF,OAAO;gBACLzC,QAAQ;gBACRgB,MAAM;oBACJ0I,QAAQ2f;oBACRmB,SAAS;wBACPV,WAAW;4BACT7f,cAAc5F,KAAK+iB,KAAK,CAAC,AAACgD,mBAAmBD,YAAa;4BAC1DM,gBAAgBL;wBAClB;wBACA3X,SAAS;4BACPxI,cAAc5F,KAAK+iB,KAAK,CAAC,AAACiD,iBAAiBF,YAAa;4BACxDM,gBAAgBJ;4BAChBvjB,gBAAgBuiB,mBAAmBliB,MAAM,CAAC,CAACX,KAAKgjB,IAAMhjB,MAAMgjB,EAAE/W,OAAO,CAACiX,eAAe,EAAE;wBACzF;wBACApjB,OAAO;4BACL2D,cAAc5F,KAAK+iB,KAAK,CAAC,AAACkD,eAAeH,YAAa;4BACtDM,gBAAgBH;4BAChBV,gBAAgBP,mBAAmBliB,MAAM,CAAC,CAACX,KAAKgjB,IAAMhjB,MAAMgjB,EAAEljB,KAAK,CAACwB,QAAQ,EAAE;wBAChF;wBACAoiB,OAAO;4BACLjgB,cAAc5F,KAAK+iB,KAAK,CAAC,AAACmD,eAAeJ,YAAa;4BACtDM,gBAAgBF;4BAChBG,eAAerB,mBAAmBliB,MAAM,CAAC,CAACX,KAAKgjB,IAAMhjB,MAAMgjB,EAAEU,KAAK,CAACrf,KAAK,EAAE,KAAKsf;4BAC/EQ,iBAAiBtB,mBAAmBliB,MAAM,CAAC,CAACX,KAAKgjB,IAAMhjB,MAAMgjB,EAAEU,KAAK,CAAC9kB,OAAO,EAAE,KAAK+kB;wBACrF;oBACF;gBACF;YACF;QAEF,EAAE,OAAOjd,OAAO;YACdxB,QAAQwB,KAAK,CAAC,mDAAmDA;YACjE,OAAO;gBACLlN,QAAQ;gBACRC,SAAS;gBACTe,MAAM;YACR;QACF;IACF;IAEF,MAAM4pB,gBAAgBtrB,MAAc,EAAE,CACtC;IAEA,MAAMurB,qBAAqBvrB,MAAc,EAAEmG,KAAU,EAAE;QACrD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAE/F,IAAI4kB,iBAAiBrrB,YAAE,CACtBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL;QACvB,IAAGoG,YAAY;YACbolB,iBAAiBA,eAChBnrB,KAAK,CAAC,cAAc,MAAMsG;QAC7B;QACA,IAAGN,UAAU;YACXmlB,iBAAiBA,eAChBnrB,KAAK,CAAC,cAAc,MAAMwG;QAC7B;QACA,MAAM4kB,YAAY,MAAMD,eACvBjrB,MAAM,CAAC;YACJ;YACA;YACA;SACH,EACAiB,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAM+pB;QACV;IAEF;IAEA,MAAMC,oBAAoB1rB,MAAc,EAAEmG,KAAU,EAAE;QACpD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE4I,MAAM,EAAE0c,SAAS,EAAE,GAAGxlB;QAEpD,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAE/F,MAAMglB,gBAAgBzrB,YAAE,CACvBC,UAAU,CAAC,sCACX+gB,SAAS,CAAC,gCAAgC,WAAW,gBACrDA,SAAS,CAAC,8CAA8C,WAAW,4BACnEA,SAAS,CAAC,kBAAkB,QAAQ,oBACpC9gB,KAAK,CAAC,gBAAgB,KAAKL,QAC3BK,KAAK,CAAC,iBAAiB,MAAMsG,YAC7BtG,KAAK,CAAC,iBAAiB,MAAMwG,UAC7BtG,MAAM,CAAC;YACJ;YACA,CAACuG,KAAOA,GAAGG,EAAE,CAACC,GAAG,CAAC,cAAcC,EAAE,CAAC;SACtC;QAED,IAAI8H,WAAW,gBAAgB;YAC3B2c,cACCzK,SAAS,CAAC,wBAAwB,SAAS,qBAC3C9gB,KAAK,CAAC,SAAS,KAAKsrB;QACzB;QAEA,IAAI1c,WAAW,YAAY;YACvB2c,cACCvrB,KAAK,CAAC,QAAQ,KAAKsrB;QACxB;QAEA,MAAME,WAAW,MAAMD,cACtB7pB,OAAO,CAAC,kBACRP,OAAO;QAER,OAAO;YACHd,QAAQ;YACRgB,MAAMmqB;QACV;IACF;IAEJ,MAAMC,+BAA+B9rB,MAAc,EAAEmG,KAAiD,EAAE;QACtG,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,oDAAoD;QACpD,MAAMmhB,cAAclhB,aAChB5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KACvCpH,QAAQgH,OAAO,CAAC,QAAQI,MAAM;QAClC,MAAM4gB,YAAYnhB,WACd7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KACnCpH,QAAQkH,KAAK,CAAC,QAAQE,MAAM;QAEhC,0DAA0D;QAC1D,MAAMmlB,uBAA4B,MAAM5rB,YAAE,CACvCC,UAAU,CAAC,qBACX0B,QAAQ,CAAC,4BAA4B,eAAe,SACpDA,QAAQ,CAAC,cAAc,QAAQ,eAC/BzB,KAAK,CAAC,cAAc,KAAKL,QACzBK,KAAK,CAAC,eAAe,MAAMinB,aAC3BjnB,KAAK,CAAC,eAAe,MAAMmnB,WAC3BjnB,MAAM,CAAC,CAACuG,KAAO;gBACdA,GAAGG,EAAE,CAACC,GAAG,CAAC,gBAAgBC,EAAE,CAAC;gBAC7BL,GAAGG,EAAE,CAACC,GAAG,CAAC,aAAaC,EAAE,CAAC;gBAC1BL,GAAGG,EAAE,CAACC,GAAG,CAAC,eAAeC,EAAE,CAAC;gBAC5BL,GAAGG,EAAE,CAACC,GAAG,CAAC,WAAWC,EAAE,CAAC;gBACxBL,GAAGG,EAAE,CAACyG,KAAK,CAAC,SAASvG,EAAE,CAAC;aACzB,EACA3G,gBAAgB;QAEjB,4CAA4C;QAC5C,MAAMwrB,kBAAkB,MAAM7rB,YAAE,CAC/BC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMinB,aACxBjnB,KAAK,CAAC,YAAY,MAAMmnB,WACxBjnB,MAAM,CAAC,CAACuG,KAAO;gBACdA,GAAGG,EAAE,CAACC,GAAG,CAAC,YAAYC,EAAE,CAAC;gBACzBL,GAAGG,EAAE,CAACC,GAAG,CAAC,SAASC,EAAE,CAAC;gBACtBL,GAAGG,EAAE,CAACC,GAAG,CAAC,WAAWC,EAAE,CAAC;gBACxBL,GAAGG,EAAE,CAACC,GAAG,CAAC,OAAOC,EAAE,CAAC;gBACpBL,GAAGG,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC;aACtB,EACA3G,gBAAgB;QAYnB,oDAAoD;QACpD,IAAI,CAACwrB,mBAAmBA,gBAAgBxD,UAAU,KAAK,GAAG;YACxD,OAAO;gBACL9nB,QAAQ;gBACRgB,MAAM;oBACJuqB,iBAAiB;wBACfC,4BAA4B;4BAC1BxkB,UAAU;4BACVykB,kBAAkB;4BAClBjkB,OAAO;4BACPkkB,eAAe;4BACfnkB,SAAS;4BACTokB,iBAAiB;4BACjBlkB,KAAK;4BACLmkB,aAAa;wBACf;oBACF;gBACF;YACF;QACF;QAEA,2BAA2B;QAC3B,MAAMC,cAAc/sB,MAAMgoB,WAAWgF,IAAI,CAAClF,aAAa,SAAS;QAChE,MAAMmF,cAAcnrB,OAAO0qB,gBAAgBxkB,cAAc,IAAI,KAAK+kB;QAClE,MAAMG,WAAWprB,OAAO0qB,gBAAgBW,WAAW,IAAI,KAAKJ;QAC5D,MAAMK,aAAatrB,OAAO0qB,gBAAgBa,aAAa,IAAI,KAAKN;QAChE,MAAMO,SAASxrB,OAAO0qB,gBAAgBe,SAAS,IAAI,KAAKR;QAExD,yDAAyD;QACzD,MAAMS,gBAAgBN,WAAW,GAAG,0BAA0B;QAC9D,MAAMO,kBAAkBL,aAAa,GAAG,uBAAuB;QAC/D,MAAMM,cAAcJ,SAAS,GAAG,sBAAsB;QACtD,MAAMK,0BAA0BH,gBAAgBC,kBAAkBC;QAElE,uBAAuB;QACvB,MAAME,eAAeD,0BAA0B,IAAI,AAACH,gBAAgBG,0BAA2B,MAAM;QACrG,MAAME,iBAAiBF,0BAA0B,IAAI,AAACF,kBAAkBE,0BAA2B,MAAM;QACzG,MAAMG,aAAaH,0BAA0B,IAAI,AAACD,cAAcC,0BAA2B,MAAM;QAEjG,6DAA6D;QAC7D,MAAMI,qBAAqB,IAAI,CAACC,gCAAgC,CAAC;YAC/D9lB,UAAU3C,KAAK+iB,KAAK,CAAC2E;YACrBxkB,SAASlD,KAAK+iB,KAAK,CAAC8E;YACpB1kB,OAAOnD,KAAK+iB,KAAK,CAAC4E;YAClBvkB,KAAKpD,KAAK+iB,KAAK,CAACgF;QAClB;QAEA,OAAO;YACLpsB,QAAQ;YACRgB,MAAM;gBACJuH,MAAM,IAAI1F,OAAO+I,WAAW;gBAC5BmhB,aAAaF,mBAAmBE,WAAW;gBAC3CC,cAAcH,mBAAmBG,YAAY;gBAC7CC,UAAUJ,mBAAmBI,QAAQ;gBACrCC,iBAAiBL,mBAAmBK,eAAe;gBACnDC,cAAcN,mBAAmBM,YAAY;gBAC7CC,kBAAkBP,mBAAmBO,gBAAgB;gBACrD7B,iBAAiB;oBACfC,4BAA4B;wBAC1BxkB,UAAU3C,KAAK+iB,KAAK,CAAC2E;wBACrBN,kBAAkB;wBAClBjkB,OAAOnD,KAAK+iB,KAAK,CAAC4E;wBAClBN,eAAernB,KAAK+iB,KAAK,CAACsF;wBAC1BnlB,SAASlD,KAAK+iB,KAAK,CAAC8E;wBACpBP,iBAAiBtnB,KAAK+iB,KAAK,CAACuF;wBAC5BllB,KAAKpD,KAAK+iB,KAAK,CAACgF;wBAChBR,aAAavnB,KAAK+iB,KAAK,CAACwF;oBAC1B;gBACF;YACF;QACF;IACF;IAIA,MAAMS,0BAA0B/tB,MAAc,EAAEmG,KAAiD,EAAE;QACjG,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,oDAAoD;QACpD,MAAMmhB,cAAclhB,aAChB,IAAI7C,KAAK6C,cACT,IAAI7C;QACR,MAAMikB,YAAYnhB,WACd,IAAI9C,KAAK8C,YACT,IAAI9C;QAER+jB,YAAY0G,QAAQ,CAAC,GAAG,GAAG,GAAG;QAC9BxG,UAAUwG,QAAQ,CAAC,IAAI,IAAI,IAAI;QAE/B,iDAAiD;QACjD,MAAM1e,SAAS,MAAMnP,YAAE,CACpBC,UAAU,CAAC,sBACX0B,QAAQ,CAAC,oCAAoC,eAAe,eAC5DzB,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAMinB,aAC5BjnB,KAAK,CAAC,gBAAgB,MAAMmnB,WAC5BjnB,MAAM,CAAC;YACN,+DAA+D;YAC/D;YACA,4CAA4C;YAC5C,CAACuG,KAAOA,GAAGG,EAAE,CAACC,GAAG,CAACJ,GAAGsa,GAAG,CAAC,iBAAiBja,EAAE,CAAC;YAC7C,0CAA0C;YAC1C,CAACL,KAAOA,GAAGG,EAAE,CAACC,GAAG,CAACJ,GAAGsa,GAAG,CAAC,iBAAiBja,EAAE,CAAC;SAC9C,EACApF,OAAO,CAAC,QACRe,OAAO,CAAC,QAAQ,OAChBtB,OAAO;QAEV,0CAA0C;QAC1C,MAAMysB,gBAAgB3e,OAAOzH,MAAM,CAAC,CAACX,KAAK6R,MAAQ7R,MAAM5F,OAAOyX,IAAIvQ,QAAQ,IAAI,IAAI;QACnF,MAAM0lB,cAAc5e,OAAOzH,MAAM,CAAC,CAACX,KAAK6R,MAAQ7R,MAAM5F,OAAOyX,IAAItQ,MAAM,IAAI,IAAI;QAC/E,MAAM0lB,kBAAkBF,gBAAgB3e,OAAOnM,MAAM,IAAI;QACzD,MAAMirB,gBAAgBF,cAAc5e,OAAOnM,MAAM,IAAI;QAErD,OAAO;YACLzC,QAAQ;YACRgB,MAAM;gBACJ2sB,OAAO/e,OAAOV,GAAG,CAAC,CAACmK,MAAc,CAAA;wBAC/B9P,MAAM8P,IAAI9P,IAAI,CAACqD,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC1CtB,UAAUlH,OAAOyX,IAAIvQ,QAAQ,IAAI;wBACjCC,QAAQnH,OAAOyX,IAAItQ,MAAM,IAAI;oBAC/B,CAAA;gBACA6lB,kBAAkBH;gBAClBI,gBAAgBH;gBAChBI,sBAAsB;YACxB;QACF;IACF;IAEI,MAAMC,oBAAoBzuB,MAAc,EAAEmG,KAAU,EAAE;QACpD,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QACjC,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAE/F,MAAMgM,WAAW,MAAMzS,YAAE,CACxBC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5CA,SAAS,CAAC,mCAAmC,UAAU,2BACvDrf,QAAQ,CAAC,uCAAuC,iBAAiB,UACjEA,QAAQ,CAAC,8CAA8C,WAAW,6BAClEA,QAAQ,CAAC,kBAAkB,QAAQ,oBACnCA,QAAQ,CAAC,6BAA6B,cAAc,cACpDA,QAAQ,CAAC,+BAA+B,gBAAgB,qBACxDvB,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA,CAACuG,KACCA,GACGG,EAAE,CAACyG,KAAK,CAAC5G,GAAGsa,GAAG,CAAC,8BAChBC,QAAQ,GACRla,EAAE,CAAC;YACR;YACAsW,IAAAA,WAAG,CAAQ,CAAC,4DAA4D,CAAC,CAACtW,EAAE,CAAC;SAC9E,EACF9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,kBAAkB,MAAMsG,YAC9BtG,KAAK,CAAC,kBAAkB,MAAMwG,UAC9B9E,OAAO,CAAC;YAAC;YAAU;YAAY;SAAuB,EACtDP,OAAO;QAER,MAAMktB,iBAAiB9b,SAAShE,GAAG,CAAC,CAACqX;YACjC,OAAO;gBACHhd,MAAMgd,KAAKhd,IAAI;gBACfoK,cAAc4S,KAAK5S,YAAY;gBAC/B7L,gBAAgBye,KAAKze,cAAc;gBACnC+Y,cAAc0F,KAAK1F,YAAY;gBAC/BsB,gBAAgBvgB,OAAO2kB,KAAKpE,cAAc;gBAC1CjV,MAAMqZ,KAAKrZ,IAAI,IAAI;gBACnBkV,eAAemE,KAAKnE,aAAa,IAAI;YACzC;QACJ;QAEA,MAAM6M,kBAAkBD,eAAe7mB,MAAM,CAAC,CAACC,KAAUme;YACrDne,IAAIN,cAAc,IAAIye,KAAKze,cAAc;YACzCM,IAAIyY,YAAY,IAAI0F,KAAK1F,YAAY;YACrC,OAAOzY;QACX,GAAG;YAAEN,gBAAgB;YAAG+Y,cAAc;QAAE;QAExC,OAAO;YACH7f,QAAQ;YACRgB,MAAM;gBACFkR,UAAU8b;gBACVnlB,SAASolB,gBAAgBpO,YAAY;gBACrC7Y,UAAUinB,gBAAgBnnB,cAAc;gBACxC6mB,OAAOK;YACX;QACJ;IACF;IAEA,MAAME,4BAA4B5uB,MAAc,EAAEmG,KAAU,EAAE;QAC5D,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,oDAAoD;QACpD,MAAMmhB,cAAclhB,aAChB,IAAI7C,KAAK6C,cACT,IAAI7C;QACR,MAAMikB,YAAYnhB,WACd,IAAI9C,KAAK8C,YACT,IAAI9C;QAER+jB,YAAY0G,QAAQ,CAAC,GAAG,GAAG,GAAG;QAC9BxG,UAAUwG,QAAQ,CAAC,IAAI,IAAI,IAAI;QAE/B,qDAAqD;QACrD,MAAM1e,SAAS,MAAMnP,YAAE,CACpBC,UAAU,CAAC,+BACXC,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAMinB,aAC5BjnB,KAAK,CAAC,gBAAgB,MAAMmnB,WAC5BjnB,MAAM,CAAC;YACN,wCAAwC;YACxC,CAACuG,KAAOA,GAAGG,EAAE,CAACyG,KAAK,CAAC5G,GAAGsa,GAAG,CAAC,WAAWja,EAAE,CAAC;YACzC,kDAAkD;YAClD,CAACL,KAAOA,GAAGG,EAAE,CAACC,GAAG,CAACJ,GAAGsa,GAAG,CAAC,uBAAuBja,EAAE,CAAC;YACnD,+DAA+D;YAC/D;YACA,+CAA+C;YAC/C,CAACL,KAAOA,GAAGG,EAAE,CAACC,GAAG,CAACJ,GAAGsa,GAAG,CAAC,qBAAqBja,EAAE,CAAC;SAClD,EACApF,OAAO,CAAC,QACRe,OAAO,CAAC,QAAQ,OAChBtB,OAAO;QAEV,kBAAkB;QAClB,MAAM2T,gBAAgB7F,OAAOzH,MAAM,CAAC,CAACX,KAAK6R,MAAQ7R,MAAM5F,OAAOyX,IAAInG,QAAQ,IAAI,IAAI;QACnF,MAAMic,eAAevf,OAAOzH,MAAM,CAAC,CAACX,KAAK6R,MAAQ7R,MAAM5F,OAAOyX,IAAIxP,OAAO,IAAI,IAAI;QACjF,MAAMulB,gBAAgBxf,OAAOzH,MAAM,CAAC,CAACX,KAAK6R,MAAQ7R,MAAM5F,OAAOyX,IAAIrR,QAAQ,IAAI,IAAI;QAEnF,mBAAmB;QACnB,MAAMqnB,YAAYzf,OAAOV,GAAG,CAAC,CAACmK,MAAc,CAAA;gBAC1CiW,OAAOjW,IAAI9P,IAAI,CAACqD,WAAW,GAAGxC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3CmlB,iBAAiB3tB,OAAOyX,IAAIxP,OAAO,IAAI;YACzC,CAAA;QAEA,OAAO;YACL7I,QAAQ;YACRgB,MAAM;gBACJkR,UAAUuC;gBACV5L,SAASslB;gBACTnnB,UAAUonB;gBACVT,OAAOU;YACT;QACF;IACF;IAGE,MAAMG,4BAA4BlvB,MAAc,EAAEmG,KAAiD,EAAE;QACnG,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QAEjC,iDAAiD;QACjD,MAAMgpB,gBAAgB/oB,aAClB5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KACvCpH,QAAQgH,OAAO,CAAC,SAASI,MAAM;QACnC,MAAMwoB,cAAc/oB,WAChB7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KACnCpH,QAAQkH,KAAK,CAAC,SAASE,MAAM;QAEjC,iDAAiD;QACjD,MAAMyoB,eAAe,MAAMlvB,YAAE,CAC1BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,gBAAgB,MAAM8uB,eAC5B9uB,KAAK,CAAC,gBAAgB,MAAM+uB,aAC5B7uB,MAAM,CAAC,CAACuG,KAAO;gBACd2W,IAAAA,WAAG,CAAQ,CAAC,kBAAkB,CAAC,CAACtW,EAAE,CAAC;gBACnCL,GAAGG,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC;aACtB,EACApF,OAAO,CAAC,QACRe,OAAO,CAAC,SAAS,QACjBtC,gBAAgB;QACnB,MAAM8uB,mBAAmBD,eAAe,GAAGE,OAAOF,aAAaG,IAAI,EAAEC,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;QAE7F,4CAA4C;QAC5C,MAAM9a,cAAc,MAAMxU,YAAE,CACzBC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,gBAAgB,MAAM8uB,eAC5B9uB,KAAK,CAAC,gBAAgB,MAAM+uB,aAC5B7uB,MAAM,CAAC,gBACPuC,OAAO,CAAC,gBAAgB,OACxBtB,OAAO;QACV,IAAIkuB,kBAAkB;QACtB,IAAI/a,YAAYxR,MAAM,GAAG,GAAG;YAC1B,MAAMwsB,YAAsB,EAAE;YAC9B,IAAK,IAAIzI,IAAI,GAAGA,IAAIvS,YAAYxR,MAAM,EAAE+jB,IAAK;gBAC3C,MAAMsF,OAAOhtB,MAAMmV,WAAW,CAACuS,EAAE,CAAC3G,YAAY,EAAEiM,IAAI,CAAC7X,WAAW,CAACuS,IAAI,EAAE,CAAC3G,YAAY,EAAE,QAAQ;gBAC9FoP,UAAUpiB,IAAI,CAACif;YACjB;YACAkD,kBAAkBC,UAAUxsB,MAAM,GAAG,IAAIwsB,UAAU9nB,MAAM,CAAC,CAACwW,GAAGC,IAAMD,IAAIC,GAAG,KAAKqR,UAAUxsB,MAAM,GAAG;QACrG;QACA,MAAMysB,2BAA2BF,gBAAgBntB,OAAO,CAAC,KAAK;QAE9D,yCAAyC;QACzC,MAAMstB,YAAY,MAAM1vB,YAAE,CACvBC,UAAU,CAAC,2BACX+gB,SAAS,CAAC,qBAAqB,SAAS,cACxC9gB,KAAK,CAAC,cAAc,KAAKL,QACzBK,KAAK,CAAC,eAAe,MAAM8uB,eAC3B9uB,KAAK,CAAC,eAAe,MAAM+uB,aAC3B7uB,MAAM,CAACkd,IAAAA,WAAG,CAAM,CAAC,iBAAiB,CAAC,CAACtW,EAAE,CAAC,cAAc,mBAAmB;SACxE3F,OAAO;QACV,MAAMsuB,kBAAkBD,UAAUjhB,GAAG,CAAC,CAACwK,IAAMA,EAAE1H,SAAS,EAAE0M,IAAI,CAAC,CAACC,GAAQC,IAAWD,IAAIC;QACvF,MAAMyR,YAAYD,gBAAgB3sB,MAAM,GAAG,IAAI3D,MAAMswB,eAAe,CAAC,EAAE,EAAEjS,MAAM,CAAC,WAAW;QAC3F,MAAMmS,WAAWF,gBAAgB3sB,MAAM,GAAG,IAAI3D,MAAMswB,eAAe,CAACA,gBAAgB3sB,MAAM,GAAG,EAAE,EAAE0a,MAAM,CAAC,WAAW;QAEnH,yEAAyE;QACzE,MAAMgN,YAAYrrB,MAAM4vB,aAAa5C,IAAI,CAAC2C,eAAe,SAAS;QAClE,MAAMc,YAAYJ,UAAUjhB,GAAG,CAAC,CAACwK,IAAM5Z,MAAM4Z,EAAE1H,SAAS,EAAE8d,IAAI;QAC9D,MAAMU,UAAUD,UAAU9sB,MAAM,GAAG,IAAI4B,KAAK4lB,GAAG,IAAIsF,aAAa;QAChE,MAAME,UAAUF,UAAU9sB,MAAM,GAAG,IAAI4B,KAAKiD,GAAG,IAAIioB,aAAa;QAEhE,4EAA4E;QAC5E,MAAMG,WAAW,GAAG,yCAAyC;QAC7D,MAAMC,QAAQF,UAAUD;QACxB,MAAMI,WAAWD,QAAQ,IAAItrB,KAAK0K,IAAI,CAAC4gB,QAAQD,YAAY;QAC3D,MAAMG,YAA4D,EAAE;QACpE,IAAK,IAAIrJ,IAAI,GAAGA,IAAIkJ,UAAUlJ,IAAK;YACjC,MAAMsJ,QAAQN,UAAUhJ,IAAIoJ;YAC5B,MAAMG,MAAM1rB,KAAK4lB,GAAG,CAAC6F,QAAQF,WAAW,GAAG;YAC3CC,UAAUhjB,IAAI,CAAC;gBACbwS,MAAM,GAAGwP,OAAOiB,OAAOf,QAAQ,CAAC,GAAG,KAAK,GAAG,CAAC;gBAC5Ce;gBACAC;YACF;QACF;QAEA,kCAAkC;QAClC,MAAMC,kBAAkB,MAAMpM,QAAQC,GAAG,CACvCgM,UAAU3hB,GAAG,CAAC,OAAO+hB;YACnB,MAAMC,eAAe,MAAMzwB,YAAE,CAC1BC,UAAU,CAAC,2BACX+gB,SAAS,CAAC,qBAAqB,SAAS,cACxC9gB,KAAK,CAAC,cAAc,KAAKL,QACzBK,KAAK,CAAC,eAAe,MAAM8uB,eAC3B9uB,KAAK,CAAC,eAAe,MAAM+uB,aAC3B/uB,KAAK,CAAC,CAACyG,KACNA,GAAG6W,GAAG,CAAC;oBACL7W,GAAG2W,IAAAA,WAAG,CAAQ,CAAC,iBAAiB,CAAC,EAAE,MAAMkT,KAAKH,KAAK;oBACnD1pB,GAAG2W,IAAAA,WAAG,CAAQ,CAAC,iBAAiB,CAAC,EAAE,MAAMkT,KAAKF,GAAG;iBAClD,GAEFlwB,MAAM,CAAC,CAACuG,KAAO;oBAAC2W,IAAAA,WAAG,CAAQ,CAAC,iBAAiB,CAAC,CAACtW,EAAE,CAAC;iBAAO,EACzDka,QAAQ,GACR7f,OAAO;YACV,MAAMqvB,UAAUhG,YAAY,IAAI,AAAC+F,aAAaztB,MAAM,GAAG0nB,YAAa,MAAM;YAC1E,OAAO;gBAAE9K,MAAM4Q,KAAK5Q,IAAI;gBAAE8Q,SAAS9rB,KAAK+iB,KAAK,CAAC+I;YAAS;QACzD;QAGF,4CAA4C;QAC5C,MAAMC,mBAAmB,MAAM3wB,YAAE,CAC9BC,UAAU,CAAC,kBACXC,KAAK,CAAC,YAAY,KAAK,gBACvBE,MAAM,CAAC;YAAC;YAAM;SAAuB,EACrCiB,OAAO;QACV,MAAMuvB,eAAeD,iBAAiBjpB,MAAM,CAAC,CAACC,KAAUkpB;YACtDlpB,GAAG,CAACkpB,MAAMrvB,EAAE,CAAC,GAAGqvB,MAAM/uB,IAAI;YAC1B,OAAO6F;QACT,GAAG,CAAC;QAEJ,qDAAqD;QACrD,MAAMmpB,qBAAqB,MAAM9wB,YAAE,CAChCC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,wBAAwB,2BAA2B,wCAC7DA,SAAS,CAAC,sCAAuC,yCAAyC,oDAC1FA,SAAS,CAAC,aAAa,gBAAgB,kDACvC9gB,KAAK,CAAC,gCAAgC,KAAKL,QAC3CK,KAAK,CAAC,qCAAqC,MAAM8uB,eACjD9uB,KAAK,CAAC,qCAAqC,MAAM+uB,aACjD7uB,MAAM,CAAC,CAACuG,KAAY;gBACnB;gBACAA,GAAGG,EAAE,CAACC,GAAG,CAAC,wCAAwCC,EAAE,CAAC;aACtD,EACApF,OAAO,CAAC,6BACRP,OAAO;QAEV,MAAMstB,gBAAgBmC,mBAAmBppB,MAAM,CAAC,CAACX,KAAKgjB,IAAMhjB,MAAM5F,OAAO4oB,EAAE1iB,cAAc,GAAG;QAC5F,MAAM0pB,8BAA8BD,mBAAmBriB,GAAG,CAAC,CAACsb,IAAO,CAAA;gBACjE7R,cAAc0Y,YAAY,CAAC7G,EAAEiH,eAAe,CAAC,IAAI;gBACjDN,SAAS/B,gBAAgB,IAAI/pB,KAAK+iB,KAAK,CAAC,AAACxmB,OAAO4oB,EAAE1iB,cAAc,IAAIsnB,gBAAiB,OAAO;YAC9F,CAAA;QAEA,uBAAuB;QACvB,OAAO;YACLpuB,QAAQ;YACRgB,MAAM;gBACJ0vB,oBAAoB9B;gBACpB+B,kBAAkBzB;gBAClB0B,YAAYvB;gBACZwB,WAAWvB;gBACXwB,mBAAmBd;gBACnBe,qBAAqBP;YACvB;QACF;IACF;IAEF,MAAMQ,8BAA8B1xB,MAAc,EAAEmG,KAAU,EAAE;QAC9D,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAE,GAAGF;QACjC,MAAMQ,aAAaP,aAAa5G,MAAM4G,YAAYI,OAAO,CAAC,OAAOI,MAAM,KAAKpH,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QACzG,MAAMC,WAAWR,WAAW7G,MAAM6G,UAAUK,KAAK,CAAC,OAAOE,MAAM,KAAKpH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;QAG/F,MAAMqqB,qBAAqB,MAAM9wB,YAAE,CAClCC,UAAU,CAAC,sCACX+gB,SAAS,CAAC,gCAAgC,WAAW,gBACrDA,SAAS,CAAC,8CAA8C,WAAW,4BACnEA,SAAS,CAAC,kBAAkB,QAAQ,oBACpCA,SAAS,CAAC,wBAAwB,SAAS,qBAC3C9gB,KAAK,CAAC,gBAAgB,KAAKL,QAC3BK,KAAK,CAAC,iBAAiB,MAAMsG,YAC7BtG,KAAK,CAAC,iBAAiB,MAAMwG,UAC7BtG,MAAM,CAAC;YACJ;YACA;SACH,EACAwB,OAAO,CAAC,mBACRP,OAAO;QAER,MAAMmwB,2BAA2BV,mBAAmBppB,MAAM,CAAC,CAACC,KAAUme;YAClE,6DAA6D;YAC7D,MAAM4K,UAAU5K,KAAKzjB,MAAM,GAAGyuB,mBAAmBppB,MAAM,CAAC,CAACC,KAAUme,OAAcne,MAAMme,KAAKzjB,MAAM,EAAE,KAAK;YACzGsF,IAAIyF,IAAI,CAAC;gBACLyhB,OAAO/I,KAAK5N,YAAY;gBACxB7V,QAAQyjB,KAAKzjB,MAAM;gBACnBquB,SAAS9rB,KAAK+iB,KAAK,CAAC+I;YACxB;YACA,OAAO/oB;QACX,GAAG,EAAE;QAOL,OAAO;YACHpH,QAAQ;YACRgB,MAAMiwB;QACV;IACF;IAEA,MAAMC,sBAAsB5xB,MAAc,EAAE;QAC1C,MAAMmM,QAAQ3M,QAAQgH,OAAO,CAAC,OAAOI,MAAM;QAE3C,oCAAoC;QACpC,MAAM0gB,cAAc9nB,QAAQgH,OAAO,CAAC,QAAQI,MAAM;QAClD,MAAMirB,iBAAiB,MAAM1xB,YAAE,CAC5BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,gBAAgB,MAAMinB,aAC5BjnB,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B5L,MAAM,CAAC,CAACuG,KAAO;gBAAC2W,IAAAA,WAAG,CAAQ,CAAC,kBAAkB,CAAC,CAACtW,EAAE,CAAC;aAAO,EAC1D3F,OAAO;QACV,MAAMswB,gBAAgBtyB,MAAM2M,OAAOqgB,IAAI,CAAClF,aAAa,SAAS;QAC9D,MAAMyK,iBAAiBD,gBAAgB,IAAI,AAACD,eAAe1uB,MAAM,GAAG2uB,gBAAiB,MAAM;QAE3F,iCAAiC;QACjC,MAAM9J,eAAexoB,QAAQgH,OAAO,CAAC,SAASI,MAAM;QACpD,MAAMorB,kBAAkB,MAAM7xB,YAAE,CAC7BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,gBAAgB,MAAM2nB,cAC5B3nB,KAAK,CAAC,gBAAgB,MAAM8L,OAC5B5L,MAAM,CAAC,CAACuG,KAAO;gBAAC2W,IAAAA,WAAG,CAAQ,CAAC,kBAAkB,CAAC,CAACtW,EAAE,CAAC;aAAO,EAC1D3F,OAAO;QACV,MAAMywB,iBAAiBzyB,MAAM2M,OAAOqgB,IAAI,CAACxE,cAAc,SAAS;QAChE,MAAMkK,kBAAkBD,iBAAiB,IAAI,AAACD,gBAAgB7uB,MAAM,GAAG8uB,iBAAkB,MAAM;QAE/F,2BAA2B;QAC3B,MAAME,gBAAgB,MAAMhyB,YAAE,CAC3BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAAC,CAACuG,KAAO;gBAAC2W,IAAAA,WAAG,CAAQ,CAAC,kBAAkB,CAAC,CAACtW,EAAE,CAAC;aAAO,EAC1DrE,OAAO,CAAC,gBAAgB,QACxBtB,OAAO;QACV,MAAM4wB,gBAAgB,IAAIC,IAAIF,cAAcvjB,GAAG,CAAC,CAACsb,IAAMA,EAAEzf,GAAG;QAC5D,IAAIwc,gBAAgB;QACpB,IAAIE,cAAchb;QAClB,MAAOimB,cAAcnZ,GAAG,CAACzZ,MAAM2nB,aAAatJ,MAAM,CAAC,eAAgB;YACjEoJ;YACAE,cAAc3nB,MAAM2nB,aAAamL,QAAQ,CAAC,GAAG,OAAO1rB,MAAM;QAC5D;QAEA,gCAAgC;QAChC,IAAIogB,YAAY;QAChB,IAAIuL,aAAa;QACjB,MAAMC,mBAAmBL,cAAcvjB,GAAG,CAAC,CAACsb,IAAM1qB,MAAM0qB,EAAEzf,GAAG,EAAE7D,MAAM,IAAIwX,IAAI,CAAC,CAACC,GAAGC,IAAMD,EAAEtP,OAAO,KAAKuP,EAAEvP,OAAO;QAC/G,IAAK,IAAImY,IAAI,GAAGA,IAAIsL,iBAAiBrvB,MAAM,EAAE+jB,IAAK;YAChDqL,aAAa;YACb,IAAIE,IAAIvL,IAAI;YACZ,MAAOuL,IAAID,iBAAiBrvB,MAAM,IAAI3D,MAAMgzB,gBAAgB,CAACC,EAAE,EAAEjG,IAAI,CAACgG,gBAAgB,CAACC,IAAI,EAAE,EAAE,WAAW,EAAG;gBAC3GF;gBACAE;YACF;YACAzL,YAAYjiB,KAAKiD,GAAG,CAACgf,WAAWuL;YAChCrL,IAAIuL,IAAI,GAAG,yBAAyB;QACtC;QAEA,uBAAuB;QACvB,OAAO;YACL/xB,QAAQ;YACRgB,MAAM;gBACJgxB,MAAM3tB,KAAK+iB,KAAK,CAACiK;gBACjBY,OAAO5tB,KAAK+iB,KAAK,CAACoK;gBAClB5nB,QAAQ2c;gBACR1c,eAAeyc;YACjB;QACF;IACF;IAEA,KAAK;IACL,MAAM4L,uBAAuB5yB,MAAc,EAAE;QAC3C,MAAM6B,OAAO,MAAM1B,YAAE,CACpBC,UAAU,CAAC,+BACXG,MAAM,CAAC;YACN;YACA;YACA;YACA;SACD,EACAF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,cAAc,MAAM,MAC1BG,gBAAgB;QAEjB,MAAM+a,OAAO,CAAC;oBACF,EAAE1Z,MAAM6F,SAAS;oBACjB,EAAE7F,MAAMoG,QAAQ;wBACZ,EAAEpG,MAAMqG,MAAM;oBAClB,EAAErG,MAAMsG,IAAI;QACxB,CAAC;QAED,OAAOoT;IACT;IAGA,MAAMsX,gBAAgBtX,IAAY,EAAEvb,MAAc,EAAE;QAClD,kCAAkC;QAClC,OAAO,CAAC,8CAA8C,EAAEub,KAAK;;;;;;;;;;wBAU7C,CAAC;IACnB;IAEA,MAAMuX,YAAYhhB,OAAe,EAAE;QACjC,MAAMyN,WAAW,MAAMpf,YAAE,CACxBC,UAAU,CAAC,qCACX0B,QAAQ,CAAC,4CAA4C,cAAc,QACnEzB,KAAK,CAAC,QAAQ,KAAKyR,SACnBvR,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAiB,OAAO;QAER,MAAMuxB,WAAWxT,SAAS3Q,GAAG,CAAC,CAACqX;YAC3B,OAAO,CAAC;sBACE,EAAEA,KAAKhH,SAAS,CAAC;sBACjB,EAAEgH,KAAK1M,SAAS,CAAC;wBACf,EAAE0M,KAAK+M,aAAa,CAAC,CAAC,EAAE/M,KAAKgN,SAAS,CAAC;sBACzC,EAAEhN,KAAKiN,aAAa,CAAC;sBACrB,EAAEjN,KAAKkN,YAAY,CAAC;0BAChB,EAAElN,KAAKmN,UAAU,CAAC;sBACtB,EAAEnN,KAAKoN,QAAQ,CAAC;mBACnB,EAAEpN,KAAKqN,UAAU,CAAC;YACzB,CAAC;QACL,GAAGhvB,IAAI,CAAC;QAER,OAAOyuB;IACT;IAEA,MAAMQ,2BAA2BhU,QAAa,EAAE;QAC9C,MAAM,EAAEzN,OAAO,EAAE,GAAGyN;QAEpB,MAAMwT,WAAW,MAAM,IAAI,CAACD,WAAW,CAAChhB;QAExC,OAAO,CAAC;;;;UAIN,EAAEihB,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;QA0Bb,CAAC;IACH;IAEA,MAAMS,2BAA2BjY,IAAY,EAAEvb,MAAc,EAAE;QAC7D,OAAO,CAAC,0BAA0B,EAAEub,KAAK;;;;;EAK/C,CAAC;IACG;IAEA,MAAMkY,iBAAiB;QACrB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2BR,CAAC;IACH;IAEA,MAAMC,iBAAiB;QACrB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;QA2BR,CAAC;IACH;IAGA,MAAMC,cAAcpY,IAAY,EAAEgE,QAAa,EAAEvf,MAAc,EAAE;QAC/D,IAAI,CAACub,QAAQ,OAAOA,SAAS,YAAYA,KAAKvB,IAAI,OAAO,IAAI;YAC3D,IAAGuF,SAASqU,SAAS,KAAK,eAAe;gBACvC,MAAM,IAAIjjB,MAAM;YAClB;QACF;QAEA,IAAI6F;QACJ,OAAO+I,SAASqU,SAAS;YACvB,KAAK;gBACHpd,SAAS,MAAM,IAAI,CAACqc,eAAe,CAACtX,MAAMvb;gBAC1C;YACF,KAAK;gBACHwW,SAAS,MAAM,IAAI,CAACgd,0BAA0B,CAACjY,MAAMvb;gBACrD;YACF,KAAK;gBACHwW,SAAS,MAAM,IAAI,CAAC+c,0BAA0B,CAAChU;gBAC/C;YACF;gBACE,MAAM,IAAI5O,MAAM;QACpB;QAEA,MAAM8F,WAAgB,MAAM,IAAI,CAACC,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC9W,MAAM,CAAC;YAC9D+W,OAAO;YACPC,UAAU;gBACR;oBACE7W,MAAM;oBACN8W,SAAS;gBACX;gBACA;oBAAE9W,MAAM;oBAAQ8W,SAASP;gBAAO;aACjC;YACDQ,aAAa;YACbC,YAAY;YACZC,iBAAiB;gBAAEtK,MAAM;YAAc;QACzC;QAGA,MAAMuK,iBAAsBV,SAASW,OAAO,CAAC,EAAE,CAACzW,OAAO,CAACoW,OAAO;QAE/D,OAAOI;IACT;IAEA,MAAM0c,eAAeC,MAAc,EAAE;QACnC,uBAAuB;QACvB,IAAI,CAACA,UAAU,OAAOA,WAAW,YAAYA,OAAO9Z,IAAI,OAAO,IAAI;YACjE,MAAM,IAAIrJ,MAAM;QAClB;QAEA,sCAAsC;QAGxC,MAAMojB,aAAa,MAAM,IAAI,CAACrd,MAAM,CAACC,IAAI,CAACC,WAAW,CAAC9W,MAAM,CAAC;YACzD+W,OAAO;YACPC,UAAU;gBACR;oBAAE7W,MAAM;oBAAU8W,SAAS;gBAA2E;gBACtG;oBACE9W,MAAM;oBACN8W,SAAS;wBACL;4BACInK,MAAM;4BACNonB,WAAW;gCACPC,KAAKH;4BACT;wBACJ;wBACA;4BACIlnB,MAAM;4BACN2O,MAAM,MAAM,IAAI,CAACkY,cAAc;wBACnC;qBACH;gBACL;aAAE;QACN;QAEA,OAAOM,WAAW3c,OAAO,CAAC,EAAE,CAACzW,OAAO,CAACoW,OAAO;IAG5C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAuEE,GACF;IAEA,MAAMmd,eAAeJ,MAAc,EAAE9zB,MAAc,EAAE;QACnD,IAAI;YACF,kBAAkB;YAClB,IAAI,CAAC8zB,UAAU,OAAOA,WAAW,YAAYA,OAAO9Z,IAAI,OAAO,IAAI;gBACjE,MAAM,IAAIrJ,MAAM;YAClB;YAEA,yBAAyB;YACzB,MAAMwjB,cAAcL,OAAOM,KAAK,CAAC;YACjC,IAAI,CAACD,eAAeA,YAAYhxB,MAAM,GAAG,GAAG;gBAC1C,MAAM,IAAIwN,MAAM;YAClB;YAEA,MAAM,GAAG0jB,UAAUC,QAAQ,GAAGH;YAC9B,MAAMI,2BAA2B;gBAAC;gBAAQ;gBAAO;gBAAO;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAO;gBAAO;aAAO;YAE3G,MAAMC,UAAUH,SAASvqB,KAAK,CAAC,IAAI,CAAC,EAAE;YAEtC,IAAI,CAACyqB,yBAAyBrwB,QAAQ,CAACswB,UAAU;gBAC/C,MAAM,IAAI7jB,MAAM,CAAC,6BAA6B,EAAE0jB,SAAS,oBAAoB,EAAEE,yBAAyBjwB,IAAI,CAAC,OAAO;YACtH;YAEA,+BAA+B;YAC/B,IAAIe;YACJ,IAAI;gBACFA,SAASovB,OAAO/V,IAAI,CAAC4V,SAAS;YAChC,EAAE,OAAO1mB,OAAO;gBACd,MAAM,IAAI+C,MAAM;YAClB;YAEA,6BAA6B;YAC7B,MAAMvM,YAAYC,MAAKC,IAAI,CAAC;YAC5B,MAAMowB,SAAQC,SAAS,CAACvwB;YAExB,sCAAsC;YACtC,MAAMS,aAAa,CAAC,MAAM,EAAEtB,KAAKuB,GAAG,GAAG,CAAC,EAAEC,KAAKC,MAAM,GAAGT,QAAQ,CAAC,IAAIU,SAAS,CAAC,GAAG,CAAC,EAAEuvB,SAAS;YAC9F,MAAMtvB,WAAWb,MAAKC,IAAI,CAACF,WAAWS;YAEtC,iBAAiB;YACjB,MAAML,IAAGC,QAAQ,CAACuf,SAAS,CAAC9e,UAAUG;YAEtC,6CAA6C;YAC7C,IAAI,CAACb,IAAG+e,UAAU,CAACre,aAAaV,IAAGowB,QAAQ,CAAC1vB,UAAUlB,IAAI,KAAK,GAAG;gBAChE,MAAM,IAAI2M,MAAM;YAClB;YAEA,gDAAgD;YAGhD,8BAA8B;YAC9B;;;;;UAKA,GAEA,MAAMkkB,WAAW,IAAIC,KAAK;gBAACzvB;aAAO,EAAE;gBAAEuH,MAAMynB;YAAS;YACrD,MAAMvwB,OAAO,IAAIixB,KAAK;gBAACF;aAAS,EAAEhwB,YAAY;gBAAE+H,MAAMynB;YAAS;YAG/D,MAAMW,OAAO,IAAIC;YACjBD,KAAKE,MAAM,CAAC,QAAQpxB;YACpBkxB,KAAKE,MAAM,CAAC,SAAS;YAErB,MAAMC,SAAS;gBACXC,SAAS;oBACL,iBAAiB,CAAC,OAAO,EAAE7O,QAAQC,GAAG,CAAC6O,cAAc,EAAE;oBACvD,gBAAgB;gBAEpB;YACJ;YACA,MAAM5e,WAAW,MAAM6e,cAAK,CAACC,IAAI,CAAC,kDAAkDP,MAAMG;YAG1F,kDAAkD;YAClD,MAAM3wB,IAAGC,QAAQ,CAAC+wB,MAAM,CAACtwB;YAEzB,MAAMuwB,gBAAgBhf,SAAS/U,IAAI,CAAC6Z,IAAI;YAExC,MAAMgE,WAAW;gBACfqU,WAAW;YACb;YAEA,OAAO,MAAM,IAAI,CAACD,aAAa,CAAC8B,eAAelW,UAAUvf;QAC3D,EAAE,OAAO4N,OAAO;YACdxB,QAAQwB,KAAK,CAAC,4BAA4BA;YAC1C,OAAO;gBAAEA,OAAO;YAA6B;QAC/C;IAEF;IAEA,MAAM8nB,kBAAkB5B,MAAc,EAAE9zB,MAAc,EAAE;QACtD,IAAI;YACF,iBAAiB;YACnB,IAAI,CAAC8zB,UAAU,OAAOA,WAAW,YAAYA,OAAO9Z,IAAI,OAAO,IAAI;gBACjE,MAAM,IAAIrJ,MAAM;YACpB;YAIA,mCAAmC;YACnC,MAAMwjB,cAAcL,OAAOM,KAAK,CAAC;YACjC,IAAI,CAACD,eAAeA,YAAYhxB,MAAM,GAAG,GAAG;gBACxC,MAAM,IAAIwN,MAAM;YACpB;YAGA,MAAM,GAAG0jB,UAAUC,QAAQ,GAAGH;YAC9B,MAAMwB,kBAAkB;gBAAC;gBAAa;gBAAc;gBAAc;gBAAa;aAAY;YAG3F,IAAI,CAACA,gBAAgBzxB,QAAQ,CAACmwB,WAAW;gBACrC,MAAM,IAAI1jB,MAAM,CAAC,6BAA6B,EAAE0jB,UAAU;YAC9D;YAEA,mCAAmC;YACnC,IAAIhvB;YAEJ,IAAI;gBACAA,SAASovB,OAAO/V,IAAI,CAAC4V,SAAS;YAClC,EAAE,OAAO1mB,OAAO;gBACZ,MAAM,IAAI+C,MAAM;YACpB;YAIA,mCAAmC;YACnC,MAAMvM,YAAYC,MAAKC,IAAI,CAAC;YAC5B,MAAME,IAAGC,QAAQ,CAACC,KAAK,CAACN,WAAW;gBAAEO,WAAW;YAAK;YAErD,sCAAsC;YACtC,MAAM6vB,UAAUH,SAASvqB,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,MAAMjF,aAAa,CAAC,MAAM,EAAEtB,KAAKuB,GAAG,GAAG,CAAC,EAAEC,KAAKC,MAAM,GAAGT,QAAQ,CAAC,IAAIU,SAAS,CAAC,GAAG,CAAC,EAAEuvB,SAAS;YAC9F,MAAMtvB,WAAWb,MAAKC,IAAI,CAACF,WAAWS;YAEtC,IAAI+wB,kBAAkB;YAEtB,4BAA4B;YAE5B,IAAI;gBACA,MAAMC,UAAW7R,SAAS,CAAC9e,UAAUG;YACzC,EAAE,OAAOuI,OAAO;gBACZ,MAAM,IAAI+C,MAAM,CAAC,iCAAiC,EAAE/C,MAAMjN,OAAO,EAAE;YACvE;YAEI,MAAM4zB,2BAA2B;gBAAC;gBAAQ;gBAAO;gBAAO;gBAAO;gBAAQ;gBAAQ;gBAAO;gBAAO;gBAAO;aAAO;YAC3G,IAAIuB,cAAc;YAClB,IAAIC,YAAY;YAEhB,IAAI,CAACxB,yBAAyBrwB,QAAQ,CAACswB,UAAU;gBAC/C,kBAAkB;gBAClBsB,cAAc,CAAC,MAAM,EAAEvyB,KAAKuB,GAAG,GAAG,CAAC,EAAEC,KAAKC,MAAM,GAAGT,QAAQ,CAAC,IAAIU,SAAS,CAAC,GAAG,KAAK,CAAC;gBACnF,MAAM+wB,oBAAoB3xB,MAAKC,IAAI,CAACF,WAAW0xB;gBAC/C,MAAM1wB,IAAAA,cAAK,EAACC,QAAQU,MAAM,CAACiwB;gBAC3BD,YAAYC;YACd;YAEA,MAAMC,aAAa,AAAC,CAACL,kBAAmB1wB,WAAW6wB;YAEnD,kEAAkE;YAClE,+FAA+F;YAE/F,mCAAmC;YACnC,MAAMtf,WAAW,MAAM,IAAI,CAACC,MAAM,CAACwf,KAAK,CAACC,cAAc,CAACr2B,MAAM,CAAC;gBAC7DgE,MAAMU,IAAG4xB,gBAAgB,CAACH;gBAC1Bpf,OAAO;YACT;YAEA,oDAAoD;YACpDrS,IAAG6xB,UAAU,CAACnxB;YACd,IAAI0wB,iBAAiB;gBACnBpxB,IAAG6xB,UAAU,CAACN;YAChB;YAEA,MAAMN,gBAAgBhf,SAAS8E,IAAI;YAEnC,MAAMgE,WAAW;gBACfqU,WAAW;YACb;YAEA,OAAO,MAAM,IAAI,CAACD,aAAa,CAAC8B,eAAelW,UAAUvf;QAC3D,EAAE,OAAO4N,OAAO;YACdxB,QAAQwB,KAAK,CAAC,4BAA4BA;YAC1C,OAAO;gBAAEA,OAAO;YAA6B;QAC/C;IACF;IAGA,MAAM0oB,oBAAoBt2B,MAAc,EAAEmG,KAAU,EAAEnD,IAAS,EAAE;QAC/DoJ,QAAQC,GAAG,CAAC,mCAAmC;YAAErM;YAAQmG;YAAOnD;QAAK;QAErE,MAAMuzB,WAAWpwB,MAAMytB,SAAS,IAAI,OAAO,gCAAgC;QAC3E,MAAM4C,SAASrwB,MAAM2L,OAAO,IAAI;QAEhC1F,QAAQC,GAAG,CAAC,8BAA8B;YAAEkqB;YAAUC;QAAO;QAE7D,MAAMjX,WAAW;YACfqU,WAAW2C;YACXzkB,SAAS0kB;QACX;QAEA,MAAMC,UAAUzzB,KAAK4J,IAAI;QACzB,MAAMmK,UAAU/T,KAAK+T,OAAO;QAC5B,IAAIN;QAEJrK,QAAQC,GAAG,CAAC,wBAAwBoqB;QAEpC,IAAIA,YAAY,QAAQ;YACtBhgB,WAAW,MAAM,IAAI,CAACkd,aAAa,CAAC5c,SAASwI,UAAUvf;QACzD,OAAO,IAAIy2B,YAAY,SAAS;YAC9BhgB,WAAW,MAAM,IAAI,CAACod,cAAc,CAAC9c;QACvC,OAAO,IAAI0f,YAAY,SAAS;YAC9BhgB,WAAW,MAAM,IAAI,CAACyd,cAAc,CAACnd,SAAS/W;QAChD,OAAO;YACL,MAAM,IAAI2Q,MAAM;QAClB;QAEAvE,QAAQC,GAAG,CAAC,sBAAsBoK;QAElC,yCAAyC;QACzC,MAAMigB,WAAWlf,KAAKC,KAAK,CAAC,IAAI,CAACH,qBAAqB,CAACb;QAEvDrK,QAAQC,GAAG,CAAC,qBAAqBqqB;QAEjC,IAAI9I,kBAAkB8I,UAAU9I,mBAAmB,EAAE;QAErDxhB,QAAQC,GAAG,CAAC,8CAA8CuhB;QAE1D,IAAG7a,MAAMC,OAAO,CAAC4a,oBAAoBA,iBAAiBzqB,SAAS,GAAG;YAChEyqB,kBAAkBA,gBAAgBhf,GAAG,CAAC,CAACqX;gBACrC,mDAAmD;gBACnD,IAAI,CAAClT,MAAMC,OAAO,CAACiT,OAAO;oBACxB7Z,QAAQU,IAAI,CAAC,2BAA2BmZ;oBACxC,OAAOA;gBACT;gBACA,OAAOA,KAAKrX,GAAG,CAAC,CAAC+nB;oBACf,OAAO;wBACL,GAAGA,KAAK;wBACR3kB,UAAU1P,WAAWhB,OAAOq1B,MAAM3kB,QAAQ,EAAEzP,OAAO,CAAC;wBACpDmF,UAAUpF,WAAWhB,OAAOq1B,MAAMjvB,QAAQ,EAAEnF,OAAO,CAAC;wBACpD0F,SAAS3F,WAAWhB,OAAOq1B,MAAM1uB,OAAO,EAAE1F,OAAO,CAAC;wBAClD2F,OAAO5F,WAAWhB,OAAOq1B,MAAMzuB,KAAK,EAAE3F,OAAO,CAAC;wBAC9C4F,KAAK7F,WAAWhB,OAAOq1B,MAAMxuB,GAAG,EAAE5F,OAAO,CAAC;wBAC1CuG,OAAOxG,WAAWhB,OAAOq1B,MAAM7tB,KAAK,EAAEvG,OAAO,CAAC;oBAChD;gBACF;YACF;QACF;QAEA6J,QAAQC,GAAG,CAAC,0CAA0CuhB;QAEtD,MAAMte,SAAS;YACb5O,QAAQ;YACRgB,MAAMksB,mBAAmB,EAAE;QAC7B;QAEAxhB,QAAQC,GAAG,CAAC,uBAAuBiD;QAEnC,OAAOA;IACT;IAEA,eAAe;IACf,MAAMsnB,eAAe52B,MAAc,EAAE;QACnC,IAAI62B,YAAiB;QACrB,IAAIC,QAAa;QAEjB,wBAAwB;QACxB,MAAMC,cAAc,MAAM52B,YAAE,CAACC,UAAU,CAAC,cACvCC,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAAC,UACPC,gBAAgB;QAEjB,IAAIu2B,aAAa;YACfF,YAAYE,YAAYr2B,MAAM;YAE9B,IAAGm2B,cAAc,YAAY;gBAC3BA,YAAY;YACd;QACF;QAEA,oBAAoB;QACpB,MAAMG,UAAU,MAAM72B,YAAE,CAACC,UAAU,CAAC,eACnCC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,WAAW,KAAK,GACtBE,MAAM,CAAC,WACPC,gBAAgB;QAEjB,IAAIw2B,SAAS;YACXF,QAAQ;QACV;QAEA,OAAO;YACLp2B,QAAQ;YACRgB,MAAM;gBACJm1B;gBACAC;YACF;QACF;IACF;IAGA,aAAa;IACb,MAAMG,gBAAgBj0B,IAAS,EAAEhD,MAAc,EAAE;QAC/C,MAAM,EAAEk3B,OAAO,EAAE,GAAGl0B;QAEpB,wCAAwC;QACxC,MAAM+zB,cAAc,MAAM52B,YAAE,CAACC,UAAU,CAAC,cACvCC,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAAC,MACPC,gBAAgB;QAEjB,IAAIu2B,aAAa;YACf,OAAO;gBACLr2B,QAAQ;gBACRgB,MAAM,EAAE;YACV;QACF;QAEA,IAAIy1B,SAAS;QACb,IAAID,SAAS;YACX,MAAME,UAAe,MAAMj3B,YAAE,CAACC,UAAU,CAAC,mBACxCC,KAAK,CAAC,UAAU,KAAK62B,QAAQle,WAAW,GAAGgB,IAAI,IAC/C3Z,KAAK,CAAC,aAAa,KAAK,oBACxBE,MAAM,CAAC;gBAAC;aAAU,EAClBC,gBAAgB;YAEjB,IAAI42B,SAAS;gBACXD,SAASC,QAAQ/1B,OAAO;YAC1B;QACF;QAEA,MAAMg2B,gBAAqB;YACzB32B,QAAQ;YACRW,SAASrB;YACTs3B,aAAaH;YACbI,QAAQL,UAAUA,QAAQle,WAAW,GAAGgB,IAAI,KAAK;YACjDwd,WAAW;YACXrsB,YAAY,IAAI5H;YAChBD,YAAY,IAAIC;QAClB;QAEA,MAAMk0B,eAAe,MAAMt3B,YAAE,CAACgB,UAAU,CAAC,cACxCC,MAAM,CAACi2B,eACP5V,uBAAuB;QAExB,OAAO;YACL/gB,QAAQ;YACRgB,MAAM,EAAE;QACV;IACF;IAEA,QAAQ;IACR,WAAW;IACX,MAAMg2B,cAAc13B,MAAc,EAAE23B,MAAc,EAAE;QAClD,MAAMC,OAAO,MAAMz3B,YAAE,CAACC,UAAU,CAAC,4BAChCC,KAAK,CAAC,MAAM,KAAKs3B,QACjBp3B,MAAM,CAAC;YAAC;SAA+B,EACvCkhB,uBAAuB;QAExB,MAAMoW,eAAeD,KAAKE,4BAA4B;QAEtD,YAAY;QACZ,MAAMj2B,OAAO,MAAM1B,YAAE,CAACC,UAAU,CAAC,SAChCC,KAAK,CAAC,MAAM,KAAKL,QACjBO,MAAM,CAAC;YAAC;YAAS;SAAW,EAC5BkhB,uBAAuB;QAExB,0CAA0C;QAC1C,IAAIsW,aAAal2B,KAAKm2B,QAAQ;QAE9B,IAAI,CAACD,YAAY;YACf,MAAME,WAAW,MAAM,IAAI,CAACC,MAAM,CAACC,SAAS,CAACr4B,MAAM,CACjD;gBACE,aAAa;gBACbQ,OAAOuB,KAAKvB,KAAK;gBACjBgF,UAAU;oBACRtF,QAAQA,OAAOuE,QAAQ;gBACzB;YACF;YAGFwzB,aAAaE,SAASt2B,EAAE;YAExB,MAAMxB,YAAE,CAACqD,WAAW,CAAC,SACpBC,GAAG,CAAC;gBACHu0B,UAAUD;YACZ,GACC13B,KAAK,CAAC,MAAM,KAAKL,QACjBwB,OAAO;QACV;QAEA,aAAa;QACb,MAAM42B,UAAU,MAAM,IAAI,CAACF,MAAM,CAACG,QAAQ,CAACC,QAAQ,CAACx4B,MAAM,CAAC;YACzDy4B,sBAAsB;gBAAC;aAAO;YAC9BC,MAAM;YACNC,qBAAqBz4B,OAAOuE,QAAQ;YACpC0zB,UAAUF;YACVW,aAAanS,QAAQC,GAAG,CAACmS,kBAAkB;YAC3CC,YAAYrS,QAAQC,GAAG,CAACqS,iBAAiB;YACzCC,YAAY;gBACV;oBACEC,OAAOlB;oBACP7lB,UAAU;gBACZ;aACD;QACH;QAEA,OAAO;YACLtR,QAAQ;YACRgB,MAAM;gBACJuyB,KAAKmE,QAAQnE,GAAG;YAClB;QACF;IACF;IAEA,QAAQ;IACR,MAAM+E,YAAYh5B,MAAc,EAAE;QAChC,wCAAwC;QACxC,MAAMi5B,wBAAwB,MAAM94B,YAAE,CAACC,UAAU,CAAC,uBACjDC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,UAAU,KAAK,UACrBE,MAAM,CAAC,WACPC,gBAAgB;QAEjB,qBAAqB;QACrB,MAAM04B,QAAQ,MAAM/4B,YAAE,CAACC,UAAU,CAAC,eACjC0B,QAAQ,CAAC,kCAAkC,SAAS,cACpDA,QAAQ,CAAC,2BAA2B,0BAA0B,SAC9DvB,MAAM,CAAC;YACN;YAAS;YAAgB;YAAW;YACpCkd,IAAAA,WAAG,CAAA,CAAC,4BAA4B,CAAC,CAACtW,EAAE,CAAC;YACrCsW,IAAAA,WAAG,CAAA,CAAC,kCAAkC,CAAC,CAACtW,EAAE,CAAC;YAC3CsW,IAAAA,WAAG,CAAA,CAAC,sCAAsC,CAAC,CAACtW,EAAE,CAAC;YAC/C;SACD,EACA9G,KAAK,CAAC,eAAe,KAAK,OAC1BA,KAAK,CAAC,iBAAiB,MAAM,MAC7BA,KAAK,CAAC,iBAAiB,MAAM,MAC7BmB,OAAO;QAER,uDAAuD;QACvD,OAAO;YACLd,QAAQ;YACRgB,MAAMw3B,MAAMtqB,GAAG,CAAC,CAACgpB;gBACf,OAAO;oBACL,GAAGA,IAAI;oBACPuB,UAAUF,wBAAwBrB,KAAKj2B,EAAE,KAAKs3B,sBAAsBG,OAAO,GAAG;gBAChF;YACF;QACF;IACF;IAEF,uBAAuB;IACvB,MAAMC,mBAAmBr5B,MAAc,EAAE;QACrC,MAAMs5B,gBAAgB,MAAMn5B,YAAE,CACzBC,UAAU,CAAC,6BACX0B,QAAQ,CAAC,cAAc,QAAQ,cAC/BA,QAAQ,CAAC,2BAA2B,SAAS,+BAC7CzB,KAAK,CAAC,cAAc,KAAKL,QACzBO,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAuC,OAAO,CAAC,iBAAiB,QACzBtB,OAAO;QAEZ,OAAO;YACHd,QAAQ;YACRgB,MAAM43B;QACV;IACJ;IAEA,MAAMC,mBAAmBC,cAAsB,EAAEx5B,MAAc,EAAE;QAC7D,gDAAgD;QAChD,MAAMy5B,eAAe,MAAMt5B,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKm5B,gBACjBn5B,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAAC;YAAC;YAAM;YAAU;SAA+B,EACvDC,gBAAgB;QAErB,IAAI,CAACi5B,cAAc;YACf,MAAM,IAAIh5B,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA6B;YAC3C,GAAG;QACP;QAEA,IAAI84B,aAAa/4B,MAAM,KAAK,YAAY;YACpC,MAAM,IAAID,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAgC;YAC9C,GAAG;QACP;QAEA,yDAAyD;QACzD,MAAMR,YAAE,CACHqD,WAAW,CAAC,uBACZC,GAAG,CAAC;YACDi2B,sBAAsB;YACtBp2B,YAAY,IAAIC;QACpB,GACClD,KAAK,CAAC,MAAM,KAAKm5B,gBACjBh4B,OAAO;QAEZ,OAAO;YACHd,QAAQ;YACRC,SAAS;QACb;IACJ;IAEA,MAAMg5B,8BAA8BH,cAAsB,EAAEx5B,MAAc,EAAE;QACxE,gDAAgD;QAChD,MAAMy5B,eAAe,MAAMt5B,YAAE,CACxBC,UAAU,CAAC,uBACXC,KAAK,CAAC,MAAM,KAAKm5B,gBACjBn5B,KAAK,CAAC,WAAW,KAAKL,QACtBO,MAAM,CAAC;YAAC;YAAM;SAAS,EACvBC,gBAAgB;QAErB,IAAI,CAACi5B,cAAc;YACf,MAAM,IAAIh5B,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA6B;YAC3C,GAAG;QACP;QAEA,IAAI84B,aAAa/4B,MAAM,KAAK,YAAY;YACpC,MAAM,IAAID,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAAgC;YAC9C,GAAG;QACP;QAEA,yBAAyB;QACzB,MAAMR,YAAE,CACHqD,WAAW,CAAC,uBACZC,GAAG,CAAC;YACD/C,QAAQ;YACRk5B,UAAU,IAAIr2B;YACdD,YAAY,IAAIC;QACpB,GACClD,KAAK,CAAC,MAAM,KAAKm5B,gBACjBh4B,OAAO;QAEZ,OAAO;YACHd,QAAQ;YACRC,SAAS;QACb;IACJ;IAEA,sBAAsB;IACtB,MAAMk5B,kBAAkB75B,MAAc,EAAE;QACpC,MAAM85B,eAAe,MAAM35B,YAAE,CACxBC,UAAU,CAAC,qBACX0B,QAAQ,CAAC,2BAA2B,SAAS,yBAC7CzB,KAAK,CAAC,aAAa,KAAKL,QACxBO,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAuC,OAAO,CAAC,gBAAgB,QACxBtB,OAAO;QAEZ,OAAO;YACHd,QAAQ;YACRgB,MAAMo4B;QACV;IACJ;IAEA,MAAMC,wBAAwBC,aAAqB,EAAEh6B,MAAc,EAAE;QACjE,MAAM0X,cAAc,MAAMvX,YAAE,CACvBC,UAAU,CAAC,qBACX0B,QAAQ,CAAC,2BAA2B,SAAS,yBAC7CA,QAAQ,CAAC,6BAA6B,SAAS,eAC/CA,QAAQ,CAAC,cAAc,QAAQ,cAC/BzB,KAAK,CAAC,QAAQ,KAAK25B,eACnB35B,KAAK,CAAC,aAAa,KAAKL,QACxBO,MAAM,CAAC;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACH,EACAC,gBAAgB;QAErB,IAAI,CAACkX,aAAa;YACd,MAAM,IAAIjX,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAAC;iBAA4B;YAC1C,GAAG;QACP;QAEA,OAAO;YACHD,QAAQ;YACRgB,MAAMgW;QACV;IACJ;IAEA,6DAA6D;IAC7D,MAAMuiB,YAAYj6B,MAAc,EAAEmG,KAAU,EAAE;QAC1C,IAAI;YACA,8DAA8D;YAC9D,MAAMwf,cAAc,MAAMxlB,YAAE,CACvBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtB8C,OAAO,CAAC,cAAc,QACtB0L,KAAK,CAAC,IACNjO,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;aACH,EACAiB,OAAO;YAEZ,8BAA8B;YAC9B,MAAM04B,gBAAgBvU,YAAY/W,GAAG,CAACurB,CAAAA,IAAKA,EAAEx4B,EAAE;YAC/C,IAAIukB,SAAgB,EAAE;YACtB,IAAIgU,cAAc/2B,MAAM,GAAG,GAAG;gBAC1B+iB,SAAS,MAAM/lB,YAAE,CACZC,UAAU,CAAC,sBACXC,KAAK,CAAC,iBAAiB,MAAM65B,eAC7B35B,MAAM,CAAC;oBACJ;oBACA;oBACA;iBACH,EACAiB,OAAO;YAChB;YAEA,wDAAwD;YACxD,MAAM44B,eAAezU,YAAY/W,GAAG,CAACmX,CAAAA;gBACjC,MAAMsU,aAAanU,OAAOjX,MAAM,CAACC,CAAAA,IAAKA,EAAEsV,aAAa,KAAKuB,WAAWpkB,EAAE;gBACvE,MAAM24B,WAAgB,CAAC;gBACvBD,WAAWvhB,OAAO,CAAC1W,CAAAA;oBACfk4B,QAAQ,CAACl4B,MAAMsiB,cAAc,CAAC,GAAGtiB,MAAMkW,SAAS;gBACpD;gBAEA,OAAO;oBACH9V,QAAQujB,WAAWvjB,MAAM;oBACzBuT,SAASgQ,WAAWwU,mBAAmB;oBACvCtxB,MAAM8c,WAAW5a,UAAU;oBAC3B+a,QAAQoU;gBACZ;YACJ;YAEA,OAAO;gBACH55B,QAAQ;gBACRgB,MAAM04B;YACV;QACJ,EAAE,OAAOxsB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,gCAAgCA;YAC9C,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM,EAAE;YACZ;QACJ;IACJ;IAEA,8BAA8B;IAC9B,MAAM84B,4BAA4Bx6B,MAAc,EAAEmG,KAAU,EAAE;QAC1D,IAAI;YACA,+CAA+C;YAC/C,MAAMQ,aAAanH,QAAQ8yB,QAAQ,CAAC,GAAG,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;YACpE,MAAMC,WAAWrH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;YAE5C,qCAAqC;YACrC,MAAM6zB,YAAY,MAAMt6B,YAAE,CACrBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBtG,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;aACH,EACAuC,OAAO,CAAC,YAAY,OACpBtB,OAAO;YAEZ,OAAO;gBACHd,QAAQ;gBACRgB,MAAM+4B;YACV;QACJ,EAAE,OAAO7sB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,6CAA6CA;YAC3D,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM,EAAE;YACZ;QACJ;IACJ;IAEA,6BAA6B;IAC7B,MAAMg5B,2BAA2B16B,MAAc,EAAEmG,KAAU,EAAE;QACzD,IAAI;YACA,6CAA6C;YAC7C,MAAMQ,aAAanH,QAAQ8yB,QAAQ,CAAC,GAAG,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;YACpE,MAAMC,WAAWrH,QAAQkH,KAAK,CAAC,OAAOE,MAAM;YAE5C,MAAMgjB,cAAc,MAAMzpB,YAAE,CACvBC,UAAU,CAAC,6BACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBtG,MAAM,CAAC;gBACJ;gBACA;aACH,EACAuC,OAAO,CAAC,YAAY,OACpBtB,OAAO;YAEZ,OAAO;gBACHd,QAAQ;gBACRgB,MAAMkoB;YACV;QACJ,EAAE,OAAOhc,OAAO;YACZxB,QAAQwB,KAAK,CAAC,2CAA2CA;YACzD,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM,EAAE;YACZ;QACJ;IACJ;IAEA,oBAAoB;IACpB,MAAMi5B,qBAAqB36B,MAAc,EAAEmG,KAAU,EAAE;QACnD,IAAI;YACA,4EAA4E;YAC5E,4EAA4E;YAC5E,OAAO;gBACHzF,QAAQ;gBACRgB,MAAM,CAAC;YACX;QACJ,EAAE,OAAOkM,OAAO;YACZxB,QAAQwB,KAAK,CAAC,qCAAqCA;YACnD,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM,CAAC;YACX;QACJ;IACJ;IAEA,oBAAoB;IACpB,MAAMk5B,qBAAqB56B,MAAc,EAAEmG,KAAU,EAAE;QACnD,IAAI;YACA,MAAM,EAAEusB,IAAI,EAAE,GAAGvsB;YAEjB,mEAAmE;YACnE,IAAIQ;YACJ,IAAIE;YAEJ,IAAI6rB,MAAM;gBACN,MAAMmI,WAAWr7B,MAAMkzB;gBACvB/rB,aAAak0B,SAASr0B,OAAO,CAAC,QAAQI,MAAM;gBAC5CC,WAAWg0B,SAASn0B,KAAK,CAAC,QAAQE,MAAM;YAC5C,OAAO;gBACH,0BAA0B;gBAC1BD,aAAanH,QAAQgH,OAAO,CAAC,QAAQI,MAAM;gBAC3CC,WAAWrH,QAAQkH,KAAK,CAAC,QAAQE,MAAM;YAC3C;YAEA,yDAAyD;YACzD,MAAMk0B,kBAAkBt7B,MAAMmH,YAAYkX,MAAM,CAAC;YACjD,MAAMkd,gBAAgBv7B,MAAMqH,UAAUgX,MAAM,CAAC;YAE7C,sDAAsD;YACtD,MAAMmd,eAAe,MAAM76B,YAAE,CACxBC,UAAU,CAAC,oBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,aAAa,MAAMy6B,iBACzBz6B,KAAK,CAAC,aAAa,MAAM06B,eACzBx6B,MAAM,CAAC;gBACJ;gBACA;gBACA;aACH,EACAiB,OAAO;YAEZ,wDAAwD;YACxD,MAAMooB,cAAc,MAAMzpB,YAAE,CACvBC,UAAU,CAAC,6BACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBtG,MAAM,CAAC;gBACJ;gBACAJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC;aACxB,EACApF,OAAO,CAAC,YACRP,OAAO;YAEZ,8CAA8C;YAC9C,MAAMy5B,oBAAoB,MAAM96B,YAAE,CAC7BC,UAAU,CAAC,wBACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBtG,MAAM,CAAC;gBACJ;gBACAJ,YAAE,CAAC8G,EAAE,CAACyG,KAAK,CAAC,MAAMvG,EAAE,CAAC;aACxB,EACApF,OAAO,CAAC,YACRP,OAAO;YAEZ,kCAAkC;YAClC,MAAM05B,iBAAiBF,aAAanzB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAOuD,CAAAA,IAAI+O,KAAK,IAAI,CAAA,GAAI;YACjF,MAAM2hB,qBAAqBH,aAAanzB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAOuD,CAAAA,IAAI2wB,eAAe,IAAI,CAAA,GAAI;YAC/F,MAAMC,iBAAiBH,iBAAiB,IAAI,AAACC,qBAAqBD,iBAAkB,MAAM;YAE1F,MAAMI,yBAAyB1R,YAAY/hB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAM5F,OAAOmJ,IAAI8wB,gBAAgB,IAAI,IAAI;YACzG,MAAMC,uBAAuBP,kBAAkBpzB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAM5F,OAAOmJ,IAAIgxB,gBAAgB,IAAI,IAAI;YAC7G,MAAMC,oBAAoBF,uBAAuB,IAAI,AAACF,yBAAyBE,uBAAwB,MAC/EF,yBAAyB,IAAI,MAAM,GAAG,oDAAoD;YAElH,wCAAwC;YACxC,MAAMzR,YAAY,MAAM1pB,YAAE,CACrBC,UAAU,CAAC,eACXC,KAAK,CAAC,WAAW,KAAKL,QACtBK,KAAK,CAAC,YAAY,MAAMsG,YACxBtG,KAAK,CAAC,YAAY,MAAMwG,UACxBtG,MAAM,CAAC;gBACJ;gBACA;gBACA;aACH,EACAiB,OAAO;YAEZ,MAAMm6B,oBAAoB9R,UAAUhiB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAOuD,CAAAA,IAAImxB,YAAY,IAAI,CAAA,GAAI;YACxF,MAAMC,wBAAwBhS,UAAUhiB,MAAM,CAAC,CAACX,KAAKuD,MAAQvD,MAAOuD,CAAAA,IAAIqxB,gBAAgB,IAAI,CAAA,GAAI;YAChG,MAAMC,kBAAkBJ,oBAAoB,IAAI,AAACE,wBAAwBF,oBAAqB,MAAM;YAEpG,MAAMK,oBAAoB,AAACX,CAAAA,iBAAiBK,oBAAoBK,eAAc,IAAK;YAEnF,OAAO;gBACHr7B,QAAQ;gBACRgB,MAAM;oBACFu6B,MAAM;wBACFvxB,WAAWywB;wBACX/yB,OAAO8yB;wBACPzQ,YAAY1lB,KAAK+iB,KAAK,CAACuT,iBAAiB,MAAM;oBAClD;oBACAloB,SAAS;wBACLzI,WAAW4wB;wBACXlzB,OAAOozB,wBAAwBF;wBAC/B7Q,YAAY1lB,KAAK+iB,KAAK,CAAC4T,oBAAoB,MAAM;oBACrD;oBACA10B,OAAO;wBACH0D,WAAWmxB;wBACXzzB,OAAOuzB;wBACPlR,YAAY1lB,KAAK+iB,KAAK,CAACiU,kBAAkB,MAAM;oBACnD;oBACAG,SAAS;wBACLzR,YAAY1lB,KAAK+iB,KAAK,CAACkU,oBAAoB,MAAM;oBACrD;gBACJ;YACJ;QACJ,EAAE,OAAOpuB,OAAO;YACZxB,QAAQwB,KAAK,CAAC,qCAAqCA;YACnD,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM;oBACFu6B,MAAM;wBAAEvxB,WAAW;wBAAGtC,OAAO;wBAAGqiB,YAAY;oBAAE;oBAC9CtX,SAAS;wBAAEzI,WAAW;wBAAGtC,OAAO;wBAAGqiB,YAAY;oBAAE;oBACjDzjB,OAAO;wBAAE0D,WAAW;wBAAGtC,OAAO;wBAAGqiB,YAAY;oBAAE;oBAC/CyR,SAAS;wBAAEzR,YAAY;oBAAE;gBAC7B;YACJ;QACJ;IACJ;IAEA,0BAA0B;IAE1B;;KAEC,GACD,MAAM0R,aAAan8B,MAAc,EAAEgD,IAAS,EAAE;QAC1C,IAAI;YACA,MAAM,EAAE0N,UAAU,EAAE0rB,UAAU,EAAEhpB,SAAS,EAAEnK,IAAI,EAAE,GAAGjG;YAEpDoJ,QAAQC,GAAG,CAAC,wDAAwDrM;YACpEoM,QAAQC,GAAG,CAAC,mBAAmBqE;YAC/BtE,QAAQC,GAAG,CAAC,YAAYpD;YAExB,iDAAiD;YACjD,IAAIozB,oBAAoB3rB;YACxB,IAAI,CAAC2rB,mBAAmB;gBACpB,MAAMtuB,iBAAiB,MAAM,IAAI,CAAC8J,0BAA0B,CAAC7X;gBAC7D,IAAI+N,eAAerM,IAAI,CAACoW,YAAY,EAAE;oBAClCukB,oBAAoBtuB,eAAerM,IAAI,CAACC,EAAE;gBAC9C,OAAO;oBACH,MAAM,IAAIgP,MAAM;gBACpB;YACJ;YAEA,gCAAgC;YAChC0rB,oBAAoB/6B,OAAO+6B;YAC3B,IAAI1f,MAAM0f,oBAAoB;gBAC1B,MAAM,IAAI1rB,MAAM;YACpB;YAEA,6CAA6C;YAC7C,MAAMpE,WAAW,MAAMpM,YAAE,CACpBC,UAAU,CAAC,mBACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKg8B,mBACjBh8B,KAAK,CAAC,aAAa,KAAKL,QACxBQ,gBAAgB;YAErB,IAAI,CAAC+L,UAAU;gBACX,MAAM,IAAIoE,MAAM;YACpB;YAEA,0EAA0E;YAC1E,MAAM2rB,mBAAmB,MAAMn8B,YAAE,CAC5BC,UAAU,CAAC,4BACXoM,SAAS,GACTnM,KAAK,CAAC,eAAe,KAAKg8B,mBAC1Bv5B,OAAO,CAAC,MAAM,OACdtB,OAAO;YAEZ,IAAI86B,iBAAiBn5B,MAAM,KAAK,GAAG;gBAC/B,MAAM,IAAIwN,MAAM;YACpB;YAEA,6EAA6E;YAC7E,MAAM4rB,kBAAkBD,gBAAgB,CAAC,EAAE;YAE3C,gCAAgC;YAChC,MAAME,iBAAiB,MAAMr8B,YAAE,CAC1BgB,UAAU,CAAC,wBACXC,MAAM,CAAC;gBACJC,SAASrB;gBACTwR,aAAa6qB;gBACb/b,qBAAqBic,gBAAgB56B,EAAE;gBACvC4e,cAAc,IAAIhd;gBAClBiE,gBAAgB;gBAChBgZ,cAAc;gBACdN,KAAK;gBACLlV,UAAU,IAAIzH,KAAK0F;gBACnBkC,YAAY,IAAI5H;gBAChBD,YAAY,IAAIC;YACpB,GACC/C,gBAAgB;YAErB,MAAMi8B,YAAYn7B,OAAOk7B,eAAej7B,QAAQ;YAEhD6K,QAAQC,GAAG,CAAC,sCAAsCowB;YAElD,OAAO;gBACH/7B,QAAQ;gBACRgB,MAAM;oBACF+6B;oBACA/rB,YAAY2rB;oBACZliB,WAAWoiB,gBAAgB56B,EAAE;oBAC7BoW,aAAawkB,gBAAgBt6B,IAAI;oBACjCtB,SAAS;gBACb;YACJ;QAEJ,EAAE,OAAOiN,OAAO;YACZxB,QAAQwB,KAAK,CAAC,6BAA6BA;YAC3C,MAAM,IAAInN,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAACiN,MAAMjN,OAAO,IAAI;iBAAkC;YACjE,GAAG;QACP;IACJ;IAEA;;KAEC,GACD,MAAM+7B,kBAAkB18B,MAAc,EAAEmG,KAAU,EAAE;QAChD,IAAI;YACA,MAAM,EAAE8C,IAAI,EAAE,GAAG9C;YACjB,MAAMw2B,aAAa1zB,OAAO,IAAI1F,KAAK0F,QAAQ,IAAI1F;YAE/C6I,QAAQC,GAAG,CAAC,oDAAoDswB;YAEhE,MAAM/pB,WAAW,MAAMzS,YAAE,CACpBC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5CA,SAAS,CAAC,mCAAmC,UAAU,2BACvD5gB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAMb,MAAMm9B,YAAYn2B,OAAO,CAAC,OAAOI,MAAM,IACnEvG,KAAK,CAAC,gBAAgB,MAAMb,MAAMm9B,YAAYj2B,KAAK,CAAC,OAAOE,MAAM,IACjE9D,OAAO,CAAC,kBAAkB,QAC1BtB,OAAO;YAEZ,OAAO;gBACHd,QAAQ;gBACRgB,MAAMkR;YACV;QAEJ,EAAE,OAAOhF,OAAO;YACZxB,QAAQwB,KAAK,CAAC,qCAAqCA;YACnD,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM,EAAE;YACZ;QACJ;IACJ;IAEA;;KAEC,GACD,MAAMk7B,kBAAkB58B,MAAc,EAAEmG,KAAU,EAAE;QAChD,IAAI;YACA,MAAM,EAAE02B,SAAS,OAAO,EAAEtuB,OAAO,CAAC,EAAEC,QAAQ,EAAE,EAAE,GAAGrI;YACnD,MAAMsI,SAAS,AAACF,CAAAA,OAAO,CAAA,IAAKC;YAE5BpC,QAAQC,GAAG,CAAC,6DAA6DwwB;YAEzE,uCAAuC;YACvC,IAAI5sB;YACJ,IAAIC,UAAU,IAAI3M;YAElB,OAAQs5B;gBACJ,KAAK;gBACL,KAAK;oBACD5sB,YAAYzQ,QAAQ8yB,QAAQ,CAAC,GAAG,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;oBAC7D;gBACJ,KAAK;gBACL,KAAK;oBACDqJ,YAAYzQ,QAAQ8yB,QAAQ,CAAC,IAAI,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;oBAC9D;gBACJ,KAAK;gBACL,KAAK;oBACDqJ,YAAYzQ,QAAQ8yB,QAAQ,CAAC,IAAI,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;oBAC9D;gBACJ;oBACIqJ,YAAYzQ,QAAQ8yB,QAAQ,CAAC,IAAI,QAAQ9rB,OAAO,CAAC,OAAOI,MAAM;YACtE;YAEA,MAAMgM,WAAW,MAAMzS,YAAE,CACpBC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5CA,SAAS,CAAC,mCAAmC,UAAU,2BACvD5gB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM4P,WAC5B5P,KAAK,CAAC,gBAAgB,MAAM6P,QAC7B,4DAA4D;YAC5D,mFAAmF;aAClFpN,OAAO,CAAC,gBAAgB,QACxB0L,KAAK,CAACA,OACNC,MAAM,CAACA,QACPjN,OAAO;YAEZ,iCAAiC;YACjC,MAAM2N,cAAc,MAAMhP,YAAE,CACvBC,UAAU,CAAC,+BACXG,MAAM,CAACJ,YAAE,CAAC8G,EAAE,CAACsa,QAAQ,GAAGpa,EAAE,CAAC,UAC3B9G,KAAK,CAAC,eAAe,KAAKL,QAC1BK,KAAK,CAAC,gBAAgB,MAAM4P,WAC5B5P,KAAK,CAAC,gBAAgB,MAAM6P,QAC7B,qDAAqD;YACrD,8DAA8D;aAC7D1P,gBAAgB;YAErB,MAAM4H,QAAQ9G,OAAO6N,aAAa/G,SAAS;YAE3C,+BAA+B;YAC/B,MAAM+M,gBAAgBvC,SAASzP,MAAM;YACrC,MAAM25B,cAAclqB,SAAS/K,MAAM,CAAC,CAACX,KAAK2N,IAAM3N,MAAO2N,CAAAA,EAAE2L,YAAY,IAAI,CAAA,GAAI;YAC7E,MAAMuc,mBAAmB5nB,gBAAgB,IACrCvC,SAAS/K,MAAM,CAAC,CAACX,KAAK2N,IAAM3N,MAAO2N,CAAAA,EAAErN,cAAc,IAAI,CAAA,GAAI,KAAK2N,gBAAgB;YAEpF,OAAO;gBACHzU,QAAQ;gBACRgB,MAAM;oBACFkR,UAAUA,SAAShE,GAAG,CAACiG,CAAAA;wBACnB,wEAAwE;wBACxE,IAAImoB,kBAAkB,IAAI,mBAAmB;wBAC7C,IAAInoB,EAAE0L,YAAY,IAAI1L,EAAErN,cAAc,GAAG,GAAG;4BACxC,iFAAiF;4BACjFw1B,kBAAkB;wBACtB;wBAEA,OAAO;4BACH,GAAGnoB,CAAC;4BACJ,2DAA2D;4BAC3DnK,WAAW,AAACmK,EAAErN,cAAc,IAAIqN,EAAErN,cAAc,GAAG,KAAOqN,EAAE2L,YAAY,IAAI3L,EAAE2L,YAAY,GAAG,KAAM3L,EAAE0L,YAAY,KAAK;4BACtH1U,UAAUmxB;4BACVC,oBAAoBpoB,EAAE0L,YAAY,CAAC,mCAAmC;wBAC1E;oBACJ;oBACApL;oBACA2nB;oBACAC;oBACAxtB,YAAY;wBACRhB;wBACAC;wBACApG;wBACAoH,YAAYzK,KAAK0K,IAAI,CAACrH,QAAQoG;oBAClC;gBACJ;YACJ;QAEJ,EAAE,OAAOZ,OAAO;YACZxB,QAAQwB,KAAK,CAAC,oCAAoCA;YAClD,OAAO;gBACHlN,QAAQ;gBACRgB,MAAM;oBACFkR,UAAU,EAAE;oBACZuC,eAAe;oBACf2nB,aAAa;oBACbC,kBAAkB;oBAClBxtB,YAAY;wBAAEhB,MAAM;wBAAGC,OAAO;wBAAIpG,OAAO;wBAAGoH,YAAY;oBAAE;gBAC9D;YACJ;QACJ;IACJ;IAEA;;KAEC,GACD,MAAM0tB,kBAAkBl9B,MAAc,EAAEy8B,SAAiB,EAAE;QACvD,IAAI;YAGA,iCAAiC;YACjC,MAAMrE,UAAU,MAAMj4B,YAAE,CACnBC,UAAU,CAAC,+BACX+gB,SAAS,CAAC,yBAAyB,SAAS,mBAC5Crf,QAAQ,CAAC,mCAAmC,UAAU,2BACtDvB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,UAAU,KAAKiB,OAAOm7B,YAC5Bp8B,KAAK,CAAC,eAAe,KAAKL,QAC1BQ,gBAAgB;YAErB,IAAI,CAAC43B,SAAS;gBACV,MAAM,IAAIznB,MAAM;YACpB;YAEA,uFAAuF;YACvF,MAAMyC,YAAY,MAAMjT,YAAE,CACrBC,UAAU,CAAC,8CACX0B,QAAQ,CAAC,kBAAkB,QAAQ,oBACnCA,QAAQ,CAAC,uBAAuB,QAAQ,qBACxCvB,MAAM,CAAC;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACH,EACAF,KAAK,CAAC,mBAAmB,KAAK+3B,QAAQ1kB,UAAU,EAChDlS,OAAO;YAEZ,8DAA8D;YAC9D,MAAMooB,cAAc;gBAChBjoB,IAAIy2B,QAAQ1kB,UAAU;gBACtBzR,MAAMm2B,QAAQ/kB,YAAY;gBAC1ByO,eAAe1O,UAAUxE,GAAG,CAACurB,CAAAA,IAAKA,EAAE9hB,YAAY,EAAEpJ,MAAM,CAAC,CAACkuB,GAAGjW,GAAG7I,IAAMA,EAAE5C,OAAO,CAAC0hB,OAAOjW,GAAG5iB,IAAI,CAAC;gBAC/F8O,WAAWA,UAAUxE,GAAG,CAAC4E,CAAAA,WAAa,CAAA;wBAClC7R,IAAI6R,SAAS7R,EAAE;wBACfM,MAAMuR,SAAS2E,aAAa,IAAI3E,SAAS4pB,WAAW,IAAI;wBACxDzpB,MAAMH,SAASG,IAAI;wBACnBC,MAAMJ,SAASI,IAAI;wBACnBC,KAAKL,SAASK,GAAG;wBACjBC,cAAcN,SAASM,YAAY;wBACnCuE,cAAc7E,SAAS6E,YAAY;wBACnC9F,OAAOiB,SAASjB,KAAK;oBACzB,CAAA;YACJ;YAEA,OAAO;gBACH7R,QAAQ;gBACRgB,MAAM;oBACF02B,SAAS;wBACLz2B,IAAIy2B,QAAQiF,UAAU;wBACtB7rB,aAAa4mB,QAAQ5mB,WAAW;wBAChCkC,YAAY0kB,QAAQ1kB,UAAU;wBAC9BL,cAAc+kB,QAAQ/kB,YAAY;wBAClCiqB,eAAelF,QAAQkF,aAAa;wBACpC/c,cAAc6X,QAAQ7X,YAAY;wBAClC/Y,gBAAgB4wB,QAAQ5wB,cAAc;wBACtCgZ,cAAc4X,QAAQ5X,YAAY;wBAClCvX,MAAMmvB,QAAQptB,QAAQ;wBACtBG,YAAYitB,QAAQjtB,UAAU;wBAC9B0D,WAAWupB,QAAQvpB,SAAS;wBAC5B/E,OAAOsuB,QAAQtuB,KAAK;oBACxB;oBACAqJ,SAASyW;oBACT,iDAAiD;oBACjD9R,cAAc;oBACdnW,IAAIy2B,QAAQ5mB,WAAW;oBACvBvP,MAAMm2B,QAAQkF,aAAa;oBAC3B1qB,UAAU;wBAACgX;qBAAY;gBAC3B;YACJ;QAEJ,EAAE,OAAOhc,OAAO;YACZxB,QAAQwB,KAAK,CAAC,oCAAoCA;YAClD,MAAM,IAAInN,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAACiN,MAAMjN,OAAO,IAAI;iBAAgC;YAC/D,GAAG;QACP;IACJ;IAEA;;KAEC,GACD,MAAM48B,gBAAgBv9B,MAAc,EAAEma,SAAiB,EAAEqjB,cAAoB,EAAE;QAC3E,IAAI;YACApxB,QAAQC,GAAG,CAAC,kDAAkD8N;YAC9D/N,QAAQC,GAAG,CAAC,wCAAwCmxB;YAEpD,qBAAqB;YACrB,IAAI,CAACrjB,aAAawC,MAAMrb,OAAO6Y,aAAa;gBACxC,MAAM,IAAIxJ,MAAM;YACpB;YAEA,2BAA2B;YAC3B,MAAMynB,UAAU,MAAMj4B,YAAE,CACnBC,UAAU,CAAC,wBACXoM,SAAS,GACTnM,KAAK,CAAC,MAAM,KAAKiB,OAAO6Y,YACxB9Z,KAAK,CAAC,WAAW,KAAKL,QACtBQ,gBAAgB;YAErB,IAAI,CAAC43B,SAAS;gBACV,MAAM,IAAIznB,MAAM,CAAC,kCAAkC,EAAEwJ,UAAU,WAAW,EAAEna,QAAQ;YACxF;YAEAoM,QAAQC,GAAG,CAAC,sCAAsC;gBAC9C1K,IAAIy2B,QAAQz2B,EAAE;gBACd6P,aAAa4mB,QAAQ5mB,WAAW;gBAChC+O,cAAc6X,QAAQ7X,YAAY;gBAClClf,SAAS+2B,QAAQ/2B,OAAO;YAC5B;YAEA,6BAA6B;YAC7B,IAAIo8B,kBAAkB,IAAI,qBAAqB;YAE/C,gDAAgD;YAChD,IAAID,gBAAgB3xB,YAAY2xB,eAAe3xB,QAAQ,GAAG,GAAG;gBACzD4xB,kBAAkB14B,KAAK+iB,KAAK,CAAC0V,eAAe3xB,QAAQ,GAAG,KAAK,6BAA6B;gBACzFO,QAAQC,GAAG,CAAC,2CAA2CoxB,iBAAiB;YAC5E,OAAO,IAAIrF,QAAQ7X,YAAY,IAAI6X,QAAQ7X,YAAY,CAACxR,OAAO,KAAK,IAAIxL,KAAK,cAAcwL,OAAO,IAAI;gBAClG,0EAA0E;gBAC1E0uB,kBAAkB14B,KAAK+iB,KAAK,CAAC,AAAC,CAAA,IAAIvkB,OAAOwL,OAAO,KAAKqpB,QAAQ7X,YAAY,CAACxR,OAAO,EAAC,IAAM,CAAA,OAAO,EAAC;gBAChG3C,QAAQC,GAAG,CAAC,6CAA6CoxB,iBAAiB;YAC9E,OAAO;gBACHrxB,QAAQC,GAAG,CAAC,8BAA8BoxB,iBAAiB;YAC/D;YAEA,0CAA0C;YAC1C,MAAM57B,OAAO,MAAM1B,YAAE,CAChBC,UAAU,CAAC,SACXG,MAAM,CAAC;gBAAC;aAAS,EACjBF,KAAK,CAAC,MAAM,KAAKL,QACjBQ,gBAAgB;YAErB,MAAMk9B,aAAa77B,MAAMW,UAAU,IAAI,0BAA0B;YAEjE,2DAA2D;YAC3D,MAAM0d,MAAMkY,QAAQlY,GAAG,IAAI,KAAK,oCAAoC;YACpE,MAAMyd,iBAAiB54B,KAAK+iB,KAAK,CAAC5H,MAAMwd,aAAcp8B,CAAAA,OAAOm8B,mBAAmB,EAAC;YAEjF,0CAA0C;YAC1CrxB,QAAQC,GAAG,CAAC;YACZD,QAAQC,GAAG,CAAC,wBAAwBoxB,iBAAiB;YACrDrxB,QAAQC,GAAG,CAAC,uBAAuBsxB;YAEnC,MAAMx9B,YAAE,CACHqD,WAAW,CAAC,wBACZC,GAAG,CAAC;gBACD+D,gBAAgBm2B;gBAChBr6B,YAAY,IAAIC;YAGpB,GACClD,KAAK,CAAC,MAAM,KAAKiB,OAAO6Y,YACxB9Z,KAAK,CAAC,WAAW,KAAKL,QACtBwB,OAAO;YAEZ4K,QAAQC,GAAG,CAAC;YAEZD,QAAQC,GAAG,CAAC;YAEZ,OAAO;gBACH3L,QAAQ;gBACRgB,MAAM;oBACF+6B,WAAWtiB;oBACXtO,UAAU4xB;oBACVE;oBACAh9B,SAAS;gBACb;YACJ;QAEJ,EAAE,OAAOiN,OAAO;YACZxB,QAAQwB,KAAK,CAAC,+BAA+BA;YAC7C,MAAM,IAAInN,qBAAa,CAAC;gBACpBC,QAAQ;gBACRC,SAAS;oBAACiN,MAAMjN,OAAO,IAAI;iBAAqC;YACpE,GAAG;QACP;IACJ;IAEA,oEAAoE;IAC5D6sB,iCAAiCoQ,SAAc,EAAE;QACrD,MAAM,EAAEl2B,QAAQ,EAAEO,OAAO,EAAEC,KAAK,EAAEC,GAAG,EAAE,GAAGy1B;QAE1C,sEAAsE;QACtE,MAAMC,iBAAiB;YACnB;gBACIC,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,GAAGjD,KAAK4lB,GAAG,CAAC,IAAIjjB,WAAW;gBACnDs2B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQgH,WAAW,OAAO,aAAa;gBACvC+iB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAACjjB,WAAW,OAAQ;YAClD;YACA;gBACIo2B,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,GAAGjD,KAAK4lB,GAAG,CAAC,IAAI1iB,UAAU;gBAClD+1B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQuH,UAAU,MAAM,aAAa;gBACrCwiB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAAC1iB,UAAU,MAAO;YAChD;YACA;gBACI61B,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,KAAKjD,KAAK4lB,GAAG,CAAC,GAAG1iB,UAAU;gBACnD+1B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQuH,UAAU,KAAK,aAAa;gBACpCwiB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAAC1iB,UAAU,MAAO;YAChD;YACA;gBACI61B,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,KAAKjD,KAAK4lB,GAAG,CAAC,MAAMjjB,WAAW;gBACvDs2B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQgH,WAAW,OAAO,aAAa;gBACvC+iB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAACjjB,WAAW,OAAQ;YAClD;YACA;gBACIo2B,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,IAAIjD,KAAK4lB,GAAG,CAAC,KAAKziB,QAAQ;gBAClD81B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQwH,QAAQ,MAAM,aAAa;gBACnCuiB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAACziB,QAAQ,MAAO;YAC9C;YACA;gBACI41B,iBAAiB;gBACjB77B,MAAM;gBACN87B,eAAeh5B,KAAKiD,GAAG,CAAC,KAAKjD,KAAK4lB,GAAG,CAAC,KAAKxiB,MAAM;gBACjD61B,mBAAmB;gBACnB/rB,MAAM;gBACNvR,QAAQyH,MAAM,KAAK,aAAa;gBAChCsiB,YAAY1lB,KAAK4lB,GAAG,CAAC,KAAK,AAACxiB,MAAM,KAAM;YAC3C;SACH;QAED,MAAMulB,eAAemQ,eAAe5uB,MAAM,CAACmK,CAAAA,IAAKA,EAAE1Y,MAAM,KAAK,eAAe0Y,EAAE1Y,MAAM,KAAK;QACzF,MAAMitB,WAAWkQ,eAAe5uB,MAAM,CAACmK,CAAAA,IAAKA,EAAE1Y,MAAM,KAAK;QAEzD,MAAMktB,kBAAkBF,aAAa9e,GAAG,CAACqvB,CAAAA,QAAU,CAAA;gBAC/CH,iBAAiBG,MAAMH,eAAe;gBACtCI,gBAAgB,CAAC,oBAAoB,EAAED,MAAMh8B,IAAI,EAAE;gBACnDqX,OAAO,IAAI,CAAC6kB,mBAAmB,CAACF,MAAMH,eAAe;gBACrDM,UAAUH,MAAMv9B,MAAM,KAAK,cAAc,SAAS;YACtD,CAAA;QAEA,MAAMmtB,eAAe9oB,KAAK+iB,KAAK,CAC3B+V,eAAeh2B,MAAM,CAAC,CAACX,KAAK+2B,QAAU/2B,MAAM+2B,MAAMxT,UAAU,EAAE,KAAKoT,eAAe16B,MAAM;QAG5F,OAAO;YACHsqB,aAAaoQ;YACbnQ;YACAC;YACAC;YACAC;YACAC,kBAAkBJ,aAAa9e,GAAG,CAACsb,CAAAA,IAAKA,EAAEjoB,IAAI,EAAEkM,KAAK,CAAC,GAAG;QAC7D;IACJ;IAEQgwB,oBAAoBL,eAAuB,EAAY;QAC3D,MAAMO,UAAuC;YACzC,aAAa;gBAAC;gBAAU;gBAAY;gBAAQ;aAAY;YACxD,QAAQ;gBAAC;gBAAkB;gBAAU;gBAAa;aAAW;YAC7D,eAAe;gBAAC;gBAAS;gBAAS;gBAAQ;aAAa;YACvD,WAAW;gBAAC;gBAAS;gBAAU;gBAAW;aAAW;YACrD,aAAa;gBAAC;gBAAW;gBAAW;gBAAQ;aAAW;YACvD,WAAW;gBAAC;gBAAU;gBAAY;gBAAS;aAAU;QACzD;QAEA,OAAOA,OAAO,CAACP,gBAAgB,IAAI;YAAC;SAAqB;IAC7D;IAtoOAQ,aAAc;QACZ,aAAa;QACb,IAAI,CAACpG,MAAM,GAAG,IAAIqG,cAAM,CAAChY,QAAQC,GAAG,CAACgY,SAAS,EAAE;QAEhD;QAEA,IAAI,CAAC9nB,MAAM,GAAG,IAAI+nB,eAAM,CAAC;YACvBC,QAAQnY,QAAQC,GAAG,CAAC6O,cAAc;QACpC;IACF;AA8nOJ"}