import{t as O,aL as B,R as l,j as e,aM as H,aN as L,aO as U,af as N,ap as G,P as k,aP as C,aQ as Q,ad as J,b as K,y as m}from"./index-yuwXvJOX.js";function X(){var v,w,y;const g=O(),[S]=B();S.get("mode");const h=[{id:1,value_option:"Hipertrofia"},{id:2,value_option:"Força"},{id:3,value_option:"Resistência"},{id:4,value_option:"Funcional"},{id:5,value_option:"Cardio"},{id:6,value_option:"Flexibilidade"}],[E,b]=l.useState(h),[r,n]=l.useState({name:"",type:null,objective:"",startDate:new Date().toISOString().split("T")[0],frequency:4,split:"A-B-C-D",workouts:[],notes:""});l.useEffect(()=>{(async()=>{try{const t=await K.get("admin/select_options/coach_types");b(t.data)}catch(t){console.error("Error fetching coach types:",t),b(h)}})()},[]),l.useEffect(()=>{r.workouts.length===0&&n(s=>({...s,workouts:Array(s.frequency).fill(null).map((t,a)=>({name:`Treino ${String.fromCharCode(65+a)}`,exercises:[]}))}))},[]),l.useEffect(()=>{r.frequency>0&&r.workouts.length===0&&n(s=>({...s,workouts:Array(s.frequency).fill(null).map((t,a)=>({name:`Treino ${String.fromCharCode(65+a)}`,exercises:[]}))}))},[r.frequency]);const[i,j]=l.useState(0),[T,p]=l.useState(!1),[x,u]=l.useState([]),[A,f]=l.useState(!1),D=()=>{n(s=>({...s,workouts:[...s.workouts,{name:`Treino ${String.fromCharCode(65+s.workouts.length)}`,exercises:[]}]}))},P=s=>{window.confirm("Tem certeza que deseja excluir este treino?")&&(n(t=>({...t,workouts:t.workouts.filter((a,o)=>o!==s)})),i>=s&&j(Math.max(0,i-1)))},_=s=>{u(t=>t.some(o=>o.id===s.id)?t.filter(o=>o.id!==s.id):[...t,s])},q=()=>{if(x.length===0){m.error("Selecione pelo menos um exercício",{position:"bottom-right"});return}p(!1),f(!0)},W=s=>{n(t=>({...t,workouts:t.workouts.map((a,o)=>o===i?{...a,exercises:[...a.exercises,...s]}:a)})),u([]),f(!1)},F=s=>{s!==0&&n(t=>({...t,workouts:t.workouts.map((a,o)=>{if(o===i){const c=[...a.exercises];return[c[s-1],c[s]]=[c[s],c[s-1]],{...a,exercises:c}}return a})}))},R=s=>{const t=r.workouts[i];s!==t.exercises.length-1&&n(a=>({...a,workouts:a.workouts.map((o,c)=>{if(c===i){const d=[...o.exercises];return[d[s],d[s+1]]=[d[s+1],d[s]],{...o,exercises:d}}return o})}))},z=s=>{n(t=>({...t,workouts:t.workouts.map((a,o)=>o===i?{...a,exercises:a.exercises.filter((c,d)=>d!==s)}:a)}))},M=async s=>{if(s.preventDefault(),r.name.trim()===""){m.error("Insira um nome para o protocolo.",{position:"bottom-right"});return}if(r.type===null){m.error("Selecione um tipo de treino.",{position:"bottom-right"});return}if(r.startDate.trim()===""){m.error("Insira uma data de início para o protocolo.",{position:"bottom-right"});return}if(r.workouts.length===0||r.workouts[0].exercises.length===0){m.error("Adicione ao menos um exercício para o treino.",{position:"bottom-right"});return}try{console.log("Saving protocol:",r),m.success("Protocolo salvo com sucesso!",{position:"bottom-right"}),g("/dashboard/workout",{replace:!0})}catch{m.error("Erro ao salvar protocolo",{position:"bottom-right"})}},$=()=>{g("/dashboard/workout",{replace:!0})};return T?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(H,{onSelect:_,onClose:()=>{p(!1),u([])},multiSelect:!0,selectedExercises:x,onConfirm:q})}):A?e.jsx("div",{className:"min-h-screen bg-snapfit-black",children:e.jsx(L,{exercises:x,onConfirm:W,onCancel:()=>{f(!1),u([])}})}):e.jsxs("div",{className:"min-h-screen bg-snapfit-black",children:[e.jsx("div",{className:"sticky top-0 z-10 bg-snapfit-black border-b border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center justify-between p-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("button",{onClick:$,className:"p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors",children:e.jsx(U,{className:"w-5 h-5"})}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(N,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h1",{className:"text-lg font-medium text-white",children:"Criar Protocolo Manual"})]})]}),e.jsxs("button",{onClick:M,className:"flex items-center gap-1.5 px-4 py-2 text-sm font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:[e.jsx(G,{className:"w-4 h-4"}),"Salvar"]})]})}),e.jsx("div",{className:"p-4 pb-20",children:e.jsxs("div",{className:"max-w-4xl mx-auto space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-lg font-medium text-white mb-4",children:"Informações Gerais"}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Nome do Protocolo"}),e.jsx("input",{type:"text",value:r.name,onChange:s=>n(t=>({...t,name:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: Protocolo Hipertrofia"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Tipo"}),e.jsxs("select",{value:(r==null?void 0:r.type)||"",onChange:s=>n(t=>({...t,type:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white appearance-none",children:[e.jsx("option",{value:"",children:"Selecione o tipo..."}),(E||[]).map((s,t)=>e.jsx("option",{value:s.id,children:s.value_option},t))]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Divisão de Treino"}),e.jsx("input",{type:"text",value:r.split,onChange:s=>n(t=>({...t,split:s.target.value})),placeholder:"Ex: A-B-C-D",className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Data de Início"}),e.jsx("input",{type:"date",value:r.startDate,onChange:s=>n(t=>({...t,startDate:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Frequência Semanal"}),e.jsx("select",{value:r.frequency,onChange:s=>n(t=>({...t,frequency:Number(s.target.value)})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",children:[2,3,4,5,6].map(s=>e.jsxs("option",{value:s,children:[s,"x por semana"]},s))})]}),e.jsxs("div",{className:"sm:col-span-2 space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Objetivo"}),e.jsx("textarea",{value:r.objective,onChange:s=>n(t=>({...t,objective:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white h-20 resize-none",placeholder:"Descreva o objetivo principal deste protocolo..."})]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h2",{className:"text-lg font-medium text-white",children:"Treinos"}),e.jsxs("button",{onClick:D,className:"flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(k,{className:"w-4 h-4"}),"Adicionar Treino"]})]}),r.workouts.length>0&&e.jsx("div",{className:"flex gap-2 mb-4 overflow-x-auto",children:r.workouts.map((s,t)=>e.jsxs("button",{onClick:()=>j(t),className:`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg whitespace-nowrap transition-colors ${i===t?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-gray-400 hover:text-white hover:bg-snapfit-dark-gray/80"}`,children:[s.name,r.workouts.length>1&&e.jsx("button",{onClick:a=>{a.stopPropagation(),P(t)},className:"ml-1 p-0.5 text-red-400 hover:text-red-300 rounded",children:e.jsx(C,{className:"w-3 h-3"})})]},t))}),r.workouts.length>0&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-400",children:"Nome do Treino"}),e.jsx("input",{type:"text",value:((v=r.workouts[i])==null?void 0:v.name)||"",onChange:s=>n(t=>({...t,workouts:t.workouts.map((a,o)=>o===i?{...a,name:s.target.value}:a)})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: Treino A - Peito e Tríceps"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-md font-medium text-white",children:"Exercícios"}),e.jsxs("button",{onClick:()=>p(!0),className:"flex items-center gap-1.5 px-3 py-1.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(k,{className:"w-4 h-4"}),"Adicionar Exercícios"]})]}),((w=r.workouts[i])==null?void 0:w.exercises.length)===0?e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(N,{className:"w-12 h-12 mx-auto mb-3 opacity-50"}),e.jsx("p",{children:"Nenhum exercício adicionado"}),e.jsx("p",{className:"text-sm",children:'Clique em "Adicionar Exercícios" para começar'})]}):e.jsx("div",{className:"space-y-3",children:(y=r.workouts[i])==null?void 0:y.exercises.map((s,t)=>e.jsx("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-start justify-between mb-3",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-medium text-white mb-1",children:s.exercise.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-400",children:[e.jsxs("span",{children:[s.sets," séries"]}),e.jsxs("span",{children:[s.reps," repetições"]}),e.jsxs("span",{children:["RPE ",s.rpe]}),e.jsxs("span",{children:[s.restTime,"s descanso"]})]}),s.notes&&e.jsx("p",{className:"text-sm text-gray-400 mt-2",children:s.notes})]}),e.jsxs("div",{className:"flex items-center gap-1 ml-3",children:[e.jsx("button",{onClick:()=>F(t),disabled:t===0,className:"p-1 text-gray-400 hover:text-snapfit-green disabled:opacity-30 disabled:cursor-not-allowed",children:e.jsx(Q,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>R(t),disabled:t===r.workouts[i].exercises.length-1,className:"p-1 text-gray-400 hover:text-snapfit-green disabled:opacity-30 disabled:cursor-not-allowed",children:e.jsx(J,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>z(t),className:"p-1 text-red-400 hover:text-red-300",children:e.jsx(C,{className:"w-4 h-4"})})]})]})},t))})]})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-lg font-medium text-white mb-4",children:"Notas Adicionais"}),e.jsx("textarea",{value:r.notes,onChange:s=>n(t=>({...t,notes:s.target.value})),className:"w-full p-3 text-sm border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white h-24 resize-none",placeholder:"Adicione observações importantes sobre o protocolo..."})]})]})})]})}export{X as CreateWorkoutProtocolPage};
