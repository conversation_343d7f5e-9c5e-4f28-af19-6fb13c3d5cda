import{c as z,r as S,j as e,bb as E,bn as Q,ae as V,a as ce,ao as I,y as w,bo as ee,bp as de,bq as se,br as xe,bs as L,b6 as me,l as _,Z as W,bt as pe,aO as J,bu as te,bv as he,R as j,bw as ge,bx as P,b9 as ue,by as fe,bz as be,bA as je,ba as ae,ah as O,f as D,a8 as re,bB as Ne,bC as ye,bD as U,bE as ve,bF as we,aT as ke,bG as Ce,af as Se,n as R,bH as M,bI as Pe,bJ as Te,bK as K,ag as Ee,p as ne,bL as Fe,bM as $e,bN as Me,P as Ae,C as _e,U as De,bO as Re,ap as ze,G as Ie,t as Le,L as Oe,bP as G,ar as H,au as Ue,bQ as X}from"./index-yuwXvJOX.js";import{G as q}from"./globe-C_TbOdRf.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qe=[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]],Be=z("briefcase",qe);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ve=[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]],ie=z("crown",Ve);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]],B=z("lock",Je);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]],He=z("user-check",Ge);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]],Qe=z("user-minus",We);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ke=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],Xe=z("user-x",Ke);function Ye({onSyncSuccess:s,expectedType:i}){const[r,t]=S.useState(""),[n,d]=S.useState(!1),[m,h]=S.useState(!1),f=async()=>{if(!r.trim()){w.error("Digite o código de sincronização",{position:"bottom-right"});return}if(r.length!==6){w.error("O código deve ter 6 dígitos",{position:"bottom-right"});return}d(!0);try{const x=await(await fetch("https://api.mysnapfit.com.br/users/sync-with-professional",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({code:r})})).json();if(x.success){if(i&&x.professional.role!==i){const c=i==="coach"?"coach":"nutricionista",u=x.professional.role==="coach"?"coach":"nutricionista";w.error(`Este código é de um ${u}, mas você está tentando sincronizar com um ${c}`,{position:"bottom-right"});return}w.success(x.message,{position:"bottom-right"}),t(""),h(!1),s==null||s(x.professional)}else w.error(x.message,{position:"bottom-right"})}catch{w.error("Erro ao sincronizar. Tente novamente.",{position:"bottom-right"})}finally{d(!1)}},a=p=>{const x=p.replace(/\D/g,"").slice(0,6);t(x)};return m?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(E,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Código de Sincronização"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Digite o código fornecido pelo seu profissional"})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("input",{type:"text",value:r,onChange:p=>a(p.target.value),placeholder:"000000",className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white text-center text-lg font-mono tracking-widest focus:border-snapfit-green focus:outline-none",maxLength:6,disabled:n}),e.jsx("p",{className:"text-xs text-gray-500 mt-1 text-center",children:"Código de 6 dígitos fornecido pelo profissional"})]}),e.jsx("div",{className:"bg-blue-500/10 rounded-lg p-3 border border-blue-500/20",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-blue-400 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsx("p",{className:"font-medium text-blue-400 mb-1",children:"Como funciona:"}),e.jsx("p",{children:"• Seu coach/nutricionista gera um código único"}),e.jsx("p",{children:"• Digite o código aqui para se conectar"}),e.jsx("p",{children:"• Você receberá protocolos personalizados"})]})]})}),e.jsxs("div",{className:"flex gap-3",children:[e.jsx("button",{onClick:()=>{h(!1),t("")},className:"flex-1 px-4 py-2.5 text-sm font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",disabled:n,children:"Cancelar"}),e.jsxs("button",{onClick:f,disabled:n||r.length!==6,className:"flex-1 flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[n?e.jsx(ce,{className:"w-4 h-4 animate-spin"}):e.jsx(I,{className:"w-4 h-4"}),n?"Sincronizando...":"Sincronizar"]})]})]})]}):e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(E,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Sincronizar com Profissional"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Conecte-se com seu coach ou nutricionista"})]})]}),e.jsxs("button",{onClick:()=>h(!0),className:"w-full flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors",children:[e.jsx(Q,{className:"w-4 h-4"}),"Sincronizar Agora"]})]})}function Ze({onInviteSuccess:s,professionalType:i}){const[r,t]=S.useState({name:"",phone:"",email:""}),[n,d]=S.useState(!1),m=(c,u)=>{t(l=>({...l,[c]:u}))},h=()=>{const c=i==="coach"?"coach":"nutricionista";return`Olá ${r.name}! 👋

Sou João e gostaria de te convidar para usar o SnapFit, um app incrível para ${i==="coach"?"personal trainers":"nutricionistas"}!

🎯 *Por que usar o SnapFit?*
• Dashboard profissional GRATUITA
• Crie protocolos personalizados para seus clientes
• Seus clientes recebem notificações automáticas
• Acompanhe o progresso em tempo real
• Integração com WhatsApp

💰 *Totalmente gratuito para profissionais!*
(Apenas algumas features avançadas com IA são pagas)

📱 *Como começar:*
1. Baixe o SnapFit na App Store/Play Store
2. Cadastre-se como ${c}
3. Me envie seu código de sincronização
4. Pronto! Estaremos conectados

Link para download: https://snapfit.app/download

Aceita fazer parte dessa revolução no acompanhamento nutricional/fitness? 🚀

Qualquer dúvida, estou aqui! 😊`},f=async()=>{if(!r.name.trim()){w.error("Nome é obrigatório",{position:"bottom-right"});return}try{const u=await(await fetch("https://api.mysnapfit.com.br/invites/generate-whatsapp-link",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({professionalName:r.name,professionalPhone:r.phone,professionalType:i})})).json();if(u.success&&u.link)window.open(u.link,"_blank"),s==null||s();else{const l=h(),o=r.phone.replace(/\D/g,"");let b;o?b=`https://wa.me/55${o}?text=${encodeURIComponent(l)}`:b=`https://wa.me/?text=${encodeURIComponent(l)}`,window.open(b,"_blank"),s==null||s()}}catch{const u=h(),l=r.phone.replace(/\D/g,"");let o;l?o=`https://wa.me/55${l}?text=${encodeURIComponent(u)}`:o=`https://wa.me/?text=${encodeURIComponent(u)}`,window.open(o,"_blank"),s==null||s()}},a=async()=>{if(!r.name.trim()||!r.email.trim()){w.error("Nome e email são obrigatórios",{position:"bottom-right"});return}d(!0);try{const u=await(await fetch("https://api.mysnapfit.com.br/invites/send-professional-invite",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({professionalName:r.name,professionalEmail:r.email,professionalType:i})})).json();u.success?(w.success("Convite enviado por email!",{position:"bottom-right"}),s==null||s()):w.error(u.message||"Erro ao enviar email",{position:"bottom-right"})}catch{w.error("Erro ao enviar email",{position:"bottom-right"})}finally{d(!1)}},p=async()=>{if(!r.name.trim()){w.error("Nome é obrigatório",{position:"bottom-right"});return}const c=h();try{await navigator.clipboard.writeText(c),w.success("Mensagem copiada!",{position:"bottom-right"})}catch{w.error("Erro ao copiar mensagem",{position:"bottom-right"})}},x=i==="coach"?"coach":"nutricionista";return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(ee,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-medium text-white",children:["Convidar ",x]}),e.jsxs("p",{className:"text-xs text-gray-400",children:["Convide seu ",x," para usar o SnapFit"]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-xs font-medium text-gray-300 mb-1",children:["Nome do ",x," *"]}),e.jsx("input",{type:"text",value:r.name,onChange:c=>m("name",c.target.value),placeholder:"Ex: Dr. Ana Silva",className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white text-sm placeholder-gray-400 focus:border-snapfit-green focus:outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-300 mb-1",children:"WhatsApp (opcional)"}),e.jsx("input",{type:"tel",value:r.phone,onChange:c=>m("phone",c.target.value),placeholder:"(11) 99999-9999",className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white text-sm placeholder-gray-400 focus:border-snapfit-green focus:outline-none"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-300 mb-1",children:"Email (opcional)"}),e.jsx("input",{type:"email",value:r.email,onChange:c=>m("email",c.target.value),placeholder:"<EMAIL>",className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-gray-600 rounded-lg text-white text-sm placeholder-gray-400 focus:border-snapfit-green focus:outline-none"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{onClick:f,disabled:!r.name.trim(),className:"w-full flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-black bg-green-500 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(de,{className:"w-4 h-4"}),"Enviar pelo WhatsApp"]}),e.jsxs("button",{onClick:a,disabled:!r.name.trim()||!r.email.trim()||n,className:"w-full flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(se,{className:"w-4 h-4"}),n?"Enviando...":"Enviar por Email"]}),e.jsxs("button",{onClick:p,disabled:!r.name.trim(),className:"w-full flex items-center justify-center gap-2 px-4 py-2.5 text-sm font-medium text-gray-300 bg-gray-600 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(xe,{className:"w-4 h-4"}),"Copiar Mensagem"]})]}),e.jsx("div",{className:"bg-blue-500/10 rounded-lg p-3 border border-blue-500/20",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(I,{className:"w-4 h-4 text-blue-400 flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsx("p",{className:"font-medium text-blue-400 mb-1",children:"Como funciona:"}),e.jsxs("p",{children:["• Seu ",x," se cadastra gratuitamente"]}),e.jsx("p",{children:"• Ele gera um código de sincronização"}),e.jsx("p",{children:"• Você usa o código para se conectar"}),e.jsx("p",{children:"• Pronto! Vocês estarão sincronizados"})]})]})}),e.jsx("div",{className:"bg-snapfit-green/10 rounded-lg p-3 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-snapfit-green flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsxs("p",{className:"font-medium text-snapfit-green mb-1",children:["Benefícios para o ",x,":"]}),e.jsx("p",{children:"• Dashboard profissional gratuita"}),e.jsx("p",{children:"• Criação ilimitada de protocolos"}),e.jsx("p",{children:"• Acompanhamento de clientes em tempo real"}),e.jsx("p",{children:"• Integração com WhatsApp"})]})]})})]})}function es({onHireSuccess:s,professionalType:i}){const[r,t]=S.useState([]),[n,d]=S.useState(null),[m,h]=S.useState("individual"),[f,a]=S.useState(!0),[p,x]=S.useState(!1);S.useEffect(()=>{c()},[i]);const c=async()=>{try{await new Promise(g=>setTimeout(g,500)),t(i==="coach"?[{id:"coach-1",name:"Carlos Santos",photo:"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=100&h=100&fit=crop",specialty:"Hipertrofia e Força",rating:4.9,experience:"8 anos",clients:150,price:200,description:"Especialista em ganho de massa muscular e força. Formado em Educação Física com pós em Treinamento de Força.",certifications:["CREF","NSCA-CPT","Pós-graduação em Treinamento"],available:!0},{id:"coach-2",name:"Marina Silva",photo:"https://images.unsplash.com/photo-1594824804732-ca8db7d1e2ca?w=100&h=100&fit=crop",specialty:"Emagrecimento e Condicionamento",rating:4.8,experience:"6 anos",clients:120,price:200,description:"Focada em emagrecimento saudável e condicionamento físico. Especialista em treinos funcionais.",certifications:["CREF","Certificação Funcional","Nutrição Esportiva"],available:!0}]:[{id:"nutri-1",name:"Dra. Ana Oliveira",photo:"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=100&h=100&fit=crop",specialty:"Emagrecimento e Reeducação Alimentar",rating:4.9,experience:"10 anos",clients:200,price:200,description:"Nutricionista clínica especializada em emagrecimento e reeducação alimentar. CRN ativo.",certifications:["CRN","Pós em Nutrição Clínica","Especialização em Obesidade"],available:!0},{id:"nutri-2",name:"Dr. Pedro Costa",photo:"https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=100&h=100&fit=crop",specialty:"Nutrição Esportiva",rating:4.8,experience:"7 anos",clients:180,price:200,description:"Especialista em nutrição esportiva e ganho de massa muscular. Atende atletas e praticantes de musculação.",certifications:["CRN","Nutrição Esportiva","Suplementação"],available:!0}])}catch{w.error("Erro ao carregar profissionais",{position:"bottom-right"})}finally{a(!1)}},u=async()=>{if(n){x(!0);try{await new Promise(b=>setTimeout(b,2e3)),w.success(`${n.name} contratado(a) com sucesso!`,{position:"bottom-right"}),s==null||s(n)}catch{w.error("Erro ao contratar profissional",{position:"bottom-right"})}finally{x(!1)}}},l=()=>m==="combo"?350:200,o=i==="coach"?"coach":"nutricionista";return f?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(L,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-medium text-white",children:["Contratar ",o," SnapFit"]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Carregando profissionais..."})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 text-center",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-2",children:e.jsx(L,{className:"w-4 h-4 text-blue-400 animate-pulse"})}),e.jsx("p",{className:"text-sm text-gray-400",children:"Carregando profissionais disponíveis..."})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30",children:e.jsx(L,{className:"w-4 h-4 text-blue-400"})}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-medium text-white",children:["Contratar ",o," SnapFit"]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Profissionais certificados e experientes"})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 border border-gray-600",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Escolha seu plano:"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("label",{className:"flex items-center gap-3 p-2 rounded-lg border border-gray-600 cursor-pointer hover:border-snapfit-green/50 transition-colors",children:[e.jsx("input",{type:"radio",name:"plan",value:"individual",checked:m==="individual",onChange:b=>h(b.target.value),className:"text-snapfit-green"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:i==="coach"?"Só Coach":"Só Nutricionista"}),e.jsx("span",{className:"text-xs text-snapfit-green font-bold",children:"R$ 200/mês"})]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Acompanhamento individual especializado"})]})]}),e.jsxs("label",{className:"flex items-center gap-3 p-2 rounded-lg border border-blue-500/50 bg-blue-500/5 cursor-pointer hover:border-blue-500 transition-colors",children:[e.jsx("input",{type:"radio",name:"plan",value:"combo",checked:m==="combo",onChange:b=>h(b.target.value),className:"text-blue-500"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ie,{className:"w-4 h-4 text-blue-400"}),e.jsx("span",{className:"text-sm font-medium text-white",children:"Coach + Nutricionista"}),e.jsx("span",{className:"text-xs text-blue-400 font-bold",children:"R$ 350/mês"}),e.jsx("span",{className:"text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full",children:"POPULAR"})]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Acompanhamento completo - Economize R$ 50!"})]})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h4",{className:"text-sm font-medium text-white",children:["Escolha seu ",o,":"]}),r.map(b=>e.jsx("div",{className:`p-3 rounded-lg border cursor-pointer transition-colors ${(n==null?void 0:n.id)===b.id?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-gray-500"}`,onClick:()=>d(b),children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("img",{src:b.photo,alt:b.name,className:"w-12 h-12 rounded-full object-cover border border-snapfit-green/30"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h5",{className:"text-sm font-medium text-white",children:b.name}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(me,{className:"w-3 h-3 text-yellow-400 fill-current"}),e.jsx("span",{className:"text-xs text-gray-400",children:b.rating})]})]}),e.jsx("p",{className:"text-xs text-snapfit-green mb-1",children:b.specialty}),e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:b.description}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-500",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(_,{className:"w-3 h-3"}),e.jsx("span",{children:b.experience})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(E,{className:"w-3 h-3"}),e.jsxs("span",{children:[b.clients," clientes"]})]})]})]}),(n==null?void 0:n.id)===b.id&&e.jsx(I,{className:"w-5 h-5 text-snapfit-green"})]})},b.id))]}),e.jsx("button",{onClick:u,disabled:!n||p,className:"w-full flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:p?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin"}),"Processando..."]}):e.jsxs(e.Fragment,{children:[e.jsx(L,{className:"w-4 h-4"}),"Contratar por R$ ",l(),"/mês"]})}),e.jsx("div",{className:"bg-snapfit-green/10 rounded-lg p-3 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(W,{className:"w-4 h-4 text-snapfit-green flex-shrink-0 mt-0.5"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsx("p",{className:"font-medium text-snapfit-green mb-1",children:"Incluso no plano:"}),e.jsx("p",{children:"• Protocolos personalizados semanais"}),e.jsx("p",{children:"• Acompanhamento via WhatsApp"}),e.jsx("p",{children:"• Ajustes conforme seu progresso"}),e.jsx("p",{children:"• Suporte nutricional/treino completo"}),e.jsx("p",{children:"• Primeira semana grátis!"})]})]})})]})}function Y({title:s,professional:i,onEdit:r,onSync:t,type:n}){const[d,m]=S.useState(!1),[h,f]=S.useState(!1),[a,p]=S.useState(null),[x,c]=S.useState(!1),u=N=>{m(!1),f(!1),p(null),t==null||t(N),c(!0),setTimeout(()=>c(!1),3e3)},l=()=>{m(!1),f(!1),p(null),w.success("Convite enviado com sucesso!",{position:"bottom-right"})},o=N=>{m(!1),f(!1),p(null),t==null||t(N),w.success(`${n==="coach"?"Coach":"Nutricionista"} contratado com sucesso!`,{position:"bottom-right"})},b=()=>{m(!0),f(!0)},g=N=>{f(!1),p(N)},k=()=>{p(null),f(!0)},y=()=>{const N=n==="coach"?"/dashboard/workout/import":"/dashboard/diet/import";window.location.href=N};return i?e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:s}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsxs("div",{className:"flex items-center gap-1 px-1.5 py-0.5 text-xs font-medium text-snapfit-green bg-snapfit-green/10 rounded-full border border-snapfit-green/30",children:[e.jsx(I,{className:"w-2.5 h-2.5"}),e.jsx("span",{className:"text-xs",children:"Conectado"})]}),r&&e.jsx("button",{onClick:r,className:"p-1 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-lg transition-colors",children:e.jsx(te,{className:"w-3 h-3"})})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:i.photo,alt:i.name,className:"w-8 h-8 rounded-full object-cover border border-snapfit-green/30"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-xs font-medium text-white",children:i.name}),e.jsx("p",{className:"text-xs text-snapfit-green",children:i.specialty})]})]}),e.jsx("div",{className:"mt-2 pt-2 border-t border-snapfit-green/20",children:e.jsxs("button",{onClick:y,className:"w-full flex items-center justify-center gap-1 px-2 py-1.5 text-xs font-medium text-snapfit-green bg-snapfit-green/10 border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/20 transition-colors",children:[e.jsx(he,{className:"w-3 h-3"}),"Ver Protocolos"]})})]}):e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-3 border border-snapfit-green/20",children:[e.jsx("div",{className:"flex items-center justify-between mb-2",children:e.jsx("h3",{className:"text-sm font-medium text-white",children:s})}),d?e.jsxs("div",{className:"space-y-4",children:[h&&!a&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"text-center mb-3",children:e.jsx("h3",{className:"text-sm font-medium text-white mb-1",children:"Como você quer se conectar?"})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("button",{onClick:()=>g("sync"),className:"w-full flex items-center gap-2 p-2.5 text-left bg-snapfit-dark-gray border border-gray-600 rounded-lg hover:border-snapfit-green/50 transition-colors",children:[e.jsx("div",{className:"w-6 h-6 bg-snapfit-green/20 rounded-full flex items-center justify-center",children:e.jsx(pe,{className:"w-3 h-3 text-snapfit-green"})}),e.jsx("div",{className:"flex-1",children:e.jsxs("h4",{className:"text-xs font-medium text-white",children:["Tenho um ",n==="coach"?"coach":"nutricionista"]})})]}),e.jsxs("button",{onClick:()=>g("invite"),className:"w-full flex items-center gap-2 p-2.5 text-left bg-snapfit-dark-gray border border-gray-600 rounded-lg hover:border-blue-500/50 transition-colors",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center",children:e.jsx(ee,{className:"w-3 h-3 text-blue-400"})}),e.jsx("div",{className:"flex-1",children:e.jsxs("h4",{className:"text-xs font-medium text-white",children:["Convidar meu ",n==="coach"?"coach":"nutricionista"]})})]}),e.jsxs("button",{onClick:()=>g("hire"),className:"w-full flex items-center gap-2 p-2.5 text-left bg-snapfit-dark-gray border border-orange-500/50 rounded-lg hover:border-orange-500 transition-colors",children:[e.jsx("div",{className:"w-6 h-6 bg-orange-500/20 rounded-full flex items-center justify-center",children:e.jsx(L,{className:"w-3 h-3 text-orange-400"})}),e.jsx("div",{className:"flex-1",children:e.jsxs("h4",{className:"text-xs font-medium text-white",children:["Contratar ",n==="coach"?"coach":"nutricionista"," SnapFit"]})})]})]})]}),a==="sync"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("button",{onClick:k,className:"p-0.5 text-gray-400 hover:text-white transition-colors",children:e.jsx(J,{className:"w-3 h-3"})}),e.jsx("h3",{className:"text-xs font-medium text-white",children:"Código de Sincronização"})]}),e.jsx(Ye,{onSyncSuccess:u,expectedType:n})]}),a==="invite"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("button",{onClick:k,className:"p-0.5 text-gray-400 hover:text-white transition-colors",children:e.jsx(J,{className:"w-3 h-3"})}),e.jsx("h3",{className:"text-xs font-medium text-white",children:"Convidar Profissional"})]}),e.jsx(Ze,{onInviteSuccess:l,professionalType:n})]}),a==="hire"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx("button",{onClick:k,className:"p-0.5 text-gray-400 hover:text-white transition-colors",children:e.jsx(J,{className:"w-3 h-3"})}),e.jsx("h3",{className:"text-xs font-medium text-white",children:"Contratar Profissional"})]}),e.jsx(es,{onHireSuccess:o,professionalType:n})]}),e.jsx("button",{onClick:()=>{m(!1),f(!1),p(null)},className:"w-full px-3 py-1.5 text-xs font-medium text-gray-400 border border-gray-600 rounded-lg hover:text-white hover:border-gray-500 transition-colors",children:"Cancelar"})]}):e.jsxs("div",{className:"text-center py-3",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-2",children:e.jsx(E,{className:"w-4 h-4 text-gray-400"})}),e.jsxs("p",{className:"text-xs text-gray-400 mb-3",children:["Nenhum ",n==="coach"?"coach":"nutricionista"," conectado"]}),e.jsxs("button",{onClick:b,className:"flex items-center justify-center gap-2 px-4 py-2 text-xs font-medium text-black bg-snapfit-green rounded-lg hover:bg-snapfit-green/90 transition-colors mx-auto",children:[e.jsx(Q,{className:"w-3 h-3"}),"Conectar com meu ",n==="coach"?"coach":"nutricionista"]})]})]})}function ss({onInvite:s}){const[i,r]=j.useState("month"),[t,n]=j.useState([]),[d,m]=j.useState(!0),[h,f]=j.useState(null),[a,p]=j.useState(null);j.useEffect(()=>{x()},[]);const x=async()=>{m(!0),f(null);try{const l=await ge();n(l)}catch{f("Erro ao carregar amigos")}finally{m(!1)}},c=async(l,o)=>{if(window.confirm(`Tem certeza que deseja remover ${o} da sua lista de amigos?`)){p(l);try{await be(l),n(b=>b.filter(g=>g.id!==l))}catch{f("Erro ao remover amigo")}finally{p(null)}}},u=j.useMemo(()=>t.sort((l,o)=>{const b=i==="month"?l.monthlyPoints||l.points:i==="year"&&l.yearlyPoints||l.points;return(i==="month"?o.monthlyPoints||o.points:i==="year"&&o.yearlyPoints||o.points)-b}),[t,i]);return d?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(P,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"text-sm font-bold text-white",children:"Ranking de Amigos"})]}),e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-snapfit-green"})})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(P,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("h3",{className:"text-sm font-bold text-white",children:["Ranking de Amigos",t.length>0&&e.jsxs("span",{className:"ml-2 text-xs text-gray-400",children:["(",t.length,")"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"relative flex-1",children:e.jsxs("div",{className:"flex items-center gap-2 bg-snapfit-dark-gray rounded-full px-3 py-1.5 border border-snapfit-green/20",children:[e.jsx(ue,{className:"w-3.5 h-3.5 text-snapfit-green"}),e.jsxs("select",{value:i,onChange:l=>r(l.target.value),className:"w-full text-xs border-0 bg-transparent focus:ring-0 text-gray-400 appearance-none pr-4",children:[e.jsx("option",{value:"month",children:"Este Mês"}),e.jsx("option",{value:"year",children:"Este Ano"}),e.jsx("option",{value:"all",children:"Desde Sempre"})]})]})}),e.jsxs("button",{onClick:s,className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium text-black bg-snapfit-green rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:[e.jsx(E,{className:"w-3.5 h-3.5"}),"Convidar"]})]})]}),h&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:h})}),t.length>0?e.jsx("div",{className:"space-y-3",children:u.map((l,o)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10 group",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:l.photo,alt:l.name,className:"w-10 h-10 rounded-full object-cover border-2 border-snapfit-green/30"}),e.jsx("div",{className:`absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${o===0?"bg-snapfit-green text-black":o===1?"bg-gray-300 text-gray-700":o===2?"bg-amber-600 text-amber-100":"bg-snapfit-dark-gray text-gray-400 border border-snapfit-green/20"}`,children:o+1})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-white",children:l.name}),e.jsxs("div",{className:"flex items-center gap-1.5 text-xs text-gray-400",children:[e.jsxs("span",{className:"bg-snapfit-green/10 px-1.5 py-0.5 rounded text-snapfit-green",children:["@",l.username]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:["Ativo ",new Date(l.lastActive).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex items-center gap-1.5 text-sm",children:[e.jsx("div",{className:"w-6 h-6 bg-snapfit-green/10 rounded-full flex items-center justify-center border border-snapfit-green/20",children:e.jsx(P,{className:"w-3.5 h-3.5 text-snapfit-green"})}),e.jsx("span",{className:"text-snapfit-green font-medium",children:l.points.toLocaleString()})]}),e.jsxs("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx("button",{className:"p-1.5 text-gray-400 hover:text-blue-400 hover:bg-blue-400/10 rounded-lg transition-colors",title:"Enviar mensagem",children:e.jsx(fe,{className:"w-3.5 h-3.5"})}),e.jsx("button",{onClick:()=>c(l.id,l.name),disabled:a===l.id,className:"p-1.5 text-gray-400 hover:text-red-400 hover:bg-red-400/10 rounded-lg transition-colors disabled:opacity-50",title:"Remover amigo",children:a===l.id?e.jsx("div",{className:"animate-spin rounded-full h-3.5 w-3.5 border-b border-current"}):e.jsx(Qe,{className:"w-3.5 h-3.5"})})]})]})]},l.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-dark-gray rounded-full flex items-center justify-center mx-auto mb-4 border border-snapfit-green/20",children:e.jsx(E,{className:"w-8 h-8 text-gray-400"})}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Você ainda não tem amigos"}),e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Adicione amigos para competir no ranking!"}),e.jsx("button",{onClick:s,className:"mt-3 px-4 py-2 text-sm font-medium bg-snapfit-green/10 hover:bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 rounded-lg transition-colors",children:"Adicionar Amigos"})]})]})}function ts(){const[s,i]=j.useState(""),[r,t]=j.useState([]),[n,d]=j.useState(!1),[m,h]=j.useState(null),[f,a]=j.useState(null),p=j.useCallback(async o=>{if(o.trim().length<2){t([]);return}d(!0),h(null);try{const b=await je(o);t(b)}catch{h("Erro ao buscar usuários"),t([])}finally{d(!1)}},[]);j.useEffect(()=>{const o=setTimeout(()=>{p(s)},300);return()=>clearTimeout(o)},[s,p]);const x=async o=>{a(o);try{await Ne(o),t(b=>b.map(g=>g.id===o?{...g,friendshipStatus:"pending_sent"}:g))}catch{h("Erro ao enviar solicitação de amizade")}finally{a(null)}},c=o=>{switch(o){case"friends":return e.jsx(re,{className:"w-4 h-4 text-green-400"});case"pending_sent":return e.jsx(D,{className:"w-4 h-4 text-yellow-400"});case"pending_received":return e.jsx(D,{className:"w-4 h-4 text-blue-400"});case"blocked":return e.jsx(O,{className:"w-4 h-4 text-red-400"});default:return e.jsx(Q,{className:"w-4 h-4 text-snapfit-green"})}},u=o=>{switch(o){case"friends":return"Amigos";case"pending_sent":return"Enviado";case"pending_received":return"Recebido";case"blocked":return"Bloqueado";default:return"Adicionar"}},l=o=>o==="none";return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(E,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"text-lg font-bold text-white",children:"Buscar Amigos"})]}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:e.jsx(ae,{className:"h-5 w-5 text-gray-400"})}),e.jsx("input",{type:"text",value:s,onChange:o=>i(o.target.value),placeholder:"Buscar por nome ou @username...",className:"block w-full pl-10 pr-3 py-2 border border-snapfit-green/20 rounded-lg bg-snapfit-dark-gray text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green focus:border-transparent"})]}),m&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:m})}),n&&e.jsx("div",{className:"flex items-center justify-center py-4",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-snapfit-green"})}),r.length>0&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-400",children:"Resultados da busca"}),e.jsx("div",{className:"space-y-2",children:r.map(o=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("img",{src:o.photo,alt:o.name,className:"w-10 h-10 rounded-full object-cover border-2 border-snapfit-green/30"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-white",children:o.name}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-400",children:[e.jsxs("span",{className:"bg-snapfit-green/10 px-2 py-0.5 rounded text-snapfit-green",children:["@",o.username]}),e.jsxs("span",{children:[o.points.toLocaleString()," pontos"]})]})]})]}),e.jsxs("button",{onClick:()=>l(o.friendshipStatus)&&x(o.id),disabled:!l(o.friendshipStatus)||f===o.id,className:`flex items-center gap-2 px-3 py-1.5 text-xs font-medium rounded-lg transition-colors ${l(o.friendshipStatus)?"bg-snapfit-green/10 hover:bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-gray-600/20 text-gray-400 border border-gray-600/30 cursor-not-allowed"}`,children:[f===o.id?e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-current"}):c(o.friendshipStatus),e.jsx("span",{children:u(o.friendshipStatus)})]})]},o.id))})]}),s.length===0&&e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-2",children:"Dicas para encontrar amigos:"}),e.jsxs("ul",{className:"text-xs text-gray-400 space-y-1",children:[e.jsx("li",{children:"• Digite pelo menos 2 caracteres para buscar"}),e.jsx("li",{children:"• Busque por nome completo ou @username"}),e.jsx("li",{children:"• Use @ antes do username (ex: @maria_silva)"}),e.jsx("li",{children:"• Usernames são únicos e facilitam encontrar amigos específicos"})]})]})]})}function as(){const[s,i]=j.useState([]),[r,t]=j.useState(!0),[n,d]=j.useState(null),[m,h]=j.useState(null);j.useEffect(()=>{f()},[]);const f=async()=>{t(!0),d(null);try{const c=await ye();i(c)}catch{d("Erro ao carregar solicitações de amizade")}finally{t(!1)}},a=async c=>{h(c);try{await ve(c),i(u=>u.filter(l=>l.id!==c))}catch{d("Erro ao aceitar solicitação")}finally{h(null)}},p=async c=>{h(c);try{await we(c),i(u=>u.filter(l=>l.id!==c))}catch{d("Erro ao rejeitar solicitação")}finally{h(null)}},x=c=>{const u=new Date(c),o=Math.floor((new Date().getTime()-u.getTime())/(1e3*60*60));return o<1?"Agora há pouco":o<24?`${o}h atrás`:`${Math.floor(o/24)}d atrás`};return r?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(U,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"text-lg font-bold text-white",children:"Solicitações de Amizade"})]}),e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-snapfit-green"})})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(U,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("h3",{className:"text-lg font-bold text-white",children:["Solicitações de Amizade",s.length>0&&e.jsx("span",{className:"ml-2 bg-snapfit-green text-black text-xs px-2 py-0.5 rounded-full",children:s.length})]})]}),n&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:n})}),s.length>0?e.jsx("div",{className:"space-y-3",children:s.map(c=>e.jsxs("div",{className:"flex items-center justify-between p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:c.fromUser.photo,alt:c.fromUser.name,className:"w-12 h-12 rounded-full object-cover border-2 border-snapfit-green/30"}),e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-snapfit-green rounded-full flex items-center justify-center",children:e.jsx(D,{className:"w-2.5 h-2.5 text-black"})})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-white",children:c.fromUser.name}),e.jsxs("p",{className:"text-xs text-gray-400",children:["@",c.fromUser.username," • ",c.fromUser.points.toLocaleString()," pontos • ",x(c.createdAt)]}),e.jsx("p",{className:"text-xs text-snapfit-green",children:"Quer ser seu amigo"})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>a(c.id),disabled:m===c.id,className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-green-500/10 hover:bg-green-500/20 text-green-400 border border-green-500/30 rounded-lg transition-colors disabled:opacity-50",children:[m===c.id?e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-current"}):e.jsx(He,{className:"w-3 h-3"}),e.jsx("span",{children:"Aceitar"})]}),e.jsxs("button",{onClick:()=>p(c.id),disabled:m===c.id,className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-red-500/10 hover:bg-red-500/20 text-red-400 border border-red-500/30 rounded-lg transition-colors disabled:opacity-50",children:[m===c.id?e.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b border-current"}):e.jsx(Xe,{className:"w-3 h-3"}),e.jsx("span",{children:"Rejeitar"})]})]})]},c.id))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-dark-gray rounded-full flex items-center justify-center mx-auto mb-4 border border-snapfit-green/20",children:e.jsx(U,{className:"w-8 h-8 text-gray-400"})}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Nenhuma solicitação de amizade pendente"}),e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Quando alguém te enviar uma solicitação, ela aparecerá aqui"})]})]})}function rs({isOpen:s,onClose:i}){const[r,t]=j.useState("search");return s?e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(E,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Gerenciar Amigos"})]}),e.jsx("button",{onClick:i,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(O,{className:"w-5 h-5"})})]}),e.jsxs("div",{className:"flex border-b border-snapfit-green/20",children:[e.jsxs("button",{onClick:()=>t("search"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${r==="search"?"text-snapfit-green border-b-2 border-snapfit-green bg-snapfit-green/5":"text-gray-400 hover:text-white hover:bg-snapfit-dark-gray/50"}`,children:[e.jsx(ae,{className:"w-4 h-4"}),e.jsx("span",{children:"Buscar Amigos"})]}),e.jsxs("button",{onClick:()=>t("requests"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors ${r==="requests"?"text-snapfit-green border-b-2 border-snapfit-green bg-snapfit-green/5":"text-gray-400 hover:text-white hover:bg-snapfit-dark-gray/50"}`,children:[e.jsx(U,{className:"w-4 h-4"}),e.jsx("span",{children:"Solicitações"})]})]}),e.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-140px)]",children:[r==="search"&&e.jsx(ts,{}),r==="requests"&&e.jsx(as,{})]}),e.jsx("div",{className:"p-4 border-t border-snapfit-green/20 bg-snapfit-dark-gray/30",children:e.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[e.jsx("span",{children:"Dica: Use @username para encontrar amigos específicos"}),e.jsx("button",{onClick:i,className:"px-3 py-1.5 text-gray-400 hover:text-white border border-gray-600/30 hover:border-gray-500/50 rounded-lg transition-colors",children:"Fechar"})]})})]})}):null}function ns({title:s,subtitle:i,onContinue:r,autoClose:t=!1,autoCloseDelay:n=3e3}){const[d,m]=j.useState(!1),[h,f]=j.useState(t?Math.ceil(n/1e3):0);return j.useEffect(()=>{const a=setTimeout(()=>{m(!0)},200);return()=>clearTimeout(a)},[]),j.useEffect(()=>{if(t&&h>0){const a=setTimeout(()=>{h===1?r():f(h-1)},1e3);return()=>clearTimeout(a)}},[t,h,r]),e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-2xl border border-snapfit-green/30 p-8 max-w-md w-full text-center transform transition-all duration-500 ${d?"scale-100 opacity-100":"scale-95 opacity-0"}`,children:[e.jsxs("div",{className:"relative mb-6",children:[e.jsx("div",{className:`w-20 h-20 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto border-2 border-snapfit-green/30 transition-all duration-700 ${d?"scale-100 rotate-0":"scale-0 rotate-180"}`,children:e.jsx(I,{className:`w-10 h-10 text-snapfit-green transition-all duration-500 delay-300 ${d?"scale-100":"scale-0"}`})}),e.jsx("div",{className:"absolute inset-0 pointer-events-none",children:[...Array(6)].map((a,p)=>e.jsx(ke,{className:`absolute w-4 h-4 text-yellow-400 transition-all duration-1000 delay-500 ${d?"opacity-100":"opacity-0"}`,style:{top:`${20+Math.sin(p*60*Math.PI/180)*30}%`,left:`${50+Math.cos(p*60*Math.PI/180)*30}%`,transform:d?"scale(1) rotate(360deg)":"scale(0) rotate(0deg)",transitionDelay:`${500+p*100}ms`}},p))})]}),e.jsx("h2",{className:`text-xl font-bold text-white mb-2 transition-all duration-500 delay-700 ${d?"translate-y-0 opacity-100":"translate-y-4 opacity-0"}`,children:s}),e.jsx("p",{className:`text-gray-400 text-sm mb-6 transition-all duration-500 delay-800 ${d?"translate-y-0 opacity-100":"translate-y-4 opacity-0"}`,children:i}),e.jsx("div",{className:`flex justify-center mb-6 transition-all duration-500 delay-900 ${d?"translate-y-0 opacity-100":"translate-y-4 opacity-0"}`,children:e.jsxs("div",{className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green/10 border border-snapfit-green/20 rounded-lg",children:[e.jsx(P,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-xs text-snapfit-green font-medium",children:"Desafio Criado!"})]})}),e.jsxs("button",{onClick:r,className:`w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green hover:bg-snapfit-green-light text-black font-medium rounded-lg transition-all duration-300 ${d?"translate-y-0 opacity-100":"translate-y-4 opacity-0"}`,style:{transitionDelay:"1000ms"},children:[e.jsx("span",{children:"Continuar"}),e.jsx(Ce,{className:"w-4 h-4"}),t&&h>0&&e.jsxs("span",{className:"ml-2 text-xs opacity-70",children:["(",h,"s)"]})]}),e.jsx("div",{className:`absolute inset-0 bg-gradient-to-r from-snapfit-green/5 via-transparent to-snapfit-green/5 rounded-xl transition-opacity duration-1000 delay-500 ${d?"opacity-100":"opacity-0"}`})]})})}function is({formData:s,updateFormData:i,error:r}){var t;return e.jsxs("div",{className:"space-y-3 sm:space-y-6",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Informações Básicas"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Nome e descrição do seu desafio"})]}),e.jsxs("div",{className:"space-y-3 sm:space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs sm:text-sm font-medium text-white mb-2",children:"Nome do Desafio *"}),e.jsx("input",{type:"text",value:s.name,onChange:n=>i({name:n.target.value}),placeholder:"Ex: Desafio de Janeiro 2024",className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green transition-colors",maxLength:50}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("p",{className:"text-xs text-gray-400",children:s.name.length>=3?"✓ Nome válido":"Mínimo 3 caracteres"}),e.jsxs("span",{className:"text-xs text-gray-500",children:[s.name.length,"/50"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs sm:text-sm font-medium text-white mb-2",children:"Descrição (opcional)"}),e.jsxs("div",{className:"relative",children:[e.jsx("textarea",{value:s.description,onChange:n=>i({description:n.target.value}),placeholder:"Descreva seu desafio, regras especiais, motivação...",rows:4,className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green resize-none transition-colors",maxLength:200}),e.jsx(Se,{className:"absolute top-3 right-3 w-4 h-4 text-gray-400"})]}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Opcional - ajuda outros usuários a entender o desafio"}),e.jsxs("span",{className:"text-xs text-gray-500",children:[((t=s.description)==null?void 0:t.length)||0,"/200"]})]})]}),r&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:r})})]})]})}function ls({formData:s,updateFormData:i,error:r}){const t=m=>{switch(m){case"top_3":return{title:"Top 3",subtitle:"Premiação por posição",description:"Os 3 primeiros colocados ganham prêmios",distribution:"1º lugar: 50% • 2º lugar: 30% • 3º lugar: 20%",pros:["Mais participantes ganham","Competição equilibrada","Ideal para desafios longos"],cons:["Prêmio menor por pessoa","Precisa de mais participantes"],icon:P,color:"yellow"};case"double_or_nothing":return{title:"Dobro ou Nada",subtitle:"Alto risco, alta recompensa",description:"Metade ganha o dobro, metade perde tudo",distribution:"50% melhores dobram • 50% piores perdem tudo",pros:["Prêmio maior para vencedores","Mais emocionante","Ideal para desafios curtos"],cons:["Metade não ganha nada","Mais arriscado"],icon:R,color:"red"}}},n=t("top_3"),d=t("double_or_nothing");return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Tipo de Desafio"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Como os prêmios serão distribuídos?"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("label",{className:`block p-4 border-2 rounded-xl cursor-pointer transition-all ${s.type==="top_3"?"border-yellow-500/50 bg-yellow-500/5":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"type",value:"top_3",checked:s.type==="top_3",onChange:m=>i({type:m.target.value}),className:"sr-only"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.type==="top_3"?"bg-yellow-500/20":"bg-snapfit-dark-gray"}`,children:e.jsx(P,{className:`w-6 h-6 ${s.type==="top_3"?"text-yellow-500":"text-gray-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-white",children:n.title}),e.jsx("span",{className:"text-xs px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded",children:"Recomendado"})]}),e.jsx("p",{className:"text-sm text-gray-400",children:n.distribution})]})]})]}),e.jsxs("label",{className:`block p-4 border-2 rounded-xl cursor-pointer transition-all ${s.type==="double_or_nothing"?"border-red-500/50 bg-red-500/5":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"type",value:"double_or_nothing",checked:s.type==="double_or_nothing",onChange:m=>i({type:m.target.value}),className:"sr-only"}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.type==="double_or_nothing"?"bg-red-500/20":"bg-snapfit-dark-gray"}`,children:e.jsx(R,{className:`w-6 h-6 ${s.type==="double_or_nothing"?"text-red-500":"text-gray-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-white",children:d.title}),e.jsx("span",{className:"text-xs px-2 py-0.5 bg-red-500/20 text-red-500 rounded",children:"Alto Risco"})]}),e.jsx("p",{className:"text-sm text-gray-400",children:d.distribution})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("h4",{className:"text-sm font-medium text-white mb-3 flex items-center gap-2",children:[e.jsx(M,{className:"w-4 h-4 text-yellow-500"}),"Simulação de Prêmios"]}),e.jsxs("div",{className:"text-xs text-gray-400",children:[e.jsxs("p",{className:"mb-2",children:["Com ",e.jsx("span",{className:"text-white font-medium",children:"10 participantes"})," pagando"," ",e.jsxs("span",{className:"text-yellow-500 font-medium",children:[s.entryFee," SnapCoins"]})," cada:"]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded p-3",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{children:"Total arrecadado:"}),e.jsxs("span",{className:"text-yellow-500 font-medium",children:[s.entryFee*10," SnapCoins"]})]}),s.type==="top_3"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"1º lugar:"}),e.jsxs("span",{className:"text-green-400",children:[Math.floor(s.entryFee*10*.5)," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"2º lugar:"}),e.jsxs("span",{className:"text-blue-400",children:[Math.floor(s.entryFee*10*.3)," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"3º lugar:"}),e.jsxs("span",{className:"text-purple-400",children:[Math.floor(s.entryFee*10*.2)," SnapCoins"]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"5 melhores (cada um):"}),e.jsxs("span",{className:"text-green-400",children:[s.entryFee*2," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{children:"5 piores:"}),e.jsx("span",{className:"text-red-400",children:"0 SnapCoins"})]})]})]})]})]}),r&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:r})})]})]})}function os({formData:s,updateFormData:i,error:r}){var h,f;const t=a=>{switch(a){case"public":return{title:"Público",subtitle:"Todos podem ver e participar",description:"Qualquer usuário do SnapFit pode encontrar e participar do seu desafio",pros:["Mais participantes","Maior competição","Fácil de encontrar"],cons:["Menos controle","Participantes desconhecidos"],icon:q,color:"blue"};case"friends":return{title:"Apenas Amigos",subtitle:"Somente seus amigos podem participar",description:"Apenas pessoas da sua lista de amigos podem ver e participar",pros:["Competição entre conhecidos","Mais motivação","Ambiente familiar"],cons:["Menos participantes","Limitado aos seus amigos"],icon:E,color:"green"};case"password":return{title:"Com Senha",subtitle:"Acesso restrito por senha",description:"Apenas quem souber a senha pode participar. Ideal para grupos específicos",pros:["Controle total","Grupos exclusivos","Privacidade"],cons:["Precisa compartilhar senha","Pode ter poucos participantes"],icon:B,color:"yellow"}}},n=t("public"),d=t("friends"),m=t("password");return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Visibilidade"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Quem pode participar?"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("label",{className:`block p-4 border-2 rounded-xl cursor-pointer transition-all ${s.visibility==="public"?"border-blue-500/50 bg-blue-500/5":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"visibility",value:"public",checked:s.visibility==="public",onChange:a=>i({visibility:a.target.value}),className:"sr-only"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.visibility==="public"?"bg-blue-500/20":"bg-snapfit-dark-gray"}`,children:e.jsx(q,{className:`w-6 h-6 ${s.visibility==="public"?"text-blue-500":"text-gray-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-white",children:n.title}),e.jsx("span",{className:"text-xs px-2 py-0.5 bg-blue-500/20 text-blue-500 rounded",children:"Mais Popular"})]}),e.jsx("p",{className:"text-sm text-gray-400",children:n.subtitle})]})]})]}),e.jsxs("label",{className:`block p-4 border-2 rounded-xl cursor-pointer transition-all ${s.visibility==="friends"?"border-green-500/50 bg-green-500/5":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"visibility",value:"friends",checked:s.visibility==="friends",onChange:a=>i({visibility:a.target.value}),className:"sr-only"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.visibility==="friends"?"bg-green-500/20":"bg-snapfit-dark-gray"}`,children:e.jsx(E,{className:`w-6 h-6 ${s.visibility==="friends"?"text-green-500":"text-gray-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"font-semibold text-white mb-1",children:d.title}),e.jsx("p",{className:"text-sm text-gray-400",children:d.subtitle})]})]})]}),e.jsxs("label",{className:`block p-4 border-2 rounded-xl cursor-pointer transition-all ${s.visibility==="password"?"border-yellow-500/50 bg-yellow-500/5":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"visibility",value:"password",checked:s.visibility==="password",onChange:a=>i({visibility:a.target.value}),className:"sr-only"}),e.jsxs("div",{className:"flex items-start gap-4",children:[e.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${s.visibility==="password"?"bg-yellow-500/20":"bg-snapfit-dark-gray"}`,children:e.jsx(B,{className:`w-6 h-6 ${s.visibility==="password"?"text-yellow-500":"text-gray-400"}`})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h4",{className:"font-semibold text-white",children:m.title}),e.jsx("span",{className:"text-xs px-2 py-0.5 bg-yellow-500/20 text-yellow-500 rounded",children:"Exclusivo"})]}),e.jsx("p",{className:"text-sm text-gray-400",children:m.subtitle})]})]})]}),s.visibility==="password"&&e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("label",{className:"block text-sm font-medium text-white mb-2",children:[e.jsx(Pe,{className:"w-4 h-4 inline mr-2"}),"Senha do Desafio *"]}),e.jsx("input",{type:"text",value:s.password||"",onChange:a=>i({password:a.target.value}),placeholder:"Digite uma senha fácil de lembrar",className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green transition-colors",maxLength:20}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("p",{className:"text-xs text-gray-400",children:(h=s.password)!=null&&h.trim()?"✓ Senha definida":"Digite uma senha para continuar"}),e.jsxs("span",{className:"text-xs text-gray-500",children:[((f=s.password)==null?void 0:f.length)||0,"/20"]})]}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:'Use senhas simples como "VIP2024" ou "AMIGOS"'})]}),r&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:r})})]})]})}function cs({formData:s,updateFormData:i,wallet:r,error:t}){var f;const n=a=>{switch(a){case 7:return{label:"1 Semana",description:"Desafio rápido e intenso",recommended:"Dobro ou Nada"};case 15:return{label:"2 Semanas",description:"Tempo ideal para mudanças",recommended:"Ambos os tipos"};case 30:return{label:"1 Mês",description:"Desafio completo e equilibrado",recommended:"Top 3"};case 60:return{label:"2 Meses",description:"Desafio bimestral",recommended:"Top 3"};case 90:return{label:"3 Meses",description:"Transformação trimestral",recommended:"Top 3"};case 180:return{label:"6 Meses",description:"Desafio semestral",recommended:"Top 3"};case 365:return{label:"1 Ano",description:"Transformação anual",recommended:"Top 3"};case"custom":return{label:"Personalizado",description:"Escolha a data de fim",recommended:"Ambos os tipos"}}},d=new Date().toISOString().split("T")[0],m=new Date;m.setMonth(m.getMonth()+3);const h=m.toISOString().split("T")[0];return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Configurações"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Duração, taxa e data de início"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Duração *"}),e.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2",children:[7,15,30,60,90,180,365,"custom"].map(a=>{const p=n(a);return e.jsxs("label",{className:`block p-3 border-2 rounded-lg cursor-pointer transition-all text-center ${s.duration===a?"border-snapfit-green bg-snapfit-green/10":"border-snapfit-green/20 hover:border-snapfit-green/40"}`,children:[e.jsx("input",{type:"radio",name:"duration",value:a,checked:s.duration===a,onChange:x=>i({duration:Number(x.target.value)}),className:"sr-only"}),e.jsx("div",{className:"text-sm sm:text-lg font-bold text-white mb-1",children:a==="custom"?"📅":`${a}d`}),e.jsx("div",{className:"text-xs text-gray-400",children:p.label})]},a)})})]}),s.duration==="custom"&&e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-white mb-2",children:[e.jsx(_,{className:"w-4 h-4 inline mr-2"}),"Data de Fim *"]}),e.jsx("input",{type:"date",value:s.customEndDate||"",onChange:a=>i({customEndDate:a.target.value}),min:(()=>{const a=new Date(s.startDate);return a.setDate(a.getDate()+1),a.toISOString().split("T")[0]})(),max:(()=>{const a=new Date(s.startDate);return a.setFullYear(a.getFullYear()+2),a.toISOString().split("T")[0]})(),className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-snapfit-green transition-colors"}),e.jsx("div",{className:"flex justify-between items-center mt-2",children:e.jsx("div",{className:"text-xs text-gray-400",children:s.customEndDate?(()=>{const a=new Date(s.startDate),x=new Date(s.customEndDate).getTime()-a.getTime();return`Duração: ${Math.ceil(x/(1e3*60*60*24))} dias`})():"Selecione a data de fim"})}),e.jsxs("div",{className:"mt-3",children:[e.jsx("p",{className:"text-xs text-gray-400 mb-2",children:"Sugestões:"}),e.jsx("div",{className:"flex gap-2 flex-wrap",children:[{label:"1 Mês",days:30},{label:"3 Meses",days:90},{label:"6 Meses",days:180},{label:"1 Ano",days:365}].map(a=>{const p=new Date(s.startDate);p.setDate(p.getDate()+a.days);const x=p.toISOString().split("T")[0];return e.jsx("button",{onClick:()=>i({customEndDate:x}),className:`px-3 py-1 text-xs rounded border transition-colors ${s.customEndDate===x?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-dark-gray border-snapfit-green/20 text-gray-400 hover:border-snapfit-green/40"}`,children:a.label},a.label)})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Taxa de Entrada *"}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"number",min:"10",max:"1000",step:"10",value:s.entryFee,onChange:a=>i({entryFee:Number(a.target.value)}),className:"w-full px-4 py-3 pr-20 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-snapfit-green transition-colors"}),e.jsxs("div",{className:"absolute inset-y-0 right-0 pr-4 flex items-center",children:[e.jsx(M,{className:"w-4 h-4 text-yellow-500 mr-1"}),e.jsx("span",{className:"text-sm text-gray-400",children:"coins"})]})]}),e.jsxs("div",{className:"flex justify-between items-center mt-2",children:[e.jsx("div",{className:"text-xs text-gray-400",children:s.entryFee<10?"Mínimo 10 coins":s.entryFee>1e3?"Máximo 1000 coins":"✓ Taxa válida"}),r&&e.jsxs("div",{className:`text-xs ${r.snapCoins.total>=s.entryFee?"text-green-400":"text-red-400"}`,children:["Você tem ",r.snapCoins.total," coins"]})]}),e.jsx("div",{className:"flex gap-2 flex-wrap mt-2",children:[50,100,200,500].map(a=>e.jsx("button",{onClick:()=>i({entryFee:a}),className:`px-3 py-1 text-xs rounded border transition-colors ${s.entryFee===a?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-dark-gray border-snapfit-green/20 text-gray-400 hover:border-snapfit-green/40"}`,children:a},a))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Data de Início *"}),e.jsx("input",{type:"date",value:s.startDate,onChange:a=>i({startDate:a.target.value}),min:d,max:h,className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-snapfit-green transition-colors"}),e.jsx("div",{className:"flex justify-between items-center mt-2",children:e.jsx("div",{className:"text-xs text-gray-400",children:s.startDate===d?"Começará hoje":new Date(s.startDate)<new Date(d)?"Data inválida":`Começará em ${Math.ceil((new Date(s.startDate).getTime()-new Date().getTime())/(1e3*60*60*24))} dias`})}),e.jsx("div",{className:"flex gap-2 flex-wrap mt-2",children:[{label:"Hoje",date:d},{label:"Amanhã",date:new Date(Date.now()+864e5).toISOString().split("T")[0]},{label:"Segunda",date:(()=>{const a=new Date,p=a.getDay(),x=p===0?1:8-p;return a.setDate(a.getDate()+x),a.toISOString().split("T")[0]})()}].map(a=>e.jsx("button",{onClick:()=>i({startDate:a.date}),className:`px-3 py-1 text-xs rounded border transition-colors ${s.startDate===a.date?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-dark-gray border-snapfit-green/20 text-gray-400 hover:border-snapfit-green/40"}`,children:a.label},a.label))})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-white mb-2",children:[e.jsx(E,{className:"w-4 h-4 inline mr-2"}),"Limite de Participantes (opcional)"]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("label",{className:"flex items-center gap-2",children:[e.jsx("input",{type:"checkbox",checked:!s.maxParticipants,onChange:a=>i({maxParticipants:a.target.checked?void 0:20}),className:"rounded border-snapfit-green/20 bg-snapfit-dark-gray text-snapfit-green focus:ring-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Sem limite"})]}),s.maxParticipants&&e.jsx("input",{type:"number",min:"5",max:"100",value:s.maxParticipants,onChange:a=>i({maxParticipants:Number(a.target.value)}),className:"w-24 px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded text-white focus:outline-none focus:ring-2 focus:ring-snapfit-green"})]}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:s.maxParticipants?`Máximo ${s.maxParticipants} participantes`:"Qualquer número de pessoas pode participar"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Prêmios Adicionais (opcional)"}),e.jsx("textarea",{value:s.additionalPrizes,onChange:a=>i({additionalPrizes:a.target.value}),placeholder:"Ex: Saco de Whey Protein para o campeão + Shaker personalizado",rows:3,className:"w-full px-4 py-3 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green resize-none transition-colors",maxLength:200}),e.jsxs("div",{className:"flex justify-between items-center mt-1",children:[e.jsx("p",{className:"text-xs text-gray-400",children:"Descreva prêmios extras que você oferecerá"}),e.jsxs("span",{className:"text-xs text-gray-500",children:[((f=s.additionalPrizes)==null?void 0:f.length)||0,"/200"]})]})]}),t&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:t})})]})]})}function ds({formData:s,wallet:i,error:r}){const t=()=>{switch(s.type){case"top_3":return{icon:P,label:"Top 3",color:"text-yellow-500",description:"1º: 50% • 2º: 30% • 3º: 20%"};case"double_or_nothing":return{icon:R,label:"Dobro ou Nada",color:"text-red-500",description:"50% dobram • 50% perdem"}}},n=()=>{switch(s.visibility){case"public":return{icon:q,label:"Público",color:"text-blue-500",description:"Todos podem participar"};case"friends":return{icon:E,label:"Apenas Amigos",color:"text-green-500",description:"Somente seus amigos"};case"password":return{icon:B,label:"Com Senha",color:"text-yellow-500",description:`Senha: ${s.password}`}}},d=c=>new Date(c).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric"}),m=c=>{const u=new Date(c),l=new Date,o=u.getTime()-l.getTime();return Math.ceil(o/(1e3*60*60*24))},h=t(),f=n(),a=h.icon,p=f.icon,x=m(s.startDate);return e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:"Revisar Desafio"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Confira as informações antes de criar"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-start gap-4 mb-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(P,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-lg font-bold text-white mb-1",children:s.name}),s.description&&e.jsx("p",{className:"text-sm text-gray-400",children:s.description})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(a,{className:`w-5 h-5 ${h.color}`}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:h.label}),e.jsx("div",{className:"text-xs text-gray-400",children:h.description})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(p,{className:`w-5 h-5 ${f.color}`}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:f.label}),e.jsx("div",{className:"text-xs text-gray-400",children:f.description})]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(D,{className:"w-5 h-5 text-gray-400"}),e.jsxs("div",{children:[e.jsx("div",{className:"text-sm font-medium text-white",children:s.duration==="custom"&&s.customEndDate?(()=>{const c=new Date(s.startDate),l=new Date(s.customEndDate).getTime()-c.getTime();return`${Math.ceil(l/(1e3*60*60*24))} dias`})():`${s.duration} dias`}),e.jsx("div",{className:"text-xs text-gray-400",children:s.duration==="custom"?"Duração personalizada":"Duração do desafio"})]})]}),e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(M,{className:"w-5 h-5 text-yellow-500"}),e.jsxs("div",{children:[e.jsxs("div",{className:"text-sm font-medium text-white",children:[s.entryFee," SnapCoins"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Taxa de entrada"})]})]})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("h5",{className:"text-sm font-medium text-white mb-3 flex items-center gap-2",children:[e.jsx(_,{className:"w-4 h-4"}),"Cronograma"]}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Início:"}),e.jsxs("span",{className:"text-white",children:[d(s.startDate),x===0&&e.jsx("span",{className:"text-green-400 ml-2",children:"(Hoje)"}),x===1&&e.jsx("span",{className:"text-blue-400 ml-2",children:"(Amanhã)"}),x>1&&e.jsxs("span",{className:"text-gray-400 ml-2",children:["(em ",x," dias)"]})]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Fim:"}),e.jsx("span",{className:"text-white",children:s.duration==="custom"&&s.customEndDate?d(s.customEndDate):d(new Date(new Date(s.startDate).getTime()+s.duration*24*60*60*1e3).toISOString())})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("h5",{className:"text-sm font-medium text-white mb-2 flex items-center gap-2",children:[e.jsx(E,{className:"w-4 h-4"}),"Participantes"]}),e.jsx("div",{className:"text-sm text-gray-400",children:s.maxParticipants?`Máximo ${s.maxParticipants} pessoas`:"Sem limite"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("h5",{className:"text-sm font-medium text-white mb-2 flex items-center gap-2",children:[e.jsx(M,{className:"w-4 h-4 text-yellow-500"}),"Sua Participação"]}),e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-gray-400",children:"Taxa:"}),e.jsxs("span",{className:"text-yellow-500",children:[s.entryFee," coins"]})]}),i&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Restante:"}),e.jsxs("span",{className:i.snapCoins.total>=s.entryFee?"text-green-400":"text-red-400",children:[i.snapCoins.total-s.entryFee," coins"]})]})]})]})]}),s.additionalPrizes&&e.jsxs("div",{className:"bg-snapfit-green/5 rounded-lg p-4 border border-snapfit-green/20",children:[e.jsxs("h5",{className:"text-sm font-medium text-snapfit-green mb-2 flex items-center gap-2",children:[e.jsx(Te,{className:"w-4 h-4"}),"Prêmios Adicionais"]}),e.jsx("p",{className:"text-sm text-gray-300",children:s.additionalPrizes})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray/50 rounded-lg p-4 border border-snapfit-green/10",children:[e.jsx("h5",{className:"text-sm font-medium text-white mb-3",children:"Simulação de Prêmios"}),e.jsxs("div",{className:"text-xs text-gray-400 mb-2",children:["Com 10 participantes pagando ",s.entryFee," SnapCoins cada:"]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded p-3 space-y-1 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Total arrecadado:"}),e.jsxs("span",{className:"text-yellow-500 font-medium",children:[s.entryFee*10," SnapCoins"]})]}),s.type==="top_3"?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"1º lugar:"}),e.jsxs("span",{className:"text-green-400",children:[Math.floor(s.entryFee*10*.5)," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"2º lugar:"}),e.jsxs("span",{className:"text-blue-400",children:[Math.floor(s.entryFee*10*.3)," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"3º lugar:"}),e.jsxs("span",{className:"text-purple-400",children:[Math.floor(s.entryFee*10*.2)," SnapCoins"]})]})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"5 melhores (cada):"}),e.jsxs("span",{className:"text-green-400",children:[s.entryFee*2," SnapCoins"]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"5 piores:"}),e.jsx("span",{className:"text-red-400",children:"0 SnapCoins"})]})]})]})]}),e.jsx("div",{className:"bg-blue-500/10 rounded-lg p-4 border border-blue-500/20",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(I,{className:"w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"text-sm",children:[e.jsx("p",{className:"text-blue-400 font-medium mb-1",children:"Tudo pronto!"}),e.jsxs("p",{className:"text-gray-400",children:["Você participará automaticamente do seu próprio desafio.",s.entryFee," SnapCoins serão descontados da sua carteira."]})]})]})}),r&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsx("p",{className:"text-sm text-red-400",children:r})})]})]})}const A=[{id:"basic",title:"Informações Básicas",component:is},{id:"type",title:"Tipo de Desafio",component:ls},{id:"visibility",title:"Visibilidade",component:os},{id:"settings",title:"Configurações",component:cs},{id:"review",title:"Revisar",component:ds}];function xs({isOpen:s,onClose:i,onSuccess:r}){const{wallet:t}=K(),[n,d]=j.useState(0),[m,h]=j.useState(!1),[f,a]=j.useState(null),[p,x]=j.useState(!1),[c,u]=j.useState(null),[l,o]=j.useState({name:"",description:"",type:"top_3",visibility:"public",password:"",duration:30,customEndDate:void 0,entryFee:100,additionalPrizes:"",startDate:new Date().toISOString().split("T")[0],maxParticipants:void 0}),b=v=>{o($=>({...$,...v})),a(null)},g=()=>{var v;switch(n){case 0:return l.name.trim().length>=3;case 1:return!!l.type;case 2:return l.visibility==="password"?!!((v=l.password)!=null&&v.trim()):!!l.visibility;case 3:const $=l.duration!=="custom"||!!l.customEndDate;return l.entryFee>=10&&l.entryFee<=1e3&&!!l.startDate&&$;case 4:return!0;default:return!1}},k=()=>{const v=g();return console.log(`🔍 Validação step ${n}:`,v,l),v?n===3&&t&&t.snapCoins.total<l.entryFee?(console.log("💰 SnapCoins insuficientes:",t.snapCoins.total,"<",l.entryFee),!1):!0:!1},y=()=>{n<A.length-1&&k()&&(d(v=>v+1),a(null))},N=()=>{n>0&&(d(v=>v-1),a(null))},C=async()=>{if(console.log("🚀 Iniciando criação do desafio...",l),!t||t.snapCoins.total<l.entryFee){a("SnapCoins insuficientes para criar este desafio");return}h(!0),a(null);try{console.log("📡 Chamando createChallenge...");const v=await Fe({...l,startDate:new Date(l.startDate).toISOString()});console.log("✅ Desafio criado com sucesso:",v),u(v),x(!0)}catch(v){console.error("❌ Erro ao criar desafio:",v),a(v instanceof Error?v.message:"Erro ao criar desafio")}finally{h(!1)}},F=()=>{d(0),a(null),x(!1),u(null),o({name:"",description:"",type:"top_3",visibility:"public",password:"",duration:30,customEndDate:void 0,entryFee:100,additionalPrizes:"",startDate:new Date().toISOString().split("T")[0],maxParticipants:void 0}),i()},T=()=>{r(),F()};if(!s)return null;if(p&&c)return e.jsx(ns,{title:"Desafio Criado!",subtitle:`"${c.name}" foi criado com sucesso. Você já está participando!`,onContinue:T,autoClose:!1});const le=A[n].component,oe=n===A.length-1;return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999] sm:flex sm:items-center sm:justify-center sm:p-4",children:e.jsxs("div",{className:"bg-snapfit-gray w-full h-full sm:w-auto sm:h-auto sm:max-w-2xl sm:max-h-[90vh] sm:rounded-xl shadow-xl border-0 sm:border border-snapfit-green/20 flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-snapfit-green/20 flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center gap-2 sm:gap-3 min-w-0 flex-1",children:[e.jsx("div",{className:"w-6 h-6 sm:w-8 sm:h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30 flex-shrink-0",children:e.jsx(P,{className:"w-3 h-3 sm:w-5 sm:h-5 text-snapfit-green"})}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("h2",{className:"text-sm sm:text-lg font-bold text-white truncate",children:"Criar Desafio"}),e.jsxs("p",{className:"text-xs text-gray-400 truncate",children:[n+1,"/",A.length,": ",A[n].title]})]})]}),e.jsx("button",{onClick:F,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors flex-shrink-0",children:e.jsx(O,{className:"w-4 h-4 sm:w-5 sm:h-5"})})]}),e.jsx("div",{className:"px-4 py-3 bg-snapfit-dark-gray/30 flex-shrink-0",children:e.jsx("div",{className:"flex items-center gap-1 sm:gap-2",children:A.map((v,$)=>e.jsxs(j.Fragment,{children:[e.jsx("div",{className:`flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 rounded-full text-xs font-medium transition-colors flex-shrink-0 ${$<n?"bg-snapfit-green text-black":$===n?"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30":"bg-gray-600 text-gray-400"}`,children:$<n?e.jsx(re,{className:"w-2 h-2 sm:w-3 sm:h-3"}):$+1}),$<A.length-1&&e.jsx("div",{className:`flex-1 h-0.5 transition-colors ${$<n?"bg-snapfit-green":"bg-gray-600"}`})]},v.id))})}),e.jsx("div",{className:"flex-1 overflow-y-auto min-h-0",children:e.jsx("div",{className:"p-3 sm:p-6 min-h-full",children:e.jsx(le,{formData:l,updateFormData:b,wallet:t,error:f})})}),e.jsx("div",{className:"p-3 sm:p-4 border-t border-snapfit-green/20 bg-snapfit-dark-gray/30 flex-shrink-0 safe-area-inset-bottom",children:e.jsxs("div",{className:"flex items-center justify-between gap-2 sm:gap-3",children:[e.jsxs("button",{onClick:N,disabled:n===0,className:"flex items-center gap-1 px-2 sm:px-3 py-2 text-gray-400 hover:text-white border border-gray-600/30 hover:border-gray-500/50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm min-h-[44px]",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden xs:inline",children:"Voltar"})]}),e.jsx("div",{className:"flex items-center gap-1 text-xs text-gray-400 px-2",children:e.jsxs("span",{children:[n+1,"/",A.length]})}),oe?e.jsx("button",{onClick:C,disabled:m||!k(),className:"flex items-center gap-1 px-2 sm:px-3 py-2 bg-snapfit-green hover:bg-snapfit-green-light text-black font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm relative min-h-[44px]",children:m?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-black/20 border-t-black rounded-full animate-spin"}),e.jsx("span",{className:"hidden xs:inline",children:"Criando..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(P,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden xs:inline",children:"Criar"})]})}):e.jsxs("button",{onClick:y,disabled:!k(),className:"flex items-center gap-1 px-2 sm:px-3 py-2 bg-snapfit-green hover:bg-snapfit-green-light text-black font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-xs sm:text-sm min-h-[44px]",children:[e.jsx("span",{className:"hidden xs:inline",children:"Próximo"}),e.jsx(ne,{className:"w-4 h-4"})]})]})}),m&&e.jsx("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center rounded-lg sm:rounded-xl",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-lg p-6 border border-snapfit-green/20 text-center",children:[e.jsx("div",{className:"w-8 h-8 border-2 border-snapfit-green/20 border-t-snapfit-green rounded-full animate-spin mx-auto mb-3"}),e.jsx("p",{className:"text-white font-medium mb-1",children:"Criando seu desafio..."}),e.jsx("p",{className:"text-xs text-gray-400",children:"Isso pode levar alguns segundos"})]})})]})})}function ms({isOpen:s,onClose:i,onSuccess:r,challenge:t}){const{wallet:n}=K(),[d,m]=j.useState(!1),[h,f]=j.useState(null),[a,p]=j.useState(""),x=async()=>{if(t){if(!n||n.snapCoins.total<t.entryFee){f("SnapCoins insuficientes para participar deste desafio");return}if(t.visibility==="password"&&!a.trim()){f("Digite a senha do desafio");return}m(!0),f(null);try{await $e(t.id,t.visibility==="password"?a:void 0),r(),i(),p("")}catch(y){f(y instanceof Error?y.message:"Erro ao participar do desafio")}finally{m(!1)}}},c=y=>{switch(y){case"public":return e.jsx(q,{className:"w-4 h-4 text-blue-400"});case"friends":return e.jsx(E,{className:"w-4 h-4 text-green-400"});case"password":return e.jsx(B,{className:"w-4 h-4 text-yellow-400"})}},u=y=>{switch(y){case"double_or_nothing":return e.jsx(R,{className:"w-4 h-4 text-red-400"});case"top_3":return e.jsx(P,{className:"w-4 h-4 text-yellow-400"})}},l=y=>{switch(y){case"top_3":return"Os 3 primeiros colocados ganham prêmios (50%, 30%, 20%)";case"double_or_nothing":return"50% melhores dobram as coins, 50% piores perdem tudo"}},o=y=>new Date(y).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),b=y=>{const N=new Date(y),C=new Date,F=N.getTime()-C.getTime(),T=Math.ceil(F/(1e3*60*60*24));return Math.max(0,T)};if(!s||!t)return null;const g=t.participants.some(y=>y.userId==="current-user"),k=t.status==="upcoming"||t.status==="active";return e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-2 sm:p-4",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl w-full max-w-lg max-h-[95vh] sm:max-h-[90vh] overflow-hidden border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(P,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Detalhes do Desafio"})]}),e.jsx("button",{onClick:i,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(O,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-4 sm:p-6 overflow-y-auto max-h-[calc(95vh-140px)] sm:max-h-[calc(90vh-140px)]",children:e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-2",children:t.name}),t.description&&e.jsx("p",{className:"text-gray-400 text-sm",children:t.description})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[c(t.visibility),e.jsx("span",{className:"text-gray-400",children:t.visibility==="public"?"Público":t.visibility==="friends"?"Amigos":"Com Senha"})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(_,{className:"w-4 h-4 text-gray-400"}),e.jsxs("span",{className:"text-gray-400",children:[t.duration," dias"]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[u(t.type),e.jsx("span",{className:"text-gray-400",children:t.type==="top_3"?"Top 3":"Dobro ou Nada"})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm",children:[e.jsx(M,{className:"w-4 h-4 text-yellow-500"}),e.jsxs("span",{className:"text-yellow-500 font-medium",children:[t.entryFee," coins"]})]})]})]}),e.jsx("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:e.jsx("p",{className:"text-xs text-gray-400",children:l(t.type)})}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Início:"}),e.jsx("span",{className:"text-white",children:o(t.startDate)})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Fim:"}),e.jsx("span",{className:"text-white",children:o(t.endDate)})]}),t.status==="active"&&e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-gray-400",children:"Tempo restante:"}),e.jsxs("span",{className:"text-blue-400",children:[b(t.endDate)," dias"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[e.jsx(E,{className:"w-4 h-4 text-gray-400"}),e.jsx("span",{className:"text-lg font-bold text-white",children:t.participants.length}),t.maxParticipants&&e.jsxs("span",{className:"text-gray-400",children:["/",t.maxParticipants]})]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Participantes"})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsxs("div",{className:"flex items-center justify-center gap-1 mb-1",children:[e.jsx(P,{className:"w-4 h-4 text-yellow-500"}),e.jsx("span",{className:"text-lg font-bold text-yellow-500",children:t.totalPrizePool})]}),e.jsx("p",{className:"text-xs text-gray-400",children:"Total em Prêmios"})]})]}),t.additionalPrizes&&e.jsxs("div",{className:"p-3 bg-snapfit-green/5 border border-snapfit-green/20 rounded-lg",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx(ie,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm font-medium text-snapfit-green",children:"Prêmios Extras"})]}),e.jsx("p",{className:"text-xs text-gray-300",children:t.additionalPrizes})]}),e.jsxs("div",{className:"flex items-center gap-3 p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("img",{src:t.createdBy.photo,alt:t.createdBy.name,className:"w-10 h-10 rounded-full"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-white",children:t.createdBy.name}),e.jsxs("p",{className:"text-xs text-gray-400",children:["@",t.createdBy.username," • Criador"]})]})]}),t.visibility==="password"&&!g&&k&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-white mb-2",children:"Senha do Desafio"}),e.jsx("input",{type:"password",value:a,onChange:y=>p(y.target.value),placeholder:"Digite a senha",className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-snapfit-green"})]}),n&&k&&!g&&e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Suas SnapCoins:"}),e.jsx("span",{className:"text-sm font-medium text-white",children:n.snapCoins.total})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Taxa de entrada:"}),e.jsx("span",{className:"text-sm font-medium text-yellow-500",children:t.entryFee})]}),e.jsxs("div",{className:"flex justify-between items-center pt-2 border-t border-snapfit-green/20 mt-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Após participar:"}),e.jsx("span",{className:`text-sm font-medium ${n.snapCoins.total>=t.entryFee?"text-green-400":"text-red-400"}`,children:n.snapCoins.total-t.entryFee})]})]}),g&&e.jsx("div",{className:"p-3 bg-green-400/10 border border-green-400/30 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(P,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-sm text-green-400 font-medium",children:"Você já está participando!"})]})}),h&&e.jsx("div",{className:"p-3 bg-red-400/10 border border-red-400/30 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(V,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-sm text-red-400",children:h})]})})]})}),e.jsx("div",{className:"p-4 sm:p-6 border-t border-snapfit-green/20 bg-snapfit-dark-gray/30",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("button",{onClick:i,className:"px-4 py-2 text-gray-400 hover:text-white border border-gray-600/30 hover:border-gray-500/50 rounded-lg transition-colors",children:"Fechar"}),k&&!g&&e.jsx("button",{onClick:x,disabled:d||n&&n.snapCoins.total<t.entryFee,className:"px-4 py-2 bg-snapfit-green hover:bg-snapfit-green-light text-black font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:d?"Participando...":`Participar (${t.entryFee} coins)`})]})})]})})}function ps(){const{challenges:s,userChallenges:i,stats:r,refreshData:t}=Me(),[n,d]=j.useState(!1),[m,h]=j.useState(!1),[f,a]=j.useState(null),p=j.useMemo(()=>{const g=s.filter(y=>y.status==="active").slice(0,2),k=s.filter(y=>y.status==="upcoming").slice(0,3-g.length);return[...g,...k]},[s]),x=g=>{a(g),h(!0)},c=()=>{t()},u=()=>{t()},l=g=>{switch(g){case"double_or_nothing":return e.jsx(R,{className:"w-3 h-3 text-red-400"});case"top_3":return e.jsx(P,{className:"w-3 h-3 text-yellow-400"})}},o=g=>{const k=new Date(g),y=new Date,N=k.getTime()-y.getTime(),C=Math.ceil(N/(1e3*60*60*24));return Math.max(0,C)},b=g=>new Date(g).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"});return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(P,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-bold text-white",children:"Desafios"}),e.jsxs("p",{className:"text-xs text-gray-400",children:[r.userActiveChallenges," ativos • ",r.activeChallenges," disponíveis"]})]})]}),e.jsxs("button",{onClick:()=>d(!0),className:"flex items-center gap-1.5 px-3 py-1.5 text-xs font-medium bg-snapfit-green/10 hover:bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 rounded-lg transition-colors",children:[e.jsx(Ae,{className:"w-3 h-3"}),e.jsx("span",{children:"Criar"})]})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-2 mb-4",children:[e.jsxs("div",{className:"text-center p-2 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-sm font-bold text-white",children:r.userTotalChallenges}),e.jsx("div",{className:"text-xs text-gray-400",children:"Meus"})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-sm font-bold text-green-400",children:r.activeChallenges}),e.jsx("div",{className:"text-xs text-gray-400",children:"Ativos"})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-sm font-bold text-blue-400",children:r.upcomingChallenges}),e.jsx("div",{className:"text-xs text-gray-400",children:"Em breve"})]})]}),p.length>0?e.jsxs("div",{className:"space-y-2",children:[e.jsx("h4",{className:"text-xs font-medium text-gray-400 mb-2",children:"Desafios em Destaque"}),p.map(g=>e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10 hover:border-snapfit-green/30 transition-colors cursor-pointer",onClick:()=>x(g),children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[l(g.type),e.jsx("span",{className:"text-sm font-medium text-white truncate",children:g.name})]}),e.jsx(ne,{className:"w-3 h-3 text-gray-400"})]}),e.jsxs("div",{className:"flex items-center justify-between text-xs",children:[e.jsxs("div",{className:"flex items-center gap-3 text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(E,{className:"w-3 h-3"}),e.jsx("span",{children:g.participants.length})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(M,{className:"w-3 h-3 text-yellow-500"}),e.jsx("span",{children:g.entryFee})]}),g.status==="active"&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(D,{className:"w-3 h-3"}),e.jsxs("span",{children:[o(g.endDate),"d"]})]}),g.status==="upcoming"&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(_,{className:"w-3 h-3"}),e.jsx("span",{children:b(g.startDate)})]})]}),e.jsx("span",{className:`px-2 py-0.5 rounded text-xs font-medium ${g.status==="active"?"bg-green-400/10 text-green-400":"bg-blue-400/10 text-blue-400"}`,children:g.status==="active"?"Ativo":"Em breve"})]})]},g.id))]}):e.jsxs("div",{className:"text-center py-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-dark-gray rounded-full flex items-center justify-center mx-auto mb-2 border border-snapfit-green/20",children:e.jsx(P,{className:"w-6 h-6 text-gray-400"})}),e.jsx("p",{className:"text-gray-400 text-sm mb-1",children:"Nenhum desafio ativo"}),e.jsx("p",{className:"text-gray-500 text-xs",children:"Crie um desafio para começar!"})]}),i.filter(g=>g.status==="active").length>0&&e.jsxs("div",{className:"mt-4 pt-3 border-t border-snapfit-green/10",children:[e.jsx("h4",{className:"text-xs font-medium text-gray-400 mb-2",children:"Meus Desafios Ativos"}),e.jsx("div",{className:"space-y-1",children:i.filter(g=>g.status==="active").slice(0,2).map(g=>e.jsxs("div",{className:"flex items-center justify-between p-2 bg-snapfit-green/5 rounded border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[l(g.type),e.jsx("span",{className:"text-xs font-medium text-white truncate",children:g.name})]}),e.jsxs("div",{className:"flex items-center gap-2 text-xs text-gray-400",children:[e.jsx(D,{className:"w-3 h-3"}),e.jsxs("span",{children:[o(g.endDate),"d"]})]})]},g.id))})]}),e.jsx("div",{className:"mt-4 pt-3 border-t border-snapfit-green/10",children:e.jsxs("details",{className:"group",children:[e.jsx("summary",{className:"text-xs font-medium text-gray-400 cursor-pointer hover:text-gray-300 transition-colors",children:"Como funcionam os desafios?"}),e.jsxs("div",{className:"mt-2 space-y-2 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(P,{className:"w-3 h-3 text-yellow-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Top 3:"})," 1º (50%), 2º (30%), 3º (20%)"]})]}),e.jsxs("div",{className:"flex items-start gap-2",children:[e.jsx(R,{className:"w-3 h-3 text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-white font-medium",children:"Dobro ou Nada:"})," 50% dobram, 50% perdem"]})]})]})]})}),e.jsx(xs,{isOpen:n,onClose:()=>d(!1),onSuccess:c}),e.jsx(ms,{isOpen:m,onClose:()=>h(!1),onSuccess:u,challenge:f})]})}function Z({isOpen:s,onClose:i,professional:r,title:t,onSave:n}){const[d,m]=S.useState(r),[h,f]=S.useState(!1),a=async x=>{x.preventDefault(),f(!0);try{await new Promise(c=>setTimeout(c,1e3)),n({...d,lastUpdate:new Date().toISOString()}),i()}catch(c){console.error("Erro ao salvar profissional:",c)}finally{f(!1)}},p=(x,c)=>{if(x.includes(".")){const[u,l]=x.split(".");m(o=>({...o,[u]:{...o[u],[l]:c}}))}else m(u=>({...u,[x]:c}))};return s?e.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-2xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("h2",{className:"text-xl font-bold text-white",children:["Editar ",t]}),e.jsx("button",{onClick:i,className:"p-2 text-gray-400 hover:text-white hover:bg-snapfit-dark-gray rounded-lg transition-colors",children:e.jsx(O,{className:"w-5 h-5"})})]}),e.jsxs("form",{onSubmit:a,className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-4",children:[e.jsx("img",{src:d.photo,alt:d.name,className:"w-16 h-16 rounded-full object-cover border-2 border-snapfit-green/30"}),e.jsxs("button",{type:"button",className:"flex items-center gap-2 px-3 py-2 text-sm font-medium text-snapfit-green bg-snapfit-green/10 hover:bg-snapfit-green/20 rounded-lg transition-colors border border-snapfit-green/30",children:[e.jsx(_e,{className:"w-4 h-4"}),"Alterar Foto"]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-2",children:[e.jsx(De,{className:"w-4 h-4 text-snapfit-green"}),"Nome"]}),e.jsx("input",{type:"text",value:d.name,onChange:x=>p("name",x.target.value),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green focus:outline-none transition-colors",placeholder:"Nome do profissional",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-2",children:[e.jsx(Be,{className:"w-4 h-4 text-snapfit-green"}),"Especialidade"]}),e.jsx("input",{type:"text",value:d.specialty,onChange:x=>p("specialty",x.target.value),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green focus:outline-none transition-colors",placeholder:"Ex: Nutrição Esportiva",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-2",children:[e.jsx(se,{className:"w-4 h-4 text-snapfit-green"}),"Email"]}),e.jsx("input",{type:"email",value:d.contact.email,onChange:x=>p("contact.email",x.target.value),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green focus:outline-none transition-colors",placeholder:"<EMAIL>",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-2",children:[e.jsx(Re,{className:"w-4 h-4 text-snapfit-green"}),"Telefone"]}),e.jsx("input",{type:"tel",value:d.contact.phone,onChange:x=>p("contact.phone",x.target.value),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green focus:outline-none transition-colors",placeholder:"(11) 99999-9999",required:!0})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"flex items-center gap-2 text-sm font-medium text-white mb-2",children:[e.jsx(_,{className:"w-4 h-4 text-snapfit-green"}),"Próxima Consulta"]}),e.jsx("input",{type:"date",value:d.nextAppointment?d.nextAppointment.split("T")[0]:"",onChange:x=>p("nextAppointment",x.target.value?new Date(x.target.value).toISOString():""),className:"w-full px-3 py-2 bg-snapfit-dark-gray border border-snapfit-green/20 rounded-lg text-white placeholder-gray-400 focus:border-snapfit-green focus:outline-none transition-colors"})]}),e.jsxs("div",{className:"flex gap-3 pt-4",children:[e.jsx("button",{type:"button",onClick:i,className:"flex-1 px-4 py-2 text-sm font-medium text-gray-400 bg-snapfit-dark-gray hover:bg-snapfit-dark-gray/80 rounded-lg transition-colors border border-snapfit-green/10",children:"Cancelar"}),e.jsxs("button",{type:"submit",disabled:h,className:"flex-1 flex items-center justify-center gap-2 px-4 py-2 text-sm font-bold text-black bg-snapfit-green hover:bg-snapfit-green/90 rounded-lg transition-colors disabled:opacity-50",children:[h?e.jsx("div",{className:"w-4 h-4 border-2 border-black/20 border-t-black rounded-full animate-spin"}):e.jsx(ze,{className:"w-4 h-4"}),h?"Salvando...":"Salvar"]})]})]})]})}):null}function hs({wallet:s}){const{snapCoins:i,snapTokens:r}=s;if(!i||!r)return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(M,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"text-lg font-bold text-white",children:"Minha Carteira"})]}),e.jsx("div",{className:"text-center text-gray-400",children:"Carregando dados da carteira..."})]});const t=r.monthlyLimit===-1?0:r.used/r.monthlyLimit*100,n=()=>r.planType==="snapmonster"?"text-purple-400":t>=90?"text-red-400":t>=70?"text-yellow-400":"text-snapfit-green",d=m=>new Date(m).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"});return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(M,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h3",{className:"text-lg font-bold text-white",children:"Minha Carteira"})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-6 h-6 bg-yellow-500/20 rounded-full flex items-center justify-center",children:e.jsx(M,{className:"w-4 h-4 text-yellow-500"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"SnapCoins"})]}),e.jsx("span",{className:"text-lg font-bold text-yellow-500",children:i.total.toLocaleString()})]}),e.jsxs("div",{className:"space-y-2 text-xs",children:[e.jsxs("div",{className:"flex justify-between text-gray-400",children:[e.jsx("span",{children:"Ganhas este mês:"}),e.jsxs("span",{className:"text-green-400",children:["+",i.monthlyEarned]})]}),e.jsxs("div",{className:"flex justify-between text-gray-400",children:[e.jsx("span",{children:"Gastas este mês:"}),e.jsxs("span",{className:"text-red-400",children:["-",i.spent]})]})]})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg p-4 border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-6 h-6 bg-blue-500/20 rounded-full flex items-center justify-center",children:e.jsx(W,{className:"w-4 h-4 text-blue-500"})}),e.jsx("span",{className:"text-sm font-medium text-white",children:"SnapTokens"})]}),e.jsx("span",{className:`text-lg font-bold ${n()}`,children:r.planType==="snapmonster"?"∞":r.remaining})]}),e.jsxs("div",{className:"space-y-2 text-xs",children:[e.jsxs("div",{className:"flex justify-between text-gray-400",children:[e.jsx("span",{children:"Plano:"}),e.jsx("span",{className:"text-white capitalize",children:r.planType.replace("snap","Snap")})]}),r.planType!=="snapmonster"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between text-gray-400",children:[e.jsx("span",{children:"Usados:"}),e.jsxs("span",{className:n(),children:[r.used,"/",r.monthlyLimit]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-1.5 mt-2",children:e.jsx("div",{className:`h-1.5 rounded-full transition-all duration-300 ${t>=90?"bg-red-400":t>=70?"bg-yellow-400":"bg-snapfit-green"}`,style:{width:`${Math.min(t,100)}%`}})})]}),e.jsxs("div",{className:"flex justify-between text-gray-400 pt-1",children:[e.jsx("span",{children:"Reset em:"}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(_,{className:"w-3 h-3"}),e.jsx("span",{children:d(r.resetDate)})]})]})]})]})]}),e.jsx("div",{className:"mt-4 pt-4 border-t border-snapfit-green/20",children:e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx(Ie,{className:"w-3 h-3 text-green-400"}),e.jsxs("span",{children:["Total ganho: ",i.earned.toLocaleString()," coins"]})]}),e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx(W,{className:"w-3 h-3 text-blue-400"}),e.jsx("span",{children:r.planType==="snapmonster"?"Tokens ilimitados":`${r.used} tokens usados`})]})]})})]})}function fs(){const s=Le(),[i,r]=j.useState(null),[t,n]=j.useState(null),[d,m]=j.useState(!1),[h,f]=j.useState(!1),[a,p]=j.useState(!1),[x,c]=j.useState(!1),[u,l]=j.useState({nutritionist:null,coach:null}),{wallet:o}=K(),b=j.useCallback(async()=>{try{m(!0),r(null);let N=localStorage.getItem("accessToken");if(!N){const F=localStorage.getItem("auth");if(F)try{N=JSON.parse(F).token}catch(T){console.error("Failed to parse auth data:",T)}}if(!N)throw new Error("No access token found. Please login again.");const C=await fetch("https://api.mysnapfit.com.br/users/me",{headers:{Authorization:`Bearer ${N}`}});if(C.ok){const F=await C.json();if(F.status==="success"&&F.data){const T=F.data;n({id:T.id,name:T.name,email:T.email,photo:T.photo,height:T.height,weight:T.weight,bodyfat:T.bodyfat,goal:T.goal,date_of_birth:T.date_of_birth}),l({coach:null,nutritionist:null})}else throw new Error("Invalid response format")}else throw new Error(`HTTP ${C.status}: ${C.statusText}`)}catch(N){console.error("Error loading user data:",N),r(N instanceof Error?N:new Error("Failed to load user data")),n(null)}finally{m(!1)}},[]);S.useEffect(()=>{b()},[b]);const g=N=>{l(C=>({...C,nutritionist:N})),console.log("Nutricionista atualizado:",N)},k=N=>{l(C=>({...C,coach:N})),console.log("Coach atualizado:",N)},y=(N,C)=>{l(F=>({...F,[C]:{id:N.id,name:N.name,specialty:C==="coach"?"Personal Trainer":"Nutricionista",photo:N.photo||(C==="coach"?"https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=50&h=50&fit=crop":"https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=50&h=50&fit=crop"),contact:{email:N.email||"",phone:N.phone||""},lastUpdate:new Date().toISOString(),role:N.role}}))};return d?e.jsx(Oe,{}):i?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-white",children:"Perfil"}),e.jsxs(G,{to:"/dashboard/settings",className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-400 hover:bg-snapfit-dark-gray hover:text-white rounded-lg transition-colors border border-snapfit-green/20",children:[e.jsx(H,{className:"w-5 h-5"}),e.jsx("span",{children:"Configurações"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-red-500/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(V,{className:"w-6 h-6 text-red-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Erro ao carregar perfil"})]}),e.jsx("p",{className:"text-gray-400 mb-4",children:i.message}),e.jsxs("button",{onClick:b,className:"flex items-center gap-2 px-4 py-2 bg-snapfit-green hover:bg-snapfit-green/80 text-white rounded-lg transition-colors",children:[e.jsx(Ue,{className:"w-4 h-4"}),"Tentar novamente"]})]})]}):t?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-white",children:"Perfil"}),e.jsxs(G,{to:"/dashboard/settings",className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-400 hover:bg-snapfit-dark-gray hover:text-white rounded-lg transition-colors border border-snapfit-green/20",children:[e.jsx(H,{className:"w-5 h-5"}),e.jsx("span",{children:"Configurações"})]})]}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx("img",{src:(t==null?void 0:t.photo)||X,alt:"Profile",className:"w-16 h-16 rounded-full object-cover border-2 border-snapfit-green/30",onError:N=>{N.target.src=X}}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-lg font-semibold text-white",children:t==null?void 0:t.name}),(t==null?void 0:t.email)&&e.jsx("p",{className:"text-sm text-gray-400",children:t.email}),(t==null?void 0:t.goal)&&e.jsxs("p",{className:"text-sm text-snapfit-green",children:["Objetivo: ",t.goal]})]})]}),e.jsxs("button",{onClick:()=>s("/edit-profile"),className:"flex items-center gap-2 px-3 py-2 text-sm font-medium text-snapfit-green bg-snapfit-green/10 hover:bg-snapfit-green/20 rounded-lg transition-colors border border-snapfit-green/30",children:[e.jsx(te,{className:"w-4 h-4"}),e.jsx("span",{className:"hidden sm:inline",children:"Editar"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[(t==null?void 0:t.height)&&t.height>0&&e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Altura"}),e.jsxs("div",{className:"text-base font-semibold text-white",children:[t.height,"cm"]})]}),(t==null?void 0:t.weight)&&t.weight>0&&e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Peso Atual"}),e.jsxs("div",{className:"text-base font-semibold text-white",children:[t.weight,"kg"]})]}),(t==null?void 0:t.bodyfat)&&t.bodyfat>0&&e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"% Gordura"}),e.jsxs("div",{className:"text-base font-semibold text-white",children:[t.bodyfat,"%"]})]}),(t==null?void 0:t.date_of_birth)&&e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Data de Nascimento"}),e.jsx("div",{className:"text-base font-semibold text-white",children:new Date(t.date_of_birth).toLocaleDateString("pt-BR")})]})]})]}),o&&e.jsx(hs,{wallet:o}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:e.jsxs("div",{className:"space-y-4",children:[e.jsx(Y,{title:"Meu Nutricionista",professional:u.nutritionist,type:"nutritionist",onEdit:()=>f(!0),onSync:N=>y(N,"nutritionist")}),e.jsx(Y,{title:"Meu Coach",professional:u.coach,type:"coach",onEdit:()=>p(!0),onSync:N=>y(N,"coach")})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:e.jsx(ss,{onInvite:()=>c(!0)})}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:e.jsx(ps,{})})]}),e.jsx(Z,{isOpen:h,onClose:()=>f(!1),professional:u.nutritionist,title:"Nutricionista",onSave:g}),e.jsx(Z,{isOpen:a,onClose:()=>p(!1),professional:u.coach,title:"Coach",onSave:k}),e.jsx(rs,{isOpen:x,onClose:()=>c(!1)})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-white",children:"Perfil"}),e.jsxs(G,{to:"/dashboard/settings",className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-400 hover:bg-snapfit-dark-gray hover:text-white rounded-lg transition-colors border border-snapfit-green/20",children:[e.jsx(H,{className:"w-5 h-5"}),e.jsx("span",{children:"Configurações"})]})]}),e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsx("div",{className:"text-center text-gray-400",children:"Nenhum dado de perfil encontrado."})})]})}export{fs as ProfilePage};
