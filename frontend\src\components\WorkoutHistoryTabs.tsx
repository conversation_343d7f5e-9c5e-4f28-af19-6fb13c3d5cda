import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>bell, History, Clock } from 'lucide-react';
import { WorkoutHistory } from './WorkoutHistory';
import { EnhancedProtocolHistory } from './EnhancedProtocolHistoryNew';
import { ProtocolDetailsModal } from './ProtocolDetailsModal';
import { ProtocolHistoryItem } from '../services/protocolHistory';

interface WorkoutHistoryTabsProps {
  onReuseProtocol?: (protocol: any) => void;
  defaultTab?: 'sessions' | 'protocols';
  hasActiveProtocol?: boolean;
}

export function WorkoutHistoryTabs({
  onReuseProtocol,
  defaultTab = 'protocols', // Default to protocols tab
  hasActiveProtocol = false
}: WorkoutHistoryTabsProps) {
  const [activeTab, setActiveTab] = useState<'sessions' | 'protocols'>(defaultTab);
  const [selectedProtocol, setSelectedProtocol] = useState<string | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  const handleViewProtocolDetails = (protocol: ProtocolHistoryItem) => {
    console.log('🔍 WorkoutHistoryTabs: Protocol selected for viewing:', protocol);
    setSelectedProtocol(protocol.id);
    setShowDetailsModal(true);
  };

  const handleReuseProtocol = (protocol: ProtocolHistoryItem, shouldEdit?: boolean) => {
    if (onReuseProtocol) {
      onReuseProtocol({ ...protocol, edit: shouldEdit });
    }
  };

  const tabs = [
    {
      id: 'sessions',
      label: 'Treinos Realizados',
      icon: Dumbbell,
      description: 'Histórico das suas sessões de treino concluídas'
    },
    {
      id: 'protocols',
      label: 'Protocolos Anteriores',
      icon: History,
      description: 'Visualize, reutilize e gerencie seus protocolos anteriores'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-snapfit-gray rounded-xl shadow-lg border border-snapfit-green/20">
        <div className="p-4 sm:p-6 border-b border-snapfit-green/20">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30">
              <Calendar className="w-5 h-5 text-snapfit-green" />
            </div>
            <h2 className="text-xl font-bold text-white">
              Histórico de Treinos
            </h2>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as 'sessions' | 'protocols')}
                  className={`flex items-center gap-3 p-3 sm:p-4 rounded-lg transition-all duration-200 text-left flex-1 ${
                    isActive
                      ? 'bg-snapfit-green/20 border border-snapfit-green/30 text-white'
                      : 'bg-snapfit-dark-gray border border-snapfit-green/10 text-gray-300 hover:bg-snapfit-green/10 hover:text-white'
                  }`}
                >
                  <div className={`p-2 rounded-lg ${
                    isActive 
                      ? 'bg-snapfit-green/30' 
                      : 'bg-snapfit-green/10'
                  }`}>
                    <Icon className={`w-5 h-5 ${
                      isActive ? 'text-snapfit-green' : 'text-gray-400'
                    }`} />
                  </div>
                  <div>
                    <h3 className="font-medium">{tab.label}</h3>
                    <p className="text-xs text-gray-400 mt-1">{tab.description}</p>
                  </div>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {activeTab === 'sessions' && (
          <div className="animate-fade-in">
            <WorkoutHistory
              showProtocolHistory={false}
              onReuseProtocol={onReuseProtocol}
            />
          </div>
        )}

        {activeTab === 'protocols' && (
          <div className="animate-fade-in">
            <EnhancedProtocolHistory
              type="workout"
              onReuseProtocol={handleReuseProtocol}
              onViewDetails={handleViewProtocolDetails}
            />
          </div>
        )}
      </div>

      {/* Protocol Details Modal */}
      {selectedProtocol && (
        <ProtocolDetailsModal
          protocolId={selectedProtocol}
          type="workout"
          isOpen={showDetailsModal}
          onClose={() => {
            console.log('🔍 WorkoutHistoryTabs: Modal close triggered');
            setShowDetailsModal(false);
            setSelectedProtocol(null);
          }}
          onReuseProtocol={handleReuseProtocol}
        />
      )}
    </div>
  );
}

// Add CSS animation classes to your global CSS if not already present
const styles = `
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
`;

// You can add this to your index.css or create a separate CSS file
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
