import{c as De,t as je,j as e,aa as de,ab as Ue,b as q,r as b,ac as Te,R as I,l as se,I as J,f as te,F as ye,ad as Ve,ae as Oe,a7 as ke,n as oe,af as Fe,ag as Me,p as ce,ah as $e,ai as Qe,B as X,aj as Ke,ak as Je,a as Ze,C as fe,al as Xe,am as Se,G as ee,an as Y,ao as le,a8 as xe,ap as Ye,aq as es,ar as ss,h as me,u as Re,as as ts,g as Ne,Q as we,at as ae,au as be,av as Ae,Z as as,aw as rs,ax as ge,ay as Ce,az as ns,m as is,aA as ls,aB as os,aC as cs,y as ds,aD as ms,L as xs,aE as Pe,aF as gs,P as us,aG as hs,aH as ps,aI as fs,aJ as bs}from"./index-BSexrr-f.js";import{f as vs}from"./date-DOfZ7AVQ.js";import{P as _e}from"./ProtocolDetailsModal-Dzzk55Te.js";import{D as ve}from"./download-DazITGIG.js";import{S as Z}from"./StatCard-Chzy5DIW.js";import{C as ue}from"./CircularProgress-LNw6vDGW.js";import{u as js}from"./useWorkoutProtocol-BgCElNOs.js";import"./workoutService-g-HTB_6w.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ys=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],Ie=De("house",ys);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ns=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],he=De("lightbulb",Ns);function Le({type:t,onReuseProtocol:s,onViewDetails:a}){je();const m=i=>{if(console.log("🔍 EnhancedProtocolHistory: Protocolo selecionado",i),console.log("🔍 EnhancedProtocolHistory: Protocol ID:",i==null?void 0:i.id),console.log("🔍 EnhancedProtocolHistory: Protocol structure:",Object.keys(i||{})),console.log("🔍 EnhancedProtocolHistory: onViewDetails function:",a),a){const y={id:(i==null?void 0:i.id)||(i==null?void 0:i.mock_id)||"unknown",name:(i==null?void 0:i.name)||"Protocolo sem nome",type:(i==null?void 0:i.objective)||"workout",objective:(i==null?void 0:i.objective)||"Não especificado",startDate:(i==null?void 0:i.started_at)||new Date().toISOString(),endDate:(i==null?void 0:i.ended_at)||null,status:(i==null?void 0:i.status)||"unknown",...i};console.log("🔍 EnhancedProtocolHistory: Transformed protocol:",y),a(y)}else console.error("❌ EnhancedProtocolHistory: onViewDetails function not available")},u=i=>{if(console.log("📋 EnhancedProtocolHistory: Protocolo duplicado",i),s){const y={id:(i==null?void 0:i.id)||(i==null?void 0:i.mock_id)||"unknown",name:(i==null?void 0:i.name)||"Protocolo sem nome",type:(i==null?void 0:i.objective)||"workout",objective:(i==null?void 0:i.objective)||"Não especificado",startDate:(i==null?void 0:i.started_at)||new Date().toISOString(),endDate:(i==null?void 0:i.ended_at)||null,status:(i==null?void 0:i.status)||"unknown",...i};s(y,!1)}},x=i=>{console.log("🏁 EnhancedProtocolHistory: Protocolo finalizado",i)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(de,{className:"w-6 h-6 text-snapfit-green"}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-white",children:["Histórico de Protocolos de ",t==="diet"?"Dieta":"Treino"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Visualize, duplique e gerencie seus protocolos anteriores com dados reais do banco de dados"})]})]})}),e.jsx(Ue,{protocolType:t,onProtocolSelect:m,onProtocolDuplicate:u,onProtocolFinish:x})]})}async function Be(){const t=[],s=new Date().toISOString();console.log("🔍 DEBUG: Starting comprehensive workout history endpoint testing...");try{console.log("🔍 DEBUG: Testing GET /users/debug/protocols/raw");const a=await q.get("users/debug/protocols/raw");t.push({endpoint:"GET /users/debug/protocols/raw",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Protocols raw data:",a)}catch(a){t.push({endpoint:"GET /users/debug/protocols/raw",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching protocols raw data:",a)}try{console.log("🔍 DEBUG: Testing GET /users/debug/workouts/raw");const a=await q.get("users/debug/workouts/raw");t.push({endpoint:"GET /users/debug/workouts/raw",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Workouts raw data:",a)}catch(a){t.push({endpoint:"GET /users/debug/workouts/raw",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching workouts raw data:",a)}try{console.log("🔍 DEBUG: Testing GET /users/workouts/history");const a=await q.get("users/workouts/history",{searchParams:{period:"month"}});t.push({endpoint:"GET /users/workouts/history",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Workout history data:",a)}catch(a){t.push({endpoint:"GET /users/workouts/history",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching workout history:",a)}try{console.log("🔍 DEBUG: Testing GET /users/protocols/workout/history");const a=await q.get("users/protocols/workout/history");t.push({endpoint:"GET /users/protocols/workout/history",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Protocol history data:",a)}catch(a){t.push({endpoint:"GET /users/protocols/workout/history",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching protocol history:",a)}return console.log("🔍 DEBUG: Endpoint testing completed. Results:",t),t}function ws(){console.log("🔍 DEBUG: Checking React Query cache status..."),console.log("🔍 DEBUG: React Query cache check needs to be implemented in component context")}function Ge(t,s){console.log(`🔍 DEBUG: ${t} - Data received:`,s),console.log(`🔍 DEBUG: ${t} - Data type:`,typeof s),console.log(`🔍 DEBUG: ${t} - Is array:`,Array.isArray(s)),Array.isArray(s)&&(console.log(`🔍 DEBUG: ${t} - Array length:`,s.length),s.length>0&&console.log(`🔍 DEBUG: ${t} - First item:`,s[0]))}async function He(){console.log("🚀 DEBUG: Starting complete workout history diagnostic...");const t=await Be(),s=t.filter(m=>m.success).length,a=t.filter(m=>!m.success).length;return console.log("📊 DEBUG: Diagnostic Summary:"),console.log(`✅ Successful endpoints: ${s}`),console.log(`❌ Failed endpoints: ${a}`),console.log("📋 Detailed results:",t),{summary:{total:t.length,successful:s,failed:a},results:t}}typeof window<"u"&&(window.debugWorkoutHistory={testEndpoints:Be,testCache:ws,testDataFlow:Ge,runComplete:He},console.log("🔧 DEBUG: Workout history debug functions available globally:"),console.log("- window.debugWorkoutHistory.testEndpoints()"),console.log("- window.debugWorkoutHistory.runComplete()"));function ks({date_start:t,date_end:s,onReuseProtocol:a,showProtocolHistory:m=!1}){const[u,x]=b.useState(null),[i,y]=b.useState(!1),{data:N,isLoading:l,error:E,refetch:D}=Te("month"),j=(N==null?void 0:N.workouts)||[];I.useEffect(()=>{N&&(console.log("🔍 WorkoutHistory: Received workout history data:",N),console.log("🔍 WorkoutHistory: Workouts array:",j),console.log("🔍 WorkoutHistory: Workouts count:",j.length),Ge("WorkoutHistory",N),j.length>0?(console.log("🔍 WorkoutHistory: First workout structure:",j[0]),console.log("🔍 WorkoutHistory: Completed workouts:",j.filter(o=>o.completed)),console.log("🔍 WorkoutHistory: In-progress workouts:",j.filter(o=>!o.completed))):(console.log("⚠️ WorkoutHistory: No workouts found in response"),console.log("🔍 WorkoutHistory: Raw API response structure:",JSON.stringify(N,null,2))))},[N,j]);const v=async()=>{console.log("🚀 WorkoutHistory: Running complete diagnostic..."),await He()};if(l)return e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-white",children:"Carregando histórico de treinos..."})})});if(E)return e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-red-400",children:"Erro ao carregar histórico de treinos"})})});const A=o=>{const r=Math.floor(o/60),c=o%60;return r>0?`${r}h ${c}min`:`${c}min`},R=o=>{x(o.id),y(!0)},k=(o,r)=>{a&&a({...o,edit:r})};return!j||j.length===0?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(se,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]}),e.jsx("button",{onClick:v,className:"px-3 py-1 bg-red-500/20 text-red-400 rounded text-sm border border-red-500/30 hover:bg-red-500/30 transition-colors",title:"Executar diagnóstico completo",children:"🔍 DEBUG"})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-600/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(J,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum treino encontrado"}),e.jsx("p",{className:"text-gray-400 text-center max-w-md",children:E?"Erro ao carregar histórico. Verifique sua conexão e tente novamente.":"Você ainda não completou nenhum treino. Comece um treino agora para ver seu histórico aqui!"}),E&&e.jsx("button",{onClick:()=>D(),className:"mt-4 px-4 py-2 bg-snapfit-green text-white rounded-lg hover:bg-snapfit-green/80 transition-colors",children:"Tentar novamente"})]})]}):m?e.jsxs(e.Fragment,{children:[e.jsx(Le,{type:"workout",onReuseProtocol:k,onViewDetails:R}),u&&e.jsx(_e,{protocolId:u,type:"workout",isOpen:i,onClose:()=>{y(!1),x(null)},onReuseProtocol:k})]}):e.jsx(e.Fragment,{children:j&&e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsx("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(se,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]})}),e.jsx("div",{className:"space-y-2 sm:space-y-4",children:j==null?void 0:j.map((o,r)=>e.jsxs("div",{className:"p-3 sm:p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx("div",{className:`p-2 rounded-full ${o.completed?"bg-snapfit-green/20 border border-snapfit-green/30":"bg-orange-400/10 border border-orange-400/30"}`,children:e.jsx(J,{className:`w-5 h-5 ${o.completed?"text-snapfit-green":"text-orange-400"}`})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-white text-sm sm:text-base",children:o.workout_name||"Treino"}),e.jsx("p",{className:"text-gray-400 text-xs sm:text-sm",children:vs(o.date)})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4 text-xs sm:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1 text-gray-300",children:[e.jsx(te,{className:"w-3 h-3 sm:w-4 sm:h-4"}),e.jsx("span",{children:A(o.duration||0)})]}),e.jsxs("div",{className:"flex items-center gap-1 text-gray-300",children:[e.jsx(ye,{className:"w-3 h-3 sm:w-4 sm:h-4"}),e.jsxs("span",{children:[o.total_calories||0," kcal"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm",children:[e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Volume"}),e.jsxs("div",{className:"font-semibold text-white",children:[o.total_weight||0,"kg"]})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Duração"}),e.jsx("div",{className:"font-semibold text-white",children:A(o.duration||0)})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Calorias"}),e.jsx("div",{className:"font-semibold text-white",children:o.total_calories||0})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Status"}),e.jsx("div",{className:`font-semibold ${o.completed?"text-snapfit-green":"text-orange-400"}`,children:o.completed?"Concluído":"Em andamento"})]})]})]},o.session_id||r))})]})})}function Ss({onReuseProtocol:t,defaultTab:s="protocols",hasActiveProtocol:a=!1}){const[m,u]=b.useState(s),[x,i]=b.useState(null),[y,N]=b.useState(!1),l=j=>{console.log("🔍 WorkoutHistoryTabs: Protocol selected for viewing:",j),i(j.id),N(!0)},E=(j,v)=>{t&&t({...j,edit:v})},D=[{id:"sessions",label:"Treinos Realizados",icon:J,description:"Histórico das suas sessões de treino concluídas"},{id:"protocols",label:"Protocolos Anteriores",icon:de,description:"Visualize, reutilize e gerencie seus protocolos anteriores"}];return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg border border-snapfit-green/20",children:e.jsxs("div",{className:"p-4 sm:p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(se,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-2",children:D.map(j=>{const v=j.icon,A=m===j.id;return e.jsxs("button",{onClick:()=>u(j.id),className:`flex items-center gap-3 p-3 sm:p-4 rounded-lg transition-all duration-200 text-left flex-1 ${A?"bg-snapfit-green/20 border border-snapfit-green/30 text-white":"bg-snapfit-dark-gray border border-snapfit-green/10 text-gray-300 hover:bg-snapfit-green/10 hover:text-white"}`,children:[e.jsx("div",{className:`p-2 rounded-lg ${A?"bg-snapfit-green/30":"bg-snapfit-green/10"}`,children:e.jsx(v,{className:`w-5 h-5 ${A?"text-snapfit-green":"text-gray-400"}`})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:j.label}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:j.description})]})]},j.id)})})]})}),e.jsxs("div",{className:"min-h-[400px]",children:[m==="sessions"&&e.jsx("div",{className:"animate-fade-in",children:e.jsx(ks,{showProtocolHistory:!1,onReuseProtocol:t})}),m==="protocols"&&e.jsx("div",{className:"animate-fade-in",children:e.jsx(Le,{type:"workout",onReuseProtocol:E,onViewDetails:l})})]}),x&&e.jsx(_e,{protocolId:x,type:"workout",isOpen:y,onClose:()=>{console.log("🔍 WorkoutHistoryTabs: Modal close triggered"),N(!1),i(null)},onReuseProtocol:E})]})}const As=`
  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }
`;if(typeof document<"u"){const t=document.createElement("style");t.textContent=As,document.head.appendChild(t)}const Cs=({exercise:t})=>{const[s,a]=b.useState(!0);return e.jsx(e.Fragment,{children:e.jsx("div",{className:"pb-5",children:e.jsxs("div",{className:"p-3 sm:p-4 rounded-lg bg-snapfit-dark-gray border border-snapfit-gray/30",children:[e.jsxs("button",{onClick:()=>a(!s),className:"flex items-center justify-between w-full gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[e.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center bg-snapfit-dark-gray text-gray-400 border border-snapfit-gray/30",children:e.jsx(Ve,{className:`w-3 h-3 transition-transform ${s?"":"rotate-180"}`})}),e.jsx("h3",{className:"text-sm font-medium text-white truncate max-w-[180px]",children:t.name})]}),e.jsxs("div",{className:"text-xs text-gray-400 flex-shrink-0 bg-snapfit-dark-gray px-2 py-1 rounded-full border border-snapfit-gray/30",children:[t.sets,"x",t.reps," • ",t.rest_seconds,"s"]})]}),!s&&e.jsxs("div",{className:"mt-4 space-y-4 animate-fade-in",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-2 text-center bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Séries"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.sets})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Reps"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.reps})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"RPE"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.rpe})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Descanso"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[t.rest_seconds,"s"]})]})]}),t.notes&&e.jsxs("div",{className:"flex items-start gap-2 p-2 sm:p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx(Oe,{className:"w-4 h-4 text-snapfit-green mt-0.5"}),e.jsx("p",{className:"text-xs text-gray-300",children:t.notes})]}),t.gif_url&&e.jsx("div",{className:"rounded-lg overflow-hidden border border-snapfit-green/20",children:e.jsx("img",{src:t.gif_url,alt:t.name,className:"w-full h-48 object-cover"})})]})]})})})};function Ps({protocolId:t,protocolName:s,startDate:a,splitInfo:m,frequency:u,objective:x,completedWorkouts:i,notes:y,workouts:N,selectedWorkout:l,onSelectWorkout:E,onGenerateNewProtocol:D,onEditNotes:j,workoutsDb:v,onDeleteProtocol:A}){var h;je();const[R,k]=I.useState(!1),o=g=>{switch(g){case"hypertrophy":return"Hipertrofia";case"weight-loss":return"Emagrecimento";case"maintenance":return"Manutenção";case"strength":return"Força";default:return g}},[r,c]=I.useState(0);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(se,{className:"w-5 h-5"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h2",{className:"text-lg font-semibold text-white",children:s||"Protocolo de Treino"}),e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold",children:"Protocolo Atual"})]}),e.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:["Início: ",new Date(a).toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ke,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Divisão"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:m})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(se,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Frequência"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:u})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(oe,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Objetivo"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:o(x)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ke,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:i})]})]}),y&&e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(Fe,{className:"w-4 h-4"})}),e.jsx("h3",{className:"text-sm sm:text-base font-medium text-white",children:"Observações do Coach"})]}),e.jsx("button",{onClick:j,className:"text-sm text-snapfit-green hover:text-snapfit-green/80 hidden",children:"Editar"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-400 whitespace-pre-wrap bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:y})]}),e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-base font-medium text-white",children:"Treinos"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{const g=v.length;r==0?c(g-1):c(r-1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(Me,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>{const g=v.length;r==g-1?c(0):c(r+1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(ce,{className:"w-4 h-4"})})]})]}),e.jsx("div",{className:"flex gap-2 overflow-x-auto pb-4 scrollbar-hide",children:v.map((g,T)=>e.jsx("button",{onClick:()=>{c(T)},className:`flex-none px-3 py-1.5 text-xs sm:text-sm rounded-full transition-colors whitespace-nowrap ${r===T?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-gray/30"}`,children:g==null?void 0:g.name},T))})]})]}),e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold mb-2",children:"Treino"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:(h=v[r])==null?void 0:h.name}),e.jsxs("span",{className:"text-sm text-gray-400",children:[v[r].exercises.length," ",v[r].exercises.length>1?"exercícios":"exercício"]})]})}),e.jsx("div",{className:"space-y-4",children:v[r].exercises.map((g,T)=>e.jsx(Cs,{exercise:g},g.id))})]}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("button",{className:"flex justify-between items-center gap-1 text-sm text-gray-400 hover:text-snapfit-green transition-colors p-2 rounded-full bg-snapfit-dark-gray border border-snapfit-gray/30",onClick:A,children:[e.jsx($e,{className:"w-4 h-4"})," ",e.jsx("span",{children:"Remover Protocolo"})]})}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsxs("button",{onClick:()=>{console.log("🎯 UnifiedWorkoutCard: Iniciar Treino button clicked"),console.log("🔍 UnifiedWorkoutCard: Opening WorkoutSelectionModal"),console.log("🔍 UnifiedWorkoutCard: Using CONSISTENT workflow with Add (+) button"),k(!0)},className:"flex items-center justify-center gap-2 px-6 py-3 w-full sm:w-auto bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 font-bold","data-testid":"unified-workout-card-start-button",title:"Iniciar Treino - Abre seleção de treinos",children:[e.jsx("span",{children:"Iniciar Treino"}),e.jsx(ce,{className:"w-5 h-5"})]})}),R&&e.jsx(Qe,{onClose:()=>{console.log("🔍 UnifiedWorkoutCard: Closing WorkoutSelectionModal"),k(!1)}})]})}function Es({onGenerateAI:t,onCreateManual:s,onImportFromCoach:a,onReadProtocol:m,onClose:u}){return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Novo Protocolo de Treino"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{onClick:t,className:`\r
            w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10`,children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(X,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Gerar com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gerar protocolo personalizado baseado no seu perfil"})]})]}),e.jsxs("button",{onClick:s,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Fe,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Criar Manualmente"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Monte seu próprio protocolo selecionando exercícios"})]})]}),m&&e.jsxs("button",{onClick:m,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Ke,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Ler seu protocolo atual com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Faça upload de um PDF ou imagem do seu protocolo atual"})]})]}),e.jsxs("button",{onClick:a,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(ve,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Importar do Coach"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Importar protocolo preparado pelo seu coach"})]})]})]}),e.jsx("button",{onClick:u,className:"w-full mt-6 px-4 py-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"})]})})}async function Ds(){const t=Je();if(!t.isAuthenticated)throw new Error("User must be authenticated to fetch protocols");try{const s=await fetch("https://api.mysnapfit.com.br/protocols/coach",{headers:{Authorization:`Bearer ${t.token}`}});if(!s.ok)throw new Error("Failed to fetch coach protocols");return s.json()}catch(s){throw console.error("Error fetching coach protocols:",s),s}}function Ts({onImport:t,onCancel:s}){const[a,m]=I.useState([]),[u,x]=I.useState(!0),[i,y]=I.useState(null);return I.useEffect(()=>{async function N(){try{const l=await Ds();m(l)}catch(l){y(l instanceof Error?l.message:"Erro ao carregar protocolos")}finally{x(!1)}}N()},[]),u?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx(Ze,{className:"w-8 h-8 text-indigo-600 animate-spin"})}):i?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"text-red-600 mb-4",children:i}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ve,{className:"w-6 h-6 text-indigo-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Importar do Coach"})]}),a.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"Nenhum protocolo disponível para importação."}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-4",children:[a.map(N=>{var l,E,D;return e.jsxs("button",{onClick:()=>t(N),className:"w-full flex items-center justify-between p-4 bg-white rounded-lg hover:bg-gray-50 transition-colors text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-800",children:N.name}),e.jsxs("div",{className:"flex items-center gap-4 mt-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:(l=N.coach)==null?void 0:l.photo,alt:(E=N.coach)==null?void 0:E.name,className:"w-6 h-6 rounded-full"}),e.jsx("span",{className:"text-sm text-gray-600",children:(D=N.coach)==null?void 0:D.name})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(se,{className:"w-4 h-4"}),e.jsx("span",{children:new Date(N.createdAt).toLocaleDateString()})]})]})]}),e.jsx(ve,{className:"w-5 h-5 text-gray-400"})]},N.id)}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"})})]})]})}function pe({data:t,title:s,subtitle:a,height:m=200,showLegend:u=!0,type:x="bar",className:i="",animate:y=!0}){if(!t||!Array.isArray(t)||t.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${i}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Nenhum dado disponível"})]});const N=t.map(h=>h.value).filter(h=>typeof h=="number"&&!isNaN(h)&&isFinite(h));if(N.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${i}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Dados inválidos"})]});const l=Math.max(...N),E=Math.min(...N),D=N.reduce((h,g)=>h+g,0)/N.length,j=l-E;let v,A;if(j===0)l===0?(A=0,v=1):(A=Math.max(0,l*.8),v=l*1.2);else if(x==="bar")if(j<l*.2){const h=E*.8;if(A=Math.max(0,h),v=l*1.1,v-A<l*.3){const g=(l+E)/2,T=Math.max(l*.15,j*2);A=Math.max(0,g-T),v=g+T}}else{const h=Math.max(j*.05,l*.05);v=l+h,A=Math.max(0,E-h)}else{const h=j>0?j*.1:l*.1;v=l+h,A=Math.max(0,E-h)}const R=["#B9FF43","#66B100","#1A3201","#4CAF50","#8BC34A"],k=b.useRef(t.map(()=>0)),o=b.useRef(0);b.useEffect(()=>{k.current=t.map(()=>0)},[t]),b.useEffect(()=>{if(y&&x==="bar"){const h=()=>{let g=!1;const T=k.current.map((p,d)=>{if(!t[d]||typeof t[d].value!="number"||!isFinite(t[d].value))return p;const n=v-A,M=(n>0?(t[d].value-A)/n:0)*100;if(!isFinite(M))return p;const _=M-p;return Math.abs(_)>.5?(g=!0,p+_*.1):M});k.current=T,g&&(o.current=requestAnimationFrame(h))};return o.current=requestAnimationFrame(h),()=>{cancelAnimationFrame(o.current)}}},[t,v,A,y,x]);const r=()=>e.jsx("div",{className:"flex items-end h-full gap-1 sm:gap-2 px-1",children:t.map((h,g)=>{const T=typeof h.value=="number"&&!isNaN(h.value)&&isFinite(h.value)?h.value:0,p=v-A,d=p>0?(T-A)/p:0,n=y?k.current[g]??d*100:d*100,C=h.color||R[g%R.length];return e.jsxs("div",{className:"flex flex-col items-center flex-1",children:[e.jsx("div",{className:"relative w-full h-full flex flex-col justify-end",children:e.jsxs("div",{className:"w-full rounded-lg relative group overflow-hidden",style:{height:`${n}%`,minHeight:"4px",boxShadow:`0 0 10px ${C}40`},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t",style:{backgroundImage:`linear-gradient(to top, ${C}, ${C}80)`}}),e.jsx("div",{className:"absolute -top-8 left-1/2 -translate-x-1/2 bg-snapfit-dark-gray text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-snapfit-green/30",children:h.value})]})}),e.jsx("span",{className:"text-xs mt-2 text-gray-400 truncate max-w-full text-center",children:h.label})]},g)})}),c=()=>{const h=t.map((p,d)=>{const n=typeof p.value=="number"&&!isNaN(p.value)&&isFinite(p.value)?p.value:0,C=t.length>1?d/(t.length-1)*100:50,M=v-A,f=100-(M>0?(n-A)/M:0)*100;return{x:isFinite(C)?C:50,y:isFinite(f)?f:50,...p,value:n}});let g="",T="";return h.forEach((p,d)=>{if(d===0)g+=`M${p.x},${p.y}`,T+=`M${p.x},100 L${p.x},${p.y}`;else{const n=h[d-1],C=n.x+(p.x-n.x)/2,M=n.x+(p.x-n.x)/2;g+=` C${C},${n.y} ${M},${p.y} ${p.x},${p.y}`,T+=` L${p.x},${p.y}`}}),x==="area"&&(T+=` L${h[h.length-1].x},100 L${h[0].x},100 Z`),e.jsxs("div",{className:"h-full w-full px-2 pt-4 pb-6 relative",children:[e.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 100 100",preserveAspectRatio:"none",children:[e.jsx("line",{x1:"0",y1:"0",x2:"100",y2:"0",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"25",x2:"100",y2:"25",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"50",x2:"100",y2:"50",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"75",x2:"100",y2:"75",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"100",x2:"100",y2:"100",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5"}),x==="area"&&e.jsx("path",{d:T,fill:"url(#areaGradient)",opacity:"0.2"}),e.jsx("path",{d:g,fill:"none",stroke:"#B9FF43",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),h.map((p,d)=>e.jsxs("g",{className:"group",children:[e.jsx("circle",{cx:p.x,cy:p.y,r:"4",fill:"transparent",stroke:"#B9FF4330",strokeWidth:"4",className:"transition-all duration-300 group-hover:stroke-opacity-50 group-hover:r-6"}),e.jsx("circle",{cx:p.x,cy:p.y,r:"3",fill:"#B9FF43",stroke:"#000",strokeWidth:"1",className:"transition-all duration-300 group-hover:r-4"}),e.jsx("circle",{cx:p.x,cy:p.y,r:"10",fill:"transparent",className:"cursor-pointer"}),e.jsxs("g",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx("rect",{x:p.x-25,y:p.y-30,width:"50",height:"22",rx:"11",fill:"#1E1E1E",stroke:"#B9FF4330",strokeWidth:"1"}),e.jsx("text",{x:p.x,y:p.y-16,textAnchor:"middle",fill:"#B9FF43",fontSize:"10",fontWeight:"bold",children:p.value})]})]},d)),e.jsx("defs",{children:e.jsxs("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:"#B9FF43",stopOpacity:"0.8"}),e.jsx("stop",{offset:"100%",stopColor:"#B9FF43",stopOpacity:"0.1"})]})})]}),e.jsx("div",{className:"flex justify-between absolute bottom-0 left-2 right-2",children:t.map((p,d)=>e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[40px] text-center",children:p.label},d))})]})};return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl p-4 sm:p-5 ${i}`,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:s}),a&&e.jsx("p",{className:"text-gray-400 text-sm mt-1",children:a})]}),e.jsx("div",{style:{height:`${m}px`},className:"mt-2",children:x==="bar"?r():c()}),u&&e.jsx("div",{className:"mt-3 pt-3 border-t border-snapfit-gray/30",children:x==="line"||x==="bar"&&t.length>3?e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsxs("span",{className:"text-gray-300",children:["Máx: ",l.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-400"}),e.jsxs("span",{className:"text-gray-300",children:["Mín: ",E.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-snapfit-green"}),e.jsxs("span",{className:"text-gray-300",children:["Média: ",D.toFixed(0)]})]}),x==="bar"&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-yellow-400"}),e.jsxs("span",{className:"text-gray-300",children:["Total: ",t.reduce((h,g)=>h+g.value,0).toFixed(0)]})]})]}):e.jsx("div",{className:"flex flex-wrap gap-3",children:t.map((h,g)=>{const T=h.color||R[g%R.length];return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:T}}),e.jsxs("span",{className:"text-xs text-gray-400",children:[h.label,": ",h.value]})]},g)})})})]})}const ze=()=>{const[t,s]=b.useState([]),[a,m]=b.useState([]),[u,x]=b.useState(!1),[i,y]=b.useState(!1),[N,l]=b.useState(null),E={id:"analysis_1",videoId:"video_1",exerciseName:"Agachamento",overallScore:78,analysisDate:new Date().toISOString(),postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75,improvementPoints:[{id:"imp_1",category:"posture",severity:"medium",description:"Joelhos tendendo a cair para dentro durante a descida",bodyPart:"Joelhos",timeInVideo:15},{id:"imp_2",category:"range_of_motion",severity:"low",description:"Pode descer um pouco mais para ativar melhor os glúteos",bodyPart:"Quadris",timeInVideo:18}],injuryRisks:[{id:"risk_1",riskLevel:"medium",bodyPart:"Joelhos",description:"Valgo dinâmico pode causar sobrecarga nos ligamentos",preventionTips:["Fortalecer glúteo médio","Melhorar mobilidade de tornozelo","Praticar agachamento com elástico"],relatedMovements:["Afundo","Step-up","Agachamento búlgaro"]}],correctionSuggestions:[{id:"corr_1",step:1,title:"Posicionamento dos pés",description:"Mantenha os pés ligeiramente mais afastados que a largura dos ombros",focusArea:"Base de apoio",difficulty:"easy"},{id:"corr_2",step:2,title:"Ativação do core",description:"Contraia o abdômen antes de iniciar o movimento",focusArea:"Estabilização",difficulty:"medium"}],preparatoryExercises:[{id:"prep_1",name:"Agachamento na parede",description:"Agachamento com apoio das costas na parede",sets:3,reps:"10-15",purpose:"Aprender o padrão de movimento correto"},{id:"prep_2",name:"Ponte de glúteo",description:"Fortalecimento dos glúteos em decúbito dorsal",sets:3,reps:"15-20",purpose:"Fortalecer glúteos para melhor estabilização"}]},D=async(k,o,r)=>{try{x(!0),l(null),await new Promise(h=>setTimeout(h,2e3));const c={id:`video_${Date.now()}`,exerciseName:o,angle:r,videoFile:k,uploadDate:new Date().toISOString(),duration:30,analysisStatus:"pending"};return s(h=>[c,...h]),await j(c.id),c}catch(c){throw l(c instanceof Error?c.message:"Erro no upload"),c}finally{x(!1)}},j=async k=>{try{y(!0),l(null),s(r=>r.map(c=>c.id===k?{...c,analysisStatus:"analyzing"}:c)),await new Promise(r=>setTimeout(r,5e3));const o={...E,id:`analysis_${Date.now()}`,videoId:k,analysisDate:new Date().toISOString()};return m(r=>[o,...r]),s(r=>r.map(c=>c.id===k?{...c,analysisStatus:"completed"}:c)),o}catch(o){throw l(o instanceof Error?o.message:"Erro na análise"),s(r=>r.map(c=>c.id===k?{...c,analysisStatus:"error"}:c)),o}finally{y(!1)}};return{videos:t,analyses:a,isUploading:u,isAnalyzing:i,error:N,uploadVideo:D,analyzeVideo:j,getEvolutionData:k=>({exerciseName:k,timeline:[{date:"2024-01-01",overallScore:65,postureScore:70,rangeOfMotionScore:60,stabilityScore:65,speedScore:65},{date:"2024-01-15",overallScore:72,postureScore:75,rangeOfMotionScore:68,stabilityScore:72,speedScore:73},{date:"2024-02-01",overallScore:78,postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75}],trend:"improving",targetReached:!1,nextGoal:"Atingir 85+ em todos os aspectos técnicos"}),getEnvironmentAnalysis:()=>({id:"env_1",spaceType:"home",availableSpace:"medium",equipment:["Halteres","Colchonete","Elásticos"],limitations:["Teto baixo","Vizinhos embaixo"],suggestedExercises:[{id:"ex_1",name:"Agachamento com halteres",category:"strength",difficulty:"intermediate",equipment:["Halteres"],spaceRequired:"minimal",duration:15,description:"Agachamento segurando halteres para aumentar a resistência",benefits:["Fortalece pernas","Melhora equilíbrio","Baixo impacto"]},{id:"ex_2",name:"Prancha com variações",category:"strength",difficulty:"beginner",equipment:["Colchonete"],spaceRequired:"minimal",duration:10,description:"Exercício isométrico para core",benefits:["Fortalece core","Melhora postura","Silencioso"]}],adaptations:["Use tênis com amortecimento para reduzir ruído","Prefira exercícios isométricos após 22h","Coloque colchonete extra para absorver impacto"]}),getRecoveryAnalysis:()=>({id:"rec_1",date:new Date().toISOString(),fatigueLevel:35,muscleGroups:[{name:"Pernas",fatigueLevel:60,recoveryTime:24,status:"recovering"},{name:"Peito",fatigueLevel:20,recoveryTime:8,status:"recovered"},{name:"Costas",fatigueLevel:40,recoveryTime:16,status:"recovering"}],sleepQuality:75,stressLevel:30,recommendations:[{id:"rec_rec_1",type:"active_recovery",priority:"medium",title:"Caminhada leve",description:"Faça uma caminhada de 20-30 minutos para acelerar a recuperação",duration:25},{id:"rec_rec_2",type:"stretching",priority:"high",title:"Alongamento de pernas",description:"Foque em quadríceps, isquiotibiais e panturrilhas",duration:15,instructions:["Alongue cada músculo por 30 segundos","Respire profundamente durante o alongamento","Não force além do confortável"]}]})}};function Fs(){const{videos:t,analyses:s,isUploading:a,error:m,uploadVideo:u}=ze(),[x,i]=b.useState(""),[y,N]=b.useState("frontal"),[l,E]=b.useState(!1),[D,j]=b.useState(null),v=async r=>{if(!x){alert("Por favor, selecione um exercício primeiro");return}if(!r.type.includes("video")){alert("Por favor, envie apenas arquivos de vídeo");return}try{await u(r,x,y)}catch(c){console.error("Erro no upload:",c)}},A=r=>{r.preventDefault(),E(!1);const c=Array.from(r.dataTransfer.files);c.length>0&&v(c[0])},R=r=>{const c=r.target.files;c&&c.length>0&&v(c[0])},k=r=>{switch(r){case"pending":return e.jsx(te,{className:"w-4 h-4 text-yellow-400"});case"analyzing":return e.jsx("div",{className:"w-4 h-4 border-2 border-snapfit-green border-t-transparent rounded-full animate-spin"});case"completed":return e.jsx(le,{className:"w-4 h-4 text-green-400"});case"error":return e.jsx(Y,{className:"w-4 h-4 text-red-400"});default:return null}},o=r=>r>=80?"text-green-400":r>=60?"text-yellow-400":"text-red-400";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(fe,{className:"w-5 h-5 text-snapfit-green"}),"Análise de Movimento"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Exercício"}),e.jsxs("select",{value:x,onChange:r=>i(r.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"",children:"Selecione um exercício"}),Xe.map(r=>e.jsx("option",{value:r,children:r},r))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Ângulo da Filmagem"}),e.jsxs("select",{value:y,onChange:r=>N(r.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"frontal",children:"Frontal"}),e.jsx("option",{value:"lateral",children:"Lateral"}),e.jsx("option",{value:"posterior",children:"Posterior"})]})]})]}),e.jsx("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${l?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-snapfit-green/50"}`,onDrop:A,onDragOver:r=>r.preventDefault(),onDragEnter:()=>E(!0),onDragLeave:()=>E(!1),children:a?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto animate-pulse",children:e.jsx(Se,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Fazendo upload..."}),e.jsx("div",{className:"text-sm text-gray-400",children:"Aguarde um momento"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(fe,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Arraste seu vídeo aqui ou clique para selecionar"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suportamos MP4, MOV, AVI (máx. 100MB)"})]}),e.jsx("input",{type:"file",accept:"video/*",onChange:R,className:"hidden",id:"video-upload"}),e.jsxs("label",{htmlFor:"video-upload",className:"inline-flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors cursor-pointer",children:[e.jsx(Se,{className:"w-4 h-4"}),"Selecionar Vídeo"]})]})}),e.jsxs("div",{className:"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"💡 Dicas para melhor análise:"}),e.jsxs("ul",{className:"text-sm text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Filme em boa iluminação"}),e.jsx("li",{children:"• Mantenha o corpo inteiro no quadro"}),e.jsx("li",{children:"• Grave pelo menos 3-5 repetições"}),e.jsx("li",{children:"• Use roupas que permitam ver o movimento"})]})]})]}),s.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Análises Recentes"]}),e.jsx("div",{className:"space-y-4",children:s.slice(0,3).map(r=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:r.exerciseName}),e.jsx("div",{className:"text-sm text-gray-400",children:new Date(r.analysisDate).toLocaleDateString("pt-BR")})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`text-lg font-bold ${o(r.overallScore)}`,children:[r.overallScore,"/100"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Score Geral"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(r.postureScore)}`,children:r.postureScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Postura"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(r.rangeOfMotionScore)}`,children:r.rangeOfMotionScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Amplitude"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(r.stabilityScore)}`,children:r.stabilityScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Estabilidade"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${o(r.speedScore)}`,children:r.speedScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Velocidade"})]})]}),e.jsx("button",{onClick:()=>j(D===r.id?null:r.id),className:"w-full px-4 py-2 text-sm text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:D===r.id?"Ocultar Detalhes":"Ver Detalhes"}),D===r.id&&e.jsxs("div",{className:"mt-4 space-y-4 border-t border-gray-600 pt-4",children:[r.improvementPoints.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Pontos de Melhoria:"}),e.jsx("div",{className:"space-y-2",children:r.improvementPoints.map(c=>e.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[e.jsx("span",{className:`mt-1 ${c.severity==="high"?"text-red-400":c.severity==="medium"?"text-orange-400":"text-yellow-400"}`,children:"•"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-300",children:c.description}),e.jsxs("span",{className:"text-gray-500 ml-2",children:["(",c.bodyPart,")"]})]})]},c.id))})]}),r.correctionSuggestions.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Sugestões de Correção:"}),e.jsx("div",{className:"space-y-2",children:r.correctionSuggestions.map(c=>e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[c.step,". ",c.title]}),e.jsx("div",{className:"text-gray-300 ml-4",children:c.description})]},c.id))})]})]})]},r.id))})]}),t.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Vídeos em Processamento"}),e.jsx("div",{className:"space-y-3",children:t.filter(r=>r.analysisStatus!=="completed").map(r=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[k(r.analysisStatus),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:r.exerciseName}),e.jsxs("div",{className:"text-sm text-gray-400",children:["Ângulo: ",r.angle," • ",new Date(r.uploadDate).toLocaleTimeString("pt-BR")]})]})]}),e.jsx("div",{className:"text-sm text-gray-400",children:r.analysisStatus==="analyzing"?"Analisando...":r.analysisStatus==="pending"?"Na fila":"Erro"})]},r.id))})]}),m&&e.jsx("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro:"}),e.jsx("span",{children:m})]})})]})}function Ms({isOpen:t,onClose:s,workout:a}){var z,G;const[m,u]=b.useState(0),[x,i]=b.useState(!1),[y,N]=b.useState(60),[l,E]=b.useState(0),[D,j]=b.useState(!1),[v,A]=b.useState(null),[R,k]=b.useState(0),[o,r]=b.useState(!1),[c,h]=b.useState(!1),[g,T]=b.useState(0),[p,d]=b.useState({}),n=a==null?void 0:a.exercises[m];b.useEffect(()=>{if(a!=null&&a.exercises&&Object.keys(p).length===0){const P={};a.exercises.forEach(S=>{P[S.id]={sets:Array.from({length:S.sets||4},()=>({weight:0,reps:0,completed:!1}))}}),d(P)}},[a,p]),b.useEffect(()=>{t&&!v&&A(new Date)},[t]),b.useEffect(()=>{let P;return D&&l>0&&(P=setInterval(()=>{E(S=>S<=1?(j(!1),i(!1),0):S-1)},1e3)),()=>clearInterval(P)},[D,l]);const C=(P,S,F,$)=>{d(H=>({...H,[P]:{...H[P],sets:H[P].sets.map((W,K)=>K===S?{...W,[F]:typeof $=="string"?parseFloat($)||0:$}:W)}}))},M=(P,S)=>{d(F=>({...F,[P]:{...F[P],sets:F[P].sets.map(($,H)=>H===S?{...$,completed:!0}:$)}})),k(F=>F+5),T(y),r(!0),h(!0)},_=()=>n?p[n.id]:null,f=P=>{const S=Math.floor(P/60),F=P%60;return`${S}:${F.toString().padStart(2,"0")}`},B=()=>{if(!v)return"00:00";const S=Math.floor((new Date().getTime()-v.getTime())/1e3);return f(S)},Q=()=>m>=(a==null?void 0:a.exercises.length),O=()=>a!=null&&a.exercises.length?Math.round(m/a.exercises.length*100):0;return!t||!a?null:e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:a.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-400 mt-1",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(te,{className:"w-4 h-4"}),B()]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(oe,{className:"w-4 h-4"}),R," kcal"]})]})]}),e.jsx("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:e.jsx($e,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Progresso do Treino"}),e.jsxs("span",{className:"text-sm text-snapfit-green font-medium",children:[O(),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-snapfit-green h-2 rounded-full transition-all duration-300",style:{width:`${O()}%`}})})]}),Q()?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(xe,{className:"w-8 h-8 text-snapfit-green"})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Treino Concluído!"}),e.jsx("p",{className:"text-gray-400 mb-4",children:"Parabéns! Você completou seu treino adaptado."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:B()}),e.jsx("div",{className:"text-sm text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-orange-400",children:R}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]}),e.jsx("button",{onClick:s,className:"w-full px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:"Finalizar Treino"})]}):o?e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx(te,{className:"text-white text-lg"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-blue-300",children:"Descanso entre séries"}),e.jsxs("div",{className:"text-xl font-bold text-blue-400",children:[Math.floor(g/60).toString().padStart(2,"0"),":",(g%60).toString().padStart(2,"0")]})]})]}),e.jsx("button",{onClick:()=>{r(!1),h(!1),T(0)},className:"px-4 py-2 bg-blue-500 text-white rounded-xl text-sm font-medium hover:bg-blue-600 transition-all hover:scale-105",children:"Pular"})]})})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-xl p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("button",{onClick:()=>u(P=>Math.max(0,P-1)),disabled:m===0,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(Me,{className:"w-5 h-5"})}),e.jsxs("div",{className:"text-center flex-1",children:[e.jsxs("div",{className:"text-xs text-gray-400 mb-1",children:["Exercício ",m+1," de ",((z=a==null?void 0:a.exercises)==null?void 0:z.length)||0]}),e.jsx("h2",{className:"text-lg font-bold text-white",children:n==null?void 0:n.name})]}),e.jsx("button",{onClick:()=>u(P=>{var S;return Math.min((((S=a==null?void 0:a.exercises)==null?void 0:S.length)||1)-1,P+1)}),disabled:m>=(((G=a==null?void 0:a.exercises)==null?void 0:G.length)||1)-1,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(ce,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-snapfit-green to-green-400 transition-all duration-500 ease-out rounded-full shadow-lg shadow-snapfit-green/30",style:{width:`${(()=>{var F;const P=Object.values(p).filter($=>$.sets.some(H=>H.completed)).length,S=((F=a==null?void 0:a.exercises)==null?void 0:F.length)||1;return P/S*100})()}%`}})})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400 bg-gray-800/50 rounded-lg px-4 py-2",children:[e.jsxs("span",{children:[(n==null?void 0:n.sets)||4," séries"]}),e.jsxs("span",{children:[(n==null?void 0:n.reps)||"8"," reps"]}),e.jsx("span",{className:"text-snapfit-green",children:n==null?void 0:n.muscle}),e.jsxs("span",{children:[y,"s descanso"]})]}),a&&a.exercises&&a.exercises[m]&&(()=>{const P=_();return P?e.jsx("div",{className:"space-y-3",children:P.sets.map((S,F)=>e.jsxs("div",{className:`rounded-xl p-4 border transition-all duration-300 ${S.completed?"bg-snapfit-green/10 border-snapfit-green/30 shadow-lg shadow-snapfit-green/10":"bg-gray-800/50 border-gray-700 hover:border-gray-600"}`,children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${S.completed?"bg-snapfit-green text-black":"bg-gray-700 text-gray-300"}`,children:F+1}),S.completed&&e.jsxs("div",{className:"flex items-center gap-1 text-snapfit-green",children:[e.jsx(xe,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:"Concluída"})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1 text-center",children:"Reps"}),e.jsx("input",{type:"number",inputMode:"numeric",pattern:"[0-9]*",value:S.reps||"",onChange:$=>C(n.id,F,"reps",$.target.value),disabled:S.completed,className:"w-full text-center text-xl font-bold bg-transparent border-none outline-none text-white disabled:opacity-50 focus:text-snapfit-green transition-colors",placeholder:(n==null?void 0:n.reps)||"0",min:"0"})]}),!S.completed&&e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("button",{onClick:()=>C(n.id,F,"reps",(S.reps||0)+1),className:"w-6 h-6 bg-gray-600 hover:bg-snapfit-green hover:text-black rounded text-xs font-bold transition-all",children:"+"}),e.jsx("button",{onClick:()=>C(n.id,F,"reps",Math.max(0,(S.reps||0)-1)),className:"w-6 h-6 bg-gray-600 hover:bg-red-500 rounded text-xs font-bold transition-all",children:"-"})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>M(n.id,F),disabled:S.completed||!S.reps,className:`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${S.completed?"bg-snapfit-green text-black shadow-lg shadow-snapfit-green/30":S.reps?"bg-gray-700 text-gray-300 hover:bg-snapfit-green hover:text-black hover:shadow-lg hover:shadow-snapfit-green/30 hover:scale-105":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:e.jsx(xe,{className:"w-5 h-5"})})})]})]},F))}):null})(),(()=>{const P=(a==null?void 0:a.exercises.filter(S=>{const F=p[S.id];return!(F!=null&&F.sets.some($=>$.completed))}))||[];if(P.length>0){const F=(()=>{for(let $=m+1;$<(a==null?void 0:a.exercises.length);$++){const H=a.exercises[$],W=p[H.id];if(!(W!=null&&W.sets.some(K=>K.completed)))return $}for(let $=0;$<m;$++){const H=a.exercises[$],W=p[H.id];if(!(W!=null&&W.sets.some(K=>K.completed)))return $}return null})();if(F!==null)return e.jsxs("button",{onClick:()=>u(F),className:"w-full py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-medium transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx("span",{children:"Próximo Exercício"}),e.jsx(ce,{className:"w-5 h-5"})]})}return P.length===0?e.jsxs("button",{onClick:s,className:"w-full py-4 bg-snapfit-green hover:bg-snapfit-green/90 text-black rounded-xl font-bold transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx(Ye,{className:"w-5 h-5"}),e.jsx("span",{children:"Finalizar Treino"})]}):null})()]})]})})}function $s(){const{getEnvironmentAnalysis:t}=ze(),[s,a]=b.useState("home"),[m,u]=b.useState("medium"),[x,i]=b.useState(["Nenhum (Peso Corporal)"]),[y,N]=b.useState([]),[l,E]=b.useState(""),[D,j]=b.useState(30),[v,A]=b.useState(!1),[R,k]=b.useState(!1),[o,r]=b.useState(null),c=t(),h=[{id:"workout_1",name:"Push A - Peito e Tríceps",muscleGroups:["Peito","Tríceps","Ombros"],exercises:[{name:"Supino Reto",sets:4,reps:"8-10",muscle:"Peito"},{name:"Supino Inclinado",sets:3,reps:"10-12",muscle:"Peito"},{name:"Desenvolvimento",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Tríceps Pulley",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Tríceps Francês",sets:3,reps:"10-12",muscle:"Tríceps"}]},{id:"workout_2",name:"Pull A - Costas e Bíceps",muscleGroups:["Costas","Bíceps"],exercises:[{name:"Puxada Frontal",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Curvada",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Unilateral",sets:3,reps:"10-12",muscle:"Costas"},{name:"Pullover",sets:3,reps:"12-15",muscle:"Costas"},{name:"Rosca Direta",sets:4,reps:"10-12",muscle:"Bíceps"},{name:"Rosca Martelo",sets:3,reps:"10-12",muscle:"Bíceps"}]},{id:"workout_3",name:"Legs A - Quadríceps e Glúteos",muscleGroups:["Quadríceps","Glúteos","Panturrilhas"],exercises:[{name:"Agachamento Livre",sets:4,reps:"8-10",muscle:"Quadríceps"},{name:"Leg Press 45°",sets:4,reps:"12-15",muscle:"Quadríceps"},{name:"Agachamento Búlgaro",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Extensão de Pernas",sets:3,reps:"12-15",muscle:"Quadríceps"},{name:"Afundo",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Panturrilha em Pé",sets:4,reps:"15-20",muscle:"Panturrilhas"}]},{id:"workout_4",name:"Push B - Ombros e Tríceps",muscleGroups:["Ombros","Tríceps"],exercises:[{name:"Desenvolvimento com Halteres",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:4,reps:"12-15",muscle:"Ombros"},{name:"Elevação Posterior",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Desenvolvimento Arnold",sets:3,reps:"10-12",muscle:"Ombros"},{name:"Tríceps Testa",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Mergulho",sets:3,reps:"8-12",muscle:"Tríceps"}]}],g=n=>{i(C=>{if(n==="Nenhum (Peso Corporal)")return["Nenhum (Peso Corporal)"];const M=C.filter(_=>_!=="Nenhum (Peso Corporal)");if(M.includes(n)){const _=M.filter(f=>f!==n);return _.length===0?["Nenhum (Peso Corporal)"]:_}else return[...M,n]})},T=n=>{N(C=>C.includes(n)?C.filter(M=>M!==n):[...C,n])},p=()=>{const n=h.find(_=>_.id===l);if(!n)return;const C=n.exercises.map(_=>{let f={..._};return!x.includes("Barra")&&_.name.includes("Supino")?(f.name="Flexão de Braço",f.reps="8-15"):!x.includes("Halteres")&&_.name.includes("Halteres")?(f.name=_.name.replace("com Halteres","com Peso Corporal"),f.reps="10-15"):!x.includes("Barra Fixa")&&_.name.includes("Puxada")&&(f.name="Remada Invertida",f.reps="6-12"),f}),M={id:`adapted_${Date.now()}`,name:`${n.name} (Adaptado)`,originalWorkout:n.name,muscleGroups:n.muscleGroups,environment:s,availableTime:D,estimatedCalories:Math.round(D*4.5),exercises:C,adaptations:["Exercícios adaptados para equipamentos disponíveis","Tempo ajustado para duração desejada","Intensidade mantida para mesmos grupos musculares"]};r(M),A(!0)},d=["Teto baixo","Vizinhos embaixo","Ruído limitado","Piso escorregadio"];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Ie,{className:"w-5 h-5 text-snapfit-green"}),"Configuração do Ambiente"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Tipo de Espaço"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{value:"home",label:"Casa",icon:"🏠"},{value:"gym",label:"Academia",icon:"🏋️"},{value:"outdoor",label:"Ar Livre",icon:"🌳"},{value:"office",label:"Escritório",icon:"🏢"},{value:"hotel",label:"Hotel",icon:"🏨"}].map(n=>e.jsxs("button",{onClick:()=>a(n.value),className:`p-3 rounded-lg border transition-all duration-200 ${s===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-lg mb-1",children:n.icon}),e.jsx("div",{className:"text-xs",children:n.label})]},n.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Espaço Disponível"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:[{value:"small",label:"Pequeno",desc:"< 2m²"},{value:"medium",label:"Médio",desc:"2-6m²"},{value:"large",label:"Grande",desc:"> 6m²"}].map(n=>e.jsxs("button",{onClick:()=>u(n.value),className:`p-3 rounded-lg border transition-all duration-200 text-center ${m===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-sm font-medium",children:n.label}),e.jsx("div",{className:"text-xs opacity-75",children:n.desc})]},n.value))})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Equipamentos Disponíveis"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:es.map(n=>e.jsx("button",{onClick:()=>g(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${x.includes(n)?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Limitações do Ambiente"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:d.map(n=>e.jsx("button",{onClick:()=>T(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${y.includes(n)?"bg-orange-500/20 border-orange-500 text-orange-400":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-orange-500/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Treino do Protocolo para Adaptar"}),e.jsx("div",{className:"space-y-3",children:h.map(n=>e.jsxs("button",{onClick:()=>E(n.id),className:`w-full p-4 rounded-lg border transition-all duration-200 text-left ${l===n.id?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium",children:n.name}),e.jsxs("div",{className:"text-sm opacity-75",children:[n.exercises.length," exercícios"]})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:n.muscleGroups.map((C,M)=>e.jsx("span",{className:`px-2 py-1 text-xs rounded ${l===n.id?"bg-snapfit-green/30 text-snapfit-green":"bg-gray-700 text-gray-400"}`,children:C},M))})]},n.id))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Tempo Disponível: ",D," minutos"]}),e.jsx("input",{type:"range",min:"10",max:"120",step:"5",value:D,onChange:n=>j(Number(n.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsx("span",{children:"10 min"}),e.jsx("span",{children:"60 min"}),e.jsx("span",{children:"120 min"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:p,disabled:!l,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(he,{className:"w-5 h-5"}),"Adaptar Treino para Ambiente"]}),!l&&e.jsx("p",{className:"text-xs text-gray-400 text-center mt-2",children:"Selecione um treino do protocolo para adaptar"})]})]}),v&&o&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(he,{className:"w-5 h-5 text-snapfit-green"}),"Treino Adaptado Gerado"]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Calorias Estimadas"}),e.jsxs("div",{className:"text-lg font-bold text-orange-400",children:[o.estimatedCalories," kcal"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:o.name}),e.jsx("div",{className:"text-xs text-gray-400",children:"Treino Adaptado"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[o.availableTime," min"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:o.exercises.length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Exercícios"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:s==="home"?"Casa":s==="gym"?"Academia":s==="outdoor"?"Ar Livre":s==="office"?"Escritório":"Hotel"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Ambiente"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Grupos Musculares:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:o.muscleGroups.map((n,C)=>e.jsx("span",{className:"px-3 py-1 bg-snapfit-green/20 text-snapfit-green text-sm rounded-full border border-snapfit-green/30",children:n},C))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Exercícios Adaptados:"}),e.jsx("div",{className:"space-y-3",children:o.exercises.map((n,C)=>e.jsx("div",{className:"p-3 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center text-snapfit-green font-medium text-sm",children:C+1}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium",children:n.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[n.sets," séries × ",n.reps," reps"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-snapfit-green font-medium",children:n.muscle}),e.jsx("div",{className:"text-xs text-gray-400",children:"Músculo Alvo"})]})]})},C))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>k(!0),className:"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})}),"Iniciar Treino Adaptado"]}),e.jsx("button",{onClick:()=>{A(!1),r(null)},className:"px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"})]})]}),v&&c.adaptations.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ss,{className:"w-5 h-5 text-snapfit-green"}),"Adaptações Recomendadas"]}),e.jsx("div",{className:"space-y-3",children:c.adaptations.map((n,C)=>e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx(he,{className:"w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-300",children:n})]},C))})]}),e.jsx(Ms,{isOpen:R,onClose:()=>k(!1),workout:o})]})}const V={all:["ai","analysis"],recovery:t=>[...V.all,"recovery",t],movement:()=>[...V.all,"movement"],environment:()=>[...V.all,"environment"],history:t=>[...V.all,"history",t],insights:t=>[...V.all,"insights",t]};function Rs(t="week"){return me({queryKey:V.recovery(t),queryFn:async()=>{var s,a,m,u,x,i,y,N,l,E,D,j,v,A,R;console.log("🔄 useRecoveryAnalysis: Fetching recovery data for period:",t);try{const[k,o]=await Promise.all([q.get("wearables/data",{searchParams:{period:t,type:"recovery"}}),q.get("analytics/insights/ai",{searchParams:{category:"recovery",period:t}})]);console.log("📊 useRecoveryAnalysis: Wearables data:",k),console.log("🤖 useRecoveryAnalysis: AI insights:",o);const r=(k==null?void 0:k.data)||{},c=(o==null?void 0:o.data)||{},h={sleepData:{averageHours:((s=r.sleep)==null?void 0:s.average_duration)||0,quality:((a=r.sleep)==null?void 0:a.quality_score)||0,deepSleepPercentage:((m=r.sleep)==null?void 0:m.deep_sleep_percentage)||0,remSleepPercentage:((u=r.sleep)==null?void 0:u.rem_sleep_percentage)||0,sleepEfficiency:((x=r.sleep)==null?void 0:x.efficiency)||0},hrvData:{average:((i=r.hrv)==null?void 0:i.average)||0,trend:((y=r.hrv)==null?void 0:y.trend)||"stable",score:((N=r.hrv)==null?void 0:N.recovery_score)||0},stressData:{level:((l=r.stress)==null?void 0:l.average_level)||0,trend:((E=r.stress)==null?void 0:E.trend)||"stable"},energyData:{level:((D=r.energy)==null?void 0:D.average_level)||0,trend:((j=r.energy)==null?void 0:j.trend)||"stable"},heartRateData:{resting:((v=r.heart_rate)==null?void 0:v.resting_average)||0,max:((A=r.heart_rate)==null?void 0:A.max_recorded)||0,zones:((R=r.heart_rate)==null?void 0:R.zones)||{}},aiAnalysis:{recoveryStatus:c.recovery_status||"unknown",fatigueLevel:c.fatigue_level||0,recommendations:c.recommendations||[],insights:c.insights||[],confidence:c.confidence||0},overallScore:c.overall_recovery_score||0,rawWearables:r,rawInsights:c};return console.log("✅ useRecoveryAnalysis: Processed data:",h),h}catch(k){return console.warn("⚠️ useRecoveryAnalysis: Error fetching data, using fallback:",k),{sleepData:{averageHours:0,quality:0,deepSleepPercentage:0,remSleepPercentage:0,sleepEfficiency:0},hrvData:{average:0,trend:"stable",score:0},stressData:{level:0,trend:"stable"},energyData:{level:0,trend:"stable"},heartRateData:{resting:0,max:0,zones:{}},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[],insights:[],confidence:0},overallScore:0,rawWearables:{},rawInsights:{}}}},staleTime:1e3*60*10,refetchInterval:1e3*60*30,refetchIntervalInBackground:!0,refetchOnWindowFocus:!0,retry:2})}function _s(){return me({queryKey:V.movement(),queryFn:async()=>{console.log("🔄 useMovementAnalysisHistory: Fetching movement analysis data");try{const t=await q.get("analytics/movement/history");console.log("📊 useMovementAnalysisHistory: Response:",t);const s=(t==null?void 0:t.data)||{};return{analyses:s.analyses||[],videos:s.videos||[],totalAnalyses:s.total_analyses||0,averageScore:s.average_score||0,improvementTrend:s.improvement_trend||"stable",rawData:s}}catch(t){return console.warn("⚠️ useMovementAnalysisHistory: Error fetching data, using fallback:",t),{analyses:[],videos:[],totalAnalyses:0,averageScore:0,improvementTrend:"stable",rawData:{}}}},staleTime:1e3*60*5,refetchOnWindowFocus:!0,retry:2})}function Is(){return me({queryKey:V.environment(),queryFn:async()=>{console.log("🔄 useEnvironmentAdaptation: Fetching environment data");try{const[t,s]=await Promise.all([q.get("users/protocols/workout/active"),q.get("analytics/environment/analysis")]);console.log("📊 useEnvironmentAdaptation: Protocol data:",t),console.log("🌍 useEnvironmentAdaptation: Environment data:",s);const a=(t==null?void 0:t.data)||{},m=(s==null?void 0:s.data)||{};return{activeProtocol:a,environmentFactors:m.factors||{},adaptationSuggestions:m.suggestions||[],availableEquipment:m.equipment||[],spaceAnalysis:m.space_analysis||{},weatherData:m.weather||{},rawProtocol:a,rawEnvironment:m}}catch(t){return console.warn("⚠️ useEnvironmentAdaptation: Error fetching data, using fallback:",t),{activeProtocol:{},environmentFactors:{},adaptationSuggestions:[],availableEquipment:[],spaceAnalysis:{},weatherData:{},rawProtocol:{},rawEnvironment:{}}}},staleTime:1e3*60*15,refetchOnWindowFocus:!0,retry:2})}function Ls(t="month"){return me({queryKey:V.history(t),queryFn:async()=>{console.log("🔄 useAIAnalysisHistory: Fetching AI analysis history for period:",t);try{const s=await q.get("analytics/insights/ai/history",{searchParams:{period:t}});console.log("📊 useAIAnalysisHistory: Response:",s);const a=(s==null?void 0:s.data)||{};return{workoutAnalyses:a.workout_analyses||[],recoveryAnalyses:a.recovery_analyses||[],movementAnalyses:a.movement_analyses||[],environmentAdaptations:a.environment_adaptations||[],totalInsights:a.total_insights||0,trendsData:a.trends||{},rawData:a}}catch(s){return console.warn("⚠️ useAIAnalysisHistory: Error fetching data, using fallback:",s),{workoutAnalyses:[],recoveryAnalyses:[],movementAnalyses:[],environmentAdaptations:[],totalInsights:0,trendsData:{},rawData:{}}}},staleTime:1e3*60*10,refetchOnWindowFocus:!0,retry:2})}function Bs(){const t=Re();return ts({mutationFn:async({file:s,exerciseName:a,angle:m})=>{console.log("🔄 useUploadMovementVideo: Uploading video:",{exerciseName:a,angle:m});const u=new FormData;u.append("video",s),u.append("exerciseName",a),u.append("angle",m);const x=await q.post("analytics/movement/upload",u,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ useUploadMovementVideo: Upload response:",x),x.data},onSuccess:()=>{t.invalidateQueries({queryKey:V.movement()})},onError:s=>{console.error("❌ useUploadMovementVideo: Upload failed:",s)}})}function We(t="week"){const s=Rs(t),a=_s(),m=Is(),u=Ls(t),x=Bs();return{recovery:s.data,isLoadingRecovery:s.isLoading,recoveryError:s.error,movement:a.data,isLoadingMovement:a.isLoading,movementError:a.error,environment:m.data,isLoadingEnvironment:m.isLoading,environmentError:m.error,history:u.data,isLoadingHistory:u.isLoading,historyError:u.error,uploadVideo:x.mutate,isUploading:x.isPending,uploadError:x.error,isLoading:s.isLoading||a.isLoading||m.isLoading||u.isLoading,refetchAll:()=>{s.refetch(),a.refetch(),m.refetch(),u.refetch()}}}function Gs(){const t=Ne("week",["recovery-analysis"]),{recovery:s,isLoadingRecovery:a,recoveryError:m,refetchAll:u}=We(t.period),[x,i]=b.useState(7),[y,N]=b.useState(3),[l,E]=b.useState(7),[D,j]=b.useState(45),[v,A]=b.useState(65),[R,k]=b.useState(!1),[o,r]=b.useState(!1),[c,h]=b.useState(!1),g=s||{sleepData:{quality:0},stressData:{level:y*20},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[]},overallScore:0},p=(()=>{g.aiAnalysis.recoveryStatus;const f=g.aiAnalysis.fatigueLevel;return[{name:"Peito",group:"upper"},{name:"Costas",group:"upper"},{name:"Ombros",group:"upper"},{name:"Bíceps",group:"upper"},{name:"Tríceps",group:"upper"},{name:"Quadríceps",group:"lower"},{name:"Glúteos",group:"lower"},{name:"Panturrilha",group:"lower"}].map((Q,O)=>{const z=O%3*10;let G=f+z;G=Math.max(0,Math.min(100,G));let P="recovered",S=0;return G>70?(P="overworked",S=48):G>50?(P="fatigued",S=24):G>30?(P="recovering",S=12):(P="recovered",S=0),{name:Q.name,status:P,fatigueLevel:G,recoveryTime:S}})})(),d=()=>{h(!0),u()},n=f=>f<=30?"text-green-400":f<=60?"text-yellow-400":"text-red-400",C=f=>f<=30?"Baixa":f<=60?"Moderada":"Alta",M=f=>{switch(f){case"recovered":return"text-green-400";case"recovering":return"text-yellow-400";case"fatigued":return"text-orange-400";case"overworked":return"text-red-400";default:return"text-gray-400"}},_=f=>{switch(f){case"recovered":return e.jsx(le,{className:"w-4 h-4 text-green-400"});case"recovering":return e.jsx(te,{className:"w-4 h-4 text-yellow-400"});case"fatigued":return e.jsx(Y,{className:"w-4 h-4 text-orange-400"});case"overworked":return e.jsx(Y,{className:"w-4 h-4 text-red-400"});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Recuperação Inteligente"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise de fadiga e sugestões de descanso baseadas em IA"})]}),e.jsx(we,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(ae,{className:"w-5 h-5 text-snapfit-green"}),"Dados de Recuperação"]}),a&&e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx(be,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{className:"text-sm",children:"Carregando..."})]})]}),e.jsxs("div",{className:"mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${o?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{className:"text-white font-medium",children:o?"Wearable Conectado":"Wearable Desconectado"})]}),e.jsx("button",{onClick:()=>r(!o),className:`px-3 py-1 rounded text-sm transition-colors ${o?"bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30":"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 hover:bg-snapfit-green/30"}`,children:o?"Desconectar":"Conectar"})]}),o?e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-blue-400 font-medium",children:[v," bpm"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"FC Repouso"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-green-400 font-medium",children:[D," ms"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"HRV"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-purple-400 font-medium",children:[x,"h 23m"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Sono Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-orange-400 font-medium",children:"85%"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Qualidade"})]})]}):e.jsx("div",{className:"text-center text-gray-400 text-sm",children:"Conecte seu wearable para dados automáticos de sono, frequência cardíaca e HRV"})]}),e.jsx("h4",{className:"text-white font-medium mb-4",children:o?"Dados Complementares":"Dados Manuais"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Horas de Sono (última noite)",o&&e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"4",max:"12",value:x,onChange:f=>i(Number(f.target.value)),disabled:o,className:`w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider ${o?"opacity-50 cursor-not-allowed":""}`}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(Ae,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[x,"h"]}),e.jsx("span",{className:`text-sm ${x>=7?"text-green-400":x>=6?"text-yellow-400":"text-red-400"}`,children:x>=7?"Bom":x>=6?"Regular":"Insuficiente"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Estresse (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:y,onChange:f=>N(Number(f.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(Y,{className:"w-4 h-4 text-orange-400"}),e.jsxs("span",{className:"text-white font-medium",children:[y,"/10"]}),e.jsx("span",{className:`text-sm ${y<=3?"text-green-400":y<=6?"text-yellow-400":"text-red-400"}`,children:y<=3?"Baixo":y<=6?"Moderado":"Alto"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Energia (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:l,onChange:f=>E(Number(f.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(as,{className:"w-4 h-4 text-yellow-400"}),e.jsxs("span",{className:"text-white font-medium",children:[l,"/10"]}),e.jsx("span",{className:`text-sm ${l>=7?"text-green-400":l>=4?"text-yellow-400":"text-red-400"}`,children:l>=7?"Alto":l>=4?"Moderado":"Baixo"})]})]})]})]}),o&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["HRV (Variabilidade da FC)",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(ae,{className:"w-4 h-4 text-green-400"}),e.jsxs("span",{className:"text-white font-medium",children:[D," ms"]}),e.jsx("span",{className:`text-sm ${D>=50?"text-green-400":D>=30?"text-yellow-400":"text-red-400"}`,children:D>=50?"Excelente":D>=30?"Bom":"Baixo"})]})})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["FC de Repouso",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(ae,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[v," bpm"]}),e.jsx("span",{className:`text-sm ${v<=60?"text-green-400":v<=80?"text-yellow-400":"text-red-400"}`,children:v<=60?"Atlético":v<=80?"Normal":"Elevado"})]})})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:d,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx(ee,{className:"w-5 h-5"}),o?"Analisar com Dados Completos":"Analisar Recuperação"]}),o&&e.jsx("p",{className:"text-xs text-green-400 text-center mt-2",children:"✓ Análise aprimorada com dados do wearable"})]})]}),(c||s)&&e.jsxs(e.Fragment,{children:[m&&e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro ao carregar dados de recuperação"})]}),e.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Usando dados manuais como fallback"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(rs,{className:"w-5 h-5 text-snapfit-green"}),"Status Geral de Recuperação"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[_(g.aiAnalysis.recoveryStatus),e.jsx("span",{className:`text-sm font-medium ${M(g.aiAnalysis.recoveryStatus)}`,children:g.aiAnalysis.recoveryStatus==="recovered"?"Recuperado":g.aiAnalysis.recoveryStatus==="recovering"?"Recuperando":g.aiAnalysis.recoveryStatus==="fatigued"?"Fatigado":g.aiAnalysis.recoveryStatus==="overworked"?"Sobrecarregado":"Desconhecido"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:`text-2xl font-bold ${n(g.aiAnalysis.fatigueLevel)}`,children:[Math.round(g.aiAnalysis.fatigueLevel),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Fadiga Geral"}),e.jsx("div",{className:`text-xs ${n(g.aiAnalysis.fatigueLevel)}`,children:C(g.aiAnalysis.fatigueLevel)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-400",children:[Math.round(g.sleepData.quality),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Qualidade do Sono"}),e.jsx("div",{className:"text-xs text-blue-400",children:g.sleepData.quality>=80?"Excelente":g.sleepData.quality>=60?"Boa":"Regular"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-400",children:[Math.round(g.stressData.level),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Nível de Estresse"}),e.jsx("div",{className:"text-xs text-orange-400",children:g.stressData.level<=30?"Baixo":g.stressData.level<=60?"Moderado":"Alto"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:[Math.round(g.overallScore),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Score Geral"}),e.jsx("div",{className:"text-xs text-snapfit-green",children:g.overallScore>=80?"Excelente":g.overallScore>=60?"Bom":"Regular"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Status dos Grupos Musculares:"}),e.jsx("div",{className:"space-y-3",children:p.map((f,B)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[_(f.status),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:f.name}),e.jsx("div",{className:"text-sm text-gray-400",children:f.recoveryTime>0?`Recuperação estimada: ${f.recoveryTime}h`:"Totalmente recuperado"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-medium ${n(f.fatigueLevel)}`,children:[Math.round(f.fatigueLevel),"%"]}),e.jsx("div",{className:`text-xs ${M(f.status)}`,children:f.status==="recovered"?"Recuperado":f.status==="recovering"?"Recuperando":f.status==="fatigued"?"Fatigado":"Sobrecarregado"})]})]},B))})]})]}),g.aiAnalysis.insights.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Insights da IA"]}),e.jsx("div",{className:"space-y-4",children:g.aiAnalysis.insights.map((f,B)=>e.jsx("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center",children:f.type==="sleep"?e.jsx(Ae,{className:"w-4 h-4 text-blue-400"}):f.type==="hrv"?e.jsx(ae,{className:"w-4 h-4 text-green-400"}):e.jsx(ee,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-white font-medium mb-1",children:f.title}),e.jsx("p",{className:"text-gray-300 text-sm",children:f.description}),e.jsxs("div",{className:"mt-2 text-xs text-gray-400",children:["Confiança: ",Math.round(f.confidence*100),"%"]})]})]})},B))})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(le,{className:"w-5 h-5 text-snapfit-green"}),"Recomendações Personalizadas"]}),e.jsx("div",{className:"space-y-4",children:g.aiAnalysis.recommendations.length>0?g.aiAnalysis.recommendations.map((f,B)=>e.jsx("div",{className:"p-4 rounded-lg border border-snapfit-green/20 bg-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-2xl",children:"💡"}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-gray-300",children:f})})]})},B)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(le,{className:"w-12 h-12 mx-auto mb-3 text-green-400"}),e.jsx("p",{children:"Nenhuma recomendação específica no momento."}),e.jsx("p",{className:"text-sm",children:"Continue mantendo seus hábitos atuais!"})]})})]})]})]})}function Hs({onReuseProtocol:t}){var r,c,h,g,T,p;const[s,a]=b.useState("sessions"),m=Ne("month",["workout-history","ai-insights"]),{history:u,isLoadingHistory:x,historyError:i,refetchAll:y}=We(m.period),{data:N,isLoading:l,error:E,refetch:D}=Te({startDate:(r=m.customDates)==null?void 0:r.startDate,endDate:(c=m.customDates)==null?void 0:c.endDate,limit:50}),j=(N==null?void 0:N.workouts)||[],v=[],A=d=>{switch(d){case"active":return"bg-green-500/20 text-green-400 border-green-500/30";case"completed":return"bg-blue-500/20 text-blue-400 border-blue-500/30";case"archived":return"bg-gray-500/20 text-gray-400 border-gray-500/30";default:return"bg-gray-500/20 text-gray-400 border-gray-500/30"}},R=d=>{switch(d){case"active":return e.jsx(oe,{className:"w-4 h-4"});case"completed":return e.jsx(is,{className:"w-4 h-4"});case"archived":return e.jsx(ge,{className:"w-4 h-4"});default:return e.jsx(oe,{className:"w-4 h-4"})}},k=d=>new Date(d).toLocaleDateString("pt-BR"),o=d=>{const[n,C,M]=d.split(":").map(Number);return n>0?`${n.toString().padStart(2,"0")}:${C.toString().padStart(2,"0")}h`:`${C.toString().padStart(2,"0")}:${M.toString().padStart(2,"0")}min`};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(de,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise completa do seu progresso"})]})]}),e.jsx(we,{period:m.period,onPeriodChange:m.setPeriod,onCustomDateChange:m.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex bg-snapfit-gray rounded-lg p-1 border border-snapfit-green/20 mb-6",children:[e.jsxs("button",{onClick:()=>a("sessions"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="sessions"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(J,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Treinos Realizados"})]}),e.jsxs("button",{onClick:()=>a("protocols"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="protocols"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(ge,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Protocolos"})]}),e.jsxs("button",{onClick:()=>a("ai-insights"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="ai-insights"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Insights IA"})]})]}),e.jsx("div",{className:"space-y-4",children:s==="sessions"?j.map(d=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-full bg-snapfit-green/20 border border-snapfit-green/30",children:e.jsx(J,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:d.workout_name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[d.muscle_groups," • ",k(d.date)]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-medium",children:[d.exercise_count," exercícios"]}),e.jsx("div",{className:"text-sm text-snapfit-green",children:"Concluído"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(te,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:o(d.workout_time)})]}),e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(ye,{className:"w-3 h-3 text-orange-400"}),e.jsxs("span",{children:[d.total_kcal,"kcal"]})]}),e.jsx("div",{className:"px-2 py-1 rounded-full bg-snapfit-green/20 text-snapfit-green",children:d.type})]})]},d.id)):s==="protocols"?v.length>0?v.map(d=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`p-2 rounded-full border ${A(d.status)}`,children:R(d.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:d.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[k(d.startDate),d.endDate&&` - ${k(d.endDate)}`]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>console.log("Ver detalhes:",d),className:"flex items-center gap-1 px-2 py-1 bg-gray-500/20 text-gray-300 rounded text-xs border border-gray-500/30 hover:bg-gray-500/30 transition-colors",children:[e.jsx(Ce,{className:"w-3 h-3"}),"Ver"]}),t&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>t(d),className:"flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs border border-blue-500/30 hover:bg-blue-500/30 transition-colors",children:[e.jsx(be,{className:"w-3 h-3"}),"Reutilizar"]}),e.jsxs("button",{onClick:()=>t({...d,edit:!0}),className:"flex items-center gap-1 px-2 py-1 bg-amber-500/20 text-amber-400 rounded text-xs border border-amber-500/30 hover:bg-amber-500/30 transition-colors",children:[e.jsx(ns,{className:"w-3 h-3"}),"Editar"]})]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsx("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:d.split}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[d.frequency,"x/semana"]}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[d.completedWorkouts," treinos"]}),e.jsx("div",{className:`px-2 py-1 rounded-full border ${A(d.status)}`,children:d.status==="active"?"Ativo":d.status==="completed"?"Concluído":"Arquivado"})]})]},d.id)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(ge,{className:"w-12 h-12 mx-auto mb-3"}),e.jsx("p",{children:"Nenhum protocolo encontrado"}),e.jsx("p",{className:"text-sm",children:"Seus protocolos anteriores aparecerão aqui"})]}):e.jsx("div",{className:"space-y-6",children:x?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(be,{className:"w-8 h-8 animate-spin text-snapfit-green mr-3"}),e.jsx("span",{className:"text-gray-400",children:"Carregando insights da IA..."})]}):i?e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"text-red-400 mb-2",children:"Erro ao carregar insights"}),e.jsx("button",{onClick:()=>y(),className:"text-sm text-red-300 underline",children:"Tentar novamente"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(X,{className:"w-5 h-5 text-purple-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Total de Insights"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:(u==null?void 0:u.totalInsights)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(J,{className:"w-5 h-5 text-blue-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Treino"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((h=u==null?void 0:u.workoutAnalyses)==null?void 0:h.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(ee,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Recuperação"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((g=u==null?void 0:u.recoveryAnalyses)==null?void 0:g.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Ce,{className:"w-5 h-5 text-orange-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Movimento"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((T=u==null?void 0:u.movementAnalyses)==null?void 0:T.length)||0})]})]}),((p=u==null?void 0:u.workoutAnalyses)==null?void 0:p.length)>0&&e.jsxs("div",{className:"bg-snapfit-gray rounded-lg border border-snapfit-green/10 p-6",children:[e.jsxs("h4",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(X,{className:"w-5 h-5 text-purple-400"}),"Insights Recentes da IA"]}),e.jsx("div",{className:"space-y-4",children:u.workoutAnalyses.slice(0,5).map((d,n)=>e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center",children:e.jsx(X,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-white font-medium mb-1",children:d.title||"Análise de Treino"}),e.jsx("p",{className:"text-gray-300 text-sm mb-2",children:d.description||"Análise detalhada do seu desempenho"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[e.jsx("span",{children:k(d.date||new Date().toISOString())}),e.jsxs("span",{children:["Confiança: ",Math.round((d.confidence||.8)*100),"%"]})]})]})]})},n))})]}),(!u||u.totalInsights===0)&&e.jsxs("div",{className:"text-center py-12 text-gray-400",children:[e.jsx(X,{className:"w-16 h-16 mx-auto mb-4 text-gray-500"}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Nenhum insight disponível"}),e.jsx("p",{className:"text-sm",children:"Continue treinando para gerar insights personalizados da IA"})]})]})})}),e.jsxs("div",{className:"mt-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Estatísticas do Mês"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:"12"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:"15h"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Tempo Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:"4"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Protocolos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:"3.2k"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]})]})]})]})}const Ee=[{id:"analysis",label:"Análise de Movimento",icon:fe,description:"IA analisa sua técnica e sugere melhorias"},{id:"environment",label:"Adaptação de Ambiente",icon:Ie,description:"Exercícios personalizados para seu espaço"},{id:"recovery",label:"Recuperação Inteligente",icon:ae,description:"Análise de fadiga e sugestões de descanso"},{id:"history",label:"Histórico",icon:de,description:"Seus treinos e análises anteriores"}];function zs({onReuseProtocol:t}){var u;const[s,a]=b.useState("analysis"),m=()=>{switch(s){case"analysis":return e.jsx(Fs,{});case"environment":return e.jsx($s,{});case"recovery":return e.jsx(Gs,{});case"history":return e.jsx(Hs,{onReuseProtocol:t});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Análise e Ferramentas IA"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Tecnologia avançada para otimizar seus treinos e recuperação"})]}),e.jsx("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20 overflow-x-auto",children:Ee.map(x=>{const i=x.icon,y=x.id===s;return e.jsxs("button",{onClick:()=>a(x.id),className:`flex-1 min-w-0 flex items-center justify-center gap-2 px-3 py-3 rounded-md transition-all duration-200 whitespace-nowrap ${y?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(i,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"hidden sm:inline text-sm",children:x.label}),e.jsx("span",{className:"sm:hidden text-xs",children:x.id==="analysis"?"Análise":x.id==="environment"?"Ambiente":x.id==="recovery"?"Recuperação":"Histórico"})]},x.id)})}),e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-sm text-gray-400",children:(u=Ee.find(x=>x.id===s))==null?void 0:u.description})}),e.jsx("div",{className:"min-h-[400px]",children:m()})]})}function Zs(){Re();const t=Ne("week",["workout-stats","workout-analytics"]),{protocol:s,stats:a,isLoadingProtocol:m,isLoadingStats:u,protocolError:x,statsError:i,removeProtocol:y,refetchStats:N}=js(t.period),l=a||{weeklyWorkouts:{completed:0,planned:5},totalTime:0,totalCalories:0,totalVolume:0,currentStreak:0,bestStreak:0,weeklyProgressPercentage:0,protocolCompletionPercentage:0,totalWorkouts:0},E=I.useMemo(()=>l.chartData&&l.chartData.length>0?l.chartData.map(w=>({value:w.training_volume||0,label:w.label||"N/A",color:"#B9FF43"})):[{value:0,label:"Seg",color:"#B9FF43"},{value:0,label:"Ter",color:"#B9FF43"},{value:0,label:"Qua",color:"#B9FF43"},{value:0,label:"Qui",color:"#B9FF43"},{value:0,label:"Sex",color:"#B9FF43"},{value:0,label:"Sáb",color:"#B9FF43"},{value:0,label:"Dom",color:"#B9FF43"}],[l.chartData]),[D,j]=I.useState([{value:0,label:"Supino",color:"#B9FF43"},{value:0,label:"Agachamento",color:"#4CAF50"},{value:0,label:"Levantamento",color:"#FFC107"},{value:0,label:"Remada",color:"#FF5722"}]),[v,A]=I.useState(null),[R,k]=I.useState(!1),[o,r]=I.useState(!1),[c,h]=I.useState(null),[g,T]=I.useState(`Foco em tempo sob tensão nos exercícios de peito.
Aumentar carga progressivamente nos exercícios compostos.
Manter strict form em todos os exercícios.`),[p,d]=I.useState(!1),[n,C]=I.useState(!1),[M,_]=I.useState(null),[f,B]=I.useState(!1),[Q,O]=I.useState(null),z=je(),G=ls(),P=os(),S=cs();I.useEffect(()=>{s!=null&&s.workouts&&s.workouts.length>0?(A(s.workouts[0]),console.log("✅ WorkoutPage: Primeiro treino selecionado:",s.workouts[0])):(console.log("⚠️ WorkoutPage: Nenhum treino disponível para seleção"),A(null))},[s]),I.useEffect(()=>{x&&(console.error("❌ React Query: Erro ao carregar protocolo:",x),ds.error("Erro ao carregar protocolo de treino",{position:"bottom-right"}))},[x]);const F=async()=>{!window.confirm("Tem certeza que deseja remover este protocolo?")||!(s!=null&&s.id)||y(s.id)},$=async()=>{try{const w=await G.mutateAsync("workout");w&&w.id?(B(!0),O(()=>()=>d(!0))):d(!0)}catch(w){console.error("Error checking active protocol:",w),d(!0)}},H=async()=>{var w,ie;try{const L=await G.mutateAsync("workout");console.log("🔍 Active protocol found:",L),L&&L.id?(console.log(`🏁 Finalizing active protocol with ID: ${L.id}`),await S.mutateAsync({protocolId:L.id.toString(),protocolType:"workout"}),console.log("✅ Active protocol finalized successfully")):console.log("ℹ️ No active protocol found to finalize"),Q&&(Q(),O(null)),B(!1)}catch(L){console.error("❌ Error in finalization flow:",L);const U=((ie=(w=L==null?void 0:L.response)==null?void 0:w.data)==null?void 0:ie.message)||(L==null?void 0:L.message)||"Erro desconhecido";U.includes("não encontrado")||U.includes("já finalizado")?(console.log("ℹ️ Protocol already finalized or not found, proceeding with creation"),Q&&(Q(),O(null))):console.error("❌ Unexpected error during finalization:",U),B(!1)}},W=()=>{z("/dashboard/workout/create-protocol/ai"),d(!1)},K=()=>{z("/dashboard/workout/create-protocol/manual"),d(!1)},qe=()=>{z("/dashboard/workout/create-protocol/import"),d(!1)},re=async w=>{const ie={name:w.name,type_id:w.type,objective:w.objective,started_at:w.startDate,frequency:w.frequency,split:w.split,goals:w.goals,workouts:w.workouts.map(L=>({exercises:L.exercises.map(U=>({exercise_id:U.exercise.id,sets:U.sets,reps:U.reps,rpe:U.rpe,rest_seconds:U.restTime,notes:U.notes}))})),supplements:w.supplements,general_notes:w.notes};console.log("Saving protocol:",w),r(!0),k(!0),P.mutate({protocolData:ie,protocolType:"workout",shouldFinalizeActive:!1},{onSuccess:()=>{setTimeout(()=>{k(!1),r(!1),h(null)},1e3)},onError:()=>{k(!1),r(!1)}})},ne=()=>{h(null),k(!1),r(!1),d(!1)};return R?e.jsx(ms,{type:"workout",isSuccess:o,message:o?"Protocolo gerado com sucesso! 🎉":void 0}):e.jsxs(e.Fragment,{children:[m&&e.jsx(xs,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Treino"}),p&&e.jsx(Es,{onGenerateAI:W,onCreateManual:K,onImportFromCoach:qe,onReadProtocol:()=>{z("/dashboard/workout/create-protocol/import"),d(!1)},onClose:ne}),c==="ai"&&e.jsx(Pe,{onProtocolGenerated:w=>{console.log("Protocolo de treino gerado pela IA:",w),re(w)},onClose:ne}),c==="manual"&&e.jsx(gs,{onSave:re,onCancel:ne}),c==="import"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20",children:e.jsx(Ts,{onImport:re,onCancel:ne})})}),s&&!(s!=null&&s.has_protocol)&&!c&&e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Você não tem um protocolo de treino ativo"}),e.jsxs("button",{onClick:$,className:"btn-primary flex items-center justify-center gap-2 w-full sm:w-auto",children:[e.jsx(us,{className:"w-5 h-5"}),"Novo Protocolo"]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Estatísticas de Treino"}),e.jsx(we,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),u?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Carregando estatísticas..."})]}):i?e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 text-center",children:[e.jsx("p",{className:"text-red-600 text-sm",children:"Erro ao carregar estatísticas"}),e.jsx("button",{onClick:()=>N(),className:"mt-2 text-red-700 underline text-xs",children:"Tentar novamente"})]}):e.jsx("div",{className:"mobile-scroll-container",children:e.jsxs("div",{className:"mobile-scroll-content",children:[e.jsx(Z,{title:"Treinos Semana",value:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned}`,icon:e.jsx(J,{className:"animate-pulse-slow"}),change:l.weeklyWorkouts.completed>0?10:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Tempo Total",value:`${l.totalTime} min`,icon:e.jsx(hs,{className:"animate-pulse-slow"}),change:l.totalTime>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Calorias",value:`${l.totalCalories} kcal`,icon:e.jsx(ye,{className:"animate-pulse-slow"}),change:l.totalCalories>0?8:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(Z,{title:"Volume",value:`${l.totalVolume} kg`,icon:e.jsx(ps,{className:"animate-pulse-slow"}),change:l.totalVolume>0?15:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(Z,{title:"Sequência Atual",value:`${l.currentStreak} dias`,icon:e.jsx(ee,{className:"animate-pulse-slow"}),change:l.currentStreak>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(Z,{title:"Melhor Sequência",value:`${l.bestStreak} dias`,icon:e.jsx(fs,{className:"animate-pulse-slow"}),change:l.bestStreak>0?3:0,className:"mobile-card stagger-item animate-slide-in-right"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6",children:[e.jsx(pe,{data:E,title:"Duração dos Treinos (min)",type:"bar",className:"animate-slide-in-left"}),e.jsx(pe,{data:D,title:"Progresso de Força (kg)",type:"bar",className:"animate-slide-in-right"})]})]})}),(console.log("🔍 WorkoutPage: Verificando condições de renderização:",{protocolWorkouts:!!s,creationMode:c,hasProtocol:s==null?void 0:s.has_protocol,shouldRender:s&&!c&&(s==null?void 0:s.has_protocol)}),s&&!c&&(s==null?void 0:s.has_protocol))&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"card p-6 animate-slide-up",children:e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Protocolo de Treino"}),e.jsx("div",{className:"flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:e.jsx("span",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:(s==null?void 0:s.type)||"Hipertrofia"})})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:s==null?void 0:s.objective})]}),e.jsxs("div",{className:"flex gap-2 mt-2 sm:mt-0",children:[e.jsxs("button",{className:"btn-secondary flex items-center justify-center gap-2 text-sm",onClick:()=>{console.log("🔄 Botão Editar Protocolo clicado. Protocolo ID:",s==null?void 0:s.id),s!=null&&s.id?window.location.pathname.includes("/professional/")?(console.log("🏃‍♂️ Navegando para rota profissional:",`/dashboard/professional/protocols/edit/${s.id}?type=workout`),z(`/dashboard/professional/protocols/edit/${s.id}?type=workout`)):(console.log("👤 Navegando para rota de usuário:",`/dashboard/workout/edit-protocol/${s.id}`),z(`/dashboard/workout/edit-protocol/${s.id}`)):(console.log("❌ Nenhum protocolo ID encontrado, navegando para criação"),z("/dashboard/workout/create-protocol/manual"))},children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),e.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]}),"Editar Protocolo"]}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 text-sm",onClick:$,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 5v14"}),e.jsx("path",{d:"M5 12h14"})]}),"Criar Protocolo"]})]})]})}),e.jsx(Ps,{protocolId:s==null?void 0:s.id,protocolName:s==null?void 0:s.name,startDate:s==null?void 0:s.started_at,splitInfo:s==null?void 0:s.split,frequency:`${s==null?void 0:s.frequency}x/semana`,objective:s==null?void 0:s.objective,completedWorkouts:(s==null?void 0:s.workouts_completed)||0,notes:s==null?void 0:s.notes,workouts:(s==null?void 0:s.workouts)||[],workoutsDb:s==null?void 0:s.workouts,selectedWorkout:v,onSelectWorkout:A,onGenerateNewProtocol:$,onEditNotes:()=>{console.log("Editing protocol notes...")},onDeleteProtocol:F}),e.jsxs("div",{className:"card p-6 animate-slide-up",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Estatísticas do Protocolo"}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Progresso: ",Math.round(l.protocolCompletionPercentage),"% completo"]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Frequência"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:`${(s==null?void 0:s.frequency)||0}x/semana`})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Divisão"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:(s==null?void 0:s.split)||"N/A"})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total de Treinos"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:l.totalWorkouts})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Sequência Atual"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[l.currentStreak," dias"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsx(pe,{data:E,title:"Duração dos Treinos (min)",type:"line",height:180,className:"animate-slide-in-left"}),e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h4",{className:"text-base font-bold mb-3",children:"Progresso"}),e.jsxs("div",{className:"flex justify-around gap-4",children:[e.jsx(ue,{value:l.weeklyWorkouts.completed,max:l.weeklyWorkouts.planned,label:"Esta Semana",sublabel:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned} treinos`,color:"#B9FF43",size:90}),e.jsx(ue,{value:l.weeklyProgressPercentage,max:100,label:"Meta Semanal",sublabel:`${Math.round(l.weeklyProgressPercentage)}% atingido`,color:"#4CAF50",size:90}),e.jsx(ue,{value:l.protocolCompletionPercentage,max:100,label:"Protocolo",sublabel:`${Math.round(l.protocolCompletionPercentage)}% completo`,color:"#FF9800",size:90})]})]})]})]}),n&&e.jsx(Pe,{initialMode:M,onProtocolGenerated:w=>{console.log("Protocolo gerado pela IA:",w),re(w),C(!1),_(null)},onClose:()=>{C(!1),_(null)}}),e.jsx(zs,{onReuseProtocol:w=>{w.edit?(console.log("Editando e usando protocolo:",w),alert(`Editando e usando protocolo: ${w.name||w.workout_name}`)):(console.log("Reutilizando protocolo:",w),alert(`Protocolo ${w.name||w.workout_name} reutilizado com sucesso!`))}})]}),!c&&e.jsx("div",{className:"space-y-6",children:e.jsx(Ss,{hasActiveProtocol:(s==null?void 0:s.has_protocol)||!1,defaultTab:s!=null&&s.has_protocol?"sessions":"protocols",onReuseProtocol:w=>{w.edit?(console.log("Editando e usando protocolo:",w),alert(`Editando e usando protocolo: ${w.name||w.workout_name}`)):(console.log("Reutilizando protocolo:",w),alert(`Protocolo ${w.name||w.workout_name} reutilizado com sucesso!`))}})})]}),e.jsx(bs,{isOpen:f,onClose:()=>{B(!1),O(null)},onConfirm:H,title:"Protocolo Ativo Encontrado",message:"Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?",confirmText:"Sim, Continuar",cancelText:"Cancelar",type:"warning",isLoading:G.isPending||P.isPending||S.isPending})]})}export{Zs as WorkoutPage};
