import{c as De,t as ve,j as e,aa as je,ab as ze,b as q,r as b,ac as Te,R as I,l as ae,I as Y,f as se,F as ye,ad as qe,ae as Ue,a7 as ke,n as oe,af as Fe,ag as Me,p as ce,ah as $e,ai as Ve,B as Z,aj as Oe,ak as Qe,a as Ke,C as pe,al as Je,am as Se,G as ee,an as X,ao as ie,a8 as me,ap as Ze,aq as Xe,ar as Ye,h as de,u as Re,as as es,g as Ne,Q as we,at as te,au as fe,av as Ae,Z as ss,aw as ts,ax as xe,ay as Ce,az as as,m as rs,aA as ns,aB as ls,aC as is,y as os,aD as cs,L as ds,aE as Pe,aF as ms,P as xs,aG as gs,aH as us,aI as hs,aJ as ps}from"./index-yuwXvJOX.js";import{f as fs}from"./date-DOfZ7AVQ.js";import{P as bs}from"./ProtocolDetailsModal-9zlTaKl9.js";import{D as be}from"./download-B7lZQeVB.js";import{S as J}from"./StatCard-DAJ4AKMo.js";import{C as ge}from"./CircularProgress-DqwHJlC_.js";import{u as vs}from"./useWorkoutProtocol-DL3ErRVq.js";import"./workoutService-Cdz2Ft6r.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const js=[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]],_e=De("house",js);/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ys=[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]],ue=De("lightbulb",ys);function Ns({type:t,onReuseProtocol:s,onViewDetails:a}){ve();const x=g=>{if(console.log("🔍 EnhancedProtocolHistory: Protocolo selecionado",g),console.log("🔍 EnhancedProtocolHistory: Protocol ID:",g==null?void 0:g.id),console.log("🔍 EnhancedProtocolHistory: Protocol structure:",Object.keys(g||{})),console.log("🔍 EnhancedProtocolHistory: onViewDetails function:",a),a){const k={id:(g==null?void 0:g.id)||(g==null?void 0:g.mock_id)||"unknown",name:(g==null?void 0:g.name)||"Protocolo sem nome",type:(g==null?void 0:g.objective)||"workout",objective:(g==null?void 0:g.objective)||"Não especificado",startDate:(g==null?void 0:g.started_at)||new Date().toISOString(),endDate:(g==null?void 0:g.ended_at)||null,status:(g==null?void 0:g.status)||"unknown",...g};console.log("🔍 EnhancedProtocolHistory: Transformed protocol:",k),a(k)}else console.error("❌ EnhancedProtocolHistory: onViewDetails function not available")},h=g=>{console.log("📋 EnhancedProtocolHistory: Protocolo duplicado",g),s&&s(g,!1)},m=g=>{console.log("🏁 EnhancedProtocolHistory: Protocolo finalizado",g)};return e.jsxs("div",{className:"space-y-6",children:[e.jsx("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(je,{className:"w-6 h-6 text-snapfit-green"}),e.jsxs("div",{children:[e.jsxs("h2",{className:"text-lg font-semibold text-white",children:["Histórico de Protocolos de ",t==="diet"?"Dieta":"Treino"]}),e.jsx("p",{className:"text-sm text-gray-400",children:"Visualize, duplique e gerencie seus protocolos anteriores com dados reais do banco de dados"})]})]})}),e.jsx(ze,{protocolType:t,onProtocolSelect:x,onProtocolDuplicate:h,onProtocolFinish:m})]})}async function Ie(){const t=[],s=new Date().toISOString();console.log("🔍 DEBUG: Starting comprehensive workout history endpoint testing...");try{console.log("🔍 DEBUG: Testing GET /users/debug/protocols/raw");const a=await q.get("users/debug/protocols/raw");t.push({endpoint:"GET /users/debug/protocols/raw",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Protocols raw data:",a)}catch(a){t.push({endpoint:"GET /users/debug/protocols/raw",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching protocols raw data:",a)}try{console.log("🔍 DEBUG: Testing GET /users/debug/workouts/raw");const a=await q.get("users/debug/workouts/raw");t.push({endpoint:"GET /users/debug/workouts/raw",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Workouts raw data:",a)}catch(a){t.push({endpoint:"GET /users/debug/workouts/raw",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching workouts raw data:",a)}try{console.log("🔍 DEBUG: Testing GET /users/workouts/history");const a=await q.get("users/workouts/history",{searchParams:{period:"month"}});t.push({endpoint:"GET /users/workouts/history",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Workout history data:",a)}catch(a){t.push({endpoint:"GET /users/workouts/history",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching workout history:",a)}try{console.log("🔍 DEBUG: Testing GET /users/protocols/workout/history");const a=await q.get("users/protocols/workout/history");t.push({endpoint:"GET /users/protocols/workout/history",success:!0,data:a,timestamp:s}),console.log("✅ DEBUG: Protocol history data:",a)}catch(a){t.push({endpoint:"GET /users/protocols/workout/history",success:!1,error:a.message||"Unknown error",timestamp:s}),console.error("❌ DEBUG: Error fetching protocol history:",a)}return console.log("🔍 DEBUG: Endpoint testing completed. Results:",t),t}function ws(){console.log("🔍 DEBUG: Checking React Query cache status..."),console.log("🔍 DEBUG: React Query cache check needs to be implemented in component context")}function Le(t,s){console.log(`🔍 DEBUG: ${t} - Data received:`,s),console.log(`🔍 DEBUG: ${t} - Data type:`,typeof s),console.log(`🔍 DEBUG: ${t} - Is array:`,Array.isArray(s)),Array.isArray(s)&&(console.log(`🔍 DEBUG: ${t} - Array length:`,s.length),s.length>0&&console.log(`🔍 DEBUG: ${t} - First item:`,s[0]))}async function Be(){console.log("🚀 DEBUG: Starting complete workout history diagnostic...");const t=await Ie(),s=t.filter(x=>x.success).length,a=t.filter(x=>!x.success).length;return console.log("📊 DEBUG: Diagnostic Summary:"),console.log(`✅ Successful endpoints: ${s}`),console.log(`❌ Failed endpoints: ${a}`),console.log("📋 Detailed results:",t),{summary:{total:t.length,successful:s,failed:a},results:t}}typeof window<"u"&&(window.debugWorkoutHistory={testEndpoints:Ie,testCache:ws,testDataFlow:Le,runComplete:Be},console.log("🔧 DEBUG: Workout history debug functions available globally:"),console.log("- window.debugWorkoutHistory.testEndpoints()"),console.log("- window.debugWorkoutHistory.runComplete()"));function ks({date_start:t,date_end:s,onReuseProtocol:a,showProtocolHistory:x=!1}){const[h,m]=b.useState(null),[g,k]=b.useState(!1),{data:N,isLoading:l,error:D,refetch:E}=Te("month"),S=(N==null?void 0:N.workouts)||[];I.useEffect(()=>{N&&(console.log("🔍 WorkoutHistory: Received workout history data:",N),console.log("🔍 WorkoutHistory: Workouts array:",S),console.log("🔍 WorkoutHistory: Workouts count:",S.length),Le("WorkoutHistory",N),S.length>0?(console.log("🔍 WorkoutHistory: First workout structure:",S[0]),console.log("🔍 WorkoutHistory: Completed workouts:",S.filter(i=>i.completed)),console.log("🔍 WorkoutHistory: In-progress workouts:",S.filter(i=>!i.completed))):(console.log("⚠️ WorkoutHistory: No workouts found in response"),console.log("🔍 WorkoutHistory: Raw API response structure:",JSON.stringify(N,null,2))))},[N,S]);const v=async()=>{console.log("🚀 WorkoutHistory: Running complete diagnostic..."),await Be()};if(l)return e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-white",children:"Carregando histórico de treinos..."})})});if(D)return e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-red-400",children:"Erro ao carregar histórico de treinos"})})});const A=i=>{const r=Math.floor(i/60),o=i%60;return r>0?`${r}h ${o}min`:`${o}min`},R=i=>{m(i.id),k(!0)},y=(i,r)=>{a&&a({...i,edit:r})};return!S||S.length===0?e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ae,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]}),e.jsx("button",{onClick:v,className:"px-3 py-1 bg-red-500/20 text-red-400 rounded text-sm border border-red-500/30 hover:bg-red-500/30 transition-colors",title:"Executar diagnóstico completo",children:"🔍 DEBUG"})]}),e.jsxs("div",{className:"flex flex-col items-center justify-center py-12",children:[e.jsx("div",{className:"w-16 h-16 bg-gray-600/20 rounded-full flex items-center justify-center mb-4",children:e.jsx(Y,{className:"w-8 h-8 text-gray-400"})}),e.jsx("h3",{className:"text-lg font-medium text-white mb-2",children:"Nenhum treino encontrado"}),e.jsx("p",{className:"text-gray-400 text-center max-w-md",children:D?"Erro ao carregar histórico. Verifique sua conexão e tente novamente.":"Você ainda não completou nenhum treino. Comece um treino agora para ver seu histórico aqui!"}),D&&e.jsx("button",{onClick:()=>E(),className:"mt-4 px-4 py-2 bg-snapfit-green text-white rounded-lg hover:bg-snapfit-green/80 transition-colors",children:"Tentar novamente"})]})]}):x?e.jsxs(e.Fragment,{children:[e.jsx(Ns,{type:"workout",onReuseProtocol:y,onViewDetails:R}),h&&e.jsx(bs,{protocolId:h,type:"workout",isOpen:g,onClose:()=>{k(!1),m(null)},onReuseProtocol:y})]}):e.jsx(e.Fragment,{children:S&&e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20",children:[e.jsx("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4 mb-4 sm:mb-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(ae,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"})]})}),e.jsx("div",{className:"space-y-2 sm:space-y-4",children:S==null?void 0:S.map((i,r)=>e.jsxs("div",{className:"p-3 sm:p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3 flex-1",children:[e.jsx("div",{className:`p-2 rounded-full ${i.completed?"bg-snapfit-green/20 border border-snapfit-green/30":"bg-orange-400/10 border border-orange-400/30"}`,children:e.jsx(Y,{className:`w-5 h-5 ${i.completed?"text-snapfit-green":"text-orange-400"}`})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold text-white text-sm sm:text-base",children:i.workout_name||"Treino"}),e.jsx("p",{className:"text-gray-400 text-xs sm:text-sm",children:fs(i.date)})]})]}),e.jsxs("div",{className:"flex items-center gap-2 sm:gap-4 text-xs sm:text-sm",children:[e.jsxs("div",{className:"flex items-center gap-1 text-gray-300",children:[e.jsx(se,{className:"w-3 h-3 sm:w-4 sm:h-4"}),e.jsx("span",{children:A(i.duration||0)})]}),e.jsxs("div",{className:"flex items-center gap-1 text-gray-300",children:[e.jsx(ye,{className:"w-3 h-3 sm:w-4 sm:h-4"}),e.jsxs("span",{children:[i.total_calories||0," kcal"]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-4 text-xs sm:text-sm",children:[e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Volume"}),e.jsxs("div",{className:"font-semibold text-white",children:[i.total_weight||0,"kg"]})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Duração"}),e.jsx("div",{className:"font-semibold text-white",children:A(i.duration||0)})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Calorias"}),e.jsx("div",{className:"font-semibold text-white",children:i.total_calories||0})]}),e.jsxs("div",{className:"text-center p-2 bg-snapfit-gray rounded",children:[e.jsx("div",{className:"text-gray-400",children:"Status"}),e.jsx("div",{className:`font-semibold ${i.completed?"text-snapfit-green":"text-orange-400"}`,children:i.completed?"Concluído":"Em andamento"})]})]})]},i.session_id||r))})]})})}const Ss=({exercise:t})=>{const[s,a]=b.useState(!0);return e.jsx(e.Fragment,{children:e.jsx("div",{className:"pb-5",children:e.jsxs("div",{className:"p-3 sm:p-4 rounded-lg bg-snapfit-dark-gray border border-snapfit-gray/30",children:[e.jsxs("button",{onClick:()=>a(!s),className:"flex items-center justify-between w-full gap-2",children:[e.jsxs("div",{className:"flex items-center gap-2 min-w-0",children:[e.jsx("div",{className:"w-6 h-6 rounded-full flex items-center justify-center bg-snapfit-dark-gray text-gray-400 border border-snapfit-gray/30",children:e.jsx(qe,{className:`w-3 h-3 transition-transform ${s?"":"rotate-180"}`})}),e.jsx("h3",{className:"text-sm font-medium text-white truncate max-w-[180px]",children:t.name})]}),e.jsxs("div",{className:"text-xs text-gray-400 flex-shrink-0 bg-snapfit-dark-gray px-2 py-1 rounded-full border border-snapfit-gray/30",children:[t.sets,"x",t.reps," • ",t.rest_seconds,"s"]})]}),!s&&e.jsxs("div",{className:"mt-4 space-y-4 animate-fade-in",children:[e.jsxs("div",{className:"grid grid-cols-4 gap-2 text-center bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Séries"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.sets})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Reps"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.reps})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"RPE"}),e.jsx("div",{className:"text-sm font-medium text-snapfit-green",children:t.rpe})]}),e.jsxs("div",{className:"w-[60px] sm:w-[70px]",children:[e.jsx("div",{className:"text-[10px] text-gray-400 mb-1",children:"Descanso"}),e.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[t.rest_seconds,"s"]})]})]}),t.notes&&e.jsxs("div",{className:"flex items-start gap-2 p-2 sm:p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx(Ue,{className:"w-4 h-4 text-snapfit-green mt-0.5"}),e.jsx("p",{className:"text-xs text-gray-300",children:t.notes})]}),t.gif_url&&e.jsx("div",{className:"rounded-lg overflow-hidden border border-snapfit-green/20",children:e.jsx("img",{src:t.gif_url,alt:t.name,className:"w-full h-48 object-cover"})})]})]})})})};function As({protocolId:t,protocolName:s,startDate:a,splitInfo:x,frequency:h,objective:m,completedWorkouts:g,notes:k,workouts:N,selectedWorkout:l,onSelectWorkout:D,onGenerateNewProtocol:E,onEditNotes:S,workoutsDb:v,onDeleteProtocol:A}){var u;ve();const[R,y]=I.useState(!1),i=d=>{switch(d){case"hypertrophy":return"Hipertrofia";case"weight-loss":return"Emagrecimento";case"maintenance":return"Manutenção";case"strength":return"Força";default:return d}},[r,o]=I.useState(0);return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between mb-6",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(ae,{className:"w-5 h-5"})}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2 mb-1",children:[e.jsx("h2",{className:"text-lg font-semibold text-white",children:s||"Protocolo de Treino"}),e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold",children:"Protocolo Atual"})]}),e.jsxs("p",{className:"text-sm text-gray-400 mt-1",children:["Início: ",new Date(a).toLocaleDateString()]})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ke,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Divisão"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:x})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ae,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Frequência"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:h})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(oe,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Objetivo"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:i(m)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-gray/30",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2",children:[e.jsx(ke,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsx("div",{className:"text-sm sm:text-base font-bold text-snapfit-green",children:g})]})]}),k&&e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-snapfit-dark-gray flex items-center justify-center text-snapfit-green border border-snapfit-green/30",children:e.jsx(Fe,{className:"w-4 h-4"})}),e.jsx("h3",{className:"text-sm sm:text-base font-medium text-white",children:"Observações do Coach"})]}),e.jsx("button",{onClick:S,className:"text-sm text-snapfit-green hover:text-snapfit-green/80 hidden",children:"Editar"})]}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-400 whitespace-pre-wrap bg-snapfit-dark-gray p-3 rounded-lg border border-snapfit-gray/30",children:k})]}),e.jsxs("div",{className:"border-t border-snapfit-gray/30 pt-4 sm:pt-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-base font-medium text-white",children:"Treinos"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("button",{onClick:()=>{const d=v.length;r==0?o(d-1):o(r-1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(Me,{className:"w-4 h-4"})}),e.jsx("button",{onClick:()=>{const d=v.length;r==d-1?o(0):o(r+1)},className:"p-2 text-gray-400 hover:text-snapfit-green bg-snapfit-dark-gray hover:bg-snapfit-dark-gray rounded-full transition-colors hidden sm:block border border-snapfit-gray/30",children:e.jsx(ce,{className:"w-4 h-4"})})]})]}),e.jsx("div",{className:"flex gap-2 overflow-x-auto pb-4 scrollbar-hide",children:v.map((d,T)=>e.jsx("button",{onClick:()=>{o(T)},className:`flex-none px-3 py-1.5 text-xs sm:text-sm rounded-full transition-colors whitespace-nowrap ${r===T?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-gray/30"}`,children:d==null?void 0:d.name},T))})]})]}),e.jsxs("div",{className:"card p-6 space-y-6",children:[e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsx("div",{className:"inline-block px-3 py-1 bg-snapfit-green text-black rounded-full text-xs font-bold mb-2",children:"Treino"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:(u=v[r])==null?void 0:u.name}),e.jsxs("span",{className:"text-sm text-gray-400",children:[v[r].exercises.length," ",v[r].exercises.length>1?"exercícios":"exercício"]})]})}),e.jsx("div",{className:"space-y-4",children:v[r].exercises.map((d,T)=>e.jsx(Ss,{exercise:d},d.id))})]}),e.jsx("div",{className:"flex justify-end mt-4",children:e.jsxs("button",{className:"flex justify-between items-center gap-1 text-sm text-gray-400 hover:text-snapfit-green transition-colors p-2 rounded-full bg-snapfit-dark-gray border border-snapfit-gray/30",onClick:A,children:[e.jsx($e,{className:"w-4 h-4"})," ",e.jsx("span",{children:"Remover Protocolo"})]})}),e.jsx("div",{className:"flex justify-center mt-6",children:e.jsxs("button",{onClick:()=>{console.log("🎯 UnifiedWorkoutCard: Iniciar Treino button clicked"),console.log("🔍 UnifiedWorkoutCard: Opening WorkoutSelectionModal"),console.log("🔍 UnifiedWorkoutCard: Using CONSISTENT workflow with Add (+) button"),y(!0)},className:"flex items-center justify-center gap-2 px-6 py-3 w-full sm:w-auto bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95 font-bold","data-testid":"unified-workout-card-start-button",title:"Iniciar Treino - Abre seleção de treinos",children:[e.jsx("span",{children:"Iniciar Treino"}),e.jsx(ce,{className:"w-5 h-5"})]})}),R&&e.jsx(Ve,{onClose:()=>{console.log("🔍 UnifiedWorkoutCard: Closing WorkoutSelectionModal"),y(!1)}})]})}function Cs({onGenerateAI:t,onCreateManual:s,onImportFromCoach:a,onReadProtocol:x,onClose:h}){return e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-md w-full p-6 border border-snapfit-green/20",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-6",children:"Novo Protocolo de Treino"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("button",{onClick:t,className:`\r
            w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10`,children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Z,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Gerar com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Gerar protocolo personalizado baseado no seu perfil"})]})]}),e.jsxs("button",{onClick:s,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Fe,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Criar Manualmente"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Monte seu próprio protocolo selecionando exercícios"})]})]}),x&&e.jsxs("button",{onClick:x,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(Oe,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Ler seu protocolo atual com IA"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Faça upload de um PDF ou imagem do seu protocolo atual"})]})]}),e.jsxs("button",{onClick:a,className:"w-full flex items-center gap-3 p-4 bg-snapfit-dark-gray rounded-lg hover:bg-snapfit-dark-gray/80 transition-colors text-left group border border-snapfit-green/10",children:[e.jsx("div",{className:"p-3 bg-snapfit-green rounded-xl text-black group-hover:scale-110 transition-transform",children:e.jsx(be,{className:"w-6 h-6"})}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium text-white",children:"Importar do Coach"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Importar protocolo preparado pelo seu coach"})]})]})]}),e.jsx("button",{onClick:h,className:"w-full mt-6 px-4 py-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-full transition-colors border border-snapfit-green/20",children:"Cancelar"})]})})}async function Ps(){const t=Qe();if(!t.isAuthenticated)throw new Error("User must be authenticated to fetch protocols");try{const s=await fetch("https://api.mysnapfit.com.br/protocols/coach",{headers:{Authorization:`Bearer ${t.token}`}});if(!s.ok)throw new Error("Failed to fetch coach protocols");return s.json()}catch(s){throw console.error("Error fetching coach protocols:",s),s}}function Es({onImport:t,onCancel:s}){const[a,x]=I.useState([]),[h,m]=I.useState(!0),[g,k]=I.useState(null);return I.useEffect(()=>{async function N(){try{const l=await Ps();x(l)}catch(l){k(l instanceof Error?l.message:"Erro ao carregar protocolos")}finally{m(!1)}}N()},[]),h?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx(Ke,{className:"w-8 h-8 text-indigo-600 animate-spin"})}):g?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"text-red-600 mb-4",children:g}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(be,{className:"w-6 h-6 text-indigo-600"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:"Importar do Coach"})]}),a.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("p",{className:"text-gray-600 mb-4",children:"Nenhum protocolo disponível para importação."}),e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Voltar"})]}):e.jsxs("div",{className:"space-y-4",children:[a.map(N=>{var l,D,E;return e.jsxs("button",{onClick:()=>t(N),className:"w-full flex items-center justify-between p-4 bg-white rounded-lg hover:bg-gray-50 transition-colors text-left",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-gray-800",children:N.name}),e.jsxs("div",{className:"flex items-center gap-4 mt-2",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("img",{src:(l=N.coach)==null?void 0:l.photo,alt:(D=N.coach)==null?void 0:D.name,className:"w-6 h-6 rounded-full"}),e.jsx("span",{className:"text-sm text-gray-600",children:(E=N.coach)==null?void 0:E.name})]}),e.jsxs("div",{className:"flex items-center gap-2 text-sm text-gray-500",children:[e.jsx(ae,{className:"w-4 h-4"}),e.jsx("span",{children:new Date(N.createdAt).toLocaleDateString()})]})]})]}),e.jsx(be,{className:"w-5 h-5 text-gray-400"})]},N.id)}),e.jsx("div",{className:"flex justify-end",children:e.jsx("button",{onClick:s,className:"px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",children:"Cancelar"})})]})]})}function he({data:t,title:s,subtitle:a,height:x=200,showLegend:h=!0,type:m="bar",className:g="",animate:k=!0}){if(!t||!Array.isArray(t)||t.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${g}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Nenhum dado disponível"})]});const N=t.map(u=>u.value).filter(u=>typeof u=="number"&&!isNaN(u)&&isFinite(u));if(N.length===0)return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-4 border border-snapfit-green/20 ${g}`,children:[e.jsx("h3",{className:"text-lg font-bold text-white mb-4",children:s}),e.jsx("div",{className:"flex items-center justify-center h-32 text-gray-400",children:"Dados inválidos"})]});const l=Math.max(...N),D=Math.min(...N),E=N.reduce((u,d)=>u+d,0)/N.length,S=l-D;let v,A;if(S===0)l===0?(A=0,v=1):(A=Math.max(0,l*.8),v=l*1.2);else if(m==="bar")if(S<l*.2){const u=D*.8;if(A=Math.max(0,u),v=l*1.1,v-A<l*.3){const d=(l+D)/2,T=Math.max(l*.15,S*2);A=Math.max(0,d-T),v=d+T}}else{const u=Math.max(S*.05,l*.05);v=l+u,A=Math.max(0,D-u)}else{const u=S>0?S*.1:l*.1;v=l+u,A=Math.max(0,D-u)}const R=["#B9FF43","#66B100","#1A3201","#4CAF50","#8BC34A"],y=b.useRef(t.map(()=>0)),i=b.useRef(0);b.useEffect(()=>{y.current=t.map(()=>0)},[t]),b.useEffect(()=>{if(k&&m==="bar"){const u=()=>{let d=!1;const T=y.current.map((p,c)=>{if(!t[c]||typeof t[c].value!="number"||!isFinite(t[c].value))return p;const n=v-A,M=(n>0?(t[c].value-A)/n:0)*100;if(!isFinite(M))return p;const _=M-p;return Math.abs(_)>.5?(d=!0,p+_*.1):M});y.current=T,d&&(i.current=requestAnimationFrame(u))};return i.current=requestAnimationFrame(u),()=>{cancelAnimationFrame(i.current)}}},[t,v,A,k,m]);const r=()=>e.jsx("div",{className:"flex items-end h-full gap-1 sm:gap-2 px-1",children:t.map((u,d)=>{const T=typeof u.value=="number"&&!isNaN(u.value)&&isFinite(u.value)?u.value:0,p=v-A,c=p>0?(T-A)/p:0,n=k?y.current[d]??c*100:c*100,C=u.color||R[d%R.length];return e.jsxs("div",{className:"flex flex-col items-center flex-1",children:[e.jsx("div",{className:"relative w-full h-full flex flex-col justify-end",children:e.jsxs("div",{className:"w-full rounded-lg relative group overflow-hidden",style:{height:`${n}%`,minHeight:"4px",boxShadow:`0 0 10px ${C}40`},children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t",style:{backgroundImage:`linear-gradient(to top, ${C}, ${C}80)`}}),e.jsx("div",{className:"absolute -top-8 left-1/2 -translate-x-1/2 bg-snapfit-dark-gray text-white text-xs px-2 py-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap border border-snapfit-green/30",children:u.value})]})}),e.jsx("span",{className:"text-xs mt-2 text-gray-400 truncate max-w-full text-center",children:u.label})]},d)})}),o=()=>{const u=t.map((p,c)=>{const n=typeof p.value=="number"&&!isNaN(p.value)&&isFinite(p.value)?p.value:0,C=t.length>1?c/(t.length-1)*100:50,M=v-A,f=100-(M>0?(n-A)/M:0)*100;return{x:isFinite(C)?C:50,y:isFinite(f)?f:50,...p,value:n}});let d="",T="";return u.forEach((p,c)=>{if(c===0)d+=`M${p.x},${p.y}`,T+=`M${p.x},100 L${p.x},${p.y}`;else{const n=u[c-1],C=n.x+(p.x-n.x)/2,M=n.x+(p.x-n.x)/2;d+=` C${C},${n.y} ${M},${p.y} ${p.x},${p.y}`,T+=` L${p.x},${p.y}`}}),m==="area"&&(T+=` L${u[u.length-1].x},100 L${u[0].x},100 Z`),e.jsxs("div",{className:"h-full w-full px-2 pt-4 pb-6 relative",children:[e.jsxs("svg",{width:"100%",height:"100%",viewBox:"0 0 100 100",preserveAspectRatio:"none",children:[e.jsx("line",{x1:"0",y1:"0",x2:"100",y2:"0",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"25",x2:"100",y2:"25",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"50",x2:"100",y2:"50",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"75",x2:"100",y2:"75",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5",strokeDasharray:"2"}),e.jsx("line",{x1:"0",y1:"100",x2:"100",y2:"100",stroke:"rgba(255,255,255,0.1)",strokeWidth:"0.5"}),m==="area"&&e.jsx("path",{d:T,fill:"url(#areaGradient)",opacity:"0.2"}),e.jsx("path",{d,fill:"none",stroke:"#B9FF43",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),u.map((p,c)=>e.jsxs("g",{className:"group",children:[e.jsx("circle",{cx:p.x,cy:p.y,r:"4",fill:"transparent",stroke:"#B9FF4330",strokeWidth:"4",className:"transition-all duration-300 group-hover:stroke-opacity-50 group-hover:r-6"}),e.jsx("circle",{cx:p.x,cy:p.y,r:"3",fill:"#B9FF43",stroke:"#000",strokeWidth:"1",className:"transition-all duration-300 group-hover:r-4"}),e.jsx("circle",{cx:p.x,cy:p.y,r:"10",fill:"transparent",className:"cursor-pointer"}),e.jsxs("g",{className:"opacity-0 group-hover:opacity-100 transition-opacity",children:[e.jsx("rect",{x:p.x-25,y:p.y-30,width:"50",height:"22",rx:"11",fill:"#1E1E1E",stroke:"#B9FF4330",strokeWidth:"1"}),e.jsx("text",{x:p.x,y:p.y-16,textAnchor:"middle",fill:"#B9FF43",fontSize:"10",fontWeight:"bold",children:p.value})]})]},c)),e.jsx("defs",{children:e.jsxs("linearGradient",{id:"areaGradient",x1:"0",y1:"0",x2:"0",y2:"1",children:[e.jsx("stop",{offset:"0%",stopColor:"#B9FF43",stopOpacity:"0.8"}),e.jsx("stop",{offset:"100%",stopColor:"#B9FF43",stopOpacity:"0.1"})]})})]}),e.jsx("div",{className:"flex justify-between absolute bottom-0 left-2 right-2",children:t.map((p,c)=>e.jsx("div",{className:"text-xs text-gray-600 dark:text-gray-400 truncate max-w-[40px] text-center",children:p.label},c))})]})};return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl p-4 sm:p-5 ${g}`,children:[e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"text-white font-bold text-lg",children:s}),a&&e.jsx("p",{className:"text-gray-400 text-sm mt-1",children:a})]}),e.jsx("div",{style:{height:`${x}px`},className:"mt-2",children:m==="bar"?r():o()}),h&&e.jsx("div",{className:"mt-3 pt-3 border-t border-snapfit-gray/30",children:m==="line"||m==="bar"&&t.length>3?e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center text-xs",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsxs("span",{className:"text-gray-300",children:["Máx: ",l.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-400"}),e.jsxs("span",{className:"text-gray-300",children:["Mín: ",D.toFixed(0)]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-snapfit-green"}),e.jsxs("span",{className:"text-gray-300",children:["Média: ",E.toFixed(0)]})]}),m==="bar"&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-yellow-400"}),e.jsxs("span",{className:"text-gray-300",children:["Total: ",t.reduce((u,d)=>u+d.value,0).toFixed(0)]})]})]}):e.jsx("div",{className:"flex flex-wrap gap-3",children:t.map((u,d)=>{const T=u.color||R[d%R.length];return e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:T}}),e.jsxs("span",{className:"text-xs text-gray-400",children:[u.label,": ",u.value]})]},d)})})})]})}const Ge=()=>{const[t,s]=b.useState([]),[a,x]=b.useState([]),[h,m]=b.useState(!1),[g,k]=b.useState(!1),[N,l]=b.useState(null),D={id:"analysis_1",videoId:"video_1",exerciseName:"Agachamento",overallScore:78,analysisDate:new Date().toISOString(),postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75,improvementPoints:[{id:"imp_1",category:"posture",severity:"medium",description:"Joelhos tendendo a cair para dentro durante a descida",bodyPart:"Joelhos",timeInVideo:15},{id:"imp_2",category:"range_of_motion",severity:"low",description:"Pode descer um pouco mais para ativar melhor os glúteos",bodyPart:"Quadris",timeInVideo:18}],injuryRisks:[{id:"risk_1",riskLevel:"medium",bodyPart:"Joelhos",description:"Valgo dinâmico pode causar sobrecarga nos ligamentos",preventionTips:["Fortalecer glúteo médio","Melhorar mobilidade de tornozelo","Praticar agachamento com elástico"],relatedMovements:["Afundo","Step-up","Agachamento búlgaro"]}],correctionSuggestions:[{id:"corr_1",step:1,title:"Posicionamento dos pés",description:"Mantenha os pés ligeiramente mais afastados que a largura dos ombros",focusArea:"Base de apoio",difficulty:"easy"},{id:"corr_2",step:2,title:"Ativação do core",description:"Contraia o abdômen antes de iniciar o movimento",focusArea:"Estabilização",difficulty:"medium"}],preparatoryExercises:[{id:"prep_1",name:"Agachamento na parede",description:"Agachamento com apoio das costas na parede",sets:3,reps:"10-15",purpose:"Aprender o padrão de movimento correto"},{id:"prep_2",name:"Ponte de glúteo",description:"Fortalecimento dos glúteos em decúbito dorsal",sets:3,reps:"15-20",purpose:"Fortalecer glúteos para melhor estabilização"}]},E=async(y,i,r)=>{try{m(!0),l(null),await new Promise(u=>setTimeout(u,2e3));const o={id:`video_${Date.now()}`,exerciseName:i,angle:r,videoFile:y,uploadDate:new Date().toISOString(),duration:30,analysisStatus:"pending"};return s(u=>[o,...u]),await S(o.id),o}catch(o){throw l(o instanceof Error?o.message:"Erro no upload"),o}finally{m(!1)}},S=async y=>{try{k(!0),l(null),s(r=>r.map(o=>o.id===y?{...o,analysisStatus:"analyzing"}:o)),await new Promise(r=>setTimeout(r,5e3));const i={...D,id:`analysis_${Date.now()}`,videoId:y,analysisDate:new Date().toISOString()};return x(r=>[i,...r]),s(r=>r.map(o=>o.id===y?{...o,analysisStatus:"completed"}:o)),i}catch(i){throw l(i instanceof Error?i.message:"Erro na análise"),s(r=>r.map(o=>o.id===y?{...o,analysisStatus:"error"}:o)),i}finally{k(!1)}};return{videos:t,analyses:a,isUploading:h,isAnalyzing:g,error:N,uploadVideo:E,analyzeVideo:S,getEvolutionData:y=>({exerciseName:y,timeline:[{date:"2024-01-01",overallScore:65,postureScore:70,rangeOfMotionScore:60,stabilityScore:65,speedScore:65},{date:"2024-01-15",overallScore:72,postureScore:75,rangeOfMotionScore:68,stabilityScore:72,speedScore:73},{date:"2024-02-01",overallScore:78,postureScore:85,rangeOfMotionScore:72,stabilityScore:80,speedScore:75}],trend:"improving",targetReached:!1,nextGoal:"Atingir 85+ em todos os aspectos técnicos"}),getEnvironmentAnalysis:()=>({id:"env_1",spaceType:"home",availableSpace:"medium",equipment:["Halteres","Colchonete","Elásticos"],limitations:["Teto baixo","Vizinhos embaixo"],suggestedExercises:[{id:"ex_1",name:"Agachamento com halteres",category:"strength",difficulty:"intermediate",equipment:["Halteres"],spaceRequired:"minimal",duration:15,description:"Agachamento segurando halteres para aumentar a resistência",benefits:["Fortalece pernas","Melhora equilíbrio","Baixo impacto"]},{id:"ex_2",name:"Prancha com variações",category:"strength",difficulty:"beginner",equipment:["Colchonete"],spaceRequired:"minimal",duration:10,description:"Exercício isométrico para core",benefits:["Fortalece core","Melhora postura","Silencioso"]}],adaptations:["Use tênis com amortecimento para reduzir ruído","Prefira exercícios isométricos após 22h","Coloque colchonete extra para absorver impacto"]}),getRecoveryAnalysis:()=>({id:"rec_1",date:new Date().toISOString(),fatigueLevel:35,muscleGroups:[{name:"Pernas",fatigueLevel:60,recoveryTime:24,status:"recovering"},{name:"Peito",fatigueLevel:20,recoveryTime:8,status:"recovered"},{name:"Costas",fatigueLevel:40,recoveryTime:16,status:"recovering"}],sleepQuality:75,stressLevel:30,recommendations:[{id:"rec_rec_1",type:"active_recovery",priority:"medium",title:"Caminhada leve",description:"Faça uma caminhada de 20-30 minutos para acelerar a recuperação",duration:25},{id:"rec_rec_2",type:"stretching",priority:"high",title:"Alongamento de pernas",description:"Foque em quadríceps, isquiotibiais e panturrilhas",duration:15,instructions:["Alongue cada músculo por 30 segundos","Respire profundamente durante o alongamento","Não force além do confortável"]}]})}};function Ds(){const{videos:t,analyses:s,isUploading:a,error:x,uploadVideo:h}=Ge(),[m,g]=b.useState(""),[k,N]=b.useState("frontal"),[l,D]=b.useState(!1),[E,S]=b.useState(null),v=async r=>{if(!m){alert("Por favor, selecione um exercício primeiro");return}if(!r.type.includes("video")){alert("Por favor, envie apenas arquivos de vídeo");return}try{await h(r,m,k)}catch(o){console.error("Erro no upload:",o)}},A=r=>{r.preventDefault(),D(!1);const o=Array.from(r.dataTransfer.files);o.length>0&&v(o[0])},R=r=>{const o=r.target.files;o&&o.length>0&&v(o[0])},y=r=>{switch(r){case"pending":return e.jsx(se,{className:"w-4 h-4 text-yellow-400"});case"analyzing":return e.jsx("div",{className:"w-4 h-4 border-2 border-snapfit-green border-t-transparent rounded-full animate-spin"});case"completed":return e.jsx(ie,{className:"w-4 h-4 text-green-400"});case"error":return e.jsx(X,{className:"w-4 h-4 text-red-400"});default:return null}},i=r=>r>=80?"text-green-400":r>=60?"text-yellow-400":"text-red-400";return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(pe,{className:"w-5 h-5 text-snapfit-green"}),"Análise de Movimento"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Exercício"}),e.jsxs("select",{value:m,onChange:r=>g(r.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"",children:"Selecione um exercício"}),Je.map(r=>e.jsx("option",{value:r,children:r},r))]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Ângulo da Filmagem"}),e.jsxs("select",{value:k,onChange:r=>N(r.target.value),className:"w-full px-3 py-2 bg-snapfit-gray border border-snapfit-green/20 rounded-lg text-white focus:ring-1 focus:ring-snapfit-green",children:[e.jsx("option",{value:"frontal",children:"Frontal"}),e.jsx("option",{value:"lateral",children:"Lateral"}),e.jsx("option",{value:"posterior",children:"Posterior"})]})]})]}),e.jsx("div",{className:`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${l?"border-snapfit-green bg-snapfit-green/5":"border-gray-600 hover:border-snapfit-green/50"}`,onDrop:A,onDragOver:r=>r.preventDefault(),onDragEnter:()=>D(!0),onDragLeave:()=>D(!1),children:a?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto animate-pulse",children:e.jsx(Se,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Fazendo upload..."}),e.jsx("div",{className:"text-sm text-gray-400",children:"Aguarde um momento"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"w-12 h-12 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto",children:e.jsx(pe,{className:"w-6 h-6 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:"Arraste seu vídeo aqui ou clique para selecionar"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Suportamos MP4, MOV, AVI (máx. 100MB)"})]}),e.jsx("input",{type:"file",accept:"video/*",onChange:R,className:"hidden",id:"video-upload"}),e.jsxs("label",{htmlFor:"video-upload",className:"inline-flex items-center gap-2 px-4 py-2 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors cursor-pointer",children:[e.jsx(Se,{className:"w-4 h-4"}),"Selecionar Vídeo"]})]})}),e.jsxs("div",{className:"mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx("h4",{className:"text-blue-400 font-medium mb-2",children:"💡 Dicas para melhor análise:"}),e.jsxs("ul",{className:"text-sm text-gray-300 space-y-1",children:[e.jsx("li",{children:"• Filme em boa iluminação"}),e.jsx("li",{children:"• Mantenha o corpo inteiro no quadro"}),e.jsx("li",{children:"• Grave pelo menos 3-5 repetições"}),e.jsx("li",{children:"• Use roupas que permitam ver o movimento"})]})]})]}),s.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Análises Recentes"]}),e.jsx("div",{className:"space-y-4",children:s.slice(0,3).map(r=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:r.exerciseName}),e.jsx("div",{className:"text-sm text-gray-400",children:new Date(r.analysisDate).toLocaleDateString("pt-BR")})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`text-lg font-bold ${i(r.overallScore)}`,children:[r.overallScore,"/100"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Score Geral"})]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 mb-3",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${i(r.postureScore)}`,children:r.postureScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Postura"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${i(r.rangeOfMotionScore)}`,children:r.rangeOfMotionScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Amplitude"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${i(r.stabilityScore)}`,children:r.stabilityScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Estabilidade"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`text-sm font-medium ${i(r.speedScore)}`,children:r.speedScore}),e.jsx("div",{className:"text-xs text-gray-400",children:"Velocidade"})]})]}),e.jsx("button",{onClick:()=>S(E===r.id?null:r.id),className:"w-full px-4 py-2 text-sm text-snapfit-green border border-snapfit-green/30 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:E===r.id?"Ocultar Detalhes":"Ver Detalhes"}),E===r.id&&e.jsxs("div",{className:"mt-4 space-y-4 border-t border-gray-600 pt-4",children:[r.improvementPoints.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Pontos de Melhoria:"}),e.jsx("div",{className:"space-y-2",children:r.improvementPoints.map(o=>e.jsxs("div",{className:"flex items-start gap-2 text-sm",children:[e.jsx("span",{className:`mt-1 ${o.severity==="high"?"text-red-400":o.severity==="medium"?"text-orange-400":"text-yellow-400"}`,children:"•"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-gray-300",children:o.description}),e.jsxs("span",{className:"text-gray-500 ml-2",children:["(",o.bodyPart,")"]})]})]},o.id))})]}),r.correctionSuggestions.length>0&&e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium mb-2",children:"Sugestões de Correção:"}),e.jsx("div",{className:"space-y-2",children:r.correctionSuggestions.map(o=>e.jsxs("div",{className:"text-sm",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[o.step,". ",o.title]}),e.jsx("div",{className:"text-gray-300 ml-4",children:o.description})]},o.id))})]})]})]},r.id))})]}),t.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Vídeos em Processamento"}),e.jsx("div",{className:"space-y-3",children:t.filter(r=>r.analysisStatus!=="completed").map(r=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[y(r.analysisStatus),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:r.exerciseName}),e.jsxs("div",{className:"text-sm text-gray-400",children:["Ângulo: ",r.angle," • ",new Date(r.uploadDate).toLocaleTimeString("pt-BR")]})]})]}),e.jsx("div",{className:"text-sm text-gray-400",children:r.analysisStatus==="analyzing"?"Analisando...":r.analysisStatus==="pending"?"Na fila":"Erro"})]},r.id))})]}),x&&e.jsx("div",{className:"p-4 bg-red-500/10 border border-red-500/20 rounded-lg",children:e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro:"}),e.jsx("span",{children:x})]})})]})}function Ts({isOpen:t,onClose:s,workout:a}){var W,G;const[x,h]=b.useState(0),[m,g]=b.useState(!1),[k,N]=b.useState(60),[l,D]=b.useState(0),[E,S]=b.useState(!1),[v,A]=b.useState(null),[R,y]=b.useState(0),[i,r]=b.useState(!1),[o,u]=b.useState(!1),[d,T]=b.useState(0),[p,c]=b.useState({}),n=a==null?void 0:a.exercises[x];b.useEffect(()=>{if(a!=null&&a.exercises&&Object.keys(p).length===0){const P={};a.exercises.forEach(w=>{P[w.id]={sets:Array.from({length:w.sets||4},()=>({weight:0,reps:0,completed:!1}))}}),c(P)}},[a,p]),b.useEffect(()=>{t&&!v&&A(new Date)},[t]),b.useEffect(()=>{let P;return E&&l>0&&(P=setInterval(()=>{D(w=>w<=1?(S(!1),g(!1),0):w-1)},1e3)),()=>clearInterval(P)},[E,l]);const C=(P,w,F,$)=>{c(H=>({...H,[P]:{...H[P],sets:H[P].sets.map((z,K)=>K===w?{...z,[F]:typeof $=="string"?parseFloat($)||0:$}:z)}}))},M=(P,w)=>{c(F=>({...F,[P]:{...F[P],sets:F[P].sets.map(($,H)=>H===w?{...$,completed:!0}:$)}})),y(F=>F+5),T(k),r(!0),u(!0)},_=()=>n?p[n.id]:null,f=P=>{const w=Math.floor(P/60),F=P%60;return`${w}:${F.toString().padStart(2,"0")}`},B=()=>{if(!v)return"00:00";const w=Math.floor((new Date().getTime()-v.getTime())/1e3);return f(w)},Q=()=>x>=(a==null?void 0:a.exercises.length),O=()=>a!=null&&a.exercises.length?Math.round(x/a.exercises.length*100):0;return!t||!a?null:e.jsx("div",{className:"fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4 z-[9999]",children:e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:a.name}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-400 mt-1",children:[e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(se,{className:"w-4 h-4"}),B()]}),e.jsxs("span",{className:"flex items-center gap-1",children:[e.jsx(oe,{className:"w-4 h-4"}),R," kcal"]})]})]}),e.jsx("button",{onClick:s,className:"p-2 text-gray-400 hover:text-white transition-colors",children:e.jsx($e,{className:"w-6 h-6"})})]}),e.jsxs("div",{className:"p-6 border-b border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"Progresso do Treino"}),e.jsxs("span",{className:"text-sm text-snapfit-green font-medium",children:[O(),"%"]})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:"bg-snapfit-green h-2 rounded-full transition-all duration-300",style:{width:`${O()}%`}})})]}),Q()?e.jsxs("div",{className:"p-6 text-center",children:[e.jsx("div",{className:"w-16 h-16 bg-snapfit-green/20 rounded-full flex items-center justify-center mx-auto mb-4",children:e.jsx(me,{className:"w-8 h-8 text-snapfit-green"})}),e.jsx("h3",{className:"text-xl font-bold text-white mb-2",children:"Treino Concluído!"}),e.jsx("p",{className:"text-gray-400 mb-4",children:"Parabéns! Você completou seu treino adaptado."}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 mb-6",children:[e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-snapfit-green",children:B()}),e.jsx("div",{className:"text-sm text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"p-3 bg-snapfit-dark-gray rounded-lg",children:[e.jsx("div",{className:"text-lg font-bold text-orange-400",children:R}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]}),e.jsx("button",{onClick:s,className:"w-full px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:"Finalizar Treino"})]}):i?e.jsx("div",{className:"p-6",children:e.jsx("div",{className:"bg-blue-500/10 border border-blue-500/30 rounded-xl p-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center",children:e.jsx(se,{className:"text-white text-lg"})}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-blue-300",children:"Descanso entre séries"}),e.jsxs("div",{className:"text-xl font-bold text-blue-400",children:[Math.floor(d/60).toString().padStart(2,"0"),":",(d%60).toString().padStart(2,"0")]})]})]}),e.jsx("button",{onClick:()=>{r(!1),u(!1),T(0)},className:"px-4 py-2 bg-blue-500 text-white rounded-xl text-sm font-medium hover:bg-blue-600 transition-all hover:scale-105",children:"Pular"})]})})}):e.jsxs("div",{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-xl p-4 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("button",{onClick:()=>h(P=>Math.max(0,P-1)),disabled:x===0,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(Me,{className:"w-5 h-5"})}),e.jsxs("div",{className:"text-center flex-1",children:[e.jsxs("div",{className:"text-xs text-gray-400 mb-1",children:["Exercício ",x+1," de ",((W=a==null?void 0:a.exercises)==null?void 0:W.length)||0]}),e.jsx("h2",{className:"text-lg font-bold text-white",children:n==null?void 0:n.name})]}),e.jsx("button",{onClick:()=>h(P=>{var w;return Math.min((((w=a==null?void 0:a.exercises)==null?void 0:w.length)||1)-1,P+1)}),disabled:x>=(((G=a==null?void 0:a.exercises)==null?void 0:G.length)||1)-1,className:"p-2 rounded-lg bg-gray-700 text-gray-300 disabled:opacity-30 disabled:cursor-not-allowed hover:bg-gray-600 hover:text-white transition-all",children:e.jsx(ce,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2 overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-snapfit-green to-green-400 transition-all duration-500 ease-out rounded-full shadow-lg shadow-snapfit-green/30",style:{width:`${(()=>{var F;const P=Object.values(p).filter($=>$.sets.some(H=>H.completed)).length,w=((F=a==null?void 0:a.exercises)==null?void 0:F.length)||1;return P/w*100})()}%`}})})]}),e.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-400 bg-gray-800/50 rounded-lg px-4 py-2",children:[e.jsxs("span",{children:[(n==null?void 0:n.sets)||4," séries"]}),e.jsxs("span",{children:[(n==null?void 0:n.reps)||"8"," reps"]}),e.jsx("span",{className:"text-snapfit-green",children:n==null?void 0:n.muscle}),e.jsxs("span",{children:[k,"s descanso"]})]}),a&&a.exercises&&a.exercises[x]&&(()=>{const P=_();return P?e.jsx("div",{className:"space-y-3",children:P.sets.map((w,F)=>e.jsxs("div",{className:`rounded-xl p-4 border transition-all duration-300 ${w.completed?"bg-snapfit-green/10 border-snapfit-green/30 shadow-lg shadow-snapfit-green/10":"bg-gray-800/50 border-gray-700 hover:border-gray-600"}`,children:[e.jsx("div",{className:"flex items-center justify-between mb-4",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${w.completed?"bg-snapfit-green text-black":"bg-gray-700 text-gray-300"}`,children:F+1}),w.completed&&e.jsxs("div",{className:"flex items-center gap-1 text-snapfit-green",children:[e.jsx(me,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm font-medium",children:"Concluída"})]})]})}),e.jsxs("div",{className:"grid grid-cols-2 gap-3 items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-1 text-center",children:"Reps"}),e.jsx("input",{type:"number",inputMode:"numeric",pattern:"[0-9]*",value:w.reps||"",onChange:$=>C(n.id,F,"reps",$.target.value),disabled:w.completed,className:"w-full text-center text-xl font-bold bg-transparent border-none outline-none text-white disabled:opacity-50 focus:text-snapfit-green transition-colors",placeholder:(n==null?void 0:n.reps)||"0",min:"0"})]}),!w.completed&&e.jsxs("div",{className:"flex flex-col gap-1",children:[e.jsx("button",{onClick:()=>C(n.id,F,"reps",(w.reps||0)+1),className:"w-6 h-6 bg-gray-600 hover:bg-snapfit-green hover:text-black rounded text-xs font-bold transition-all",children:"+"}),e.jsx("button",{onClick:()=>C(n.id,F,"reps",Math.max(0,(w.reps||0)-1)),className:"w-6 h-6 bg-gray-600 hover:bg-red-500 rounded text-xs font-bold transition-all",children:"-"})]})]}),e.jsx("div",{className:"flex justify-center",children:e.jsx("button",{onClick:()=>M(n.id,F),disabled:w.completed||!w.reps,className:`w-10 h-10 rounded-full flex items-center justify-center transition-all duration-300 ${w.completed?"bg-snapfit-green text-black shadow-lg shadow-snapfit-green/30":w.reps?"bg-gray-700 text-gray-300 hover:bg-snapfit-green hover:text-black hover:shadow-lg hover:shadow-snapfit-green/30 hover:scale-105":"bg-gray-700 text-gray-500 cursor-not-allowed"}`,children:e.jsx(me,{className:"w-5 h-5"})})})]})]},F))}):null})(),(()=>{const P=(a==null?void 0:a.exercises.filter(w=>{const F=p[w.id];return!(F!=null&&F.sets.some($=>$.completed))}))||[];if(P.length>0){const F=(()=>{for(let $=x+1;$<(a==null?void 0:a.exercises.length);$++){const H=a.exercises[$],z=p[H.id];if(!(z!=null&&z.sets.some(K=>K.completed)))return $}for(let $=0;$<x;$++){const H=a.exercises[$],z=p[H.id];if(!(z!=null&&z.sets.some(K=>K.completed)))return $}return null})();if(F!==null)return e.jsxs("button",{onClick:()=>h(F),className:"w-full py-4 bg-gray-700 hover:bg-gray-600 text-white rounded-xl font-medium transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx("span",{children:"Próximo Exercício"}),e.jsx(ce,{className:"w-5 h-5"})]})}return P.length===0?e.jsxs("button",{onClick:s,className:"w-full py-4 bg-snapfit-green hover:bg-snapfit-green/90 text-black rounded-xl font-bold transition-all hover:scale-[1.02] flex items-center justify-center gap-2",children:[e.jsx(Ze,{className:"w-5 h-5"}),e.jsx("span",{children:"Finalizar Treino"})]}):null})()]})]})})}function Fs(){const{getEnvironmentAnalysis:t}=Ge(),[s,a]=b.useState("home"),[x,h]=b.useState("medium"),[m,g]=b.useState(["Nenhum (Peso Corporal)"]),[k,N]=b.useState([]),[l,D]=b.useState(""),[E,S]=b.useState(30),[v,A]=b.useState(!1),[R,y]=b.useState(!1),[i,r]=b.useState(null),o=t(),u=[{id:"workout_1",name:"Push A - Peito e Tríceps",muscleGroups:["Peito","Tríceps","Ombros"],exercises:[{name:"Supino Reto",sets:4,reps:"8-10",muscle:"Peito"},{name:"Supino Inclinado",sets:3,reps:"10-12",muscle:"Peito"},{name:"Desenvolvimento",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Tríceps Pulley",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Tríceps Francês",sets:3,reps:"10-12",muscle:"Tríceps"}]},{id:"workout_2",name:"Pull A - Costas e Bíceps",muscleGroups:["Costas","Bíceps"],exercises:[{name:"Puxada Frontal",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Curvada",sets:4,reps:"8-10",muscle:"Costas"},{name:"Remada Unilateral",sets:3,reps:"10-12",muscle:"Costas"},{name:"Pullover",sets:3,reps:"12-15",muscle:"Costas"},{name:"Rosca Direta",sets:4,reps:"10-12",muscle:"Bíceps"},{name:"Rosca Martelo",sets:3,reps:"10-12",muscle:"Bíceps"}]},{id:"workout_3",name:"Legs A - Quadríceps e Glúteos",muscleGroups:["Quadríceps","Glúteos","Panturrilhas"],exercises:[{name:"Agachamento Livre",sets:4,reps:"8-10",muscle:"Quadríceps"},{name:"Leg Press 45°",sets:4,reps:"12-15",muscle:"Quadríceps"},{name:"Agachamento Búlgaro",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Extensão de Pernas",sets:3,reps:"12-15",muscle:"Quadríceps"},{name:"Afundo",sets:3,reps:"10-12",muscle:"Glúteos"},{name:"Panturrilha em Pé",sets:4,reps:"15-20",muscle:"Panturrilhas"}]},{id:"workout_4",name:"Push B - Ombros e Tríceps",muscleGroups:["Ombros","Tríceps"],exercises:[{name:"Desenvolvimento com Halteres",sets:4,reps:"8-10",muscle:"Ombros"},{name:"Elevação Lateral",sets:4,reps:"12-15",muscle:"Ombros"},{name:"Elevação Posterior",sets:3,reps:"12-15",muscle:"Ombros"},{name:"Desenvolvimento Arnold",sets:3,reps:"10-12",muscle:"Ombros"},{name:"Tríceps Testa",sets:4,reps:"10-12",muscle:"Tríceps"},{name:"Mergulho",sets:3,reps:"8-12",muscle:"Tríceps"}]}],d=n=>{g(C=>{if(n==="Nenhum (Peso Corporal)")return["Nenhum (Peso Corporal)"];const M=C.filter(_=>_!=="Nenhum (Peso Corporal)");if(M.includes(n)){const _=M.filter(f=>f!==n);return _.length===0?["Nenhum (Peso Corporal)"]:_}else return[...M,n]})},T=n=>{N(C=>C.includes(n)?C.filter(M=>M!==n):[...C,n])},p=()=>{const n=u.find(_=>_.id===l);if(!n)return;const C=n.exercises.map(_=>{let f={..._};return!m.includes("Barra")&&_.name.includes("Supino")?(f.name="Flexão de Braço",f.reps="8-15"):!m.includes("Halteres")&&_.name.includes("Halteres")?(f.name=_.name.replace("com Halteres","com Peso Corporal"),f.reps="10-15"):!m.includes("Barra Fixa")&&_.name.includes("Puxada")&&(f.name="Remada Invertida",f.reps="6-12"),f}),M={id:`adapted_${Date.now()}`,name:`${n.name} (Adaptado)`,originalWorkout:n.name,muscleGroups:n.muscleGroups,environment:s,availableTime:E,estimatedCalories:Math.round(E*4.5),exercises:C,adaptations:["Exercícios adaptados para equipamentos disponíveis","Tempo ajustado para duração desejada","Intensidade mantida para mesmos grupos musculares"]};r(M),A(!0)},c=["Teto baixo","Vizinhos embaixo","Ruído limitado","Piso escorregadio"];return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(_e,{className:"w-5 h-5 text-snapfit-green"}),"Configuração do Ambiente"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Tipo de Espaço"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{value:"home",label:"Casa",icon:"🏠"},{value:"gym",label:"Academia",icon:"🏋️"},{value:"outdoor",label:"Ar Livre",icon:"🌳"},{value:"office",label:"Escritório",icon:"🏢"},{value:"hotel",label:"Hotel",icon:"🏨"}].map(n=>e.jsxs("button",{onClick:()=>a(n.value),className:`p-3 rounded-lg border transition-all duration-200 ${s===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-lg mb-1",children:n.icon}),e.jsx("div",{className:"text-xs",children:n.label})]},n.value))})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Espaço Disponível"}),e.jsx("div",{className:"grid grid-cols-3 gap-2",children:[{value:"small",label:"Pequeno",desc:"< 2m²"},{value:"medium",label:"Médio",desc:"2-6m²"},{value:"large",label:"Grande",desc:"> 6m²"}].map(n=>e.jsxs("button",{onClick:()=>h(n.value),className:`p-3 rounded-lg border transition-all duration-200 text-center ${x===n.value?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsx("div",{className:"text-sm font-medium",children:n.label}),e.jsx("div",{className:"text-xs opacity-75",children:n.desc})]},n.value))})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Equipamentos Disponíveis"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2",children:Xe.map(n=>e.jsx("button",{onClick:()=>d(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${m.includes(n)?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Limitações do Ambiente"}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:c.map(n=>e.jsx("button",{onClick:()=>T(n),className:`p-2 rounded-lg border transition-all duration-200 text-left ${k.includes(n)?"bg-orange-500/20 border-orange-500 text-orange-400":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-orange-500/50"}`,children:e.jsx("div",{className:"text-sm",children:n})},n))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Treino do Protocolo para Adaptar"}),e.jsx("div",{className:"space-y-3",children:u.map(n=>e.jsxs("button",{onClick:()=>D(n.id),className:`w-full p-4 rounded-lg border transition-all duration-200 text-left ${l===n.id?"bg-snapfit-green/20 border-snapfit-green text-snapfit-green":"bg-snapfit-gray border-gray-600 text-gray-300 hover:border-snapfit-green/50"}`,children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h4",{className:"font-medium",children:n.name}),e.jsxs("div",{className:"text-sm opacity-75",children:[n.exercises.length," exercícios"]})]}),e.jsx("div",{className:"flex flex-wrap gap-2",children:n.muscleGroups.map((C,M)=>e.jsx("span",{className:`px-2 py-1 text-xs rounded ${l===n.id?"bg-snapfit-green/30 text-snapfit-green":"bg-gray-700 text-gray-400"}`,children:C},M))})]},n.id))})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Tempo Disponível: ",E," minutos"]}),e.jsx("input",{type:"range",min:"10",max:"120",step:"5",value:E,onChange:n=>S(Number(n.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[e.jsx("span",{children:"10 min"}),e.jsx("span",{children:"60 min"}),e.jsx("span",{children:"120 min"})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:p,disabled:!l,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed",children:[e.jsx(ue,{className:"w-5 h-5"}),"Adaptar Treino para Ambiente"]}),!l&&e.jsx("p",{className:"text-xs text-gray-400 text-center mt-2",children:"Selecione um treino do protocolo para adaptar"})]})]}),v&&i&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(ue,{className:"w-5 h-5 text-snapfit-green"}),"Treino Adaptado Gerado"]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-gray-400",children:"Calorias Estimadas"}),e.jsxs("div",{className:"text-lg font-bold text-orange-400",children:[i.estimatedCalories," kcal"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:i.name}),e.jsx("div",{className:"text-xs text-gray-400",children:"Treino Adaptado"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-snapfit-green font-medium",children:[i.availableTime," min"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"Duração"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:i.exercises.length}),e.jsx("div",{className:"text-xs text-gray-400",children:"Exercícios"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-snapfit-green font-medium",children:s==="home"?"Casa":s==="gym"?"Academia":s==="outdoor"?"Ar Livre":s==="office"?"Escritório":"Hotel"}),e.jsx("div",{className:"text-xs text-gray-400",children:"Ambiente"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Grupos Musculares:"}),e.jsx("div",{className:"flex flex-wrap gap-2",children:i.muscleGroups.map((n,C)=>e.jsx("span",{className:"px-3 py-1 bg-snapfit-green/20 text-snapfit-green text-sm rounded-full border border-snapfit-green/30",children:n},C))})]}),e.jsxs("div",{className:"mb-6",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Exercícios Adaptados:"}),e.jsx("div",{className:"space-y-3",children:i.exercises.map((n,C)=>e.jsx("div",{className:"p-3 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center text-snapfit-green font-medium text-sm",children:C+1}),e.jsxs("div",{children:[e.jsx("h5",{className:"text-white font-medium",children:n.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[n.sets," séries × ",n.reps," reps"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("div",{className:"text-sm text-snapfit-green font-medium",children:n.muscle}),e.jsx("div",{className:"text-xs text-gray-400",children:"Músculo Alvo"})]})]})},C))})]}),e.jsxs("div",{className:"flex gap-3",children:[e.jsxs("button",{onClick:()=>y(!0),className:"flex-1 flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z",clipRule:"evenodd"})}),"Iniciar Treino Adaptado"]}),e.jsx("button",{onClick:()=>{A(!1),r(null)},className:"px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"Cancelar"})]})]}),v&&o.adaptations.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Ye,{className:"w-5 h-5 text-snapfit-green"}),"Adaptações Recomendadas"]}),e.jsx("div",{className:"space-y-3",children:o.adaptations.map((n,C)=>e.jsxs("div",{className:"flex items-start gap-3 p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg",children:[e.jsx(ue,{className:"w-4 h-4 text-blue-400 mt-0.5 flex-shrink-0"}),e.jsx("div",{className:"text-sm text-gray-300",children:n})]},C))})]}),e.jsx(Ts,{isOpen:R,onClose:()=>y(!1),workout:i})]})}const V={all:["ai","analysis"],recovery:t=>[...V.all,"recovery",t],movement:()=>[...V.all,"movement"],environment:()=>[...V.all,"environment"],history:t=>[...V.all,"history",t],insights:t=>[...V.all,"insights",t]};function Ms(t="week"){return de({queryKey:V.recovery(t),queryFn:async()=>{var s,a,x,h,m,g,k,N,l,D,E,S,v,A,R;console.log("🔄 useRecoveryAnalysis: Fetching recovery data for period:",t);try{const[y,i]=await Promise.all([q.get("wearables/data",{searchParams:{period:t,type:"recovery"}}),q.get("analytics/insights/ai",{searchParams:{category:"recovery",period:t}})]);console.log("📊 useRecoveryAnalysis: Wearables data:",y),console.log("🤖 useRecoveryAnalysis: AI insights:",i);const r=(y==null?void 0:y.data)||{},o=(i==null?void 0:i.data)||{},u={sleepData:{averageHours:((s=r.sleep)==null?void 0:s.average_duration)||0,quality:((a=r.sleep)==null?void 0:a.quality_score)||0,deepSleepPercentage:((x=r.sleep)==null?void 0:x.deep_sleep_percentage)||0,remSleepPercentage:((h=r.sleep)==null?void 0:h.rem_sleep_percentage)||0,sleepEfficiency:((m=r.sleep)==null?void 0:m.efficiency)||0},hrvData:{average:((g=r.hrv)==null?void 0:g.average)||0,trend:((k=r.hrv)==null?void 0:k.trend)||"stable",score:((N=r.hrv)==null?void 0:N.recovery_score)||0},stressData:{level:((l=r.stress)==null?void 0:l.average_level)||0,trend:((D=r.stress)==null?void 0:D.trend)||"stable"},energyData:{level:((E=r.energy)==null?void 0:E.average_level)||0,trend:((S=r.energy)==null?void 0:S.trend)||"stable"},heartRateData:{resting:((v=r.heart_rate)==null?void 0:v.resting_average)||0,max:((A=r.heart_rate)==null?void 0:A.max_recorded)||0,zones:((R=r.heart_rate)==null?void 0:R.zones)||{}},aiAnalysis:{recoveryStatus:o.recovery_status||"unknown",fatigueLevel:o.fatigue_level||0,recommendations:o.recommendations||[],insights:o.insights||[],confidence:o.confidence||0},overallScore:o.overall_recovery_score||0,rawWearables:r,rawInsights:o};return console.log("✅ useRecoveryAnalysis: Processed data:",u),u}catch(y){return console.warn("⚠️ useRecoveryAnalysis: Error fetching data, using fallback:",y),{sleepData:{averageHours:0,quality:0,deepSleepPercentage:0,remSleepPercentage:0,sleepEfficiency:0},hrvData:{average:0,trend:"stable",score:0},stressData:{level:0,trend:"stable"},energyData:{level:0,trend:"stable"},heartRateData:{resting:0,max:0,zones:{}},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[],insights:[],confidence:0},overallScore:0,rawWearables:{},rawInsights:{}}}},staleTime:1e3*60*10,refetchInterval:1e3*60*30,refetchIntervalInBackground:!0,refetchOnWindowFocus:!0,retry:2})}function $s(){return de({queryKey:V.movement(),queryFn:async()=>{console.log("🔄 useMovementAnalysisHistory: Fetching movement analysis data");try{const t=await q.get("analytics/movement/history");console.log("📊 useMovementAnalysisHistory: Response:",t);const s=(t==null?void 0:t.data)||{};return{analyses:s.analyses||[],videos:s.videos||[],totalAnalyses:s.total_analyses||0,averageScore:s.average_score||0,improvementTrend:s.improvement_trend||"stable",rawData:s}}catch(t){return console.warn("⚠️ useMovementAnalysisHistory: Error fetching data, using fallback:",t),{analyses:[],videos:[],totalAnalyses:0,averageScore:0,improvementTrend:"stable",rawData:{}}}},staleTime:1e3*60*5,refetchOnWindowFocus:!0,retry:2})}function Rs(){return de({queryKey:V.environment(),queryFn:async()=>{console.log("🔄 useEnvironmentAdaptation: Fetching environment data");try{const[t,s]=await Promise.all([q.get("users/protocols/workout/active"),q.get("analytics/environment/analysis")]);console.log("📊 useEnvironmentAdaptation: Protocol data:",t),console.log("🌍 useEnvironmentAdaptation: Environment data:",s);const a=(t==null?void 0:t.data)||{},x=(s==null?void 0:s.data)||{};return{activeProtocol:a,environmentFactors:x.factors||{},adaptationSuggestions:x.suggestions||[],availableEquipment:x.equipment||[],spaceAnalysis:x.space_analysis||{},weatherData:x.weather||{},rawProtocol:a,rawEnvironment:x}}catch(t){return console.warn("⚠️ useEnvironmentAdaptation: Error fetching data, using fallback:",t),{activeProtocol:{},environmentFactors:{},adaptationSuggestions:[],availableEquipment:[],spaceAnalysis:{},weatherData:{},rawProtocol:{},rawEnvironment:{}}}},staleTime:1e3*60*15,refetchOnWindowFocus:!0,retry:2})}function _s(t="month"){return de({queryKey:V.history(t),queryFn:async()=>{console.log("🔄 useAIAnalysisHistory: Fetching AI analysis history for period:",t);try{const s=await q.get("analytics/insights/ai/history",{searchParams:{period:t}});console.log("📊 useAIAnalysisHistory: Response:",s);const a=(s==null?void 0:s.data)||{};return{workoutAnalyses:a.workout_analyses||[],recoveryAnalyses:a.recovery_analyses||[],movementAnalyses:a.movement_analyses||[],environmentAdaptations:a.environment_adaptations||[],totalInsights:a.total_insights||0,trendsData:a.trends||{},rawData:a}}catch(s){return console.warn("⚠️ useAIAnalysisHistory: Error fetching data, using fallback:",s),{workoutAnalyses:[],recoveryAnalyses:[],movementAnalyses:[],environmentAdaptations:[],totalInsights:0,trendsData:{},rawData:{}}}},staleTime:1e3*60*10,refetchOnWindowFocus:!0,retry:2})}function Is(){const t=Re();return es({mutationFn:async({file:s,exerciseName:a,angle:x})=>{console.log("🔄 useUploadMovementVideo: Uploading video:",{exerciseName:a,angle:x});const h=new FormData;h.append("video",s),h.append("exerciseName",a),h.append("angle",x);const m=await q.post("analytics/movement/upload",h,{headers:{"Content-Type":"multipart/form-data"}});return console.log("✅ useUploadMovementVideo: Upload response:",m),m.data},onSuccess:()=>{t.invalidateQueries({queryKey:V.movement()})},onError:s=>{console.error("❌ useUploadMovementVideo: Upload failed:",s)}})}function He(t="week"){const s=Ms(t),a=$s(),x=Rs(),h=_s(t),m=Is();return{recovery:s.data,isLoadingRecovery:s.isLoading,recoveryError:s.error,movement:a.data,isLoadingMovement:a.isLoading,movementError:a.error,environment:x.data,isLoadingEnvironment:x.isLoading,environmentError:x.error,history:h.data,isLoadingHistory:h.isLoading,historyError:h.error,uploadVideo:m.mutate,isUploading:m.isPending,uploadError:m.error,isLoading:s.isLoading||a.isLoading||x.isLoading||h.isLoading,refetchAll:()=>{s.refetch(),a.refetch(),x.refetch(),h.refetch()}}}function Ls(){const t=Ne("week",["recovery-analysis"]),{recovery:s,isLoadingRecovery:a,recoveryError:x,refetchAll:h}=He(t.period),[m,g]=b.useState(7),[k,N]=b.useState(3),[l,D]=b.useState(7),[E,S]=b.useState(45),[v,A]=b.useState(65),[R,y]=b.useState(!1),[i,r]=b.useState(!1),[o,u]=b.useState(!1),d=s||{sleepData:{quality:0},stressData:{level:k*20},aiAnalysis:{recoveryStatus:"unknown",fatigueLevel:0,recommendations:[]},overallScore:0},p=(()=>{d.aiAnalysis.recoveryStatus;const f=d.aiAnalysis.fatigueLevel;return[{name:"Peito",group:"upper"},{name:"Costas",group:"upper"},{name:"Ombros",group:"upper"},{name:"Bíceps",group:"upper"},{name:"Tríceps",group:"upper"},{name:"Quadríceps",group:"lower"},{name:"Glúteos",group:"lower"},{name:"Panturrilha",group:"lower"}].map((Q,O)=>{const W=O%3*10;let G=f+W;G=Math.max(0,Math.min(100,G));let P="recovered",w=0;return G>70?(P="overworked",w=48):G>50?(P="fatigued",w=24):G>30?(P="recovering",w=12):(P="recovered",w=0),{name:Q.name,status:P,fatigueLevel:G,recoveryTime:w}})})(),c=()=>{u(!0),h()},n=f=>f<=30?"text-green-400":f<=60?"text-yellow-400":"text-red-400",C=f=>f<=30?"Baixa":f<=60?"Moderada":"Alta",M=f=>{switch(f){case"recovered":return"text-green-400";case"recovering":return"text-yellow-400";case"fatigued":return"text-orange-400";case"overworked":return"text-red-400";default:return"text-gray-400"}},_=f=>{switch(f){case"recovered":return e.jsx(ie,{className:"w-4 h-4 text-green-400"});case"recovering":return e.jsx(se,{className:"w-4 h-4 text-yellow-400"});case"fatigued":return e.jsx(X,{className:"w-4 h-4 text-orange-400"});case"overworked":return e.jsx(X,{className:"w-4 h-4 text-red-400"});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Recuperação Inteligente"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise de fadiga e sugestões de descanso baseadas em IA"})]}),e.jsx(we,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(te,{className:"w-5 h-5 text-snapfit-green"}),"Dados de Recuperação"]}),a&&e.jsxs("div",{className:"flex items-center gap-2 text-gray-400",children:[e.jsx(fe,{className:"w-4 h-4 animate-spin"}),e.jsx("span",{className:"text-sm",children:"Carregando..."})]})]}),e.jsxs("div",{className:"mb-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`w-3 h-3 rounded-full ${i?"bg-green-400":"bg-gray-400"}`}),e.jsx("span",{className:"text-white font-medium",children:i?"Wearable Conectado":"Wearable Desconectado"})]}),e.jsx("button",{onClick:()=>r(!i),className:`px-3 py-1 rounded text-sm transition-colors ${i?"bg-red-500/20 text-red-400 border border-red-500/30 hover:bg-red-500/30":"bg-snapfit-green/20 text-snapfit-green border border-snapfit-green/30 hover:bg-snapfit-green/30"}`,children:i?"Desconectar":"Conectar"})]}),i?e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm",children:[e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-blue-400 font-medium",children:[v," bpm"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"FC Repouso"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-green-400 font-medium",children:[E," ms"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"HRV"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-purple-400 font-medium",children:[m,"h 23m"]}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Sono Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-orange-400 font-medium",children:"85%"}),e.jsx("div",{className:"text-gray-400 text-xs",children:"Qualidade"})]})]}):e.jsx("div",{className:"text-center text-gray-400 text-sm",children:"Conecte seu wearable para dados automáticos de sono, frequência cardíaca e HRV"})]}),e.jsx("h4",{className:"text-white font-medium mb-4",children:i?"Dados Complementares":"Dados Manuais"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["Horas de Sono (última noite)",i&&e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"4",max:"12",value:m,onChange:f=>g(Number(f.target.value)),disabled:i,className:`w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider ${i?"opacity-50 cursor-not-allowed":""}`}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(Ae,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[m,"h"]}),e.jsx("span",{className:`text-sm ${m>=7?"text-green-400":m>=6?"text-yellow-400":"text-red-400"}`,children:m>=7?"Bom":m>=6?"Regular":"Insuficiente"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Estresse (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:k,onChange:f=>N(Number(f.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(X,{className:"w-4 h-4 text-orange-400"}),e.jsxs("span",{className:"text-white font-medium",children:[k,"/10"]}),e.jsx("span",{className:`text-sm ${k<=3?"text-green-400":k<=6?"text-yellow-400":"text-red-400"}`,children:k<=3?"Baixo":k<=6?"Moderado":"Alto"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Nível de Energia (1-10)"}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("input",{type:"range",min:"1",max:"10",value:l,onChange:f=>D(Number(f.target.value)),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider"}),e.jsxs("div",{className:"flex items-center justify-center gap-2",children:[e.jsx(ss,{className:"w-4 h-4 text-yellow-400"}),e.jsxs("span",{className:"text-white font-medium",children:[l,"/10"]}),e.jsx("span",{className:`text-sm ${l>=7?"text-green-400":l>=4?"text-yellow-400":"text-red-400"}`,children:l>=7?"Alto":l>=4?"Moderado":"Baixo"})]})]})]})]}),i&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mt-6",children:[e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["HRV (Variabilidade da FC)",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(te,{className:"w-4 h-4 text-green-400"}),e.jsxs("span",{className:"text-white font-medium",children:[E," ms"]}),e.jsx("span",{className:`text-sm ${E>=50?"text-green-400":E>=30?"text-yellow-400":"text-red-400"}`,children:E>=50?"Excelente":E>=30?"Bom":"Baixo"})]})})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:["FC de Repouso",e.jsx("span",{className:"ml-2 px-2 py-1 bg-green-500/20 text-green-400 text-xs rounded",children:"Auto"})]}),e.jsx("div",{className:"space-y-3",children:e.jsxs("div",{className:"flex items-center justify-center gap-2 p-3 bg-snapfit-gray rounded-lg",children:[e.jsx(te,{className:"w-4 h-4 text-blue-400"}),e.jsxs("span",{className:"text-white font-medium",children:[v," bpm"]}),e.jsx("span",{className:`text-sm ${v<=60?"text-green-400":v<=80?"text-yellow-400":"text-red-400"}`,children:v<=60?"Atlético":v<=80?"Normal":"Elevado"})]})})]})]}),e.jsxs("div",{className:"mt-6",children:[e.jsxs("button",{onClick:c,className:"w-full flex items-center justify-center gap-2 px-6 py-3 bg-snapfit-green text-black rounded-lg hover:bg-snapfit-green/90 transition-colors font-medium",children:[e.jsx(ee,{className:"w-5 h-5"}),i?"Analisar com Dados Completos":"Analisar Recuperação"]}),i&&e.jsx("p",{className:"text-xs text-green-400 text-center mt-2",children:"✓ Análise aprimorada com dados do wearable"})]})]}),(o||s)&&e.jsxs(e.Fragment,{children:[x&&e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center gap-2 text-red-400",children:[e.jsx(X,{className:"w-4 h-4"}),e.jsx("span",{className:"font-medium",children:"Erro ao carregar dados de recuperação"})]}),e.jsx("p",{className:"text-sm text-gray-400 mt-1",children:"Usando dados manuais como fallback"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-lg font-medium text-white flex items-center gap-2",children:[e.jsx(ts,{className:"w-5 h-5 text-snapfit-green"}),"Status Geral de Recuperação"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[_(d.aiAnalysis.recoveryStatus),e.jsx("span",{className:`text-sm font-medium ${M(d.aiAnalysis.recoveryStatus)}`,children:d.aiAnalysis.recoveryStatus==="recovered"?"Recuperado":d.aiAnalysis.recoveryStatus==="recovering"?"Recuperando":d.aiAnalysis.recoveryStatus==="fatigued"?"Fatigado":d.aiAnalysis.recoveryStatus==="overworked"?"Sobrecarregado":"Desconhecido"})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:`text-2xl font-bold ${n(d.aiAnalysis.fatigueLevel)}`,children:[Math.round(d.aiAnalysis.fatigueLevel),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Fadiga Geral"}),e.jsx("div",{className:`text-xs ${n(d.aiAnalysis.fatigueLevel)}`,children:C(d.aiAnalysis.fatigueLevel)})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-blue-400",children:[Math.round(d.sleepData.quality),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Qualidade do Sono"}),e.jsx("div",{className:"text-xs text-blue-400",children:d.sleepData.quality>=80?"Excelente":d.sleepData.quality>=60?"Boa":"Regular"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-orange-400",children:[Math.round(d.stressData.level),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Nível de Estresse"}),e.jsx("div",{className:"text-xs text-orange-400",children:d.stressData.level<=30?"Baixo":d.stressData.level<=60?"Moderado":"Alto"})]}),e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-snapfit-green",children:[Math.round(d.overallScore),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"Score Geral"}),e.jsx("div",{className:"text-xs text-snapfit-green",children:d.overallScore>=80?"Excelente":d.overallScore>=60?"Bom":"Regular"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Status dos Grupos Musculares:"}),e.jsx("div",{className:"space-y-3",children:p.map((f,B)=>e.jsxs("div",{className:"flex items-center justify-between p-3 bg-snapfit-gray rounded border border-gray-600",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[_(f.status),e.jsxs("div",{children:[e.jsx("div",{className:"text-white font-medium",children:f.name}),e.jsx("div",{className:"text-sm text-gray-400",children:f.recoveryTime>0?`Recuperação estimada: ${f.recoveryTime}h`:"Totalmente recuperado"})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:`font-medium ${n(f.fatigueLevel)}`,children:[Math.round(f.fatigueLevel),"%"]}),e.jsx("div",{className:`text-xs ${M(f.status)}`,children:f.status==="recovered"?"Recuperado":f.status==="recovering"?"Recuperando":f.status==="fatigued"?"Fatigado":"Sobrecarregado"})]})]},B))})]})]}),d.aiAnalysis.insights.length>0&&e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ee,{className:"w-5 h-5 text-snapfit-green"}),"Insights da IA"]}),e.jsx("div",{className:"space-y-4",children:d.aiAnalysis.insights.map((f,B)=>e.jsx("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center",children:f.type==="sleep"?e.jsx(Ae,{className:"w-4 h-4 text-blue-400"}):f.type==="hrv"?e.jsx(te,{className:"w-4 h-4 text-green-400"}):e.jsx(ee,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-white font-medium mb-1",children:f.title}),e.jsx("p",{className:"text-gray-300 text-sm",children:f.description}),e.jsxs("div",{className:"mt-2 text-xs text-gray-400",children:["Confiança: ",Math.round(f.confidence*100),"%"]})]})]})},B))})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("h3",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(ie,{className:"w-5 h-5 text-snapfit-green"}),"Recomendações Personalizadas"]}),e.jsx("div",{className:"space-y-4",children:d.aiAnalysis.recommendations.length>0?d.aiAnalysis.recommendations.map((f,B)=>e.jsx("div",{className:"p-4 rounded-lg border border-snapfit-green/20 bg-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("span",{className:"text-2xl",children:"💡"}),e.jsx("div",{className:"flex-1",children:e.jsx("p",{className:"text-gray-300",children:f})})]})},B)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(ie,{className:"w-12 h-12 mx-auto mb-3 text-green-400"}),e.jsx("p",{children:"Nenhuma recomendação específica no momento."}),e.jsx("p",{className:"text-sm",children:"Continue mantendo seus hábitos atuais!"})]})})]})]})]})}function Bs({onReuseProtocol:t}){var r,o,u,d,T,p;const[s,a]=b.useState("sessions"),x=Ne("month",["workout-history","ai-insights"]),{history:h,isLoadingHistory:m,historyError:g,refetchAll:k}=He(x.period),{data:N,isLoading:l,error:D,refetch:E}=Te({startDate:(r=x.customDates)==null?void 0:r.startDate,endDate:(o=x.customDates)==null?void 0:o.endDate,limit:50}),S=(N==null?void 0:N.workouts)||[],v=[],A=c=>{switch(c){case"active":return"bg-green-500/20 text-green-400 border-green-500/30";case"completed":return"bg-blue-500/20 text-blue-400 border-blue-500/30";case"archived":return"bg-gray-500/20 text-gray-400 border-gray-500/30";default:return"bg-gray-500/20 text-gray-400 border-gray-500/30"}},R=c=>{switch(c){case"active":return e.jsx(oe,{className:"w-4 h-4"});case"completed":return e.jsx(rs,{className:"w-4 h-4"});case"archived":return e.jsx(xe,{className:"w-4 h-4"});default:return e.jsx(oe,{className:"w-4 h-4"})}},y=c=>new Date(c).toLocaleDateString("pt-BR"),i=c=>{const[n,C,M]=c.split(":").map(Number);return n>0?`${n.toString().padStart(2,"0")}:${C.toString().padStart(2,"0")}h`:`${C.toString().padStart(2,"0")}:${M.toString().padStart(2,"0")}min`};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(je,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-bold text-white",children:"Histórico de Treinos"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Análise completa do seu progresso"})]})]}),e.jsx(we,{period:x.period,onPeriodChange:x.setPeriod,onCustomDateChange:x.setCustomDates,className:"w-full sm:w-auto"})]}),e.jsxs("div",{className:"bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20 p-6",children:[e.jsxs("div",{className:"flex bg-snapfit-gray rounded-lg p-1 border border-snapfit-green/20 mb-6",children:[e.jsxs("button",{onClick:()=>a("sessions"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="sessions"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(Y,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Treinos Realizados"})]}),e.jsxs("button",{onClick:()=>a("protocols"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="protocols"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(xe,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Protocolos"})]}),e.jsxs("button",{onClick:()=>a("ai-insights"),className:`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all duration-200 ${s==="ai-insights"?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(Z,{className:"w-4 h-4"}),e.jsx("span",{className:"text-sm",children:"Insights IA"})]})]}),e.jsx("div",{className:"space-y-4",children:s==="sessions"?S.map(c=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 rounded-full bg-snapfit-green/20 border border-snapfit-green/30",children:e.jsx(Y,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:c.workout_name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[c.muscle_groups," • ",y(c.date)]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-white font-medium",children:[c.exercise_count," exercícios"]}),e.jsx("div",{className:"text-sm text-snapfit-green",children:"Concluído"})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(se,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{children:i(c.workout_time)})]}),e.jsxs("div",{className:"flex items-center gap-1 bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[e.jsx(ye,{className:"w-3 h-3 text-orange-400"}),e.jsxs("span",{children:[c.total_kcal,"kcal"]})]}),e.jsx("div",{className:"px-2 py-1 rounded-full bg-snapfit-green/20 text-snapfit-green",children:c.type})]})]},c.id)):s==="protocols"?v.length>0?v.map(c=>e.jsxs("div",{className:"p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:`p-2 rounded-full border ${A(c.status)}`,children:R(c.status)}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:c.name}),e.jsxs("div",{className:"text-sm text-gray-400",children:[y(c.startDate),c.endDate&&` - ${y(c.endDate)}`]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs("button",{onClick:()=>console.log("Ver detalhes:",c),className:"flex items-center gap-1 px-2 py-1 bg-gray-500/20 text-gray-300 rounded text-xs border border-gray-500/30 hover:bg-gray-500/30 transition-colors",children:[e.jsx(Ce,{className:"w-3 h-3"}),"Ver"]}),t&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>t(c),className:"flex items-center gap-1 px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs border border-blue-500/30 hover:bg-blue-500/30 transition-colors",children:[e.jsx(fe,{className:"w-3 h-3"}),"Reutilizar"]}),e.jsxs("button",{onClick:()=>t({...c,edit:!0}),className:"flex items-center gap-1 px-2 py-1 bg-amber-500/20 text-amber-400 rounded text-xs border border-amber-500/30 hover:bg-amber-500/30 transition-colors",children:[e.jsx(as,{className:"w-3 h-3"}),"Editar"]})]})]})]}),e.jsxs("div",{className:"flex flex-wrap items-center gap-3 text-xs text-gray-400",children:[e.jsx("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:c.split}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[c.frequency,"x/semana"]}),e.jsxs("div",{className:"bg-snapfit-dark-gray px-2 py-1 rounded-full",children:[c.completedWorkouts," treinos"]}),e.jsx("div",{className:`px-2 py-1 rounded-full border ${A(c.status)}`,children:c.status==="active"?"Ativo":c.status==="completed"?"Concluído":"Arquivado"})]})]},c.id)):e.jsxs("div",{className:"text-center py-8 text-gray-400",children:[e.jsx(xe,{className:"w-12 h-12 mx-auto mb-3"}),e.jsx("p",{children:"Nenhum protocolo encontrado"}),e.jsx("p",{className:"text-sm",children:"Seus protocolos anteriores aparecerão aqui"})]}):e.jsx("div",{className:"space-y-6",children:m?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx(fe,{className:"w-8 h-8 animate-spin text-snapfit-green mr-3"}),e.jsx("span",{className:"text-gray-400",children:"Carregando insights da IA..."})]}):g?e.jsxs("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center",children:[e.jsx("div",{className:"text-red-400 mb-2",children:"Erro ao carregar insights"}),e.jsx("button",{onClick:()=>k(),className:"text-sm text-red-300 underline",children:"Tentar novamente"})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Z,{className:"w-5 h-5 text-purple-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Total de Insights"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:(h==null?void 0:h.totalInsights)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Y,{className:"w-5 h-5 text-blue-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Treino"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((u=h==null?void 0:h.workoutAnalyses)==null?void 0:u.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(ee,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Recuperação"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((d=h==null?void 0:h.recoveryAnalyses)==null?void 0:d.length)||0})]}),e.jsxs("div",{className:"bg-snapfit-gray p-4 rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-2",children:[e.jsx(Ce,{className:"w-5 h-5 text-orange-400"}),e.jsx("span",{className:"text-sm text-gray-400",children:"Análises de Movimento"})]}),e.jsx("div",{className:"text-2xl font-bold text-white",children:((T=h==null?void 0:h.movementAnalyses)==null?void 0:T.length)||0})]})]}),((p=h==null?void 0:h.workoutAnalyses)==null?void 0:p.length)>0&&e.jsxs("div",{className:"bg-snapfit-gray rounded-lg border border-snapfit-green/10 p-6",children:[e.jsxs("h4",{className:"text-lg font-medium text-white mb-4 flex items-center gap-2",children:[e.jsx(Z,{className:"w-5 h-5 text-purple-400"}),"Insights Recentes da IA"]}),e.jsx("div",{className:"space-y-4",children:h.workoutAnalyses.slice(0,5).map((c,n)=>e.jsx("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/5",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center",children:e.jsx(Z,{className:"w-4 h-4 text-purple-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h5",{className:"text-white font-medium mb-1",children:c.title||"Análise de Treino"}),e.jsx("p",{className:"text-gray-300 text-sm mb-2",children:c.description||"Análise detalhada do seu desempenho"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs text-gray-400",children:[e.jsx("span",{children:y(c.date||new Date().toISOString())}),e.jsxs("span",{children:["Confiança: ",Math.round((c.confidence||.8)*100),"%"]})]})]})]})},n))})]}),(!h||h.totalInsights===0)&&e.jsxs("div",{className:"text-center py-12 text-gray-400",children:[e.jsx(Z,{className:"w-16 h-16 mx-auto mb-4 text-gray-500"}),e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Nenhum insight disponível"}),e.jsx("p",{className:"text-sm",children:"Continue treinando para gerar insights personalizados da IA"})]})]})})}),e.jsxs("div",{className:"mt-6 p-4 bg-snapfit-gray rounded-lg border border-snapfit-green/10",children:[e.jsx("h4",{className:"text-white font-medium mb-3",children:"Estatísticas do Mês"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-snapfit-green",children:"12"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Treinos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:"15h"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Tempo Total"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-yellow-400",children:"4"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Protocolos"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:"3.2k"}),e.jsx("div",{className:"text-sm text-gray-400",children:"Kcal Queimadas"})]})]})]})]})]})}const Ee=[{id:"analysis",label:"Análise de Movimento",icon:pe,description:"IA analisa sua técnica e sugere melhorias"},{id:"environment",label:"Adaptação de Ambiente",icon:_e,description:"Exercícios personalizados para seu espaço"},{id:"recovery",label:"Recuperação Inteligente",icon:te,description:"Análise de fadiga e sugestões de descanso"},{id:"history",label:"Histórico",icon:je,description:"Seus treinos e análises anteriores"}];function Gs({onReuseProtocol:t}){var h;const[s,a]=b.useState("analysis"),x=()=>{switch(s){case"analysis":return e.jsx(Ds,{});case"environment":return e.jsx(Fs,{});case"recovery":return e.jsx(Ls,{});case"history":return e.jsx(Bs,{onReuseProtocol:t});default:return null}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h2",{className:"text-xl font-bold text-white mb-2",children:"Análise e Ferramentas IA"}),e.jsx("p",{className:"text-sm text-gray-400",children:"Tecnologia avançada para otimizar seus treinos e recuperação"})]}),e.jsx("div",{className:"flex bg-snapfit-dark-gray rounded-lg p-1 border border-snapfit-green/20 overflow-x-auto",children:Ee.map(m=>{const g=m.icon,k=m.id===s;return e.jsxs("button",{onClick:()=>a(m.id),className:`flex-1 min-w-0 flex items-center justify-center gap-2 px-3 py-3 rounded-md transition-all duration-200 whitespace-nowrap ${k?"bg-snapfit-green text-black font-medium":"text-gray-400 hover:text-white hover:bg-snapfit-green/10"}`,children:[e.jsx(g,{className:"w-4 h-4 flex-shrink-0"}),e.jsx("span",{className:"hidden sm:inline text-sm",children:m.label}),e.jsx("span",{className:"sm:hidden text-xs",children:m.id==="analysis"?"Análise":m.id==="environment"?"Ambiente":m.id==="recovery"?"Recuperação":"Histórico"})]},m.id)})}),e.jsx("div",{className:"text-center",children:e.jsx("p",{className:"text-sm text-gray-400",children:(h=Ee.find(m=>m.id===s))==null?void 0:h.description})}),e.jsx("div",{className:"min-h-[400px]",children:x()})]})}function Ks(){Re();const t=Ne("week",["workout-stats","workout-analytics"]),{protocol:s,stats:a,isLoadingProtocol:x,isLoadingStats:h,protocolError:m,statsError:g,removeProtocol:k,refetchStats:N}=vs(t.period),l=a||{weeklyWorkouts:{completed:0,planned:5},totalTime:0,totalCalories:0,totalVolume:0,currentStreak:0,bestStreak:0,weeklyProgressPercentage:0,protocolCompletionPercentage:0,totalWorkouts:0},D=I.useMemo(()=>l.chartData&&l.chartData.length>0?l.chartData.map(j=>({value:j.training_volume||0,label:j.label||"N/A",color:"#B9FF43"})):[{value:0,label:"Seg",color:"#B9FF43"},{value:0,label:"Ter",color:"#B9FF43"},{value:0,label:"Qua",color:"#B9FF43"},{value:0,label:"Qui",color:"#B9FF43"},{value:0,label:"Sex",color:"#B9FF43"},{value:0,label:"Sáb",color:"#B9FF43"},{value:0,label:"Dom",color:"#B9FF43"}],[l.chartData]),[E,S]=I.useState([{value:0,label:"Supino",color:"#B9FF43"},{value:0,label:"Agachamento",color:"#4CAF50"},{value:0,label:"Levantamento",color:"#FFC107"},{value:0,label:"Remada",color:"#FF5722"}]),[v,A]=I.useState(null),[R,y]=I.useState(!1),[i,r]=I.useState(!1),[o,u]=I.useState(null),[d,T]=I.useState(`Foco em tempo sob tensão nos exercícios de peito.
Aumentar carga progressivamente nos exercícios compostos.
Manter strict form em todos os exercícios.`),[p,c]=I.useState(!1),[n,C]=I.useState(!1),[M,_]=I.useState(null),[f,B]=I.useState(!1),[Q,O]=I.useState(null),W=ve(),G=ns(),P=ls(),w=is();I.useEffect(()=>{s!=null&&s.workouts&&s.workouts.length>0?(A(s.workouts[0]),console.log("✅ WorkoutPage: Primeiro treino selecionado:",s.workouts[0])):(console.log("⚠️ WorkoutPage: Nenhum treino disponível para seleção"),A(null))},[s]),I.useEffect(()=>{m&&(console.error("❌ React Query: Erro ao carregar protocolo:",m),os.error("Erro ao carregar protocolo de treino",{position:"bottom-right"}))},[m]);const F=async()=>{!window.confirm("Tem certeza que deseja remover este protocolo?")||!(s!=null&&s.id)||k(s.id)},$=async()=>{try{const j=await G.mutateAsync("workout");j&&j.id?(B(!0),O(()=>()=>c(!0))):c(!0)}catch(j){console.error("Error checking active protocol:",j),c(!0)}},H=async()=>{var j,le;try{const L=await G.mutateAsync("workout");console.log("🔍 Active protocol found:",L),L&&L.id?(console.log(`🏁 Finalizing active protocol with ID: ${L.id}`),await w.mutateAsync({protocolId:L.id.toString(),protocolType:"workout"}),console.log("✅ Active protocol finalized successfully")):console.log("ℹ️ No active protocol found to finalize"),Q&&(Q(),O(null)),B(!1)}catch(L){console.error("❌ Error in finalization flow:",L);const U=((le=(j=L==null?void 0:L.response)==null?void 0:j.data)==null?void 0:le.message)||(L==null?void 0:L.message)||"Erro desconhecido";U.includes("não encontrado")||U.includes("já finalizado")?(console.log("ℹ️ Protocol already finalized or not found, proceeding with creation"),Q&&(Q(),O(null))):console.error("❌ Unexpected error during finalization:",U),B(!1)}},z=()=>{W("/dashboard/workout/create-protocol/ai"),c(!1)},K=()=>{W("/dashboard/workout/create-protocol/manual"),c(!1)},We=()=>{W("/dashboard/workout/create-protocol/import"),c(!1)},re=async j=>{const le={name:j.name,type_id:j.type,objective:j.objective,started_at:j.startDate,frequency:j.frequency,split:j.split,goals:j.goals,workouts:j.workouts.map(L=>({exercises:L.exercises.map(U=>({exercise_id:U.exercise.id,sets:U.sets,reps:U.reps,rpe:U.rpe,rest_seconds:U.restTime,notes:U.notes}))})),supplements:j.supplements,general_notes:j.notes};console.log("Saving protocol:",j),r(!0),y(!0),P.mutate({protocolData:le,protocolType:"workout",shouldFinalizeActive:!1},{onSuccess:()=>{setTimeout(()=>{y(!1),r(!1),u(null)},1e3)},onError:()=>{y(!1),r(!1)}})},ne=()=>{u(null),y(!1),r(!1),c(!1)};return R?e.jsx(cs,{type:"workout",isSuccess:i,message:i?"Protocolo gerado com sucesso! 🎉":void 0}):e.jsxs(e.Fragment,{children:[x&&e.jsx(ds,{}),e.jsxs("div",{className:"space-y-6",children:[e.jsx("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:"Treino"}),p&&e.jsx(Cs,{onGenerateAI:z,onCreateManual:K,onImportFromCoach:We,onReadProtocol:()=>{W("/dashboard/workout/create-protocol/import"),c(!1)},onClose:ne}),o==="ai"&&e.jsx(Pe,{onProtocolGenerated:j=>{console.log("Protocolo de treino gerado pela IA:",j),re(j)},onClose:ne}),o==="manual"&&e.jsx(ms,{onSave:re,onCancel:ne}),o==="import"&&e.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center p-4 z-50",children:e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto p-6 border border-snapfit-green/20",children:e.jsx(Es,{onImport:re,onCancel:ne})})}),s&&!(s!=null&&s.has_protocol)&&!o&&e.jsx("div",{className:"card p-6",children:e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4",children:[e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:"Você não tem um protocolo de treino ativo"}),e.jsxs("button",{onClick:$,className:"btn-primary flex items-center justify-center gap-2 w-full sm:w-auto",children:[e.jsx(xs,{className:"w-5 h-5"}),"Novo Protocolo"]})]}),e.jsxs("div",{className:"mt-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-4",children:[e.jsx("h3",{className:"text-lg font-bold",children:"Estatísticas de Treino"}),e.jsx(we,{period:t.period,onPeriodChange:t.setPeriod,onCustomDateChange:t.setCustomDates,className:"w-full sm:w-auto"})]}),h?e.jsxs("div",{className:"flex items-center justify-center py-8",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-snapfit-green"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Carregando estatísticas..."})]}):g?e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 text-center",children:[e.jsx("p",{className:"text-red-600 text-sm",children:"Erro ao carregar estatísticas"}),e.jsx("button",{onClick:()=>N(),className:"mt-2 text-red-700 underline text-xs",children:"Tentar novamente"})]}):e.jsx("div",{className:"mobile-scroll-container",children:e.jsxs("div",{className:"mobile-scroll-content",children:[e.jsx(J,{title:"Treinos Semana",value:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned}`,icon:e.jsx(Y,{className:"animate-pulse-slow"}),change:l.weeklyWorkouts.completed>0?10:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(J,{title:"Tempo Total",value:`${l.totalTime} min`,icon:e.jsx(gs,{className:"animate-pulse-slow"}),change:l.totalTime>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(J,{title:"Calorias",value:`${l.totalCalories} kcal`,icon:e.jsx(ye,{className:"animate-pulse-slow"}),change:l.totalCalories>0?8:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(J,{title:"Volume",value:`${l.totalVolume} kg`,icon:e.jsx(us,{className:"animate-pulse-slow"}),change:l.totalVolume>0?15:0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(J,{title:"Sequência Atual",value:`${l.currentStreak} dias`,icon:e.jsx(ee,{className:"animate-pulse-slow"}),change:l.currentStreak>0?5:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(J,{title:"Melhor Sequência",value:`${l.bestStreak} dias`,icon:e.jsx(hs,{className:"animate-pulse-slow"}),change:l.bestStreak>0?3:0,className:"mobile-card stagger-item animate-slide-in-right"})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6",children:[e.jsx(he,{data:D,title:"Duração dos Treinos (min)",type:"bar",className:"animate-slide-in-left"}),e.jsx(he,{data:E,title:"Progresso de Força (kg)",type:"bar",className:"animate-slide-in-right"})]})]})}),(console.log("🔍 WorkoutPage: Verificando condições de renderização:",{protocolWorkouts:!!s,creationMode:o,hasProtocol:s==null?void 0:s.has_protocol,shouldRender:s&&!o&&(s==null?void 0:s.has_protocol)}),s&&!o&&(s==null?void 0:s.has_protocol))&&e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"card p-6 animate-slide-up",children:e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6",children:[e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Protocolo de Treino"}),e.jsx("div",{className:"flex items-center gap-2 px-3 py-1.5 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:e.jsx("span",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:(s==null?void 0:s.type)||"Hipertrofia"})})]}),e.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:s==null?void 0:s.objective})]}),e.jsxs("div",{className:"flex gap-2 mt-2 sm:mt-0",children:[e.jsxs("button",{className:"btn-secondary flex items-center justify-center gap-2 text-sm",onClick:()=>{console.log("🔄 Botão Editar Protocolo clicado. Protocolo ID:",s==null?void 0:s.id),s!=null&&s.id?window.location.pathname.includes("/professional/")?(console.log("🏃‍♂️ Navegando para rota profissional:",`/dashboard/professional/protocols/edit/${s.id}?type=workout`),W(`/dashboard/professional/protocols/edit/${s.id}?type=workout`)):(console.log("👤 Navegando para rota de usuário:",`/dashboard/workout/edit-protocol/${s.id}`),W(`/dashboard/workout/edit-protocol/${s.id}`)):(console.log("❌ Nenhum protocolo ID encontrado, navegando para criação"),W("/dashboard/workout/create-protocol/manual"))},children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"}),e.jsx("path",{d:"M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"})]}),"Editar Protocolo"]}),e.jsxs("button",{className:"btn-primary flex items-center justify-center gap-2 text-sm",onClick:$,children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("path",{d:"M12 5v14"}),e.jsx("path",{d:"M5 12h14"})]}),"Criar Protocolo"]})]})]})}),e.jsx(As,{protocolId:s==null?void 0:s.id,protocolName:s==null?void 0:s.name,startDate:s==null?void 0:s.started_at,splitInfo:s==null?void 0:s.split,frequency:`${s==null?void 0:s.frequency}x/semana`,objective:s==null?void 0:s.objective,completedWorkouts:(s==null?void 0:s.workouts_completed)||0,notes:s==null?void 0:s.notes,workouts:(s==null?void 0:s.workouts)||[],workoutsDb:s==null?void 0:s.workouts,selectedWorkout:v,onSelectWorkout:A,onGenerateNewProtocol:$,onEditNotes:()=>{console.log("Editing protocol notes...")},onDeleteProtocol:F}),e.jsxs("div",{className:"card p-6 animate-slide-up",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6",children:[e.jsx("h3",{className:"text-lg font-bold text-gray-800 dark:text-white",children:"Estatísticas do Protocolo"}),e.jsxs("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Progresso: ",Math.round(l.protocolCompletionPercentage),"% completo"]})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4 mb-6",children:[e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Frequência"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:`${(s==null?void 0:s.frequency)||0}x/semana`})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Divisão"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:(s==null?void 0:s.split)||"N/A"})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total de Treinos"}),e.jsx("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:l.totalWorkouts})]}),e.jsxs("div",{className:"bg-gray-100 dark:bg-gray-800 p-3 rounded-xl",children:[e.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Sequência Atual"}),e.jsxs("div",{className:"text-base font-bold text-gray-800 dark:text-white",children:[l.currentStreak," dias"]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[e.jsx(he,{data:D,title:"Duração dos Treinos (min)",type:"line",height:180,className:"animate-slide-in-left"}),e.jsxs("div",{className:"card-glass p-4",children:[e.jsx("h4",{className:"text-base font-bold mb-3",children:"Progresso"}),e.jsxs("div",{className:"flex justify-around gap-4",children:[e.jsx(ge,{value:l.weeklyWorkouts.completed,max:l.weeklyWorkouts.planned,label:"Esta Semana",sublabel:`${l.weeklyWorkouts.completed}/${l.weeklyWorkouts.planned} treinos`,color:"#B9FF43",size:90}),e.jsx(ge,{value:l.weeklyProgressPercentage,max:100,label:"Meta Semanal",sublabel:`${Math.round(l.weeklyProgressPercentage)}% atingido`,color:"#4CAF50",size:90}),e.jsx(ge,{value:l.protocolCompletionPercentage,max:100,label:"Protocolo",sublabel:`${Math.round(l.protocolCompletionPercentage)}% completo`,color:"#FF9800",size:90})]})]})]})]}),n&&e.jsx(Pe,{initialMode:M,onProtocolGenerated:j=>{console.log("Protocolo gerado pela IA:",j),re(j),C(!1),_(null)},onClose:()=>{C(!1),_(null)}}),e.jsx(Gs,{onReuseProtocol:j=>{j.edit?(console.log("Editando e usando protocolo:",j),alert(`Editando e usando protocolo: ${j.name||j.workout_name}`)):(console.log("Reutilizando protocolo:",j),alert(`Protocolo ${j.name||j.workout_name} reutilizado com sucesso!`))}})]}),!o&&e.jsx("div",{className:"space-y-6",children:e.jsx(ks,{showProtocolHistory:!0,onReuseProtocol:j=>{j.edit?(console.log("Editando e usando protocolo:",j),alert(`Editando e usando protocolo: ${j.name||j.workout_name}`)):(console.log("Reutilizando protocolo:",j),alert(`Protocolo ${j.name||j.workout_name} reutilizado com sucesso!`))}})})]}),e.jsx(ps,{isOpen:f,onClose:()=>{B(!1),O(null)},onConfirm:H,title:"Protocolo Ativo Encontrado",message:"Ao criar um novo, o protocolo atual será finalizado. Deseja continuar?",confirmText:"Sim, Continuar",cancelText:"Cancelar",type:"warning",isLoading:G.isPending||P.isPending||w.isPending})]})}export{Ks as WorkoutPage};
