import{j as t}from"./index-yuwXvJOX.js";function y({value:c,max:l,size:e=120,strokeWidth:r=8,color:h="#B9FF43",backgroundColor:f="rgba(229, 231, 235, 0.3)",label:a,sublabel:n,centerText:s,showTarget:o=!0,animate:d=!0,className:g=""}){const i=(e-r)/2,x=i*2*Math.PI,m=Math.min(1,Math.max(0,c/l)),j=x-m*x,u=Math.round(m*100);return t.jsxs("div",{className:"flex flex-col items-center",children:[t.jsxs("div",{className:`relative ${g}`,style:{width:e,height:e},children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 dark:from-white/5 to-transparent rounded-full"}),t.jsxs("svg",{className:`transform -rotate-90 ${d?"transition-all duration-1000 ease-out-expo":""}`,style:{width:e,height:e},children:[t.jsx("circle",{strokeWidth:r,stroke:f,fill:"transparent",r:i,cx:e/2,cy:e/2}),t.jsx("circle",{className:`${d?"transition-all duration-1000 ease-out-expo":""} drop-shadow-md`,strokeWidth:r,stroke:h,fill:"transparent",r:i,cx:e/2,cy:e/2,strokeLinecap:"round",strokeDasharray:x,strokeDashoffset:j||0})]}),t.jsx("div",{className:"absolute inset-0 flex flex-col items-center justify-center text-center",children:s?typeof s=="string"?t.jsx("div",{className:"text-xl sm:text-2xl font-bold text-gray-800 dark:text-white",children:s}):s:t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"text-lg sm:text-xl font-bold text-gray-800 dark:text-white",children:c}),o&&t.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["/",l]}),t.jsxs("div",{className:"text-sm font-medium text-snapfit-green",children:[u,"%"]})]})})]}),(a||n)&&t.jsxs("div",{className:"mt-2 text-center",children:[a&&t.jsx("div",{className:"text-sm sm:text-base font-medium text-gray-800 dark:text-white",children:a}),n&&t.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:o&&t.jsx("span",{children:n})})]})]})}export{y as C};
