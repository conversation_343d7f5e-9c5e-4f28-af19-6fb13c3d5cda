import{c as Se,R as y,u as pe,j as e,L as fe,S as M,B as de,U as Ce,T as Ee,C as De,a as O,y as W,b as _,e as Ae,d as be,P as je,f as ye,r as Q,g as G,h as V,i as ve,E as Be,k as Le,l as Me,A as _e,m as ce,n as me,Z as xe,o as ge,p as Pe,q as He,F as Re,s as Ne,D as $e,M as qe,t as We,v as Te,w as Ie,x as Oe,z as ze,G as Ke,H as Ue,I as Qe,J as Ge,N as Ve,W as Xe,K as Ze,O as Je,Q as Ye,V as es,X as ss,Y as ts,_ as as,$ as ue,a0 as rs,a1 as he,a2 as os,a3 as ns}from"./index-yuwXvJOX.js";import{B as ls}from"./BadgetComingSoon-DKcNAu7i.js";import{S as T}from"./StatCard-DAJ4AKMo.js";import{C as I}from"./CircularProgress-DqwHJlC_.js";import{u as is}from"./useDashboardCache-C9cIDQs1.js";/**
 * @license lucide-react v0.487.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ds=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]],cs=Se("file-down",ds),ms=[{id:"front",label:"Frente",description:"Foto frontal em pé, braços levemente afastados"},{id:"back",label:"Costas",description:"Foto das costas em pé, braços levemente afastados"},{id:"side",label:"Lado",description:"Foto lateral em pé, braços junto ao corpo"}];function xs({onSubmit:x}){const[t,g]=y.useState("manual"),[d,n]=y.useState(""),[l,b]=y.useState(""),[i,o]=y.useState({front:{file:null,preview:null},back:{file:null,preview:null},side:{file:null,preview:null}}),[p,E]=y.useState(!1),[N,F]=y.useState(null),w=y.useRef({front:null,back:null,side:null}),[B,P]=y.useState(!1),A=pe(),r=(c,m)=>{var S;const h=(S=c.target.files)==null?void 0:S[0];if(h){o(D=>({...D,[m]:{file:h,preview:null}})),F(null);const C=new FileReader;C.onloadend=()=>{o(D=>({...D,[m]:{file:h,preview:C.result}}))},C.readAsDataURL(h)}},u=c=>{o(m=>({...m,[c]:{file:null,preview:null}})),F(null)},j=async()=>{if(!d||!i.front.file){F("Por favor, informe seu peso e adicione ao menos a foto frontal");return}E(!0),F(null);try{const m=await Ae(Number(d),180,i.front.file);b(m.toString())}catch(c){F("Não foi possível estimar o BF. Tente novamente ou insira manualmente."),console.error("BF estimation error:",c)}finally{E(!1)}},a=async c=>{if(c.preventDefault(),!d||!l){W.error("Por favor, informe seu peso e gordura corporal.",{position:"bottom-right"});return}try{P(!0);const m=new FormData;m.append("weight",d),m.append("bf",l),i.front.file&&m.append("front",i.front.file),i.back.file&&m.append("back",i.back.file),i.side.file&&m.append("side",i.side.file),console.log("FormData contents:"),console.log("weight:",d),console.log("bodyFat:",l),console.log("front photo:",i.front.file),console.log("back photo:",i.back.file),console.log("side photo:",i.side.file);for(let[C,D]of m.entries())console.log(C,D);const h=await _.postFormData("users/progress/evaluations",m);console.log(h),A.invalidateQueries({queryKey:["assessments"]}),A.invalidateQueries({queryKey:["progress","evaluations"]});const S=Object.entries(i).reduce((C,[D,R])=>(R.file&&(C[D]=R.file),C),{});x(Number(d),Number(l),S),n(""),b(""),o({front:{file:null,preview:null},back:{file:null,preview:null},side:{file:null,preview:null}}),F(null),W.success("Avaliação enviada com sucesso.",{position:"bottom-right"})}catch(m){console.error("Error submitting assessment:",m),W.error("Erro ao enviar avaliação. Tente novamente.",{position:"bottom-right"})}finally{P(!1)}};return e.jsxs(e.Fragment,{children:[B&&e.jsx(fe,{}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-3 sm:p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-6",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(M,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-white",children:"Nova Avaliação"})]}),e.jsxs("div",{className:"flex gap-2 mb-3",children:[e.jsxs("button",{type:"button",onClick:()=>g("manual"),className:`flex-1 flex items-center justify-center gap-1 px-2 py-1.5 rounded-full transition-colors text-[10px] sm:text-xs ${t==="manual"?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-green/20"}`,children:[e.jsx(M,{className:"w-3.5 h-3.5"}),e.jsx("span",{children:"Manual"})]}),e.jsxs("button",{type:"button",onClick:c=>{c.preventDefault()},className:`opacity-50 flex-1 flex items-center justify-center gap-1 px-2 py-1.5 rounded-full transition-colors text-[10px] sm:text-xs ${t==="ai"?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-green/20"}`,children:[e.jsx(de,{className:"w-3.5 h-3.5"}),e.jsx("span",{children:"Estimar com IA"})," ",e.jsx(ls,{})]})]}),e.jsxs("form",{onSubmit:a,className:"space-y-4",children:[e.jsxs("div",{className:`grid ${t==="manual"?"grid-cols-2":"grid-cols-1"} gap-4`,children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-xs font-medium text-gray-400",children:"Peso (kg)"}),e.jsx("input",{type:"number",step:"0.1",value:d,onChange:c=>n(c.target.value),className:"w-full p-1.5 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: 75.5",required:!0})]}),t==="manual"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{className:"block text-xs font-medium text-gray-400",children:"Gordura Corporal (%)"}),e.jsx("input",{type:"number",step:"0.1",value:l,onChange:c=>b(c.target.value),className:"w-full p-1.5 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: 15.5",required:!0})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-xs font-medium text-gray-400 mb-1.5",children:"Fotos (opcional)"}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-2",children:ms.map(c=>e.jsxs("div",{className:"space-y-2",children:[e.jsx("input",{type:"file",ref:m=>w.current[c.id]=m,onChange:m=>r(m,c.id),accept:"image/*",className:"hidden"}),e.jsxs("div",{className:"text-xs font-medium text-gray-400 flex items-center gap-1.5",children:[e.jsx(Ce,{className:"w-3.5 h-3.5 text-snapfit-green"}),c.label]}),i[c.id].preview?e.jsxs("div",{className:"relative",children:[e.jsx("img",{src:i[c.id].preview,alt:`Foto ${c.label}`,className:"w-full h-28 object-cover rounded-lg border border-snapfit-green/20"}),e.jsx("button",{type:"button",onClick:()=>u(c.id),className:"absolute top-1 right-1 p-1 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors",children:e.jsx(Ee,{className:"w-3.5 h-3.5"})})]}):e.jsxs("button",{type:"button",onClick:()=>{var m;return(m=w.current[c.id])==null?void 0:m.click()},className:"w-full h-28 border-2 border-dashed border-snapfit-green/30 rounded-lg flex flex-col items-center justify-center gap-1 hover:border-snapfit-green hover:bg-snapfit-green/10 transition-colors bg-snapfit-dark-gray",children:[e.jsx(De,{className:"w-5 h-5 text-snapfit-green"}),e.jsxs("div",{className:"text-center px-4",children:[e.jsx("span",{className:"text-xs text-gray-400 block",children:"Adicionar foto"}),e.jsx("span",{className:"text-[10px] text-gray-500 block mt-0.5",children:c.description})]})]})]},c.id))}),t==="ai"&&e.jsx("button",{type:"button",onClick:j,disabled:p||!d||!i.front.file,className:"w-full flex items-center justify-center gap-1 px-2 py-1.5 text-[10px] sm:text-xs bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-colors disabled:opacity-50",children:p?e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"w-3.5 h-3.5 animate-spin"}),e.jsx("span",{children:"Estimando..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"w-3.5 h-3.5"}),e.jsx("span",{children:"Estimar BF"})]})}),N&&e.jsx("div",{className:"p-2 text-xs text-red-400 bg-red-400/10 rounded-lg border border-red-400/30",children:N}),t==="ai"&&l&&e.jsxs("div",{className:"p-3 bg-snapfit-green/20 rounded-lg border border-snapfit-green/30",children:[e.jsxs("div",{className:"text-xs font-medium text-snapfit-green",children:["BF Estimado: ",l,"%"]}),e.jsx("p",{className:"text-[10px] text-gray-400 mt-1",children:"Estimativa baseada no seu peso, altura e foto."})]})]}),e.jsx("button",{type:"submit",className:"w-full px-2 py-1.5 text-[10px] sm:text-xs bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Salvar Avaliação"})]})]})]})}const gs=[{id:"shoulders",label:"Ombro",side:"single"},{id:"chest",label:"Tórax",side:"single"},{id:"waist",label:"Cintura",side:"single"},{id:"abdomen",label:"Abdômen",side:"single"},{id:"hips",label:"Quadril",side:"single"},{id:"biceps",label:"Braço",side:"both"},{id:"forearm",label:"Antebraço",side:"both"},{id:"thigh",label:"Coxa",side:"both"},{id:"calf",label:"Panturrilha",side:"both"}];function us({onSubmit:x}){const[t,g]=y.useState({}),[d,n]=y.useState(!1),l=pe(),b=async i=>{i.preventDefault(),n(!0);const o={shoulders:t.shoulders,chest:t.chest,waist:t.waist,abdomen:t.abdomen,hips:t.hips,biceps_right:t.bicepsRight,biceps_left:t.bicepsLeft,forearm_right:t.forearmRight,forearm_left:t.forearmLeft,thigh_right:t.thighRight,thigh_left:t.thighLeft,calf_right:t.calfRight,calf_left:t.calfLeft};try{const p=await _.post("users/progress/evaluations/measurements",o);console.log(p),l.invalidateQueries({queryKey:["assessments"]}),l.invalidateQueries({queryKey:["progress","evaluations"]}),W.success("Avaliação enviada com sucesso.",{position:"bottom-right"}),g({}),x(t)}catch(p){console.error("Error submitting assessment:",p),W.error("Erro ao enviar avaliação. Tente novamente.",{position:"bottom-right"})}finally{n(!1)}};return e.jsxs(e.Fragment,{children:[d&&e.jsx(fe,{}),e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-3 sm:p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(be,{className:"w-4 h-4 text-snapfit-green"})}),e.jsx("h3",{className:"text-xs sm:text-sm font-medium text-white",children:"Medidas Corporais"})]}),e.jsxs("form",{onSubmit:b,className:"space-y-3",children:[gs.map(i=>e.jsxs("div",{className:"space-y-1",children:[e.jsxs("label",{className:"block text-xs font-medium text-gray-400",children:[i.label," (cm)"]}),i.side==="both"?e.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[e.jsx("input",{type:"number",step:"0.1",value:t[`${i.id}Right`]||"",onChange:o=>g(p=>({...p,[`${i.id}Right`]:Number(o.target.value)})),className:"w-full p-1.5 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Direito",required:!0}),e.jsx("input",{type:"number",step:"0.1",value:t[`${i.id}Left`]||"",onChange:o=>g(p=>({...p,[`${i.id}Left`]:Number(o.target.value)})),className:"w-full p-1.5 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Esquerdo",required:!0})]}):e.jsx("input",{type:"number",step:"0.1",value:t[i.id]||"",onChange:o=>g(p=>({...p,[i.id]:Number(o.target.value)})),className:"w-full p-1.5 text-xs border border-snapfit-green/20 rounded-lg focus:ring-1 focus:ring-snapfit-green focus:border-snapfit-green bg-snapfit-dark-gray text-white",placeholder:"Ex: 90.5",required:!0})]},i.id)),e.jsx("button",{type:"submit",className:"w-full px-3 py-1.5 text-xs bg-snapfit-green text-black rounded-full hover:bg-snapfit-green/90 transition-colors shadow-lg hover:shadow-snapfit-green/50 active:scale-95",children:"Salvar Medidas"})]})]})]})}function hs({assessments:x,onAddBFAssessment:t,onAddMeasurements:g}){const[d,n]=y.useState("bf"),[l,b]=y.useState(!1),[i,o]=y.useState(!1);return e.jsx("div",{className:"space-y-6",children:l?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex gap-4",children:[e.jsxs("button",{onClick:()=>n("bf"),className:`flex items-center gap-2 px-4 py-2 rounded-full transition-colors ${d==="bf"?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-green/20"}`,children:[e.jsx(M,{className:"w-5 h-5"}),e.jsx("span",{children:"Peso e BF"})]}),e.jsxs("button",{onClick:()=>n("measurements"),className:`flex items-center gap-2 px-4 py-2 rounded-full transition-colors ${d==="measurements"?"bg-snapfit-green text-black":"bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-green/20"}`,children:[e.jsx(be,{className:"w-5 h-5"}),e.jsx("span",{children:"Medidas"})]})]}),e.jsxs("button",{onClick:()=>b(!1),className:"flex items-center gap-2 px-4 py-2 rounded-full bg-snapfit-dark-gray text-white hover:bg-snapfit-dark-gray/80 border border-snapfit-green/20",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),e.jsx("span",{children:"Cancelar"})]})]}),d==="bf"?e.jsx(xs,{onSubmit:(p,E,N)=>{o(new Date().toISOString()),b(!1)}}):e.jsx(us,{onSubmit:p=>{g(p),o(new Date().toISOString()),b(!1)}})]}):e.jsx("button",{onClick:()=>b(!0),className:"w-full bg-snapfit-gray rounded-xl shadow-lg p-3 sm:p-4 hover:shadow-xl transition-all duration-300 group border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(M,{className:"w-5 h-5 text-snapfit-green"})}),e.jsxs("div",{className:"text-left",children:[e.jsx("h3",{className:"text-base sm:text-lg font-medium text-white",children:"Registrar Avaliação Física"}),e.jsx("p",{className:"text-xs text-gray-400",children:"Acompanhe sua evolução registrando suas medidas"})]})]}),e.jsx("div",{className:"p-2 bg-snapfit-green/20 rounded-full border border-snapfit-green/30",children:e.jsx(je,{className:"w-5 h-5 text-snapfit-green"})})]})})})}function ps({weekData:x}){return e.jsx("div",{className:"border-t border-snapfit-green/20 pt-4",children:e.jsx("div",{className:"space-y-4",children:x.map(t=>e.jsxs("div",{className:"p-4 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/10",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("div",{className:"font-medium text-white",children:new Date(t.date).toLocaleDateString("pt-BR",{weekday:"long",day:"numeric",month:"long"})}),e.jsxs("div",{className:"text-sm text-snapfit-green",children:[t.completed,"/",t.total," refeições"]})]}),t.meals&&e.jsx("div",{className:"grid gap-2",children:t.meals.map(g=>e.jsxs("div",{className:`flex items-center justify-between p-3 rounded-md ${g.completed?"bg-snapfit-green/20 border border-snapfit-green/30":"bg-snapfit-gray border border-snapfit-green/10"}`,children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(ye,{className:"w-4 h-4 text-snapfit-green"}),e.jsx("span",{className:"text-sm text-gray-400",children:g.time}),e.jsx("span",{className:"text-sm font-medium text-white",children:g.name})]}),e.jsx("div",{className:"text-sm",children:g.completed?e.jsx("span",{className:"text-snapfit-green",children:"Realizada"}):e.jsx("span",{className:"text-red-400",children:"Não realizada"})})]},g.id))})]},t.date))})})}function U({className:x="",width:t="100%",height:g="1rem",rounded:d=!1}){return e.jsx("div",{className:`animate-pulse bg-gray-700/50 ${d?"rounded-full":"rounded"} ${x}`,style:{width:t,height:g}})}function fs({className:x="",showHeader:t=!0,lines:g=3}){return e.jsxs("div",{className:`bg-snapfit-gray rounded-xl shadow-lg p-6 border border-snapfit-green/20 ${x}`,children:[t&&e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx(U,{width:32,height:32,rounded:!0}),e.jsx(U,{width:"40%",height:"1.25rem"})]}),e.jsx("div",{className:"space-y-3",children:Array.from({length:g}).map((d,n)=>e.jsx(U,{width:n===g-1?"75%":"100%",height:"1rem"},n))})]})}function bs({children:x,className:t=""}){return e.jsxs("div",{className:`relative overflow-hidden ${t}`,children:[x,e.jsx("div",{className:"absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/10 to-transparent"})]})}const js=()=>{const x=["Dom","Seg","Ter","Qua","Qui","Sex","Sáb"],t=new Date().getDay();return x.map((g,d)=>{const n=d>t,l=d===t;if(n)return{day:g,completed:0,total:6,macrosHit:!1,caloriesHit:!1,dietFollowed:!1};const b=6,i=l?Math.floor(Math.random()*4)+2:Math.floor(Math.random()*2)===0?b:b-Math.floor(Math.random()*2),o=Math.random()>.3,p=Math.random()>.25;return{day:g,completed:i,total:b,macrosHit:o,caloriesHit:p,dietFollowed:i===b}})},ys=()=>{const[x,t]=y.useState(!1),{handleError:g}=Le(),d=G("week",["weekly-adherence"]),{data:n,isLoading:l,isError:b,refetch:i}=V({queryKey:ve("weekly-adherence",d.period,d.customStartDate,d.customEndDate),queryFn:async()=>{var r;console.log("🔄 WeeklyAdherence: Fetching adherence data with filters:",d.getApiParams());try{const u=await _.get("users/progress/weekly/consolidated");if(console.log("✅ WeeklyAdherence: Consolidated API response:",u),(r=u==null?void 0:u.data)!=null&&r.weekly){const j=u.data.weekly.map(a=>({day:a.day,completed:a.nutrition.completed,total:a.nutrition.total,macrosHit:a.nutrition.percentage>=80,caloriesHit:a.nutrition.percentage>=70,dietFollowed:a.nutrition.percentage>=90,workout:a.workout,water:a.water,sleep:a.sleep}));return{weekly:j,success_rate:u.data.summary.nutrition.success_rate,complete_meals:u.data.summary.nutrition.days_completed,perfect_days:j.filter(a=>a.dietFollowed&&a.macrosHit&&a.caloriesHit).length,summary:u.data.summary}}throw new Error("Invalid response format")}catch(u){console.warn("⚠️ WeeklyAdherence: API error, using mock data:",u);const j=js(),a=j.filter(h=>h.dietFollowed&&h.macrosHit&&h.caloriesHit).length,c=j.filter(h=>h.macrosHit&&h.caloriesHit).length,m=j.reduce((h,S)=>h+S.completed,0);return{weekly:j,success_rate:Math.round(c/j.length*100),complete_meals:m,perfect_days:a}}},...He.daily,retry:1,enabled:d.period!=="custom"||!!(d.customStartDate&&d.customEndDate)}),o=(n==null?void 0:n.weekly)||[],p=(n==null?void 0:n.success_rate)||0,E=(n==null?void 0:n.complete_meals)||0,N=(n==null?void 0:n.perfect_days)||0,F=b,w=n==null?void 0:n.summary,B=()=>o.length?(o.filter(u=>u.completed===u.total).length/o.length*100).toFixed(0):0,P=()=>{var c,m;const r={weekStart:((c=o[0])==null?void 0:c.day)||"N/A",weekEnd:((m=o[6])==null?void 0:m.day)||"N/A",successRate:B(),totalMeals:o.reduce((h,S)=>h+S.completed,0),perfectDays:o.filter(h=>h.completed===h.total).length,dailyProgress:o},u=new Blob([JSON.stringify(r,null,2)],{type:"application/json"}),j=URL.createObjectURL(u),a=document.createElement("a");a.href=j,a.download=`weekly-progress-${r.weekStart}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(j)},A=r=>{const u=new Date,j=new Date(u.setDate(u.getDate()-(u.getDay()===0?6:u.getDay()-1))),a=new Date(j);return a.setDate(a.getDate()+r),a>new Date};return l&&!o.length?e.jsx(bs,{children:e.jsx(fs,{showHeader:!0,lines:4})}):b?e.jsx(Be,{title:"Erro na Aderência Semanal",message:g(b,"carregamento da aderência semanal"),onRetry:()=>i()}):e.jsxs("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-6 space-y-6 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-snapfit-green/20 rounded-full flex items-center justify-center border border-snapfit-green/30",children:e.jsx(Me,{className:"w-5 h-5 text-snapfit-green"})}),e.jsx("h2",{className:"text-lg sm:text-xl font-bold text-white",children:"Progresso Semanal"})]}),F&&e.jsx(_e,{message:"Dados de exemplo"}),e.jsxs("div",{className:"flex items-center gap-3",children:[F&&e.jsx("div",{className:"text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded",children:"📊 Dados de exemplo"}),e.jsxs("div",{className:"text-xs sm:text-sm font-medium text-snapfit-green",children:[p,"% de sucesso"]}),e.jsx("button",{onClick:P,className:`\r
            hidden\r
            p-2 text-gray-400 hover:text-snapfit-green hover:bg-snapfit-green/10 rounded-lg transition-colors`,title:"Exportar dados",children:e.jsx(cs,{className:"w-5 h-5"})})]})]}),e.jsx("div",{className:"grid grid-cols-7 gap-2",children:o.map((r,u)=>{const j=A(u);let a={bgColor:"bg-snapfit-dark-gray",borderColor:"border-snapfit-green/10",icon:ye,iconColor:"text-gray-400",label:"Futuro"};j||(r.dietFollowed&&r.macrosHit&&r.caloriesHit?a={bgColor:"bg-snapfit-green/20",borderColor:"border-snapfit-green/40",icon:ce,iconColor:"text-snapfit-green",label:"Perfeito"}:r.macrosHit&&r.caloriesHit?a={bgColor:"bg-blue-500/20",borderColor:"border-blue-500/40",icon:me,iconColor:"text-blue-400",label:"Metas OK"}:r.completed/r.total>=.5?a={bgColor:"bg-yellow-500/20",borderColor:"border-yellow-500/40",icon:xe,iconColor:"text-yellow-400",label:"Parcial"}:a={bgColor:"bg-red-500/20",borderColor:"border-red-500/40",icon:ge,iconColor:"text-red-400",label:"Ruim"});const c=a.icon;return e.jsxs("div",{className:`p-3 rounded-lg ${a.bgColor} border ${a.borderColor} ${j?"opacity-50 cursor-not-allowed":"hover:scale-105 transition-transform"}`,title:`${r.day}: ${a.label}`,children:[e.jsx("div",{className:"text-[10px] sm:text-xs text-gray-400 mb-1",children:r.day}),e.jsx("div",{className:"flex justify-center",children:e.jsx(c,{className:`w-5 h-5 sm:w-6 sm:h-6 ${a.iconColor}`})}),!j&&e.jsxs("div",{className:"text-[10px] sm:text-xs text-center mt-1 font-medium text-white",children:[r.completed,"/",r.total]}),!j&&(r.macrosHit||r.caloriesHit)&&e.jsxs("div",{className:"flex justify-center gap-1 mt-1",children:[r.macrosHit&&e.jsx("div",{className:"w-1 h-1 bg-blue-400 rounded-full",title:"Macros OK"}),r.caloriesHit&&e.jsx("div",{className:"w-1 h-1 bg-green-400 rounded-full",title:"Calorias OK"})]})]},u)})}),e.jsxs("div",{className:"mt-4 p-3 bg-snapfit-dark-gray/50 rounded-lg border border-snapfit-green/10",children:[e.jsx("div",{className:"text-xs text-gray-400 mb-2",children:"Legenda:"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2 text-xs",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ce,{className:"w-3 h-3 text-snapfit-green"}),e.jsx("span",{className:"text-gray-300",children:"Perfeito"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(me,{className:"w-3 h-3 text-blue-400"}),e.jsx("span",{className:"text-gray-300",children:"Metas OK"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(xe,{className:"w-3 h-3 text-yellow-400"}),e.jsx("span",{className:"text-gray-300",children:"Parcial"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(ge,{className:"w-3 h-3 text-red-400"}),e.jsx("span",{className:"text-gray-300",children:"Ruim"})]})]}),e.jsxs("div",{className:"flex items-center gap-4 mt-2 text-xs text-gray-400",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-blue-400 rounded-full"}),e.jsx("span",{children:"Macros"})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"}),e.jsx("span",{children:"Calorias"})]})]})]}),w&&e.jsxs("div",{className:"mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-snapfit-green/10",children:[e.jsx("div",{className:"text-sm font-medium text-white mb-3",children:"Progresso Semanal"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-2",children:[e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Nutrição"}),e.jsxs("div",{className:"text-lg font-bold text-snapfit-green",children:[w.nutrition.success_rate,"%"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[w.nutrition.days_completed,"/7 dias"]})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-blue-500/20",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Treinos"}),e.jsxs("div",{className:"text-lg font-bold text-blue-400",children:[w.workout.success_rate,"%"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[w.workout.days_completed,"/7 dias"]})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-cyan-500/20",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Água"}),e.jsxs("div",{className:"text-lg font-bold text-cyan-400",children:[w.water.success_rate,"%"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[(w.water.total_consumed/1e3).toFixed(1),"L"]})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-purple-500/20",children:[e.jsx("div",{className:"text-xs text-gray-400",children:"Sono"}),e.jsxs("div",{className:"text-lg font-bold text-purple-400",children:[w.sleep.success_rate,"%"]}),e.jsxs("div",{className:"text-xs text-gray-500",children:[w.sleep.average_hours.toFixed(1),"h média"]})]})]})]}),e.jsx("div",{className:"mt-3 sm:mt-4 pt-3 sm:pt-4 border-t border-snapfit-green/10",children:e.jsxs("div",{className:"grid grid-cols-2 gap-2 sm:gap-4",children:[e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Refeições Completas"}),e.jsx("div",{className:"text-lg sm:text-xl font-bold text-snapfit-green",children:E.toLocaleString()})]}),e.jsxs("div",{className:"text-center p-3 bg-snapfit-dark-gray rounded-lg border border-snapfit-green/20",children:[e.jsx("div",{className:"text-xs sm:text-sm text-gray-400",children:"Dias Perfeitos"}),e.jsx("div",{className:"text-lg sm:text-xl font-bold text-snapfit-green",children:N.toLocaleString()})]})]})}),e.jsxs("div",{className:"hidden",children:[e.jsxs("button",{onClick:()=>t(!x),className:"w-full flex items-center justify-center gap-2 text-sm text-snapfit-green hover:text-snapfit-green/80 p-2 rounded-lg hover:bg-snapfit-green/10 transition-colors",children:[x?"Ocultar Histórico":"Ver Histórico Detalhado",e.jsx(Pe,{className:`w-4 h-4 transition-transform ${x?"rotate-90":""}`})]}),x&&e.jsx(ps,{weekData:o.map((r,u)=>({date:new Date(Date.now()-(6-u)*24*60*60*1e3).toISOString().split("T")[0],completed:r.completed,total:r.total,success:r.completed===r.total}))})]})]})},vs=Q.memo(ys);function Ns({streak:x}){const[t,g]=y.useState(null),d=async()=>{try{const n=await _.get("users/progress/nutritional/days_goal_sequence");g(n.data.days_goal_sequence)}catch(n){console.error("Erro ao buscar sequência de dias:",n)}};return y.useEffect(()=>{d()},[]),e.jsx("div",{className:"bg-snapfit-gray rounded-xl shadow-lg p-4 sm:p-6 border border-snapfit-green/20",children:e.jsxs("div",{className:"flex items-center justify-between gap-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-10 h-10 bg-orange-500/20 rounded-full flex items-center justify-center border border-orange-500/30",children:e.jsx(Re,{className:"w-5 h-5 sm:w-6 sm:h-6 text-orange-400"})}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-base sm:text-lg font-bold text-white",children:"Sequência"}),e.jsx("p",{className:"text-xs sm:text-sm text-gray-400",children:"Dias batendo as metas"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"text-lg sm:text-xl font-bold text-orange-400",children:t!==null?t:x}),e.jsx("div",{className:"ml-1 text-sm text-gray-400",children:"dias"})]})]})})}function ws(){const{dailyHydration:x,addWater:t,removeWater:g,isLoading:d}=Ne(),n=Math.min(100,x.current/x.goal*100),l=[100,200,300],b=async o=>{try{await t(o)}catch(p){console.error("Error adding water:",p)}},i=async o=>{if(!(x.current-o<0))try{await g(o)}catch(p){console.error("Error removing water:",p)}};return e.jsxs("div",{className:"bg-snapfit-gray rounded-xl p-4 border border-snapfit-green/20",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"w-8 h-8 bg-blue-400/10 rounded-full flex items-center justify-center border border-blue-400/30",children:e.jsx($e,{className:"w-5 h-5 text-blue-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-white",children:"Hidratação"}),e.jsxs("div",{className:"text-xs text-gray-400",children:[x.current,"ml / ",x.goal,"ml"]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-lg font-bold text-white",children:[Math.round(n),"%"]}),e.jsx("div",{className:"text-xs text-gray-400",children:"da meta"})]})]}),e.jsx("div",{className:"w-full bg-snapfit-dark-gray rounded-full h-2 mb-4",children:e.jsx("div",{className:`h-2 rounded-full transition-all duration-300 ${n>=100?"bg-snapfit-green":n>=75?"bg-blue-400":n>=50?"bg-yellow-400":"bg-red-400"}`,style:{width:`${Math.min(100,n)}%`}})}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex gap-2",children:l.map(o=>e.jsxs("button",{onClick:()=>b(o),disabled:d,className:"flex-1 flex items-center justify-center gap-1 p-2 bg-blue-500/10 hover:bg-blue-500/20 rounded-lg border border-blue-400/20 text-white text-xs disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:[e.jsx(je,{className:"w-3 h-3 text-blue-400"}),o,"ml"]},`add-${o}`))}),e.jsx("div",{className:"flex gap-2",children:l.map(o=>e.jsxs("button",{onClick:()=>i(o),disabled:x.current<o||d,className:"flex-1 flex items-center justify-center gap-1 p-2 bg-red-500/10 hover:bg-red-500/20 rounded-lg border border-red-400/20 text-white text-xs disabled:opacity-30 disabled:cursor-not-allowed transition-colors",children:[e.jsx(qe,{className:"w-3 h-3 text-red-400"}),o,"ml"]},`remove-${o}`))})]}),e.jsx("div",{className:"mt-3 text-center",children:n>=100?e.jsx("div",{className:"text-xs text-snapfit-green",children:"🎉 Meta atingida!"}):n>=75?e.jsx("div",{className:"text-xs text-blue-400",children:"💪 Quase lá!"}):n>=50?e.jsx("div",{className:"text-xs text-yellow-400",children:"⚡ Continue assim!"}):e.jsx("div",{className:"text-xs text-red-400",children:"💧 Beba mais água!"})})]})}function Ds(){var X,Z,J,Y,ee,se,te,ae,re,oe,ne;const x=We(),[t,g]=Q.useState("month"),[d,n]=Q.useState(null),l=G("month",["body-evolution","dashboard"]),[b,i]=y.useMemo(()=>{try{const s=localStorage.getItem("accessToken");return[s,!!s]}catch(s){return console.error("❌ Error accessing localStorage:",s),[null,!1]}},[]),{prefetchDashboardData:o,cleanupStaleCache:p,invalidateAnalytics:E}=is(),{data:N,isLoading:F,error:w}=Te(t),{data:B,isLoading:P,error:A}=Ie(t),{data:r,isLoading:u,error:j}=Oe(),{data:a,isLoading:c,error:m}=ze(t),{data:h,isLoading:S,error:C,refetch:D}=V({queryKey:["body-evolution",l.period,l.customStartDate,l.customEndDate],queryFn:async()=>{console.log("🔄 Fetching body evolution data for period:",l.period);try{l.setLoading(!0),l.setError(null);const s=l.getApiParams();console.log("📊 Body evolution API params:",s);let f;try{console.log("🌐 Trying dashboard endpoint: dashboard/body-composition"),f=await _.get("dashboard/body-composition",{searchParams:s}),console.log("✅ Dashboard endpoint success:",f)}catch{console.log("⚠️ Dashboard endpoint failed, trying users endpoint: users/progress/chart/weight_fat"),f=await _.get("users/progress/chart/weight_fat",{searchParams:s}),console.log("✅ Users endpoint success:",f)}let v=f;return f!=null&&f.data&&(v=f.data),(f==null?void 0:f.status)==="success"&&(f!=null&&f.data)&&(v=f.data),console.log("📊 Body evolution processed data:",v),v||[]}catch(s){const f=ns(s,"BodyEvolution");return l.setError(f),console.warn("⚠️ Body evolution API failed, using empty data"),[]}finally{l.setLoading(!1)}},staleTime:1e3*60*10,refetchOnWindowFocus:!1,retry:1,enabled:i&&(l.period!=="custom"||l.customStartDate&&l.customEndDate)}),{dailyHydration:R}=Ne();y.useEffect(()=>{i||(console.warn("⚠️ User not authenticated, redirecting to login"),window.history.pushState({},"","/login"),window.dispatchEvent(new PopStateEvent("popstate")))},[i]);const H=G("month",["assessments"]),{data:k,isLoading:we,error:z}=V({queryKey:ve("assessments",H.period,H.customStartDate,H.customEndDate),queryFn:async()=>{if(console.log("🔄 Fetching assessments data"),console.log("🔑 Auth token exists:",!!b),!b)throw new Error("No authentication token available");try{const s=await _.get("users/assessments");return console.log("📊 Assessments response:",s),s&&s.status==="success"?(console.log("✅ Assessments data loaded successfully"),s.data||null):s&&s.data?(console.log("✅ Assessments data loaded (alternative structure)"),s.data):(console.warn("⚠️ Unexpected assessments response structure:",s),s||null)}catch(s){throw console.error("❌ Assessments API error:",s),s}},staleTime:1e3*60*10,refetchOnWindowFocus:!1,retry:2,enabled:i&&(H.period!=="custom"||!!(H.customStartDate&&H.customEndDate))}),ke=F||P||u||c||we||S,Fe=w||A||j||m||z||C;y.useEffect(()=>(o(t),()=>{p()}),[t,o,p]),y.useEffect(()=>{E(t)},[t,E]);const $=((X=N==null?void 0:N.history)==null?void 0:X.map(s=>({value:s.weight,label:new Date(s.date).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"}),color:"#B9FF43"})))||[],q=y.useMemo(()=>h&&h.length>0?h.map(s=>({value:parseFloat(s.weight)||0,label:new Date(s.date||s.created_at).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"}),color:"#B9FF43"})):[],[h]),L=y.useMemo(()=>h&&h.length>0?h.map(s=>({value:parseFloat(s.bf)||parseFloat(s.body_fat)||parseFloat(s.bodyFat)||0,label:new Date(s.date||s.created_at).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"}),color:"#ef4444"})):[],[h]);if(y.useMemo(()=>a!=null&&a.bodyFatHistory&&a.bodyFatHistory.length>0?a.bodyFatHistory.map(s=>({value:s.bodyFat||s.body_fat||0,label:new Date(s.date).toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit"}),color:"#ef4444"})):$.map((s,f)=>{var v;return{value:15+(Math.random()-.5)*2,label:((v=$[f])==null?void 0:v.label)||"",color:"#ef4444"}}),[a,$]),(Z=B==null?void 0:B.exercises)!=null&&Z.map(s=>({name:s.name,current:s.current,previous:s.previous,change:s.change})),ke)return e.jsx("div",{className:"min-h-screen bg-snapfit-black flex items-center justify-center",children:e.jsxs("div",{className:"text-center",children:[e.jsx(O,{className:"w-8 h-8 animate-spin text-snapfit-green mx-auto mb-4"}),e.jsx("p",{className:"text-white",children:"Carregando dados de progresso..."})]})});if(Fe){const s={assessments:z,weight:w,strength:A,adherence:j,analytics:m};return console.error("🚨 Dashboard errors:",s),Object.values(s).some(v=>{var K,le,ie;return v&&(((K=v.message)==null?void 0:K.includes("401"))||((le=v.message)==null?void 0:le.includes("Unauthorized"))||((ie=v.message)==null?void 0:ie.includes("authentication")))})?e.jsx("div",{className:"min-h-screen bg-snapfit-black flex items-center justify-center p-4",children:e.jsxs("div",{className:"bg-yellow-500/10 text-yellow-400 rounded-xl border border-yellow-500/20 p-6 max-w-md w-full text-center",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Sessão Expirada"}),e.jsx("p",{className:"text-sm mb-4",children:"Sua sessão expirou. Faça login novamente."}),e.jsx("button",{onClick:()=>window.location.href="/login",className:"px-4 py-2 bg-yellow-500 text-snapfit-black rounded-lg hover:bg-yellow-600 transition-colors",children:"Fazer Login"})]})}):e.jsx("div",{className:"min-h-screen bg-snapfit-black flex items-center justify-center p-4",children:e.jsxs("div",{className:"bg-red-500/10 text-red-400 rounded-xl border border-red-500/20 p-6 max-w-md w-full text-center",children:[e.jsx("h3",{className:"text-lg font-medium mb-2",children:"Erro ao carregar dados"}),e.jsx("p",{className:"text-sm mb-4",children:z?"Erro ao carregar dados de avaliações":w?"Erro ao carregar dados de peso":A?"Erro ao carregar dados de força":j?"Erro ao carregar dados de aderência":m?"Erro ao carregar dados de análise":"Não foi possível carregar os dados de progresso"}),e.jsxs("div",{className:"text-xs text-gray-500 mb-4",children:["Token: ",localStorage.getItem("accessToken")?"Presente":"Ausente"]}),e.jsxs("div",{className:"space-x-2",children:[e.jsx("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors",children:"Tentar novamente"}),e.jsx("button",{onClick:()=>x("/login"),className:"px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"Fazer Login"})]})]})})}return e.jsxs("div",{className:"space-y-6",children:[(k==null?void 0:k.name)&&e.jsxs("h1",{className:"text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white",children:["Olá, ",k==null?void 0:k.name," 👋"]}),e.jsx(Ns,{streak:((J=k==null?void 0:k.assessments)==null?void 0:J.days_met_goals)||21}),e.jsx(hs,{assessments:$,onAddBFAssessment:(s,f,v)=>{console.log("Assessment added:",{weight:s,bodyFat:f,photo:v}),n(new Date().toISOString())},onAddMeasurements:s=>{console.log("Measurements added:",s),n(new Date().toISOString())}}),e.jsxs("div",{className:"mobile-cards-wrapper",children:[e.jsx("div",{className:"mobile-scroll-container",children:e.jsxs("div",{className:"mobile-scroll-content md:grid md:grid-cols-2 md:gap-4",children:[e.jsx(T,{title:"Peso Atual",value:N!=null&&N.current?`${N.current} kg`:"-- kg",icon:e.jsx(M,{className:"animate-pulse-slow"}),change:(N==null?void 0:N.change)||0,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(T,{title:"Gordura Corporal",value:a!=null&&a.bodyFatPercentage?`${a.bodyFatPercentage}%`:"--%",icon:e.jsx(Ke,{className:"animate-pulse-slow"}),change:-1.2,className:"mobile-card stagger-item animate-slide-in-right",showScientificBadge:!0}),e.jsx(T,{title:"Calorias Hoje",value:`${(a==null?void 0:a.calorieAdherence)||0} kcal`,icon:e.jsx(Ue,{className:"animate-pulse-slow"}),change:0,className:"mobile-card stagger-item animate-slide-in-right"}),e.jsx(T,{title:"Treinos Semana",value:`${((Y=r==null?void 0:r.workout)==null?void 0:Y.completed)||0}/${((ee=r==null?void 0:r.workout)==null?void 0:ee.total)||6}`,icon:e.jsx(Qe,{className:"animate-pulse-slow"}),change:((se=r==null?void 0:r.workout)==null?void 0:se.percentage)||0,className:"mobile-card stagger-item animate-slide-in-right"})]})}),e.jsx("div",{className:"mobile-scroll-indicator"})]}),e.jsx(ws,{}),e.jsxs("div",{className:"card p-4 sm:p-6 animate-slide-up",children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Progresso Semanal"}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-4 gap-4",children:[e.jsx(I,{value:((te=r==null?void 0:r.workout)==null?void 0:te.completed)||0,max:((ae=r==null?void 0:r.workout)==null?void 0:ae.total)||6,label:"Treinos",color:"#B9FF43",className:"stagger-item animate-fade-in"}),e.jsx(I,{value:85,max:100,label:"Nutrição",color:"#B9FF43",className:"stagger-item animate-fade-in"}),e.jsx(I,{value:70,max:100,label:"Sono",color:"#B9FF43",className:"stagger-item animate-fade-in"}),e.jsx(I,{value:R.current,max:R.goal,label:"Água",color:"#B9FF43",className:"stagger-item animate-fade-in"})]})]}),e.jsx(vs,{weekData:r,weekDataDb:(re=k==null?void 0:k.assessments)==null?void 0:re.weekly_diet_progress}),e.jsx(Ge,{}),e.jsx(Ve,{}),e.jsx(Xe,{}),e.jsx(Ze,{}),e.jsx(Je,{}),e.jsxs("div",{className:"card p-4",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h3",{className:"text-base font-bold text-white",children:"Evolução Corporal"}),l.error&&e.jsxs("div",{className:"text-xs text-yellow-400 bg-yellow-400/10 px-2 py-1 rounded",children:["⚠️ ",l.error]}),l.isLoading&&e.jsx(O,{className:"w-4 h-4 animate-spin text-snapfit-green"})]}),e.jsx(Ye,{period:l.period,onPeriodChange:l.setPeriod,onCustomDateChange:l.setCustomDates})]}),S?e.jsx("div",{className:"h-[200px] sm:h-[250px] flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-gray-400",children:[e.jsx(O,{className:"w-8 h-8 animate-spin mx-auto mb-2"}),e.jsx("p",{className:"text-sm",children:"Carregando dados de evolução corporal..."})]})}):q.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-[200px] sm:h-[250px]",children:e.jsx(es,{width:"100%",height:"100%",children:e.jsxs(ss,{data:q.map((s,f)=>{var v;return{label:s.label,weight:s.value,bodyFat:((v=L[f])==null?void 0:v.value)||0}}),margin:{top:5,right:30,bottom:5,left:5},children:[e.jsx(ts,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),e.jsx(as,{dataKey:"label",fontSize:12,tickMargin:10,tick:{fontSize:10,fill:"rgba(255,255,255,0.5)"},stroke:"rgba(255,255,255,0.2)"}),e.jsx(ue,{yAxisId:"weight",orientation:"left",fontSize:12,tickMargin:10,tick:{fontSize:10,fill:"rgba(255,255,255,0.5)"},stroke:"rgba(255,255,255,0.2)",label:{value:"Peso (kg)",angle:-90,position:"insideLeft",style:{textAnchor:"middle",fill:"rgba(255,255,255,0.5)",fontSize:"10px"}}}),e.jsx(ue,{yAxisId:"bf",orientation:"right",fontSize:12,tickMargin:10,tick:{fontSize:10,fill:"rgba(255,255,255,0.5)"},stroke:"rgba(255,255,255,0.2)",label:{value:"BF (%)",angle:90,position:"insideRight",style:{textAnchor:"middle",fill:"rgba(255,255,255,0.5)",fontSize:"10px"}}}),e.jsx(rs,{contentStyle:{backgroundColor:"#1E1E1E",border:"1px solid rgba(185, 255, 67, 0.3)",color:"white"},itemStyle:{color:"white"},labelStyle:{color:"white"}}),e.jsx(he,{yAxisId:"weight",type:"monotone",dataKey:"weight",name:"Peso (kg)",stroke:"#B9FF43",strokeWidth:2,dot:{r:4,fill:"#B9FF43",stroke:"#B9FF43"},activeDot:{r:6,fill:"#B9FF43",stroke:"#B9FF43"},filter:"drop-shadow(0 0 3px rgba(185, 255, 67, 0.5))"}),e.jsx(he,{yAxisId:"bf",type:"monotone",dataKey:"bodyFat",name:"Gordura (%)",stroke:"#FF6B6B",strokeWidth:2,dot:{r:4,fill:"#FF6B6B",stroke:"#FF6B6B"},activeDot:{r:6,fill:"#FF6B6B",stroke:"#FF6B6B"},filter:"drop-shadow(0 0 3px rgba(255, 107, 107, 0.5))"})]})})}),e.jsx("div",{className:"mt-4 pt-3 border-t border-snapfit-green/20",children:e.jsxs("div",{className:"flex flex-wrap gap-4 justify-center text-xs text-gray-300",children:[e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-snapfit-green"}),e.jsxs("span",{children:["Peso: ",Math.max(...q.map(s=>s.value)).toFixed(1),"kg máx"]})]}),L.length>0&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-red-400"}),e.jsxs("span",{children:["BF: ",Math.min(...L.map(s=>s.value)).toFixed(1),"% mín"]})]}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-blue-400"}),e.jsxs("span",{children:["Variação: ",(Math.max(...q.map(s=>s.value))-Math.min(...q.map(s=>s.value))).toFixed(1),"kg"]})]}),L.length>1&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("div",{className:"w-2 h-2 rounded-full bg-yellow-400"}),e.jsxs("span",{children:["Tendência: ",((oe=L[L.length-1])==null?void 0:oe.value)<((ne=L[0])==null?void 0:ne.value)?"↓ Reduzindo":"↑ Aumentando"]})]})]})})]}):C?e.jsx("div",{className:"h-[200px] sm:h-[250px] flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-red-400",children:[e.jsx(M,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Erro ao carregar dados de evolução corporal"}),e.jsx("p",{className:"text-xs mt-1",children:"Verifique sua conexão e tente novamente"}),e.jsx("button",{onClick:()=>{console.log("🔄 Manual refetch triggered for body evolution data"),D()},className:"mt-2 px-3 py-1 bg-red-500/20 text-red-400 rounded text-xs hover:bg-red-500/30 transition-colors",children:"Tentar novamente"})]})}):e.jsx("div",{className:"h-[200px] sm:h-[250px] flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-gray-400",children:[e.jsx(M,{className:"w-12 h-12 mx-auto mb-2 opacity-50"}),e.jsx("p",{className:"text-sm",children:"Nenhum dado de evolução corporal disponível"}),e.jsx("p",{className:"text-xs mt-1",children:"Adicione avaliações para ver seu progresso"}),e.jsxs("p",{className:"text-xs mt-1 text-snapfit-green",children:["Período: ",l.period==="custom"?"Personalizado":l.period]})]})})]}),e.jsxs("div",{className:"card p-6",children:[e.jsx("h3",{className:"text-lg font-bold mb-4",children:"Histórico de Avaliações"}),e.jsx(os,{assessments:$,refresh:d})]})]})}export{Ds as HomePage};
